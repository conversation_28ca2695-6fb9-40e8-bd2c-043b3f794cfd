package closing

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
)

var userId int64 = 164820419855511077

func init() {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
}

//
//func TestClosingWare(t *testing.T) {
//	log.InitLogger("log", "info", false)
//	mr := proto.MRecord{
//		OrderId:         ,
//		Offset:          "",
//		MatchID:         0,
//		Code:            "",
//		DealPrice:       decimal.Decimal{},
//		DealVolume:      decimal.Decimal{},
//		MatchTime:       time.Time{},
//		IsProtocalTrade: false,
//		Level:           0,
//		IsMaker:         false,
//		IsReturn:        false,
//		UserID:          0,
//		AccountType:     0,
//	}
//	service.DealWareClose(mr)
//}
