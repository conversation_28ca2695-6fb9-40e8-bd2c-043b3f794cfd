package core

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	rpcclient "bc/libs/rpc.client"
	"encoding/gob"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"testing"
)

var userId int64 = 164820419855511077

func init() {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
}

func TestClosingWare(t *testing.T) {
	log.InitLogger("log", "info", false)

}

func TestOpen(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcCore("0.0.0.0:18010")
	gob.Register(new(proto.OrderOpenRsp)) //订单处理
	arg := &proto.OrderOpenArgs{
		RequestId:          database.NextID(),
		PlanOrderId:        1,
		ContractCode:       "BTCUSDT",
		HoldType:           define.AccountTypeByFullHouse,
		Side:               "S",
		Amount:             10,
		Leverage:           100,
		OrderType:          0, //1.条件单
		UserId:             userId,
		IpAddress:          "aaa",
		DeviceId:           "bbb",
		APPID:              2,
		Limit:              0,
		Stop:               0,
		EntrustLimit:       decimal.Decimal{},
		EntrustStop:        decimal.Decimal{},
		TriggerType:        0,
		TriggerEntrustType: 0,
		TriggerPrice:       decimal.Decimal{},
		EntrustType:        define.EntrustTypeMarket,
		EntrustStrategy:    0,
		Price:              nums.NewFromInt64(49000),
		Mode:               3,
		ClientOrderId:      0,
		IsDepthBatchPlace:  false,
	}
	reply := &define.Reply{}
	err := rpcclient.EntrustOpen(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply))
}

func TestOpenWare(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcCore("0.0.0.0:18010")
	gob.Register(new(proto.OrderOpenRsp)) //订单处理
	arg := &proto.OrderOpenArgs{
		RequestId:          database.NextID(),
		PlanOrderId:        0,
		ContractCode:       "BTCUSDT",
		HoldType:           define.AccountTypeByWareHouse,
		Side:               "S",
		Amount:             10,
		Leverage:           100,
		OrderType:          0, //1.条件单
		UserId:             userId,
		IpAddress:          "aaa",
		DeviceId:           "bbb",
		APPID:              2,
		Limit:              0,
		Stop:               0,
		EntrustLimit:       decimal.Decimal{},
		EntrustStop:        decimal.Decimal{},
		TriggerType:        0,
		TriggerEntrustType: 0,
		TriggerPrice:       decimal.Decimal{},
		EntrustType:        define.EntrustTypeMarket,
		EntrustStrategy:    0,
		Price:              nums.NewFromInt64(49000),
		Mode:               3,
		ClientOrderId:      0,
		IsDepthBatchPlace:  false,
	}
	reply := &define.Reply{}
	err := rpcclient.EntrustOpen(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply))
}

func TestNotifyDepthChange(t *testing.T) {

}

func TestPlanOpen(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcCore("0.0.0.0:18010")
	gob.Register(new(proto.OrderOpenRsp)) //订单处理
	arg := &proto.OrderOpenArgs{
		RequestId:          database.NextID(),
		PlanOrderId:        2,
		ContractCode:       "BTCUSDT",
		HoldType:           define.AccountTypeByFullHouse,
		Side:               "B",
		Amount:             5,
		Leverage:           100,
		OrderType:          1, //1.条件单
		UserId:             userId,
		IpAddress:          "aaa",
		DeviceId:           "bbb",
		APPID:              2,
		Limit:              55000,
		Stop:               39000,
		EntrustLimit:       decimal.Decimal{},
		EntrustStop:        decimal.Decimal{},
		TriggerType:        0,
		TriggerEntrustType: 0,
		TriggerPrice:       nums.NewFromFloat(50000),
		EntrustType:        define.EntrustTypeMarket,
		EntrustStrategy:    0,
		Price:              nums.NewFromInt64(0),
		Mode:               3,
		ClientOrderId:      0,
		IsDepthBatchPlace:  false,
	}
	reply := &define.Reply{}
	err := rpcclient.EntrustOpen(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply))
}

func TestClose(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcCore("0.0.0.0:9902")
	gob.Register(new(proto.OrderCloseRsp)) //订单处理
	arg := &proto.OrderCloseArgs{
		Lang:              0,
		NToken:            "",
		PositionId:        100,
		PosId:             "164820419855511077",
		ContractCode:      "BTCUSDT",
		CloseType:         1,
		Amount:            1,
		Side:              "B",
		OrderType:         0, //1.条件单
		RequestId:         database.NextID(),
		UserId:            userId,
		IpAddress:         "aaa",
		DeviceId:          "bbb",
		APPID:             2,
		CloseId:           "",
		PlanCloseOrderId:  0,
		TriggerPrice:      decimal.Decimal{},
		TriggerBuyPrice:   0,
		TriggerSellPrice:  0,
		TriggerForcePrice: 0,
		EntrustType:       define.EntrustTypeMarket,
		EntrustStrategy:   0,
		Price:             nums.NewFromInt64(29000),
		Mode:              3,
		IsAdminOp:         false,
		ForceCloseID:      0,
		ClientOrderId:     0,
	}
	reply := &define.Reply{}
	err := rpcclient.EntrustClose(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Logf("ret:%v,errCode:%v", reply.Ret, reply.Msg)
}

func TestClosePlanClose(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcCore("0.0.0.0:18010")
	gob.Register(new(proto.OrderCloseRsp)) //订单处理
	arg := &proto.OrderCloseArgs{
		Lang:              0,
		NToken:            "",
		PositionId:        309925990715359232,
		PosId:             "164820419855511077",
		ContractCode:      "BTCUSDT",
		CloseType:         1,
		Amount:            1,
		Side:              "B",
		OrderType:         4, //1.条件单
		RequestId:         database.NextID(),
		UserId:            userId,
		IpAddress:         "aaa",
		DeviceId:          "bbb",
		APPID:             2,
		CloseId:           "",
		PlanCloseOrderId:  2422,
		TriggerPrice:      decimal.Decimal{},
		TriggerBuyPrice:   0,
		TriggerSellPrice:  0,
		TriggerForcePrice: 0,
		EntrustType:       define.EntrustTypeMarket,
		EntrustStrategy:   0,
		Price:             nums.NewFromInt64(49000),
		Mode:              3,
		IsAdminOp:         false,
		ForceCloseID:      0,
		ClientOrderId:     0,
	}
	reply := &define.Reply{}
	err := rpcclient.EntrustClose(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Logf("ret:%v,errCode:%v", reply.Ret, reply.Msg)
}

func TestFollowOpen(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcFollow("0.0.0.0:9907")
	gob.Register(new(proto.FollowOrderOpenRsp)) //订单处理
	arg := &proto.FollowOrderOpenArgs{
		RequestId:          database.NextID(),
		PlanOrderId:        0,
		ContractCode:       "BTCUSDT",
		Side:               "S",
		Amount:             10,
		Leverage:           100,
		OrderType:          0, //1.条件单
		UserId:             userId,
		IpAddress:          "aaa",
		DeviceId:           "bbb",
		APPID:              2,
		EntrustLimit:       decimal.Decimal{},
		EntrustStop:        decimal.Decimal{},
		TriggerType:        0,
		TriggerEntrustType: 0,
		TriggerPrice:       decimal.Decimal{},
		EntrustType:        define.EntrustTypeMarket,
		EntrustStrategy:    0,
		Price:              nums.NewFromInt64(49000),
		Mode:               3,
		ClientOrderId:      0,
	}
	reply := &define.Reply{}
	err := rpcclient.FollowOpen(0, arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply))
}

func TestFollowPlanOpen(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcFollow("0.0.0.0:9907")
	gob.Register(new(proto.FollowOrderOpenRsp)) //订单处理
	arg := &proto.FollowOrderOpenArgs{
		Lang:               0,
		NToken:             "",
		ContractCode:       "BTCUSDT",
		Side:               "S",
		Amount:             10,
		Leverage:           100,
		FollowOrderId:      0,
		OrderType:          0, //1.条件单
		PlanOrderId:        3,
		RequestId:          database.NextID(),
		UserId:             userId,
		IpAddress:          "aaa",
		DeviceId:           "bbb",
		APPID:              2,
		Limit:              nums.NewFromFloat(55000),
		Stop:               nums.NewFromFloat(45000),
		EntrustLimit:       decimal.Decimal{},
		EntrustStop:        decimal.Decimal{},
		TriggerType:        0,
		EntrustType:        define.EntrustTypeMarket,
		EntrustStrategy:    0,
		Price:              nums.NewFromInt64(49000),
		Mode:               3,
		TriggerEntrustType: 0,
		TriggerPrice:       decimal.Decimal{},
		ClientOrderId:      0,
	}
	reply := &define.Reply{}
	err := rpcclient.FollowOpen(0, arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply))
}

func TestFollowClose(t *testing.T) {
	log.InitLogger("log", "info", false)

	rpcclient.InitRpcFollow("0.0.0.0:9907")
	gob.Register(new(proto.OrderCloseRsp)) //订单处理
	arg := &proto.FollowOrderCloseArgs{
		ReqLang:           0,
		NToken:            "",
		PositionId:        311395859386335232,
		OrderType:         0, //1.条件单
		CloseType:         0,
		RequestId:         database.NextID(),
		UserId:            userId,
		IpAddress:         "aaa",
		DeviceId:          "bbb",
		APPID:             2,
		PlanCloseOrderId:  0,
		TriggerPrice:      decimal.Decimal{},
		TriggerBuyPrice:   0,
		TriggerSellPrice:  0,
		TriggerForcePrice: decimal.Decimal{},
		EntrustType:       define.EntrustTypeMarket,
		EntrustStrategy:   0,
		Price:             nums.NewFromInt64(49000),
		Mode:              3,
		IsAdminOp:         false,
		FollowOrderId:     0,
		ForceCloseID:      0,
		ClientOrderId:     0,
	}
	reply := &define.Reply{}
	err := rpcclient.FollowClose(arg, reply)
	if err != nil {
		t.Log("err", zap.Error(err))
		return
	}
	t.Log("r", zap.Any("rsp", reply), zap.Int("ret", reply.Ret))
}
