/*
@Time : 3/15/20 10:54 上午
<AUTHOR> mocha
@File : forecontainer
*/
package pricelist

import (
	"bc/libs/container"
	"bc/libs/define"
	"bc/libs/log"
	"bytes"
	"fmt"
	"github.com/emirpasic/gods/lists/doublylinkedlist"
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/emirpasic/gods/utils"
	"github.com/shopspring/decimal"
	"sync"
)

const (
	SideBuy  = "B"
	SideSell = "S"
)

type PriceContainer struct {
	l            sync.RWMutex
	LastPrice    decimal.Decimal
	BuyPriceMap  *treemap.Map
	SellPriceMap *treemap.Map
	contractCode string
}

func NewPriceList(symbol string, isBuyDesc bool) *PriceContainer {
	var buy, sell utils.Comparator
	if isBuyDesc {
		buy = container.FloatDescCompartor
		sell = container.FloatAscCompartor
	} else {
		buy = container.FloatAscCompartor
		sell = container.FloatDescCompartor
	}
	return &PriceContainer{
		contractCode: symbol,
		BuyPriceMap:  treemap.NewWith(buy),
		SellPriceMap: treemap.NewWith(sell),
	}
}

func (p *PriceContainer) Stat() string {
	return fmt.Sprintf("buy,size:%v,sell:size:%v,", p.BuyPriceMap.Size(), p.SellPriceMap.Size())
}

func (p *PriceContainer) String() string {
	more := p.BuyPriceMap.Iterator()
	var bf bytes.Buffer
	bf.WriteString("[{more:")
	bf.WriteString("[")
	for more.Next() {
		bf.WriteString("{ price:")
		pricePre := more.Key().(float64)
		bf.WriteString(fmt.Sprintf("%v,", pricePre))
		bf.WriteString("[")
		list := more.Value().(*doublylinkedlist.List)
		list.Each(func(index int, value interface{}) {
			fmt.Sprintf("%v,", value)
		})
		bf.WriteString("]")
		bf.WriteString("},")
	}
	bf.WriteString("],less:")

	bf.WriteString("[")
	less := p.SellPriceMap.Iterator()
	for less.Next() {
		bf.WriteString("{ price:")
		pricePre := less.Key().(float64)
		bf.WriteString(fmt.Sprintf("%v,", pricePre))
		bf.WriteString("[")
		list := less.Value().(*doublylinkedlist.List)
		list.Each(func(index int, value interface{}) {
			fmt.Sprintf("%v,", value)
		})
		bf.WriteString("]")
		bf.WriteString("},")
	}
	bf.WriteString("]")

	return bf.String()
}

func (p *PriceContainer) Add(condition int, price float64, id int64) {
	p.l.Lock()
	defer p.l.Unlock()
	log.Debugf("开始放入价格链表中，condition；%v,price:%v，id;%v", condition, price, id)
	//defer func() {
	//	log.Debugf("buy；%+v", p.More.String())
	//	log.Debugf("sell；%+v", p.Less.String())
	//}()
	switch condition {
	case define.OrderConditionGreaterOrEqual:
		v, ok := p.BuyPriceMap.Get(price)
		if !ok {
			p.BuyPriceMap.Put(price, doublylinkedlist.New(id))
			return
		}
		vs, ok := v.(*doublylinkedlist.List)
		if !ok {
			log.Error("not is doublylinkedlist.List")
			return
		}
		if vs.Contains(id) {
			log.Infof("盘中存在id:%v", id)
			return
		}
		vs.Add(id)
	case define.OrderConditionLessOrEqual:
		v, ok := p.SellPriceMap.Get(price)
		if !ok {
			p.SellPriceMap.Put(price, doublylinkedlist.New(id))
			return
		}
		vs, ok := v.(*doublylinkedlist.List)
		if !ok {
			log.Error("not is doublylinkedlist.List")
			return
		}
		if vs.Contains(id) {
			return
		}
		vs.Add(id)
	}
}

func (p *PriceContainer) RemoveId(id int64) {
	p.l.Lock()
	defer p.l.Unlock()
	p.BuyPriceMap.Each(func(_ interface{}, value interface{}) {
		vs, ok := value.(*doublylinkedlist.List)
		if !ok {
			log.Error("not is doublylinkedlist.List")
			return
		}
		vs.Remove(vs.IndexOf(id))
	})
	p.SellPriceMap.Each(func(_ interface{}, value interface{}) {
		vs, ok := value.(*doublylinkedlist.List)
		if !ok {
			log.Error("not is doublylinkedlist.List")
			return
		}
		vs.Remove(vs.IndexOf(id))
	})
}

func (p *PriceContainer) UpdateId(condition int, price float64, id int64) {
	p.RemoveId(id)
	p.Add(condition, price, id)
}

func (p *PriceContainer) RemovePrice(condition int, price float64) {
	p.l.Lock()
	defer p.l.Unlock()
	switch condition {
	case define.OrderConditionGreaterOrEqual:
		p.BuyPriceMap.Remove(price)
	case define.OrderConditionLessOrEqual:
		p.SellPriceMap.Remove(price)
	}
}

func (p *PriceContainer) Clear(condition int) {
	p.l.Lock()
	defer p.l.Unlock()
	switch condition {
	case define.OrderConditionGreaterOrEqual:
		p.BuyPriceMap.Clear()
	case define.OrderConditionLessOrEqual:
		p.SellPriceMap.Clear()
	}
}

func (p *PriceContainer) Name() string {
	return p.contractCode
}
