/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package oktrade

import (
	"bc/libs/compress"
	"bc/libs/convert"
	"bc/libs/json"
	"bc/libs/log"
	"fmt"
	"io/ioutil"
	"testing"

	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://real.okex.com:8443/ws/v3", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))

	go func() {
		for {
			w.SetPingHandler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.ReadMessage()
			//d, err := zip.UnzipByte(b)
			//if err != nil {
			//	te.Log("err",err)
			//}
			//te.Log(t, string(b), err)
			d, err := compress.FlateUnCompress(b)
			if err != nil {
				log.Errorf("e:%v", err)
			}
			//fmt.Printf(string(d))
			te.Log(t, string(d), err)
		}
	}()

	go func() {

		wsLogin(w, "06f67f12-96ad-45cc-8e6a-57fdd4931280", "abcabc", "C686217BBA3E1C777A47EE37113BFBE3")
		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}

func testLogin(w *websocket.Conn) {
	str := `{"op":"login","args":["985d5b66-57ce-40fb-b714-afc0b9787083","123456","1538054050",
"7L+zFQ+CEgGu5rzCj4+BdV2/uUHGqddA9pI6ztsRRPs="]}`
	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

}

func testDepth(w *websocket.Conn) {
	str := `{"op": "subscribe","args":["swap/depth:BTC-USDT-SWAP","swap/funding_rate:BTC-USDT-SWAP"]}`
	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

}

func TestBClient(t *testing.T) {
	c := NewMarketWsClient(nil, func(data []byte) {
		//t.Logf("d:%v", convert.Bytes2Str(data))
		//unmarsha data
		unData, err := compress.FlateUnCompress(data)
		if err != nil {
			log.Errorf("FlateUnCompress fail,%v", err)
			return
		}
		//t.Logf("undata:%v",string(unData))
		p := new(OkexBasicRsp)
		err = json.Unmarshal(unData, p)
		if err != nil {
			t.Logf("unmarsha fail,%v", err)
			return
		}
		//t.Logf("t data:%v",string(p.Data))
		switch p.Table {
		case "swap/depth":
			var ticker []OkDepth
			err = json.Unmarshal(p.Data, &ticker)
			if err != nil {
				return
			}

			t.Logf("depth:%+v", ticker)
		case "swap/funding_rate":
			t.Logf("d:%v", string(p.Data))
			var d []OkFundingRate
			err = json.Unmarshal(p.Data, &d)
			if err != nil {
				t.Logf("rate:%+v", d)
			}
			t.Logf("rate:%+v", d)
		default:
			t.Logf(convert.Bytes2Str(unData))

		}
	})
	c.Subscript([]string{"swap/funding_rate:BTC-USDT-SWAP"})
	//c.Subscript([]string{"swap/depth:BTC-USDT-SWAP","swap/funding_rate:BTC-USDT-SWAP"})
	c.Start()
	select {}
}
