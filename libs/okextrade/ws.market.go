/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package oktrade

import (
	"bc/libs/convert"
	"bc/libs/log"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	_wsMarket        = "wss://real.okex.com:8443/ws/v3"
	_sub             = "subscribe"
	_unSub           = "unsubscribe"
	_login           = "login"
	Contract         = "%s-%s-SWAP"
	TopicDepth       = "swap/depth:%s"
	TopicFundingRate = "swap/funding_rate:%s"
)

const (
	channel_hold  = "swap/position:%s"   //持仓
	channel_trade = "swap/order:%s"      //交易订单
	channel_place = "swap/order_algo:%s" //委托策略
)

const (
	ActionPartial = "partial"
	ActionUpdate  = "update"
)

//{"op": "subscribe", "args": ["swap/depth:BTC-USD-SWAP"]}

//entries

type MarketWsClient struct {
	dialer     *websocket.Dialer
	wsPoint    string
	topics     []string
	con        *websocket.Conn
	lastError  error
	errLock    sync.Mutex
	status     bool
	msgHandler func([]byte)
}

type Config struct {
	WsPoint string
	dialer  *websocket.Dialer
}

type OkBasicReq struct {
	Op   string   `json:"op"`
	Args []string `json:"args"`
}

//{"event":"error","message":"<error_message>","errorCode":"<errorCode>"}
type OkexBasicRsp struct {
	Table  string          `json:"table"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
	OkexErroRsp
}

type OkexErroRsp struct {
	Event     string `json:"event"`
	Message   string `json:"message"`
	ErrorCode string `json:"error_code"`
}

//[{"instrument_id":"BTC-USDT-SWAP","asks":[["6256.4","19","0","2"]]
type OkDepth struct {
	InstrumentId string     `json:"instrument_id"`
	Asks         [][]string `json:"asks"`
	Bids         [][]string `json:"bids"`
	Time         string     `json:"timestamp"`
	Checksum     int64      `json:"checksum"`
}

//资金费率
type OkFundingRate struct {
	EstimatedRate  string `json:"estimated_rate"`  //estimated_rate
	FundingRate    string `json:"funding_rate"`    //当期资金费率
	FundingTime    string `json:"funding_time"`    //当期资金费率时间
	InstrumentId   string `json:"instrument_id"`   //instrumentId
	InterestRate   string `json:"interest_rate"`   //利率
	SettlementTime string `json:"settlement_time"` //结算时间
}

//{
//"table": "swap/depth",
//"action": "update",
//"data": [{
//"instrument_id": "BTC-USD-SWAP",
//"asks": [
//["5621.7", "58", "0", "2"],
//["5621.8", "125", "0", "5"],
//["5621.9", "0", "0", "0"],
//["5622", "84", "0", "2"],
//["5623.5", "0", "0", "0"],
//["5624.2", "4", "0", "1"],
//["5625.1", "0", "0", "0"],
//["5625.9", "0", "0", "0"],
//["5629.3", "2", "0", "1"],
//["5650", "187", "0", "8"],
//["5789", "1", "0", "1"]
//],
//"bids": [
//["5621.3", "287", "0", "8"],
//["5621.2", "41", "0", "1"],
//["5621.1", "2", "0", "1"],
//["5621", "26", "0", "2"],
//["5620.8", "194", "0", "2"],
//["5620", "2", "0", "1"],
//["5618.8", "204", "0", "2"],
//["5618.4", "0", "0", "0"],
//["5617.2", "2", "0", "1"],
//["5609.9", "0", "0", "0"],
//["5433", "0", "0", "0"],
//["5430", "0", "0", "0"]
//],
//"timestamp": "2019-05-06T07:03:33.048Z",
//"checksum": -186865074
//}]

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) Start() {
Retry:
	conn, rsp, err := c.dialer.Dial(c.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to okex fail,err:%v,http rsp:%+v", err, rsp)
		goto Retry
	}
	c.setLastError(nil)
	c.con = conn
	c.subscript(c.topics)
	c.status = true
	//log.Infof("success get websocket client conn for binance")
	c.receive()
}

func (c *MarketWsClient) Subscript(topics []string) {
	//log.Infof("topics:%v", topics)
	c.topics = topics
	c.subscript(c.topics)
}

func (c *MarketWsClient) ReSubDepth() {
	var topics []string
	for _, v := range c.topics {
		if strings.Contains(v, "depth") {
			topics = append(topics, v)
		}
	}
	if len(topics) == 0 || !c.status {
		return
	}
	c.unSubScript(topics)
	c.subscript(topics)
}

func (c *MarketWsClient) subscript(topics []string) {
	if len(topics) == 0 {
		fmt.Println("binance 订阅topic数量为0")
		return
	}
	d := OkBasicReq{Op: _sub, Args: topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	fmt.Printf("binance sub:%v\n", convert.Bytes2Str(b))
	if c.con == nil {
		fmt.Printf("binance 连接为空")
		return
	}
	e := c.con.WriteMessage(websocket.TextMessage, b)
	fmt.Printf("write sub msg:%v", e)
	if e != nil {
		fmt.Printf("write sub msg fail,%v\n", e)
		c.setLastError(e)
	}
}

func (c *MarketWsClient) UnSubscript() {
	c.unSubScript(c.topics)
}

func (c *MarketWsClient) unSubScript(topics []string) {
	d := OkBasicReq{Op: _unSub, Args: topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	e := c.con.WriteMessage(websocket.TextMessage, b)
	if e != nil {
		c.setLastError(e)
	}
}

func (c *MarketWsClient) setLastError(err error) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.lastError = err
}

func (c *MarketWsClient) setConn(conn *websocket.Conn) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.con = conn
}

func (c *MarketWsClient) receive() {
	go func() {
		for {
			if c.reMsg() {
				return
			}
		}
	}()

}

func (c *MarketWsClient) reMsg() bool {
	if !c.status {
		return true
	}
	conn := c.con
	if conn == nil {
		c.setLastError(errors.New("ws conn is null"))
		return true
	}
	conn.SetPingHandler(func(appData string) error {
		c.con.WriteMessage(websocket.PongMessage, nil)
		return nil
	})
	_, b, err := conn.ReadMessage()
	if err != nil {
		log.Errorf("bian readmessage fail,%v", err)
		c.setLastError(err)
		return true
	}
	//fmt.Printf("d:%v,err:%v\n", string(b), err)
	if c.msgHandler != nil {
		go c.msgHandler(b)
	}
	return false
}

func (c *MarketWsClient) Loop() {
	go func() {
		for {
			if !c.status {
				continue
			}
			c.checkError()
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) checkError() {
	if c.con == nil || c.lastError != nil {
		c.status = false
		c.Start()
		return
	}
}

//{"table":"swap/funding_rate","data":[{"estimated_rate":"-0.00044","funding_rate":"-0.00057979","funding_time":"2020-03-30T16:00:00.000Z","instrument_id":"BTC-USDT-SWAP","interest_rate":"0","settlement_time":"2020-03-31T08:00:00.000Z"}]} <nil>
