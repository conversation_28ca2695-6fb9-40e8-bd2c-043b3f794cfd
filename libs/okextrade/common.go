package oktrade

import (
	"bc/libs/json"
	"bc/libs/log"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"github.com/gorilla/websocket"
	"strconv"
	"time"
)

func IsoTime() string {
	utcTime := time.Now().UTC()
	iso := utcTime.String()
	isoBytes := []byte(iso)
	iso = string(isoBytes[:10]) + "T" + string(isoBytes[11:23]) + "Z"
	return iso
}

func EpochTime() string {
	millisecond := time.Now().UnixNano() / 1000000
	epoch := strconv.Itoa(int(millisecond))
	epochBytes := []byte(epoch)
	epoch = string(epochBytes[:10]) + "." + string(epochBytes[10:])
	return epoch
}

func HmacSha256Base64Signer(message string, secretKey string) (string, error) {
	mac := hmac.New(sha256.New, []byte(secretKey))
	_, err := mac.Write([]byte(message))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(mac.Sum(nil)), nil
}

//{"op":"login","args":["985d5b66-57ce-40fb-b714-afc0b9787083","123456","1538054050.975",
//"7L+zFQ+CEgGu5rzCj4+BdV2/uUHGqddA9pI6ztsRRPs="]}
func wsLogin(conn *websocket.Conn, apiKey, secretKey, passphrase string) {
	t := EpochTime()
	fmt.Println(t)
	sign, err := HmacSha256Base64Signer(t+"GET"+"/users/self/verify", secretKey)
	//sign:=msg.Hmac256(t+"GET"+ "/users/self/verify",secretKey)
	//sign=base64.StdEncoding.EncodeToString([]byte(sign))
	args := []string{apiKey, passphrase, t, sign}
	r := &OkBasicReq{Op: _login, Args: args}
	b, err := json.Marshal(r)
	if err != nil {
		log.Error("json marshal fail,")
	}
	conn.WriteMessage(websocket.TextMessage, b)
}
