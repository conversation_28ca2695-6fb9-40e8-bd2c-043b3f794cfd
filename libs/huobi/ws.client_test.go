/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package huobi

import (
	"bc/libs/compress"
	"bc/libs/json"
	"bc/libs/log"
	"fmt"
	"io/ioutil"
	"testing"
	"time"

	"bc/libs/convert"
	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	//w, rsp, err := websocket.DefaultDialer.Dial("wss://api.hbdm.com/ws_index", nil)
	w, rsp, err := websocket.DefaultDialer.Dial("wss://api.hbdm.com/linear-swap-ws", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	t := time.Tick(5 * time.Second)
	str := `{"op": "subscribe", "args": ["trade"]}`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
	go func() {
		for {
			select {
			case <-t:
				//s := Sub{
				//	Sub: "market.BTCUSDT.basis.1min.open",
				//	//Sub: "market.BTC-USDT.trade.detail",
				//	ID: "BTCUSDT",
				//				//}
				//				s := `{
				//  "sub": "market.BTC-USDT.detail",
				//  "id": "id6"
				// }
				//`
				s := `{                                          
    "sub": "market.BTC-USDT.depth.step0",       
    "id": "id5"                                
    } 
                                   
`
				//b, _ := json.Marshal(s)
				//str1 := `{"op": "unsubscribe", "args": ["funding"]}`
				w.WriteMessage(websocket.TextMessage, []byte(s))

			}
		}
	}()

	go func() {
		for {
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			b, _ = compress.GzipUnCompress(b)
			te.Log(t, convert.Bytes2Str(b), err)
			ping := new(Ping)
			_ = json.Unmarshal(b, ping)
			if ping.Ping != 0 {
				te.Logf("ping:%+v", *ping)
				pong, _ := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
				w.WriteMessage(websocket.TextMessage, pong)
			} else {
			}

		}
	}()

	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	c := NewMarketWsClient(&Config{WsPoint: "wss://api.hbdm.com/linear-swap-ws"}, func(data []byte) {
		t.Logf("d:%v", string(data))

		//p := new(ResultPayload)
		//err := json.Unmarshal(data, p)
		//if err != nil {
		//	t.Logf("unmarsha fail,%v", err)
		//	return
		//}
		//if p.Event == "aggTrade" {
		//	ticker := new(ResultTicker)
		//	err = json.Unmarshal(data, ticker)
		//	if err != nil {
		//		return
		//	}
		//	ticker.DealTime = ticker.DealTime / 1000
		//	t.Logf("ticker:%+v", ticker)
		//
		//}
		//t.Logf(string(data))
	})
	c.Subscript([]string{"market.BTC-USDT.detail"})
	c.Start()
	s := time.Tick(20 * time.Second)
	go func() {
		for range s {
			t.Logf("开始重启")
			c.Subscript([]string{"market.BTC-USDT.detail"})
			//c.Restart()
		}
	}()

	select {}
}
