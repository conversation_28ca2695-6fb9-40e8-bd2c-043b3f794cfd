///*
//@Time : 2/7/20 3:31 下午
//<AUTHOR> mocha
//@File : main
//*/
package huobi

//
//import (
//	"bc/libs/compress"
//	"bytes"
//	"encoding/json"
//	"errors"
//	"fmt"
//	"sync"
//	"time"
//
//	"bc/libs/log"
//	"github.com/gorilla/websocket"
//)
//
//const (
//	_wsMarket = "wss://api.hbdm.com/linear-swap-w"
//)
//
////entries
//
//type MarketWsClient struct {
//	dialer         *websocket.Dialer
//	wsPoint        string
//	topics         []string
//	con            *websocket.Conn
//	lastError      error
//	lock, errLock  sync.RWMutex
//	status         bool
//	closeHandler   func(int, string)
//	msgHandler     func([]byte)
//	lastRv         time.Time
//	timeOutSeconds int64
//}
//
//type Config struct {
//	WsPoint     string
//	dialer      *websocket.Dialer
//	timeSeconds int64
//}
//
//func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
//	c := &MarketWsClient{
//		dialer:  nil,
//		wsPoint: _wsMarket,
//		topics:  nil,
//	}
//	if config != nil {
//		if config.WsPoint != "" {
//			c.wsPoint = config.WsPoint
//		}
//		if config.dialer != nil {
//			c.dialer = config.dialer
//		} else {
//			c.dialer = websocket.DefaultDialer
//		}
//		if config.timeSeconds > 0 {
//			c.timeOutSeconds = config.timeSeconds
//		}
//	}
//	c.msgHandler = f
//	return c
//}
//
//func (c *MarketWsClient) CloseHook(f func(int, string)) {
//	c.closeHandler = f
//}
//
//func (c *MarketWsClient) Start() {
//	log.Infof("begin huobi ws start")
//Retry:
//	conn, rsp, err := c.dialer.Dial(c.wsPoint, nil)
//	if err != nil {
//		log.Errorf("dial to huobi fail,err:%v,http rsp:%+v", err, rsp)
//		goto Retry
//	}
//	c.setLastError(nil)
//	c.con = conn
//	c.subscript()
//	c.status = true
//	c.setLastRev()
//	c.receive()
//
//}
//
//func (c *MarketWsClient) Subscript(topics []string) {
//	if c == nil {
//		log.Errorf("client is nil")
//		return
//	}
//	log.Infof("huobi sub topics:%v", topics)
//	c.topics = topics
//	c.subscript()
//
//}
//
//func (c *MarketWsClient) subscript() {
//	if len(c.topics) == 0 {
//		fmt.Println("huobi 订阅topic数量为0")
//		return
//	}
//	if c.con == nil {
//		fmt.Printf("连接为空")
//		c.setLastError(errors.New("conn is nil"))
//		return
//	}
//	var subData [][]byte
//	for _, topic := range c.topics {
//		d := Sub{Sub: topic}
//		b, err := json.Marshal(d)
//		if err != nil {
//			fmt.Errorf("marshal fail,%v", err)
//			return
//		}
//		subData = append(subData, b)
//	}
//	//fmt.Printf("binance sub:%v\n", convert.Bytes2Str(b))
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	for _, v := range subData {
//		e := c.con.WriteMessage(websocket.TextMessage, v)
//		fmt.Printf("write sub msg:%v", string(v))
//		if e != nil {
//			log.Errorf("write sub msg fail,%v", e)
//			c.setLastError(e)
//		}
//	}
//}
//
//func (c *MarketWsClient) unSubscript() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//}
//
//func (c *MarketWsClient) UnSubscript(topics []string) {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//}
//
//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.con.WriteMessage(websocket.TextMessage, pong)
//}
//
//func (c *MarketWsClient) checkPingData(b []byte) bool {
//	if bytes.Contains(b, []byte("ping")) {
//		c.pong()
//		return true
//	}
//	return false
//}
//
//func (c *MarketWsClient) setLastError(err error) {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	c.lastError = err
//}
//
//func (c *MarketWsClient) setLastRev() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	c.lastRv = time.Now()
//}
//
//func (c *MarketWsClient) setConn(conn *websocket.Conn) {
//	c.errLock.Lock()
//	defer c.errLock.Unlock()
//	c.con = conn
//}
//
//func (c *MarketWsClient) receive() {
//	go func() {
//		for {
//			msgType, b, err := c.con.ReadMessage()
//			if err != nil {
//				log.Errorf("huobi read fail,%v", err)
//				c.setLastError(err)
//				log.Errorf("msgType；%v，msg:%+v", msgType, string(b))
//				if e, ok := err.(*websocket.CloseError); ok {
//					if c.closeHandler != nil {
//						c.closeHandler(e.Code, e.Text)
//					}
//				}
//				break
//			}
//			if msgType == -1 {
//				log.Errorf("msgType；%v，msg:%+v", msgType, string(b))
//				c.setLastError(errors.New("msgType -1"))
//				break
//			}
//			//log.Infof("msg:%+v,msgType；%v", string(b), msgType)
//			b, err = compress.GzipUnCompress(b)
//			if err != nil {
//				log.Errorf("解压数据失败，%v", err)
//				continue
//			}
//			c.setLastRev()
//			if c.checkPingData(b) {
//				continue
//			}
//			//fmt.Printf("d:%v,err:%v\n", string(b), err)
//			if c.msgHandler != nil {
//				c.msgHandler(b)
//			}
//		}
//	}()
//
//}
//
//func (c *MarketWsClient) Loop() {
//	go func() {
//		for {
//			log.Debugf("check...................")
//			if !c.status {
//				continue
//			}
//			c.check()
//			time.Sleep(time.Second)
//		}
//	}()
//}
//
//func (c *MarketWsClient) check() {
//	c.lock.RLock()
//	defer c.lock.RUnlock()
//	var longTimeRev bool
//	if !c.lastRv.IsZero() {
//		diff := int64(time.Now().Sub(c.lastRv).Seconds())
//		log.Debugf("time diff:%v", diff)
//		gap := c.timeOutSeconds
//		if gap == 0 {
//			gap = 5
//		}
//		//if diff >= gap {
//		//	log.Errorf("long time no data,begin start")
//		//	longTimeRev = true
//		//}
//	}
//	if c.con == nil || c.lastError != nil || longTimeRev {
//		c.status = false
//		c.Start()
//	}
//}
//
//
//
////d := `{"op": "subscribe", "args": ["funding","trade"]}`
//type BaseAction struct {
//	Action string   `json:"op"`
//	Args   []string `json:"args"`
//}
//
////{"success":true,"subscribe":"funding","request":{"op":"subscribe","args":["funding"]}}
//
//type SubRsp struct {
//	Success   bool   `json:"success"`
//	Subscribe string `json:"subscribe"`
//}
//
//type BasicRsp struct {
//	Table  string          `json:"table"`
//	Action string          `json:"action"`
//	Data   json.RawMessage `json:"data"`
//}
//
//type Ping struct {
//	Ping int64 `json:"ping"`
//}
//
//type Pong struct {
//	Pong int64 `json:"pong"`
//}
//
//type OP struct {
//	OP string `json:"op"`
//	TS int64  `json:"ts"`
//}
//
//type Sub struct {
//	Sub string `json:"sub"`
//	ID  string `json:"id"`
//}
//
//type UnSub struct {
//	UnSub string `json:"unSub"`
//	ID    string `json:"id"`
//}
//
////{
////"ch":"market.BTC-USDT.trade.detail",
////"ts":1603708208346,
////"tick":{
////"id":131602265,
////"ts":1603708208335,
////"data":[
////{
////"amount":2,
////"ts":1603708208335,
////"id":1316022650000,
////"price":13073.3,
////"direction":"buy"
////}
////]
////}
////}
//
//type Ticker struct {
//	Channel string `json:"ch"`
//	Ts      int64  `json:"ts"`
//	Tick    Tick   `json:"tick"`
//}
//
//type TickData struct {
//	Amount int     `json:"amount"`
//	Price  float64 `json:"price"`
//	Side   string  `json:"direction"`
//}
//
//type Tick struct {
//	ID   int64      `json:"id"`
//	Ts   int64      `json:"ts"`
//	Data []TickData `json:"data"`
//}
//
//type IndexData struct {
//	ID            int64  `json:"id"`
//	IndexPrice    string `json:"index_price"`
//	ContractPrice string `json:"contract_price"`
//	Basic         string `json:"basic"`
//	BasicRate     string `json:"basis_rate"`
//}
//
////{
////"ch":"market.BTC-USD.basis.15min.open",
////"ts":1603709195504,
////"tick":{
////"id":1603709100,
////"index_price":"13101.595",
////"contract_price":"13100.9",
////"basis":"-0.695",
////"basis_rate":"-0.0000530469763414301846454572897422031"
////}
////}
//type Index struct {
//	Channel string    `json:"ch"`
//	Ts      int64     `json:"ts"`
//	Tick    IndexData `json:"tick"`
//}
