package utils

import (
	"regexp"
	"strings"
)

// 拼接字符串
func StrBuilder(args ...string) (str string) {
	switch len(args) {
	case 0:
		return
	case 1:
		return args[0]
	}

	n := 0
	for i := 0; i < len(args); i++ {
		n += len(args[i])
	}
	var builder strings.Builder
	builder.Grow(n)
	for _, s := range args {
		builder.WriteString(s)
	}
	str = builder.String()
	builder.Reset()
	return
}

// 拼接字符串,中间用下划线分隔
func StrBuilderByUnderCode(args ...string) (str string) {
	switch len(args) {
	case 0:
		return
	case 1:
		return args[0]
	}

	n := len(args) - 1
	for i := 0; i < len(args); i++ {
		n += len(args[i])
	}
	var builder strings.Builder
	builder.Grow(n)
	for i := range args {
		if i == 0 {
			builder.WriteString(args[i])
		} else {
			builder.WriteString("_")
			builder.WriteString(args[i])
		}
	}
	str = builder.String()
	builder.Reset()
	return
}

// 拼接字符串, 中间用中划线连接
func StrBuilderByHyphen(args ...string) (str string) {
	switch len(args) {
	case 0:
		return
	case 1:
		return args[0]
	}

	n := len(args) - 1
	for i := 0; i < len(args); i++ {
		n += len(args[i])
	}
	var builder strings.Builder
	builder.Grow(n)
	for i := range args {
		if i == 0 {
			builder.WriteString(args[i])
		} else {
			builder.WriteString("-")
			builder.WriteString(args[i])
		}
	}
	str = builder.String()
	builder.Reset()
	return
}

// 拼接字符串,中间用指定字符串分隔
func StrBuilderBySep(sep string, args ...string) (str string) {
	switch len(args) {
	case 0:
		return
	case 1:
		return args[0]
	}

	n := len(sep) * (len(args) - 1)
	for i := 0; i < len(args); i++ {
		n += len(args[i])
	}

	var builder strings.Builder
	builder.Grow(n)
	for i := range args {
		if i == 0 {
			builder.WriteString(args[i])
		} else {
			builder.WriteString(sep)
			builder.WriteString(args[i])
		}
	}
	str = builder.String()
	builder.Reset()
	return
}

var hsReg = regexp.MustCompile(`[\pC|\pP|\pS]+`)

// 包含符号
func HasSymbol(s string) bool {
	return hsReg.MatchString(s)
}

var lnReg = regexp.MustCompile(`[^A-Za-z0-9\-\s]+`)

// 只包含字母或数字
func OnlyLetterAndNumber(s string) bool {
	return !lnReg.MatchString(s)
}
