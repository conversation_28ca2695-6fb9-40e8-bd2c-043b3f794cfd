/*
@Time : 2019-05-06 17:13
<AUTHOR> mocha
@File : crypto
*/
package utils

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"hash/crc32"

	"bc/libs/convert"
)

func Sha256(s string) string {
	h := sha256.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func Md5(s string) string {
	h := md5.New()
	h.Write(convert.Str2Bytes(s))
	return hex.EncodeToString(h.Sum(nil))
}

func CRC32(str string) uint32 {
	return crc32.ChecksumIEEE([]byte(str))
}

func GetSha256(s string) string {
	h := sha256.New()
	h.Write(convert.Str2Bytes(s))
	return fmt.Sprintf("%x", h.Sum(nil))
}
