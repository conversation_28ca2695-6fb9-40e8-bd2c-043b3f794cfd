package utils

import (
	"bytes"
	"errors"
	"io/ioutil"
	"net"
	"net/http"
	"strings"

	"bc/libs/convert"
	"bc/libs/log"
	"go.uber.org/zap"
)

func GetLocalIp() (ip string, err error) {
	addrList, err := net.InterfaceAddrs()

	if err != nil {
		log.Errorf("get net interfaceAddrs return a err:%v", err)
		return
	}

	for _, address := range addrList {

		// 检查ip地址判断是否回环地址
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				ip = ipNet.IP.String()
				log.Infof("Get local ip:%v", ip)
				return
			}
		}
	}
	err = errors.New("can't find ip in net interfaceAddrs")
	return
}

func IsIpValid(ip string) bool {
	var (
		startIp = net.ParseIP("*******")
		endIp   = net.ParseIP("***************")
	)
	trial := net.ParseIP(ip)
	if trial.To4() == nil {
		return false
	}
	if trial.IsMulticast() {
		return false
	}
	if bytes.Compare(trial, startIp) >= 0 && bytes.Compare(trial, endIp) <= 0 {
		//fmt.Printf("%v is between %v and %v\n", trial, startIp, endIp)
		return true
	}
	//fmt.Printf("%v is NOT between %v and %v\n", trial, startIp, endIp)
	return false
}

func IsIpsValid(ips []string) bool {
	for _, v := range ips {
		if !IsIpValid(v) {
			return false
		}
	}
	return true
}

func GetExtranetIp() (string, error) {
	response, err := http.Get("http://ip.cip.cc")
	if err != nil {
		log.Error("http get error", zap.Error(err))
		return GetLocalIp()
	}
	defer response.Body.Close()
	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		log.Error("read body error", zap.Error(err))
		return GetLocalIp()
	}
	return convert.Bytes2Str(body), nil
}

func GetRealIP(r *http.Request) string {
	var ip string
	if r.Header.Get("X-Forwarded-For") != "" {
		ip = r.Header.Get("X-Forwarded-For")
	} else if r.Header.Get("X-Real-IP") != "" {
		ip = r.Header.Get("X-Real-IP")
	} else if r.Header.Get("Remote_addr") != "" {
		ip = r.Header.Get("Remote_addr")
	}

	if strings.Contains(ip, ",") {
		ips := strings.Split(ip, ",")
		if len(ips) > 0 {
			ip = strings.TrimSpace(ips[0])
		}
	}
	return ip
}
