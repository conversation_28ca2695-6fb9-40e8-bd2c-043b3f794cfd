package utils

import (
	"bc/libs/log"
	"crypto/tls"
	"fmt"
	"github.com/PuerkitoBio/goquery"
	"net/http"
	"time"
)

func GetDocument(url string) (document *goquery.Document, err error) {
	client := http.Client{
		Timeout: 20 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	defer time.Sleep(1 * time.Second)
	request, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		log.Errorf("=http.NewRequest fail,%v", err)
		return
	}
	//request.Header.Set("User-Agent","Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.26 Safari/537.36 Edg/85.0.564.13")
	res, err := client.Do(request)
	if err != nil {
		log.Errorf("get url data fail,%v,url:%v", err, url)
		return
	}
	defer res.Body.Close()
	if res.StatusCode != 200 {
		err = fmt.Errorf("http status fail,%v", res.Status)
		log.Errorf("status code error: %d %s", res.StatusCode, res.Status)
		return
	}

	// Load the HTML document
	doc, err := goquery.NewDocumentFromReader(res.Body)
	if err != nil {
		log.Errorf("go query fail,%v", err)
		return
	}
	return doc, err
}
