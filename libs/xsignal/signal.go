package xsignal

import (
	"fmt"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"bc/libs/conf"
	"bc/libs/log"
	"bc/libs/msg"
	"go.uber.org/zap"
)

type Signal struct {
	closeFunc [][]func()
}

var (
	s     *Signal
	_once sync.Once
)

func NewSignal() *Signal {
	_once.Do(func() {
		s = new(Signal)
	})
	return s
}

/*
	AppendCloseFunc
	添加关闭时的执行任务,多次调用添加,最终栈式执行
	不支持并行添加

	eg:
		AppendCloseFunc(funcA_1)
		AppendCloseFunc(funcB_1, funcB_2)
		AppendCloseFunc(funcC_1)

		执行顺序: funcC_1 -> funcB_1 -> funcB_2 -> funcA_1
*/
func (s *Signal) AppendCloseFunc(fs ...func()) *Signal {
	s.closeFunc = append(s.closeFunc, fs)
	return s
}

func (s *Signal) SignalMonitor() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM)

	for _s := range c {
		log.Info("received a system signal", zap.String("signal", _s.String()))
		msg.SendCautionEmail("服务停止提醒", fmt.Sprintf("%s服务接收到系统信号[%s],正在关闭!", conf.LocalName(), _s.String()))
		switch _s {
		case syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM:
			// 退出前的一些扫尾工作,先入后出
			for i := len(s.closeFunc) - 1; i >= 0; i-- {
				for _, f := range s.closeFunc[i] {
					if f != nil {
						f()
					}
				}
			}
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
