package rpcclient

import (
	"time"

	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/xrpc"
	"go.uber.org/zap"
)

var followClient = new(xrpc.RClient)

const (
	_followOpen            = "RPC.FollowOpenPosition"
	_followClose           = "RPC.FollowClosePosition"
	_followNotifyPlanClose = "RPC.NotifyFollowPlanClose"
)

func InitRpcFollow(address string) {
	var err error
	followClient, err = xrpc.NewClient(address)
	if err == nil {
		log.Info("InitRpcMarket connect success", zap.String("address", address))
		return
	}
	go func() {
		t := time.Tick(time.Second)
		var err error
		for range t {
			followClient, err = xrpc.NewClient(address)
			if err != nil {
				log.Error("init followClient connect failed", zap.String("address", address), zap.Error(err))
				continue
			} else {
				log.Info("init followClient connect success", zap.String("address", address))
				break
			}
		}
	}()
}

func FollowOpen(reqID int64, request *proto.FollowOrderOpenArgs, reply *define.Reply) (err error) {
	log.Info("NotifySymbolChange", zap.Int64("reqID", reqID), zap.Any("request", request))
	err = followClient.CallRPC(_followOpen, request, reply)
	log.Info("NotifySymbolChange", zap.Int64("reqID", reqID), zap.Any("reply", reply), zap.Error(err))
	return
}

func FollowClose(request *proto.FollowOrderCloseArgs, reply *define.Reply) (err error) {
	return followClient.CallRPC(_followClose, request, reply)
}

func FollowNotifyPlanClose(request *proto.PlanCloseOrder, reply *define.Reply) (err error) {
	return followClient.CallRPC(_followNotifyPlanClose, request, reply)
}
