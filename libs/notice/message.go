package notice

import (
	"bc/libs/conf"
	"fmt"
	"strings"
	"time"

	"bc/libs/check"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/msg"
	"bc/libs/proto"
	"go.uber.org/zap"
)

const (
	_FundPwdVerifyFailedNoticeSubjectCN = "资金密码5次错误"
	_FundPwdVerifyFailedNoticeSubjectEN = "Fund password error 5 times"
	_FundPwdVerifyFailedNoticeSubjectTC = "資金密碼5次錯誤"

	_FundPwdVerifyFailedNoticeContentCN = "您账户的资金密码连续输入错误5次，为了您账号的安全，请重新登录。"
	_FundPwdVerifyFailedNoticeContentEN = "The fund password of your account has been entered incorrectly for 5 consecutive times."
	_FundPwdVerifyFailedNoticeContentTC = "您帳戶的資金密碼連續輸入錯誤5次，為了您帳號的安全，請重新登入。"
)

func SendFundPwdVerifyFailedNotice(reqID int64, language define.ReqLang, u *proto.User) {
	var subject, content string
	switch language {
	case define.ReqLangCN:
		subject = _FundPwdVerifyFailedNoticeSubjectCN
		content = _FundPwdVerifyFailedNoticeContentCN
	//case define.ReqLangEN:
	//	subject = _FundPwdVerifyFailedNoticeSubjectEN
	//	content = _FundPwdVerifyFailedNoticeContentEN
	//case define.ReqLangTC:
	//	subject = _FundPwdVerifyFailedNoticeSubjectTC
	//	content = _FundPwdVerifyFailedNoticeContentTC
	default:
		subject = _FundPwdVerifyFailedNoticeSubjectEN
		content = _FundPwdVerifyFailedNoticeContentEN
	}

	go sendMsgNotice(reqID, u, subject, content, false)
}

const (
	_c2cEmailTitle = "法币订单通知"
	_c2cCancelMsg  = "您的订单商家已经取消，请到客户端查看，订单号：%d"
	_c2cDoneMsg    = "您的订单已完成，请到客户端查看，订单号：%d"
)

func SendMsgNotice(userID int64, orderID int64, state define.LegalOrderState, user *proto.User) {
	var content string
	if state == define.LegalOrderStateBusinessCancel {
		content = fmt.Sprintf(_c2cCancelMsg, orderID)
	} else if state == define.LegalOrderStateFinish || state == define.LegalOrderStateReissue {
		content = fmt.Sprintf(_c2cDoneMsg, orderID)
	} else {
		return
	}

	go sendMsgNotice(orderID, user, _c2cEmailTitle, content, false)
}

//发送预警信息
func SendWarningRiskEmail(account string, lang define.ReqLang) {
	content, err := define.GetMsgLanguageWithTemplate(lang, define.WarningRisk, nil)
	if err != nil {
		log.Infof("SendRiskToUser 解析文本出错，%v", err)
		return
	}
	if content == "" {
		log.Infof("发送内容为空，暂不发送，请检测配置语言选项", zap.String("account", account))
		return
	}
	log.Info("开始发送预警邮件", zap.String("account", account), zap.String("content", content))
	msg.SendEmailByMsgServiceIgnore(0, account, "账户风险率预警", content)
}

//发送强平信息
func SendForceRiskEmail(account string, lang define.ReqLang) {
	content, err := define.GetMsgLanguageWithTemplate(lang, define.ForcePosition, nil)
	if err != nil {
		log.Infof("SendRiskToUser 解析文本出错，%v", err)
		return
	}
	if content == "" {
		log.Infof("发送内容为空，暂不发送，请检测配置语言选项")
		return
	}
	msg.SendEmailByMsgServiceIgnore(0, account, "账户强平通知", content)
}

//发送预警信息
func SendWarningRiskSms(code, phone string, lang define.ReqLang) {
	content, err := define.GetMsgLanguageWithTemplate(lang, define.WarningRisk, nil)
	if err != nil {
		log.Infof("SendRiskToUser 解析文本出错，%v", err)
		return
	}
	if content == "" {
		log.Infof("发送内容为空，暂不发送，请检测配置语言选项")
		return
	}
	msg.IgnoreErrSendSms(0, phone, content, code)
}

//发送强平信息
func SendForceRiskSms(code, phone string, lang define.ReqLang) {
	content, err := define.GetMsgLanguageWithTemplate(lang, define.ForcePosition, nil)
	if err != nil {
		log.Infof("SendRiskToUser 解析文本出错，%v", err)
		return
	}
	if content == "" {
		log.Infof("发送内容为空，暂不发送，请检测配置语言选项")
		return
	}
	msg.IgnoreErrSendSms(0, phone, content, code)
}

const (
	_withdrawReleaseNoticeSubjectCN = "提币成功"
	_withdrawReleaseNoticeSubjectEN = "Withdraw successfully"
	_withdrawReleaseNoticeSubjectTC = "提幣成功"
	_withdrawReleaseNoticeSubjectKR = "출금 성공"
	_withdrawReleaseNoticeContentCN = "您提币 %s %s 已放款，请查收。"
	_withdrawReleaseNoticeContentEN = "The %s %s you withdraw has been released, please check."
	_withdrawReleaseNoticeContentTC = "您提幣 %s %s 已放款，請查收。"
	_withdrawReleaseNoticeContentKR = "%s %s 을( 를) 출금되었습니다.확인해 주십시오."
)

func SendWithdrawReleaseNotice(reqID int64, amount, coinName string, user *proto.User) {
	var subject, content string
	switch user.AreaCode {
	case define.ChineseAreaCode, define.ChineseTWAreaCode, define.ChineseHKAreaCode:
		subject = _withdrawReleaseNoticeSubjectCN
		content = fmt.Sprintf(_withdrawReleaseNoticeContentCN, amount, coinName)
	//case define.ChineseTWAreaCode, define.ChineseHKAreaCode:
	//	subject = _withdrawReleaseNoticeSubjectTC
	//	content = fmt.Sprintf(_withdrawReleaseNoticeContentTC, amount, coinName)
	//case define.SouthKoreaAreaCode:
	//	subject = _withdrawReleaseNoticeSubjectKR
	//	content = fmt.Sprintf(_withdrawReleaseNoticeContentKR, amount, coinName)
	default:
		subject = _withdrawReleaseNoticeSubjectEN
		content = fmt.Sprintf(_withdrawReleaseNoticeContentEN, amount, coinName)
	}

	go sendMsgNotice(reqID, user, subject, content, false)
}

const (
	_depositNoticeSubjectCN = "充币成功"
	_depositNoticeSubjectEN = "Top up successfully"
	_depositNoticeSubjectTC = "充幣成功"
	_depositNoticeSubjectKR = "입금 성공"
	_depositNoticeContentCN = "您充值 %s %s 已到账，请查收。"
	_depositNoticeContentEN = "The %s %s you topped up has arrived, please check."
	_depositNoticeContentTC = "您充值 %s %s 已到賬，請查收。"
	_depositNoticeContentKR = "%s %s 을( 를) 충전했습니다.확인해주시기 바랍니다."
)

func SendDepositNotice(reqID int64, amount, coinName string, user *proto.User) {
	var subject, content string
	switch user.AreaCode {
	case define.ChineseAreaCode, define.ChineseTWAreaCode, define.ChineseHKAreaCode:
		subject = _depositNoticeSubjectCN
		content = fmt.Sprintf(_depositNoticeContentCN, amount, coinName)
	//case define.ChineseTWAreaCode, define.ChineseHKAreaCode:
	//	subject = _depositNoticeSubjectTC
	//	content = fmt.Sprintf(_depositNoticeContentTC, amount, coinName)
	//case define.SouthKoreaAreaCode:
	//	subject = _depositNoticeSubjectKR
	//	content = fmt.Sprintf(_depositNoticeContentKR, amount, coinName)
	default:
		subject = _depositNoticeSubjectEN
		content = fmt.Sprintf(_depositNoticeContentEN, amount, coinName)
	}

	go sendMsgNotice(reqID, user, subject, content, false)
}

func SendDepositMonitorNotice(reqID, userID int64, amount, coinName string) {
	// 仅发送非USDT币种的充币监控邮件
	coinName = strings.ToUpper(coinName)
	if coinName == define.CoinNameUsdt {
		log.Info("SendDepositMonitorNotice skip for usdt",
			zap.Int64("reqID", reqID), zap.Int64("userID", userID), zap.String("amount", amount))
		return
	}
	content := fmt.Sprintf("币种名[%s],数量[%s],时间[%s],用户ID[%d]", coinName, amount, time.Now().Format(define.TimeFormatNormal), userID)
	log.Info("开始发送充币到账监控邮件", zap.Strings("to", conf.DepositMonitorReceiver()), zap.String("content", content))
	for _, receiver := range conf.DepositMonitorReceiver() {
		if check.EmailAddr(receiver) {
			msg.SendEmailByMsgServiceIgnore(reqID, receiver, "用户入金到账监控提醒", content)
		}
	}
}

const (
	_contractTriggerNoticeSubjectCN = "%s触发通知"
	_contractTriggerNoticeSubjectEN = "%s Trigger notification"
	_contractTriggerNoticeSubjectTC = "%s觸發通知"
	_contractTriggerNoticeSubjectKR = "%s 알림 트리거"
	_contractTriggerNoticeContentCN = "您的 %s 合约 %s 的 %s 已于%s在价格为 %s 时%s。由于行情波动，触发价与实际成交价会有所偏差。"
	_contractTriggerNoticeContentKR = "귀하의 %s 계약 %s  의 %s 이( 가) 이미 값이 %s 때 트리거됩니다."
	_contractTriggerNoticeContentEN = "The %s of your %s contract %s has been triggered on %s at the price of %s."
	_contractTriggerNoticeContentTC = "您的 %s 合約 %s 的 %s 已於 %s 在價格為 %s 時被觸發。"
)

const (
	ContractTriggerNoticeTypeLimitCN = "止盈"
	ContractTriggerNoticeTypeLimitEN = "Stop profit"
	ContractTriggerNoticeTypeLimitTC = "止盈"
	ContractTriggerNoticeTypeLimitKR = "익절"
	ContractTriggerNoticeTypeStopCN  = "止损"
	ContractTriggerNoticeTypeStopEN  = "Stop loss"
	ContractTriggerNoticeTypeStopTC  = "止損"
	ContractTriggerNoticeTypeStopKR  = "손절"
	ContractTriggerNoticeTypePlanCN  = "计划单"
	ContractTriggerNoticeTypePlanEN  = "Plan sheet"
	ContractTriggerNoticeTypePlanTC  = "計畫單"
	ContractTriggerNoticeTypePlanKR  = "계획서"
)

const (
	ContractTriggerNoticeStateSuccess = "触发成功"
	ContractTriggerNoticeStateFail    = "触发失败"
)

func SendContractTriggerNotice(reqID int64, contract, side, triggerType, price, triggerState string, triggerTime time.Time, user *proto.User) {
	var subject, content string
	subject = fmt.Sprintf(_contractTriggerNoticeSubjectCN, triggerType)
	content = fmt.Sprintf(_contractTriggerNoticeContentCN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price, triggerState)
	//switch user.AreaCode {
	//case define.ChineseAreaCode:
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectCN, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentCN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//case define.ChineseTWAreaCode, define.ChineseHKAreaCode:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitTC
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopTC
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanTC
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectTC, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentTC, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//case define.SouthKoreaAreaCode:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitKR
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopKR
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanKR
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectKR, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentKR, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//default:
	//	switch triggerType {
	//	case ContractTriggerNoticeTypeLimitCN:
	//		triggerType = ContractTriggerNoticeTypeLimitEN
	//	case ContractTriggerNoticeTypeStopCN:
	//		triggerType = ContractTriggerNoticeTypeStopEN
	//	default:
	//		triggerType = ContractTriggerNoticeTypePlanEN
	//	}
	//	subject = fmt.Sprintf(_contractTriggerNoticeSubjectEN, triggerType)
	//	content =fmt.Sprintf(_contractTriggerNoticeContentEN, contract, transTriggerSide(side), triggerType, triggerTime.Format(define.TimeFormatNormal), price)
	//}

	go sendMsgNotice(reqID, user, subject, content, false)
}

func transTriggerSide(side string) string {
	if side == define.OrderBuy {
		return "做多"
	}
	return "做空"
}

const (
	_authCodeNoticeSubjectCN = "%s验证码"
	_authCodeNoticeSubjectEN = "%s Verification code"
	_authCodeNoticeSubjectTC = "%s驗證碼"
	_authCodeNoticeSubjectKR = "%s 인증 번호"
	_authCodeNoticeContentCN = "您本次%s验证码是 %s，如非本人操作，请及时修改密码。"
	_authCodeNoticeContentEN = "Your %s verification code this time is %s."
	_authCodeNoticeContentTC = "您本次%s驗證碼是 %s，如非本人操作，請及時修改密碼。"
	_authCodeNoticeContentKR = "%s 인증 번호는 %s 입니다. 본인이 조작하지 않으면 즉시 수리해 주세요.비밀번호를 고치다."
)

type AuthCodeValues struct {
	IsCN     bool              `json:"is_cn"`
	Counter  int               `json:"counter"`
	Values   map[string]string `json:"values"`
	FullText string            `json:"full_text"`
}

func (cv *AuthCodeValues) ToString() string {
	b, _ := json.Marshal(cv)
	return string(b)
}

func SendAuthCodeNotice(reqID int64, lang define.ReqLang, user *proto.User, code proto.AuthCodeInfo) {
	action := convertAuthCodeMode(lang, code.Mode)

	var isCN bool
	var subject, content string
	switch lang {
	case define.ReqLangCN:
		isCN = true
		subject = fmt.Sprintf(_authCodeNoticeSubjectCN, action)
		content = fmt.Sprintf(_authCodeNoticeContentCN, action, code.Code)
	//case define.ReqLangTC:
	//	subject = fmt.Sprintf(_authCodeNoticeSubjectTC, action)
	//	content = fmt.Sprintf(_authCodeNoticeContentTC, action, code.Code)
	//case define.ReqLangKR:
	//	subject = fmt.Sprintf(_authCodeNoticeSubjectKR, action)
	//	content = fmt.Sprintf(_authCodeNoticeContentKR, action, code.Code)
	default:
		subject = fmt.Sprintf(_authCodeNoticeSubjectEN, action)
		content = fmt.Sprintf(_authCodeNoticeContentEN, action, code.Code)
	}

	if check.EmailAddr(user.Email) {
		v := AuthCodeValues{
			IsCN:    isCN,
			Counter: code.Counter,
			Values: map[string]string{
				"action": action,
				"code":   code.Code,
			},
			FullText: content,
		}
		msg.SendAuthCodeEmailByMsgServiceIgnore(reqID, user.Email, subject, v.ToString())
	} else {
		go sendMsgNotice(reqID, user, subject, content, true)
	}
}

var _authCodeType = map[define.ReqLang]map[uint8]string{
	define.ReqLangCN: {
		define.ForgetPassword:      "重置密码",
		define.UserRegister:        "注册",
		define.ModifyPhone:         "修改手机号",
		define.NewBindPhone:        "设置手机号",
		define.ModifyEmail:         "修改邮箱",
		define.NewBindEmail:        "设置邮箱",
		define.ModifyLoginPassword: "重置密码",
		define.ModifyFundPassword:  "重置密码",
		define.ModifyAuthVerify:    "安全验证",
	},
	define.ReqLangEN: {
		define.ForgetPassword:      "reset password",
		define.UserRegister:        "registered",
		define.ModifyPhone:         "edit phone number",
		define.NewBindPhone:        "set phone number",
		define.ModifyEmail:         "modify email",
		define.NewBindEmail:        "set up email",
		define.ModifyLoginPassword: "reset password",
		define.ModifyFundPassword:  "reset password",
		define.ModifyAuthVerify:    "safety certificate",
	},
	define.ReqLangTC: {
		define.ForgetPassword:      "重置密碼",
		define.UserRegister:        "注册",
		define.ModifyPhone:         "修改手機號",
		define.NewBindPhone:        "設定手機號",
		define.ModifyEmail:         "修改郵箱",
		define.NewBindEmail:        "設定郵箱",
		define.ModifyLoginPassword: "重置密碼",
		define.ModifyFundPassword:  "重置密碼",
		define.ModifyAuthVerify:    "安全驗證",
	},
	define.ReqLangKR: {
		define.ForgetPassword:      "비밀번호 재설정",
		define.UserRegister:        "회원가입",
		define.ModifyPhone:         "핸드폰 번호를 수정하다",
		define.NewBindPhone:        "핸드폰 번호 설정",
		define.ModifyEmail:         "이메일 수정",
		define.NewBindEmail:        "이메일 설정",
		define.ModifyLoginPassword: "비밀번호 재설정",
		define.ModifyFundPassword:  "비밀번호 재설정",
		define.ModifyAuthVerify:    "안전 검증",
	},
}

func convertAuthCodeMode(lang define.ReqLang, mode uint8) string {
	switch lang {
	case define.ReqLangCN:
		return _authCodeType[define.ReqLangCN][mode]
	//case define.ReqLangTC:
	//	return _authCodeType[define.ReqLangTC][mode]
	//case define.ReqLangKR:
	//	return _authCodeType[define.ReqLangKR][mode]
	default:
		return _authCodeType[define.ReqLangEN][mode]
	}
}

const (
	_legalReleaseNoticeSubjectCN = "法币购买成功"
	_legalReleaseNoticeSubjectEN = "Fiat currency purchase successfully"
	_legalReleaseNoticeSubjectTC = "法幣購買成功"
	_legalReleaseNoticeSubjectKR = "법정 화폐의 구매가"
	_legalReleaseNoticeContentCN = "您购买的 %s %s 已在 %s 到账，请查收。"
	_legalReleaseNoticeContentEN = "The %s %s you purchased has arrived at %s, please check."
	_legalReleaseNoticeContentTC = "您購買的 %s %s 已在 %s 到賬，請查收。"
	_legalReleaseNoticeContentKR = "구매하신 [%s] (%s)] [%s] 입금하였으니 확인해주시기 바랍니다。"

	_legalCancelNoticeSubjectCN = "法币购买失败"
	_legalCancelNoticeSubjectEN = "Fiat currency purchase failed"
	_legalCancelNoticeSubjectTC = "法幣購買失敗"
	_legalCancelNoticeSubjectKR = "법정 화폐 매수 실패"
	_legalCancelNoticeContentCN = "您的订单商家已经取消，请到客户端查看，订单号：%d。"
	_legalCancelNoticeContentEN = "Your order has been cancelled by the merchant. Please check it on the client. Order No.%d"
	_legalCancelNoticeContentTC = "您的訂單商家已經取消，請到用戶端查看，訂單號：%d。"
	_legalCancelNoticeContentKR = "고객님의 주문서는 이미 취소되었습니다.주문번호:%d"
)

func SendLegalReleaseNotice(reqID, orderID int64, state define.LegalOrderState, amount, coinName string, releaseTime time.Time, user *proto.User) {
	var success bool
	if state == define.LegalOrderStateBusinessCancel {
		success = false
	} else if state == define.LegalOrderStateFinish || state == define.LegalOrderStateReissue {
		success = true
	} else {
		return
	}

	var subject, content string
	switch user.AreaCode {
	case define.ChineseAreaCode, define.ChineseTWAreaCode, define.ChineseHKAreaCode:
		if success {
			subject = _legalReleaseNoticeSubjectCN
			content = fmt.Sprintf(_legalReleaseNoticeContentCN, amount, coinName, releaseTime.Format(define.TimeFormatNormal))
		} else {
			subject = _legalCancelNoticeSubjectCN
			content = fmt.Sprintf(_legalCancelNoticeContentCN, orderID)
		}
	//case define.ChineseTWAreaCode, define.ChineseHKAreaCode:
	//	if success {
	//		subject = _legalReleaseNoticeSubjectTC
	//		content = fmt.Sprintf(_legalReleaseNoticeContentTC, amount, coinName, releaseTime.Format(define.TimeFormatNormal))
	//	} else {
	//		subject = _legalCancelNoticeSubjectTC
	//		content = fmt.Sprintf(_legalCancelNoticeContentTC, orderID)
	//	}
	//case define.SouthKoreaAreaCode:
	//	if success {
	//		subject = _legalReleaseNoticeSubjectKR
	//		content = fmt.Sprintf(_legalReleaseNoticeContentKR, amount, coinName, releaseTime.Format(define.TimeFormatNormal))
	//	} else {
	//		subject = _legalCancelNoticeSubjectKR
	//		content = fmt.Sprintf(_legalCancelNoticeContentKR, orderID)
	//	}
	default:
		if success {
			subject = _legalReleaseNoticeSubjectEN
			content = fmt.Sprintf(_legalReleaseNoticeContentEN, amount, coinName, releaseTime.Format(define.TimeFormatNormal))
		} else {
			subject = _legalCancelNoticeSubjectEN
			content = fmt.Sprintf(_legalCancelNoticeContentEN, orderID)
		}
	}

	go sendMsgNotice(reqID, user, subject, content, false)
}

const (
	_legalWaitReleaseNoticeSubjectCN = "买家已付款"
	_legalWaitReleaseNoticeContentCN = "订单尾号 %03d，买家已标记为付款，确认收到此笔转账后请尽快放行，恶意延迟放币将触发相应机制，影响账户使用。"
)

func SendLegalWaitReleaseNotice(reqID, orderID int64, user *proto.User) {
	subject := _legalWaitReleaseNoticeSubjectCN
	content := fmt.Sprintf(_legalWaitReleaseNoticeContentCN, orderID%1000)

	go sendMsgNotice(reqID, user, subject, content, false)
}

//var _registerSafeNotice = map[define.ReqLang][2]string{
//	define.ReqLangCN: {"账户安全提醒", "尊敬的PandaFe 熊猫用户：近期，有不法分子冒用PandaFe 熊猫合约官方名义致电用户、添加微信、招揽用户进入假冒微信社群、或要求用户向某地址转入数字资产等开展的一系列诈骗行为。请不要轻易自己的账号密码、短信及谷歌验证码等信息透露给陌生人，如有疑虑，可第一时间联系PandaFe 熊猫合约官方客服。\n\nPandaFe 熊猫合约全球运营团队"},
//	define.ReqLangEN: {"Account security reminder", "Dear PandaFe users: Recently, there have been a series of scams carried out by criminals using the official name of the PandaFe Panda contract to call users, add WeChat, recruit users to enter the fake WeChat community, or ask users to transfer digital assets to a certain address.  Please do not easily disclose your account password, SMS, Google verification code and other information to strangers. If you have any doubts, please contact PandaFe official customer service as soon as possible.\n\n PandaFe Panda Contract Global Operations Team"},
//	define.ReqLangTC: {"賬戶安全提醒", "尊敬的PandaFe 熊貓用戶：近期，有不法分子冒用PandaFe 熊貓合約官方名義致電用戶、添加微信、招攬用戶進入假冒微信社群、或要求用戶向某地址轉入數字資產等開展的一系列詐騙行為。 請不要輕易自己的賬號密碼、短信及谷歌驗證碼等信息透露給陌生人，如有疑慮，可第一時間聯繫PandaFe 熊貓合約官方客服。\n\n PandaFe 熊貓合約全球運營團隊"},
//	define.ReqLangKR: {"Account security reminder", "Dear PandaFe users: Recently, there have been a series of scams carried out by criminals using the official name of the PandaFe Panda contract to call users, add WeChat, recruit users to enter the fake WeChat community, or ask users to transfer digital assets to a certain address.  Please do not easily disclose your account password, SMS, Google verification code and other information to strangers. If you have any doubts, please contact PandaFe official customer service as soon as possible.\n\n PandaFe Panda Contract Global Operations Team"},
//}
//
//func getRegisterSafeNotice(lang define.ReqLang) (string, string) {
//	m, ok := _registerSafeNotice[lang]
//	if !ok {
//		m = _registerSafeNotice[define.ReqLangEN]
//	}
//	return m[0], m[1]
//}
//
//func SendRegisterSafeNotice(reqID int64, user *proto.User, lang define.ReqLang) {
//	subject, content := getRegisterSafeNotice(lang)
//	go sendMsgNotice(reqID, user, subject, content, false)
//}

func getRegisterPasswordNoSetSafeNotice() (string, string) {
	return "Welcome to PDbit!", "You recently signed up for PDbit. Welcome to your journey with us to.\n\nPDbit Team"
}

func SendRegisterPasswordNoSetSafeNotice(reqID int64, user *proto.User) {
	subject, content := getRegisterPasswordNoSetSafeNotice()
	go sendMsgNotice(reqID, user, subject, content, false)
}

func sendMsgNotice(reqID int64, user *proto.User, subject, content string, trySms bool) {
	log.Info("开始发送用户通知", zap.Any("to", user), zap.String("sub", subject), zap.String("content", content), zap.Bool("trySms", trySms))
	if check.EmailAddr(user.Email) {
		msg.SendEmailByMsgServiceIgnore(reqID, user.Email, subject, content)
	}
	if trySms {
		// 关闭短信通知
		sendSms(reqID, user, content)
	}
}

func sendSms(reqID int64, user *proto.User, content string) {
	if (user.AreaCode == define.ChineseAreaCode && check.PhoneNumber(user.Phone)) || (user.AreaCode != define.ChineseAreaCode && check.AreaNumber(user.Phone)) {
		// 关闭短信通知
		msg.SendSmsByMsgServiceIgnore(reqID, user.Phone, content, user.AreaCode)
	}
}
