package convert

import (
	"bc/libs/define"
	"strings"
	"unsafe"
)

// 字符串转切片高效方式
func Str2Bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

// 切片转字符串高效方式
func Bytes2Str(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

const _placeholder = "****"

// 邮箱脱敏
func HideEmail(src string) string {
	if len(src) == 0 {
		return src
	}
	index := strings.Index(src, "@")
	if index == 0 {
		return src
	}
	if index < 6 {
		return src[:1] + _placeholder + src[index:]
	}
	return src[:1] + _placeholder + src[index-1:]
}

// 手机号脱敏
func HidePhone(src string) string {
	l := len(src)
	if l <= 1 {
		return src
	}
	if l < 6 {
		return src[:1] + _placeholder + src[l-1:]
	}
	if l < 11 {
		return src[:2] + _placeholder + src[l-2:]
	}
	return src[:3] + _placeholder + src[l-4:]
}

// 昵称脱敏
func HideNickname(src string) string {
	if len(src) == 0 {
		return src
	}
	return string([]rune(src)[:1]) + "****"
}

// 邀请链转数组
func ConvertChain2Array(code string) []string {
	code = strings.TrimPrefix(code, ".")
	return strings.Split(code, ".")
}

// 字符串切分,去除无效项
func StringSplitTrim(str, sep string) []string {
	s := strings.Trim(str, sep)
	return strings.Split(s, sep)
}

// EncodeTagCoinAddr 带Tag币种合并地址
func EncodeTagCoinAddr(addr, tag string) string {
	if len(tag) > 0 {
		return addr + define.CoinAddressTagSep + tag
	}
	return addr
}

// DecodeTagCoinAddr 带Tag币种拆分地址
func DecodeTagCoinAddr(addr string) (string, string) {
	if strings.Count(addr, define.CoinAddressTagSep) == 1 {
		sli := strings.Split(addr, define.CoinAddressTagSep)
		return sli[0], sli[1]
	}
	return addr, ""
}
