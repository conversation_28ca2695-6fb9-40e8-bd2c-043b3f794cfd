package convert

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"bc/libs/define"
	"bc/libs/log"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

func Float64String(f float64) string {
	return strconv.FormatFloat(f, 'f', -1, 64)
}

func NewDecimalFromFloat64(f float64) (result decimal.Decimal, isSuccess bool) {
	result, err := decimal.NewFromString(Float64String(f))
	if err != nil {
		log.Error("NewFloat64Decimal convert fail", zap.Error(err))
	}
	isSuccess = true
	return
}

func NewFromFloat(f float64) decimal.Decimal {
	r, s := NewDecimalFromFloat64(f)
	if !s {
		return decimal.Zero
	}
	return r
}

func NewFromString(f string) decimal.Decimal {
	if f == "" {
		return decimal.Zero
	}
	result, err := decimal.NewFromString(f)
	if err != nil {
		log.Errorf("NewFromString to decimal err:%v,str:%v", err, f)
		return decimal.Zero
	}
	return result
}

func Int64String(n int64) string {
	return strconv.FormatInt(n, 10)
}

func String2Int64(s string) (i int64) {
	if s == "" {
		log.Warnf("传递参数为nil")
		return 0
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		log.Errorf("String2Int64 fail,%v", err)
		return
	}
	return
}

func DecimalFloatValue(d decimal.Decimal) float64 {
	f, _ := d.Float64()
	return f
}

func FixedBank(money string) (string, error) {
	fromString, err := decimal.NewFromString(money)
	//if fromString.LessThan(define.MIN_SHOW_DECIAML) {
	//	return define.MIN_SHOW_STR, nil
	//}
	if err != nil {
		log.Error("FixedBank change decimal failed", zap.Error(err))
		return "", err
	}
	f, _ := fromString.Truncate(2).Float64()
	return fmt.Sprintf("%.2f", f), nil
}

func String2Float(amount string) float64 {
	r, _ := strconv.ParseFloat(amount, 64)
	return r
}

func FloatMod(a, b float64) int64 {
	return NewFromFloat(a).Mod(NewFromFloat(b)).IntPart()
}

func StringMod(a, b string) int64 {
	return NewFromString(a).Mod(NewFromString(b)).IntPart()
}

//格式化数值    1,234,567,898.55
func NumberFormat(str string) string {
	length := len(str)
	if length < 4 {
		return str
	}
	arr := strings.Split(str, ".") //用小数点符号分割字符串,为数组接收
	length1 := len(arr[0])
	if length1 < 4 {
		return str
	}
	count := (length1 - 1) / 3
	for i := 0; i < count; i++ {
		arr[0] = arr[0][:length1-(i+1)*3] + "," + arr[0][length1-(i+1)*3:]
	}
	return strings.Join(arr, ".") //将一系列字符串连接为一个字符串，之间用sep来分隔。
}

func String2Int(numStr string) int {
	num, _ := strconv.Atoi(numStr)
	return num
}

func Str2IntSlice(str, sep string) []int {
	var reply []int
	sli := strings.Split(str, sep)
	for i := range sli {
		num, err := strconv.Atoi(sli[i])
		if err == nil {
			reply = append(reply, num)
		}
	}

	sort.Ints(reply)
	return reply
}

func Reciprocal(x decimal.Decimal) decimal.Decimal {
	if x.IsZero() {
		return x
	}
	return define.DecimalOne.Div(x)
}
