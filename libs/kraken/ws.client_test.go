/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package kraken

import (
	"bc/libs/convert"
	"bc/libs/json"
	"bc/libs/log"
	"fmt"
	"github.com/gorilla/websocket"
	"io/ioutil"
	"strings"
	"testing"
	"time"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://ws.kraken.com", nil)
	//w, rsp, err := websocket.DefaultDialer.Dial("wss://ws.kraken.com", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	str := `{
	 "event": "subscribe",
	 "pair": [
	   "BTC/USD"
	 ],
	 "subscription": {
	   "name": "trade"
	 }
	}`
	//
	//	str := `{
	//  "event": "ping",
	//  "reqid": 42
	//}`

	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

	go func() {
		for {
			t, b, err := w.ReadMessage()
			//d, _ := zip.UnzipByte(b)
			//b, _ = compress.GzipUnCompress(b)
			te.Log(t, convert.Bytes2Str(b), err)
			//ping := new(Ping)
			//_ = json.Unmarshal(b, ping)
			//if ping.Ping != 0 {
			//	te.Logf("ping:%+v", *ping)
			//	pong, _ := json.Marshal(&Pong{Pong: ping.Ping})
			//	//pong, _ := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
			//	//pong:=[]byte(`{"pong": 1492420473027}`)
			//	w.WriteMessage(websocket.TextMessage, pong)
			//	te.Logf("pong:%+v", string(pong))
			//} else {
			//}

		}
	}()

	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("ws.log", "info", false)
	s := time.Tick(time.Minute * 1)
	c := NewMarketWsClient(&Config{WsPoint: "wss://ws.kraken.com"}, func(data []byte) {
		t.Logf("d:%v", string(data))
		t.Logf(string(data))
		var l []interface{}
		err := json.Unmarshal(data, &l)
		if err != nil {
			return
		}
		//1-数据 2-topic 3-合约名
		//1-数组 0-价格 1-量 2-时间 3-方向s-b
		topic := l[2].(string)
		if topic != "trade" {
			return
		}
		code := l[3].(string)
		code = strings.ReplaceAll(code, "XBT", "BTC")
		code = strings.Replace(code, "/USD", "USDT", -1)
		d := l[1].([]interface{})
		f := d[0].([]interface{})
		price, size, date, side := f[0].(string), f[1].(string), f[2].(string), f[3].(string)
		t.Logf("topic:%v,code；%v,price:%v,size；%v,date；%v,side；%v", topic, code, price, size, date, side)
	})
	c.Subscript([]string{"BTC/USD"})
	c.Start()
	go func() {
		for range s {
			t.Logf("开始重启")
			c.Restart()
		}
	}()
	select {}
}
