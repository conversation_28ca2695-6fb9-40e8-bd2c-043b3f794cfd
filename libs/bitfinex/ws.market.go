/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package bitfinex

import (
	"bc/libs/json"
	"context"
	"fmt"
	"sync"
	"time"

	"bc/libs/log"
	"github.com/gorilla/websocket"
)

//https://docs.bitfinex.com/docs/ws-general
const (
	_wsMarket = "wss://api-pub.bitfinex.com/ws/2"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	codes                           []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
	subCodes                        map[string]string
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to huobi fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.lastRv = time.Now()
	c.mc.lastStart = time.Now()
	log.Infof("huobi dial over r1")
	c.con = conn
	c.Receive()
	//c.PingLoop()
	//订阅
	c.sub(c.mc.topics, c.mc.codes)
	log.Infof("huobi sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

func (c *Client) sub(topic []string, symbols []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	for _, code := range symbols {
		for _, t := range topic {
			s := Sub{
				Event:   "subscribe",
				Symbol:  code,
				Channel: t,
			}
			b, _ := json.Marshal(s)
			log.Infof("sub data:%v", string(b))
			c.Write(b)
		}
	}

}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				c.con.WriteMessage(websocket.TextMessage, data)
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(msg.Msg))
				c.mc.msgHandler(msg.Msg)
			}

		}

	}()
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:   nil,
		wsPoint:  _wsMarket,
		topics:   nil,
		subCodes: make(map[string]string),
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string, codes []string) {
	if c == nil {
		return
	}
	log.Infof("%v sub topics:%v,codes:%v", "bitfinex", topics, codes)
	c.topics = topics
	c.codes = codes
	c.client.sub(c.topics, c.codes)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.client.Write(pong)
//}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.RLock()
	last := c.lastRv
	c.rvLock.RUnlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	c.ClientStatus = false
	c.client.close()
	time.Sleep(100 * time.Millisecond)
	c.client = NewClient(c)
	c.client.start()
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}

type Sub struct {
	Event     string `json:"event"`
	Channel   string `json:"channel"`
	Symbol    string `json:"symbol"`
	ChannelId int64  `json:"chanId"`
	Pair      string `json:"pair"`
}
