package bitfinex

import (
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

//https://www.bitfinex.com/
//https: //api-pub.bitfinex.com/v2/

//wss://api-pub.bitfinex.com/ws/2
//

//https: //api-pub.bitfinex.com/v2/tickers?symbols=tBTCUSD,
//[
//SYMBOL,
//BID,
//BID_SIZE,
//ASK,
//ASK_SIZE,
//DAILY_CHANGE,
//DAILY_CHANGE_RELATIVE,
//LAST_PRICE,
//VOLUME,
//HIGH,
//LOW
//],

const (
	url       = "https://api-pub.bitfinex.com/v2/"
	apiTicker = "tickers?symbols="
	name      = "bitfinex"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	fs := "t%v"
	var bf bytes.Buffer
	for s := range codes {
		code := strings.ReplaceAll(s, "USDT", "USD")
		code = fmt.Sprintf(fs, code)
		bf.WriteString(code)
		bf.WriteString(",")
	}
	return getTicker(bf.String())
}

func getTicker(arg string) (list []proto.MarketTrade) {
	u := utils.StrBuilder(url, apiTicker, arg)
	//fmt.Println(u)
	r, err := http.Get(u)
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Infof("web；%v,http fail,code；%v", u, r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Printf("%v", string(b))
	var ticker [][]interface{}
	err = json.Unmarshal(b, &ticker)
	if err != nil {
		return
	}

	//fmt.Println(ticker)

	for _, v := range ticker {
		c := v[0]
		code := c.(string)[1:]
		if strings.HasSuffix(code, "USD") {
			code = utils.StrBuilder(code, "T")
		}
		price := v[7].(float64)
		volume := v[8].(float64)
		mt := proto.MarketTrade{
			Symbol:   code,
			DealTime: time.Now().Unix(),
			Price:    nums.NewFromFloat(price),
			Volume:   nums.NewFromFloat(volume),
			Ts:       time.Now(),
			Source:   name,
		}
		list = append(list, mt)
	}

	return
}
