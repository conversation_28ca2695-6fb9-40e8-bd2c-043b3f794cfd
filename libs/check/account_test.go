package check

import (
	"bc/libs/log"
	"testing"
)

// 生成请求用密码
func TestEncryptPwd(t *testing.T) {
	t.Log(EncryptPwd("jiugejiu"))
}

// 将请求用密码转换成库密码
func TestEncryptPassWord(t *testing.T) {
	t.Log(EncryptPassWord("8O0Td24kV+X52ons1dhsICrbZ3Gyh6947/bzpsl4zAw="))
}

func TestEmailAddrV2(t *testing.T) {
	log.InitLogger("demo", "info", false)
	t.Log(EmailAddrV2("<EMAIL>"))
}

func TestAreaNumber(t *testing.T) {
	t.Log(AreaNumber("64809885"))
}
