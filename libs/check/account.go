package check

import (
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"regexp"

	"bc/libs/log"
	"go.uber.org/zap"
	"golang.org/x/crypto/pbkdf2"
)

func PhoneNumber(phone string) bool {
	res, err := regexp.MatchString(`^1[3456789]\d{9}$`, phone)
	if (!res && phone != "") || phone == "" || err != nil {
		return false
	}
	return true
}

func AreaNumber(phone string) bool {
	res, err := regexp.MatchString(`^\d{4,}$`, phone)
	if (!res && phone != "") || phone == "" || err != nil {
		return false
	}
	return true
}

func EmailAddr(email string) bool {
	res, err := regexp.MatchString(`^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$`, email)
	if (!res && email != "") || email == "" || err != nil {
		return false
	}
	return true
}

func EmailAddrV2(email string) bool {
	// 先正则校验邮件地址是否有效
	err := ValidateFormat(email)
	if err != nil {
		log.Error("EmailAddrV2 ValidateFormat error", zap.String("email", email), zap.Error(err))
		return false
	}
	// 判断邮件是否可以实际送达
	err = ValidateHost(email)
	if err != nil {
		log.Error("EmailAddrV2 ValidateHost error", zap.String("email", email), zap.Error(err))
		return false
	}
	return true
}

// 密码加密盐
var _encryptKey = []byte{200, 32, 174, 75, 124, 11, 148, 192, 228, 159, 5, 203, 14, 10, 78, 158, 204, 217, 8, 107, 232, 211, 218, 189, 219, 214, 146, 63, 123, 87, 231, 64, 151, 233, 21, 227, 222, 31, 182, 37, 20, 154, 27, 131, 164, 242, 147, 153, 175, 58, 191, 42, 156, 142, 208, 251, 185, 180, 67, 250, 88, 125, 132, 34}

func EncryptAndComparePassWord(pwd, dbPwd string) (correct bool) {
	return comparePassWord(EncryptPassWord(pwd), dbPwd)
}

func EncryptPassWord(originPwd string) (newPwd string) {
	h := sha256.New()
	saltedPwd := make([]byte, 0, len(originPwd)+len(_encryptKey))
	saltedPwd = append(saltedPwd, []byte(originPwd)...)
	saltedPwd = append(saltedPwd, _encryptKey...)
	h.Write(saltedPwd)
	newPwdBytes := h.Sum(nil)
	newPwd = hex.EncodeToString(newPwdBytes)
	return
}

func comparePassWord(pwd, dbPwd string) (correct bool) {
	if (len(pwd) == 0 && len(dbPwd) == 0) || pwd == dbPwd {
		return true
	}
	return
}

const (
	iter   = 1005
	keyLen = 32
)

// 密码加密逻辑
func EncryptPwd(pwd string) string {
	charIndex := len(pwd) % 6
	halfPass := reduce([]rune(pwd), charIndex)
	salt := sha1Digest(halfPass)
	//fmt.Println("salt: ", base64.StdEncoding.EncodeToString(salt))
	code := pbkdf2.Key([]byte(pwd), salt, iter, keyLen, sha1.New)
	//fmt.Println("code: ", code)
	return base64.StdEncoding.EncodeToString(code)
}

func reduce(r []rune, index int) string {
	var (
		sum  int
		less bool
	)
	if index < len(r) {
		sum = int(r[index] % 3)
	} else {
		sum = -1
		less = true
	}

	tmp := make([]rune, 0)
	for i := 0; i < len(r); i++ {
		if i%3 != sum {
			tmp = append(tmp, r[i])
		}
	}

	if !less {
		tmp = append(tmp, r[index])
	}
	return string(tmp)
}

func sha1Digest(str string) []byte {
	sha := sha1.New()
	sha.Write([]byte(str))
	return sha.Sum(nil)
}
