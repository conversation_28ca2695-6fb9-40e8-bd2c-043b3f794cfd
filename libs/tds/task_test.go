package tds

import (
	"bc/libs/cache"
	"bc/libs/define"
	"bc/libs/log"
	"fmt"
	"testing"
	"time"
)

func TestC(t *testing.T) {
	done := make(chan int)
	go func() {
		for {
			//检查任务是否完成
			time.Sleep(10 * time.Second)
			done <- 1
			close(done)
			return
		}
	}()
	<-done
	t.Log("done")
}

const _testName = "tName"

func TestTaskDistributed(t *testing.T) {
	task := NewTask(1, _testName)
	for i := 1; i < 10; i++ {
		task.Distribute(&Job{
			Id:       int64(i),
			TaskId:   task.ID,
			TaskType: 1,
			//Data:     i,
		})
	}
	task.Wait()
}

func init() {
	log.InitLogger("logs/panda/closing", "info", false)
	log.Info("hello")
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
}

func TestConsumer(t *testing.T) {
	DefaultTaskNodeWithConsumer("bc", "tForce", "1", 5, []string{_testName}, map[int]JobFunc{1: dealJob})
	select {}
}

func dealJob(job *Job) {
	fmt.Println(*job)
}
