package tds

import (
	"bc/libs/cache"
	"bc/libs/log"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"go.uber.org/atomic"
	"go.uber.org/zap"
	"time"
)

var (
	_prefix = ""
	_name   = _prefix + "ds_task"

	_queueName = "queue"

	_taskNode *TaskNode
)

var (
	BaseSrvKey         = utils.StrBuilderBySep(":", _name, "tds", "srv")
	TaskDistributeLock = utils.StrBuilderBySep(":", _name, "tds", "distribute")
	TaskInfo           = utils.StrBuilderBySep(":", _name, "tds", "task_info")
)

type TaskNode struct {
	srvName      string //服务名
	nodeName     string //本节点名称
	isLeader     *atomic.Bool
	evenWorkNums int
	jobTypeMap   map[int]JobFunc
}

func NewTaskNode(prefix, srv, node string, evenWorkNums int) *TaskNode {
	_prefix = prefix
	tn := &TaskNode{
		srvName:      srv,
		nodeName:     node,
		isLeader:     atomic.NewBool(false),
		evenWorkNums: 1,
	}
	if evenWorkNums > 0 {
		tn.evenWorkNums = evenWorkNums
	}
	return tn
}

func (tn *TaskNode) Reg() {
	tn.reg()
	tn.check()
}

//上报节点状态
func (tn *TaskNode) reg() {
	t := time.Tick(time.Second)
	go func() {
		for range t {
			report(tn.srvName, tn.nodeName)
		}
	}()
}

func (tn *TaskNode) check() {
	t := time.Tick(time.Second)
	go func() {
		for range t {
			tn.elect()
		}
	}()
}

func (tn *TaskNode) elect() {
	var isLeader bool
	defer func() {
		tn.isLeader.Store(isLeader)
	}()

	//从缓存获取当前Leader接口key
	key := getLeaderNodeNameKey(tn.srvName)
	leaderNodeName := GetKey(key)

	//判断leader是否为本节点
	if leaderNodeName == tn.nodeName {
		isLeader = true
		//同时给本节点leader加有效时间
		ExpKey(1*time.Minute, key)
		return
	}

	//本节点非leader,查看leader节点是否有效
	if leaderNodeName != "" && isNodeValid(tn.srvName, leaderNodeName) {
		return
	}

	cache.DelKey(key)
	s := SetLockWithExp(5*time.Minute, key, tn.nodeName)
	if s {
		isLeader = true
		return
	}

}

func (tn *TaskNode) remove() {
	removeNode(tn.srvName, tn.nodeName)
}

func (tn *TaskNode) countTaskNode() (count int64) {
	count = 1
	c, err := getValidCount(tn.srvName)
	if err != nil {
		log.Error("ListSrvNodeCount getValidCount fail", zap.Error(err))
		return
	}
	return c
}

type JobFunc func(*Job)

//消费任务
func (tn *TaskNode) Consumer(taskNames []string, jobTypeMap map[int]JobFunc) {
	//创建节点处理队列处理
	tn.jobTypeMap = jobTypeMap
	for _, name := range taskNames {
		go tn.dealTaskConsumer(name)
	}
}

func (tn *TaskNode) dealTaskConsumer(taskName string) {
	for i := 0; i < tn.evenWorkNums; i++ {
		go tn.dealConsumer(taskName, i)
	}
}

func (tn *TaskNode) dealConsumer(name string, workerId int) {
	for {
		job, err := Pop(name)
		if err != nil {
			if err != redis.Nil {
				log.Error(" (tn *TaskNode) dealConsumer fail", zap.Error(err), zap.String("taskName", name), zap.Int("workerId", workerId))
			}
			time.Sleep(100 * time.Millisecond)
			continue
		}
		if job == nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		//log.Info("job",zap.Any("data",job))
		if fn, ok := tn.jobTypeMap[job.TaskType]; ok {
			fn(job)
		} else {
			log.Info("当前任务不支持处理job任务", zap.Any("data", job))
			time.Sleep(100 * time.Millisecond)
			continue
		}
	}
}

func report(srvName, nodeName string) {
	e := setNode(srvName, nodeName)
	if e != nil {
		log.Error("setNode fail", zap.Error(e))
	}
}

// DefaultTaskNode 对外提供创建默认任务节点方法
func DefaultTaskNode(prefix, srv, node string, cWorkerNums int) {
	_taskNode = NewTaskNode(prefix, srv, node, cWorkerNums)
	_taskNode.Reg()
}

// DefaultTaskNodeWithConsumer 对外提供创建默认并附带消费者配置
func DefaultTaskNodeWithConsumer(prefix, srv, node string, cWorkers int, taskNames []string, jobTypeMap map[int]JobFunc) {
	log.Info("初始化节点消费者", zap.Any("node", node), zap.Int("workers", cWorkers))
	DefaultTaskNode(prefix, srv, node, cWorkers)
	_taskNode.Consumer(taskNames, jobTypeMap)
}

func RemoveSrvNode() {
	if _taskNode != nil {
		_taskNode.remove()
	}
}

func IsTaskNodeLeader() bool {
	return _taskNode.isLeader.Load()
}
