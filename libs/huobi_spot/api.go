package huobi_spot

import (
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

//https://api.huobi.pro/market/trade?symbol=ethusdt
//{"ch":"market.ethusdt.trade.detail","status":"ok","ts":1610445253325,"tick":{"id":115555089613,"ts":1610445253175,"data":[{"id":115555089613188377074436985,"ts":1610445253175,"trade-id":102039675755,"amount":0.4,"price":1120.38,"direction":"sell"}]}}
//const (
//	apiUrl = "https://api.huobi.pro/market/trade?symbol=%s"
//	_name  = "huobi_spot"
//)
//
//func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
//	for s, _ := range codes {
//		code := strings.ToLower(s)
//		mk := getTicker(code)
//		if mk != nil {
//			list = append(list, *mk)
//		}
//	}
//	return
//}
//
//var isApiLimit int64
//
//func getTicker(symbol string) (mk *proto.MarketTrade) {
//	if isApiLimit > 0 {
//		if time.Now().Unix()-isApiLimit < 20 {
//			return
//		}
//	}
//	u := fmt.Sprintf(apiUrl, symbol)
//	r, err := http.Get(u)
//	if err != nil {
//		return
//	}
//	defer r.Body.Close()
//
//	if r.StatusCode != http.StatusOK {
//		log.Errorf("get huobi spot tickers fail,code:%v", r.StatusCode)
//		if r.StatusCode == 429 {
//			isApiLimit = time.Now().Unix()
//		}
//		return
//	}
//	isApiLimit = 0
//
//	b, err := ioutil.ReadAll(r.Body)
//	if err != nil {
//		return
//	}
//	log.Infof("rsp:%v", string(b))
//	var tickers = new(TradeTicker)
//	err = json.Unmarshal(b, tickers)
//	if err != nil {
//		return
//	}
//	size := len(tickers.Ticker.List)
//
//	ts := tickers.Ts / 1000
//	t := time.Unix(ts, 0)
//	if time.Since(t).Seconds() > 60 {
//		log.Info("获取huobi spot 数据时间超时", zap.Any("data", tickers), zap.Any("time", t), zap.Any("seconds", ts))
//		return
//	}
//	if size == 0 {
//		return
//	}
//
//	trade := tickers.Ticker.List[0]
//	mk = &proto.MarketTrade{
//		Symbol:   strings.ToUpper(symbol),
//		DealTime: time.Now().Unix(),
//		Price:    nums.NewFromFloat(trade.Price),
//		Ts:       time.Now(),
//		Source:   "huobiSpot",
//	}
//	log.Infof("ticker:%+v", *tickers)
//	return
//}
//
//type tick struct {
//	Code   string          `json:"symbol"`
//	Price  decimal.Decimal `json:"last"`
//	Volume decimal.Decimal `json:"volume"`
//}

const tickers = "https://api.huobi.pro/market/tickers"

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	return getTickers(codes)
}

func getTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(tickers)
	if err != nil {
		log.Error("huobi spot ", zap.Error(err))
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Errorf("get huobi spot tickers fail,code:%v", r.StatusCode)
		return
	}

	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		log.Error("huobi spot getTickers", zap.Error(err))
		return
	}
	log.Infof("rsp:%v", string(b))
	var tickers = new(Tickers)
	err = json.Unmarshal(b, tickers)
	if err != nil {
		return
	}
	size := len(tickers.Data)

	ts := tickers.Ts / 1000
	t := time.Unix(ts, 0)
	if time.Since(t).Seconds() > 60 {
		log.Info("获取huobi spot 数据时间超时", zap.Any("data", tickers), zap.Any("time", t), zap.Any("seconds", ts))
		return
	}
	if size == 0 {
		return
	}

	for _, item := range tickers.Data {
		code := strings.ToUpper(item.Symbol)
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    nums.NewFromFloat(item.Close),
				Ts:       time.Now(),
				Source:   "huobiSpot",
			}
			list = append(list, mk)
		}
	}

	return
}

type Tickers struct {
	Status string    `json:"status"`
	Ts     int64     `json:"ts"`
	Data   []HTicker `json:"data"`
}

type HTicker struct {
	Symbol  string  `json:"symbol"`
	Open    float64 `json:"open"`
	High    float64 `json:"high"`
	Low     float64 `json:"low"`
	Close   float64 `json:"close"`
	Amount  float64 `json:"amount"`
	Vol     float64 `json:"vol"`
	Count   int     `json:"count"`
	Bid     float64 `json:"bid"`
	BidSize float64 `json:"bidSize"`
	Ask     float64 `json:"ask"`
	AskSize float64 `json:"askSize"`
}
