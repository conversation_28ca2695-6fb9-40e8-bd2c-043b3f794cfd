/*******************************************************
	File Name: google.go
	Author: ~gan
	Created Time: 19/01/25 - 10:24:49
	Modify Time: 19/01/31 - 18:24:49
	Func 秘钥的生成、code校验工具类
 *******************************************************/
package googleAuth

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base32"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

type GAuth struct {
	r *rand.Rand
}

var ga *GAuth

func init() {
	ga = &GAuth{
		r: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

func GetGAuth() *GAuth {
	return ga
}

func (g *GAuth) CreateSecret(length int) string {
	var secret []string
	timestamp := time.Now().Unix()
	s1 := rand.NewSource(timestamp)
	r1 := rand.New(s1)

	for i := 0; i < length; i++ {
		var r = r1.Intn(len(Table))
		secret = append(secret, Table[r])
	}
	return strings.Join(secret, "")
}

func (g *GAuth) VerifyCode(secret, code string) bool {
	return g.VerifyCodeByDiscrepancy(secret, code, 3)
}

// VerifyCodeByDiscrepancy Check if the code is correct.
// This will accept codes starting from $discrepancy*30sec ago to $discrepancy*30sec from now
func (g *GAuth) VerifyCodeByDiscrepancy(secret, code string, discrepancy int64) bool {
	t := time.Now().Unix() / 30
	for i := -discrepancy; i <= discrepancy; i++ {
		decodedKey, _ := base32.StdEncoding.DecodeString(secret)
		decodedKeyInt8 := byteToInt8(decodedKey)

		current := strconv.FormatInt(verifyCode(decodedKeyInt8, t+i), 10)
		fmt.Printf("secret:%s, code:%s\n", secret, current)
		if current == code {
			return true
		}
	}
	return false
}

func (g *GAuth) VerifyCodeByDiscrepancyWithTime(secret, code string, discrepancy int64, t int64) bool {
	for i := -discrepancy; i <= discrepancy; i++ {
		decodedKey, _ := base32.StdEncoding.DecodeString(secret)
		decodedKeyInt8 := byteToInt8(decodedKey)

		current := strconv.FormatInt(verifyCode(decodedKeyInt8, t+i), 10)
		fmt.Printf("secret:%s, code:%s\n", secret, current)
		if current == code {
			return true
		}
	}
	return false
}

func verifyCode(key []int8, t int64) int64 {
	var data, dataDesc []int8
	for i := 8; i > 0; i-- {
		data = append(data, int8(t))
		t = t >> 8
	}

	for i := 0; i <= 7; i++ {
		dataDesc = append(dataDesc, data[7-i])
	}

	keyByte := int8ToByteForHmac(key)
	dataMacByte := int8ToByteForHmac(dataDesc)
	mac := hmac.New(sha1.New, keyByte)
	mac.Write(dataMacByte)
	var res = mac.Sum(nil)
	hash := byteToInt8(res)

	var offset = hash[19] & 0xF
	var offset32 = int32(offset)
	var truncatedHash int64 = 0
	var index int32
	for i := 0; i < 4; i++ {
		truncatedHash <<= 8
		index = offset32 + int32(i)
		truncatedHash = truncatedHash | (int64(int32(hash[index]) & 0xFF))
	}

	truncatedHash &= 0x7FFFFFFF
	truncatedHash %= 1000000
	return truncatedHash
}

func byteToInt8(data []byte) []int8 {
	var res []int8
	for _, item := range data {
		res = append(res, int8(item))
	}
	return res
}

func int8ToByteForHmac(data []int8) []byte {
	var res []byte
	for _, item := range data {
		if item >= 0 {
			res = append(res, byte(item))
		} else {
			temp := 256 + int32(item)
			res = append(res, byte(temp))
		}
	}
	return res
}
