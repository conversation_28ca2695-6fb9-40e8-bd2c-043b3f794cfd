/*
@Time : 3/16/20 4:35 下午
<AUTHOR> mocha
@File : trade
*/
package database

import (
	"bc/libs/define"
	"bc/libs/proto"
	"database/sql"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"time"
)

func InsertTrade(tx *sqlx.Tx, t *proto.Trade) (err error) {
	str := "insert into tb_trade(trade_id,position_id, user_id, order_id, contract_code, side, offset, price,open_avg_price, volume, trade_value, commission, account_type, lever, trade_amount, profit,close_profit, order_type, `limit`, stop, trade_time, ip_address, imei, order_client, balance, available, total_profit,broke_price,buy_price,sell_price,platform_id,raw_profit,raw_close_profit,trade_remark,entrust_order_id,blowing_fee,raw_blowing_fee,extra_id,match_type,match_id,is_maker,cur_open_time,gift_amount,gift_fee,gift_balance,gift_available,gift_close,gift_blowing) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	_, err = tx.Exec(str, t.TradeId, t.PositionId, t.UserId, t.OrderId, t.ContractCode, t.Side, t.Offset, t.Price, t.OpenAvgPrice, t.Volume, t.TradeValue, t.Commission, t.AccountType, t.Lever, t.TradeAmount, t.Profit, t.CloseProfit, t.OrderType, t.Limit, t.Stop, time.Now(), t.IpAddress, t.Imei, t.OrderClient, t.Balance, t.Available, t.TotalProfit, t.BrokePrice, t.BuyPrice, t.SellPrice, t.PlatformID, t.RawProfit, t.RawCloseProfit, t.TradeRemark, t.EntrustOrderId, t.BlowingFee, t.RawBlowingFee, t.ExtraID, t.MatchType, t.MatchId, t.IsMaker, t.CurOpenTime, t.GiftAmount, t.GiftFee, t.GiftBalance, t.GiftAvailable, t.GiftClose, t.GiftBlowing)
	return
}

func GetTradeList(userID int64, contractCode, offset string, accountType define.AccountType, start, end time.Time, page *define.Page) ([]proto.Trade, error) {
	list := make([]proto.Trade, 0)
	//str := "SELECT trade_id,position_id, user_id, order_id, contract_code, side, offset, price, open_avg_price,volume, trade_value, commission, account_type, lever, trade_amount, profit,close_profit, order_type, `limit`, stop, UNIX_TIMESTAMP(trade_time) trade_time, ip_address, imei, order_client, balance, available, total_profit,broke_price,buy_price,sell_price,platform_id,raw_profit,raw_close_profit,entrust_order_id,entrust_mode,entrust_volume,entrust_status FROM tb_trade WHERE user_id=? AND IF(?='', TRUE, contract_code=?) AND IF(?='', TRUE, offset=?) ORDER BY trade_id DESC LIMIT ?, ?"
	str := "SELECT t1.trade_id,t1.position_id, t1.user_id, t1.order_id, t1.contract_code, t1.side, t1.offset, t1.price, t1.open_avg_price,t1.volume, t1.trade_value, t1.commission, t1.account_type, t1.lever, t1.trade_amount, t1.profit,t1.close_profit, t1.order_type, t1.`limit`, t1.stop, UNIX_TIMESTAMP(t1.trade_time) AS trade_time, t1.ip_address, t1.imei, t1.order_client, t1.balance, t1.available, t1.total_profit,t1.broke_price,t1.buy_price,t1.sell_price,t1.platform_id,t1.raw_profit,t1.raw_close_profit,t1.entrust_order_id,ifnull(t2.mode,3) as entrust_mode, ifnull(t2.entrust_type, 0) AS entrust_type, ifnull(t2.volume,t1.volume) as entrust_volume,ifnull(t2.state,200) as entrust_status FROM tb_trade t1 left join tb_order t2 on t1.entrust_order_id=t2.id WHERE t1.user_id=? AND IF(?='', TRUE, t1.contract_code=?) AND IF(?='', TRUE, t1.offset=?) AND IF(?=0, TRUE, t1.account_type=?) AND t1.trade_time BETWEEN ? AND ? ORDER BY t1.trade_id DESC LIMIT ?, ?"
	err := DefaultDB().Select(&list, str, userID, contractCode, contractCode, offset, offset, accountType, accountType, start, end, page.Page*page.Count, page.Count)
	return list, err
}

func GetCoreAndFollowContractNetPos() (list []proto.ContractNetPos, err error) {
	//list = []map[string]int64
	str := "select  t1.contract_code,sum(if(t1.side='B',1,0)*t1.volume) as buy,sum(if(t1.side='B',0,1)*t1.volume) as sell,sum(if(t1.side='B',1,-1)*t1.volume) as volume from  (SELECT contract_code,side,volume from tb_position union all select contract_code,side,volume from tb_follow_position) t1  group by t1.contract_code"
	err = DefaultDB().Select(&list, str)
	return
}

func ModifyFollowMarginByNewData(tx *sqlx.Tx, positionID int64, newMargin, newAdjustMargin, forcePrice, rawMargin decimal.Decimal) error {
	_, err := tx.Exec("UPDATE tb_follow_position SET adjust_margin=?, margin=?,raw_margin=?,force_price=? WHERE id=?", newAdjustMargin, newMargin, rawMargin, forcePrice, positionID)
	return err
}

func HasPosition(tx *sqlx.Tx, userID int64) (bool, error) {
	var flag uint8
	err := tx.Get(&flag, "SELECT 1 FROM tb_position WHERE user_id=? LIMIT 1", userID)
	if err == sql.ErrNoRows {
		err = nil
	}
	return flag != 0, err
}

func HasEntrust(tx *sqlx.Tx, userID int64) (bool, error) {
	var flag uint8
	err := tx.Get(&flag, "SELECT 1 FROM tb_order WHERE user_id=? AND state IN(?,?) LIMIT 1", userID, define.OrderStatusDefault, define.OrderStatusPart)
	if err == sql.ErrNoRows {
		err = nil
	}
	return flag != 0, err
}
