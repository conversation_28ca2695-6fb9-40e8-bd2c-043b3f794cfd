package database

import (
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
)

func InsertFundingDeal(tx *sqlx.Tx, fd *proto.FundingDeal) (err error) {
	str := "INSERT INTO tb_funding_deal (id, contract_code, funding_rate, income, giving, giving_full, giving_ware, giving_follow, create_time) VALUES (:id,:contract_code,:funding_rate,:income,:giving,:giving_full,:giving_ware,:giving_follow,:create_time)"
	if tx != nil {
		_, err = tx.NamedExec(str, fd)
	} else {
		_, err = DefaultDB().NamedExec(str, fd)
	}
	return
}
