/*
@Time : 3/9/20 1:44 下午
<AUTHOR> mocha
@File : order
*/
package database

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"bytes"
	"database/sql"
	"errors"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"time"
)

// 只取取近期交易的50条记录
func GetRecentOrders(contractCode string) ([]proto.ApiRecentOrder, error) {
	str := "select match_id as 'trade_id', contract_code, price, volume, side, unix_timestamp(trade_time) order_time from tb_trade where contract_code = ? AND identity = ? order by trade_time desc limit ?"

	list := make([]proto.ApiRecentOrder, 0, define.ApiRecentTradeMaxCount)
	err := DefaultDB().Select(&list, str, contractCode, define.Taker, define.ApiRecentTradeMaxCount)
	if err != nil {
		log.Error("GetRecentOrders db error", zap.String("contractCode", contractCode), zap.Error(err))
	}
	return list, err
}

const _currentPositionBase = "SELECT p.id, p.user_id, c.contract_code, c.contract_name, c.contract_name_en, p.side, p.price, p.volume,p.volume_lock, p.force_price, p.`limit`, p.stop, p.account_type, p.lever, p.init_margin, p.margin, p.margin_gift, p.adjust_margin, p.float_profit, p.available, p.profit_ratio, p.margin_ratio, p.contract_index, p.buy_price, p.sell_price, p.commission,p.platform_id FROM tb_position p LEFT JOIN tb_contract c ON c.contract_code = p.contract_code"

// 获取用户全部合约当前持仓
func GetAllContractCurrentPosition(userID int64) ([]proto.CurrentPosition, error) {
	list := make([]proto.CurrentPosition, 0)
	str := _currentPositionBase + " WHERE p.user_id=?"
	err := DefaultDB().Select(&list, str, userID)
	return list, err
}

// 获取用户指定合约当前持仓
func GetContractCurrentPosition(userID int64, contractCode string) ([]proto.CurrentPosition, error) {
	list := make([]proto.CurrentPosition, 0)
	str := _currentPositionBase + " WHERE p.user_id=? AND p.contract_code=?"
	err := DefaultDB().Select(&list, str, userID, contractCode)
	return list, err
}

//获取用户最近指定方向的持仓信息
func GetContractCurrentPositionBySide(userID int64, contractCode string, side string) (*proto.CurrentPosition, error) {
	var po = new(proto.CurrentPosition)
	str := _currentPositionBase + " WHERE p.user_id=? AND p.contract_code=? and side=?"
	err := DefaultDB().Get(po, str, userID, contractCode, side)
	if err != nil {
		return nil, err
	}
	return po, err
}

// 获取合约下所有持仓
func GetContractAllCurrentPosition(contractCode string) ([]proto.CurrentPosition, error) {
	list := make([]proto.CurrentPosition, 0)
	str := _currentPositionBase + " WHERE p.contract_code=?"
	err := DefaultDB().Select(&list, str, contractCode)
	return list, err
}

const _getAllPosition = "SELECT id AS 'order_id', user_id, contract_code FROM tb_position"

// 获取合约下所有持仓
func GetContractAllPosition(contractCode string) ([]proto.UserOrder, error) {
	list := make([]proto.UserOrder, 0)
	str := _getAllPosition + " WHERE contract_code=?"
	err := DefaultDB().Select(&list, str, contractCode)
	return list, err
}

// 获取用户所有持仓
func GetUserAllPosition(userID int64, contract string) ([]proto.UserOrder, error) {
	list := make([]proto.UserOrder, 0)
	str := _getAllPosition + " WHERE user_id=? AND IF(?='', TRUE, contract_code=?)"
	err := DefaultDB().Select(&list, str, userID, contract, contract)
	return list, err
}

// 获取持仓信息 账户类型/未结束订单杠杆倍数/已持仓数量
func GetUserOrderStateForPlace(tx *sqlx.Tx, userID int64, contractCode string) (*proto.UserOrderState, error) {
	state := new(proto.UserOrderState)
	// 获取未成交订单信息
	row := tx.QueryRow("SELECT IFNULL( SUM( IF ( side = 'B', volume - trade_volume, 0 )), 0 ) AS 'unsold_buy',IFNULL( SUM( IF ( side = 'S', volume - trade_volume, 0 )), 0 ) AS 'unsold_sell',IFNULL( account_type, 0) AS 'account_type',IFNULL( lever, 0) AS 'lever' FROM tb_order WHERE user_id = ? AND contract_code = ? AND state IN ( 0, 2 )", userID, contractCode)
	err := row.Scan(&state.UnsoldBuy, &state.UnsoldSell, &state.AccountType, &state.Lever)
	if err != nil {
		return state, err
	}

	// 获取持仓订单信息
	row = tx.QueryRow("SELECT IFNULL( SUM( IF ( side = 'B', volume, 0 )), 0 ) AS 'hold_buy',IFNULL( SUM( IF ( side = 'S', volume, 0 )), 0 ) AS 'hold_sell',IFNULL( IF ( ? != 0, ?, account_type ), 0 ) AS 'account_type',IFNULL( IF ( ? != 0, ?, lever ), 0 ) AS 'lever' FROM tb_position WHERE user_id = ? AND contract_code = ?", state.AccountType, state.AccountType, state.Lever, state.Lever, userID, contractCode)
	err = row.Scan(&state.HoldBuy, &state.HoldSell, &state.AccountType, &state.Lever)
	return state, err
}

const _planCloseOrder = "select plan_close_order_id,position_id, user_id, contract_code, side, amount, `limit`, `stop`,condition_limit,condition_stop, create_time,platform_id,lever,account_type,trigger_status,mode,entrust_type,entrust_limit,entrust_stop from tb_plan_close_order "

//查询所有用户的平仓条件单
func ListPlanCloseOrder(tx *sqlx.Tx, accountType int) (list []proto.PlanCloseOrder, err error) {
	str := _planCloseOrder + " where account_type=?  and trigger_status=? "
	if tx != nil {
		err = tx.Select(&list, str, accountType, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Select(&list, str, accountType, define.OrderConditionNotTrigger)
	}
	return
}

func ListPlanCloseOrderPage(tx *sqlx.Tx, page, pageSize int) (list []proto.PlanCloseOrder, err error) {
	str := _planCloseOrder + " where trigger_status=? limit ?,?"
	if tx != nil {
		err = tx.Select(&list, str, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	} else {
		err = DefaultDB().Select(&list, str, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	}
	return
}

func ListPlanCloseOrderPageForContract(tx *sqlx.Tx, page, pageSize int) (list []proto.PlanCloseOrder, err error) {
	str := _planCloseOrder + " where  account_type in (?,?,?,?) and trigger_status=? limit ?,?"
	if tx != nil {
		err = tx.Select(&list, str, define.AccountTypeByFullHouse, define.AccountTypeByWareHouse, define.AccountTypeByFullHouseSplit, define.AccountTypeByWareHouseSplit, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	} else {
		err = DefaultDB().Select(&list, str, define.AccountTypeByFullHouse, define.AccountTypeByWareHouse, define.AccountTypeByFullHouseSplit, define.AccountTypeByWareHouseSplit, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	}
	return
}

func ListPlanCloseOrderPageForFollow(tx *sqlx.Tx, page, pageSize int) (list []proto.PlanCloseOrder, err error) {
	str := _planCloseOrder + " where account_type=? and trigger_status=? limit ?,?"
	if tx != nil {
		err = tx.Select(&list, str, define.AccountTypeByFollow, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	} else {
		err = DefaultDB().Select(&list, str, define.AccountTypeByFollow, define.OrderConditionNotTrigger, page*pageSize, pageSize)
	}
	return
}

//获取计划平仓单详情
func GetPlanCloseOrder(tx *sqlx.Tx, id int64) (order *proto.PlanCloseOrder, err error) {
	o := new(proto.PlanCloseOrder)
	str := _planCloseOrder + " where plan_close_order_id=? and trigger_status=?"
	if tx != nil {
		err = tx.Get(o, str, id, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(o, str, id, define.OrderConditionNotTrigger)
	}
	if err != nil {
		return nil, err
	}
	order = o
	return
}

func GetPlanCloseOrderWithLock(tx *sqlx.Tx, id int64) (order *proto.PlanCloseOrder, err error) {
	o := new(proto.PlanCloseOrder)
	str := _planCloseOrder + " where plan_close_order_id=? for update"
	if tx != nil {
		err = tx.Get(o, str, id)
	} else {
		err = DefaultDB().Get(o, str, id)
	}
	if err != nil {
		return nil, err
	}
	order = o
	return
}

func GetPlanCloseOrderWithNoLock(tx *sqlx.Tx, id int64) (order *proto.PlanCloseOrder, err error) {
	o := new(proto.PlanCloseOrder)
	str := _planCloseOrder + " where plan_close_order_id=? "
	if tx != nil {
		err = tx.Get(o, str, id)
	} else {
		err = DefaultDB().Get(o, str, id)
	}
	if err != nil {
		return nil, err
	}
	order = o
	return
}

//修改指定计划平仓状态
func UpdatePlanCloseOrderStatus(tx *sqlx.Tx, id int64, triggerPrice decimal.Decimal, triggerTime time.Time, triggerType int, status int) (err error) {
	str := "update tb_plan_close_order set trigger_price=?,trigger_time=?,trigger_type=?,trigger_status=? where plan_close_order_id=? and trigger_status=?"
	if tx != nil {
		_, err = tx.Exec(str, triggerPrice, triggerTime, triggerType, status, id, define.OrderConditionNotTrigger)
	} else {
		_, err = DefaultDB().Exec(str, triggerPrice, triggerTime, triggerType, status, id, define.OrderConditionNotTrigger)
	}
	return
}

//根据持仓id修改计划平仓单状态
func UpdatePlanCloseOrderStatusByPosId(tx *sqlx.Tx, posId int64, triggerPrice decimal.Decimal, triggerTime time.Time, triggerType int, status int) (err error) {
	str := "update tb_plan_close_order set trigger_price=?,trigger_time=?,trigger_type=?,trigger_status=? where position_id=? and trigger_status=?"
	if tx != nil {
		_, err = tx.Exec(str, triggerPrice, triggerTime, triggerType, status, posId, define.OrderConditionNotTrigger)
	} else {
		_, err = DefaultDB().Exec(str, triggerPrice, triggerTime, triggerType, status, posId, define.OrderConditionNotTrigger)
	}
	return
}

func UpdatePlanCloseOrderStatusByPosIdByVersion2(tx *sqlx.Tx, posId int64, triggerPrice decimal.Decimal, triggerTime time.Time, triggerType int, status int) (err error) {
	str := "update tb_plan_close_order set trigger_price=?,trigger_time=?,trigger_type=?,trigger_status=? where position_id=? and trigger_status=?"
	if tx != nil {
		_, err = tx.Exec(str, triggerPrice, triggerTime, triggerType, status, posId, define.OrderConditionNotTrigger)
	} else {
		_, err = DefaultDB().Exec(str, triggerPrice, triggerTime, triggerType, status, posId, define.OrderConditionNotTrigger)
	}
	return
}

////查询用户的条件平仓单
//func ListUserPlanCloseOrder(tx *sqlx.Tx, userId int64) (list []proto.PlanCloseOrder, err error) {
//	_userCondition := " where user_id=?"
//	str := utils.StrBuilder(_planCloseOrder, _userCondition)
//	if tx != nil {
//		err = tx.Select(&list, str, userId)
//	} else {
//		err = DefaultDB().Select(&list, str, userId)
//	}
//	return
//}

//获取用户指定持仓未完成的计划列表
func ListUserPlanCloseOrdersByPositionId(tx *sqlx.Tx, posId int64) (list []proto.PlanCloseOrder, err error) {
	_userCondition := " where  position_id=? and trigger_status=?"
	str := utils.StrBuilder(_planCloseOrder, _userCondition)
	if tx != nil {
		err = tx.Select(&list, str, posId, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Select(&list, str, posId, define.OrderConditionNotTrigger)
	}
	return
}

func ListUserPlanCloseOrdersByPositionIdWithFollow(tx *sqlx.Tx, userId, posId int64) (list []proto.PlanCloseOrder, err error) {
	_userCondition := " where user_id=? and position_id=? and trigger_status=?"
	str := utils.StrBuilder(_planCloseOrder, _userCondition)
	if tx != nil {
		err = tx.Select(&list, str, userId, posId, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Select(&list, str, userId, posId, define.OrderConditionNotTrigger)
	}
	return
}

//查询用户未触发的平仓条件单数目
func CountUserPlanCloseOrder(tx *sqlx.Tx, userId int64, code, side string) (count int, err error) {
	_userCondition := "select count(1) from tb_plan_close_order where user_id=? and contract_code=? and side=? and trigger_status=?"
	if tx != nil {
		err = tx.Get(&count, _userCondition, userId, code, side, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(&count, _userCondition, userId, code, side, define.OrderConditionNotTrigger)
	}
	return
}

//插入条件平仓单
func InsertPlanCloseOrder(tx *sqlx.Tx, order *proto.PlanCloseOrder) (lastId int64, err error) {
	str := "INSERT INTO tb_plan_close_order(user_id, position_id, contract_code, side, amount, `limit`, `stop`,condition_limit,condition_stop, create_time,account_type,platform_id,lever,mode,entrust_type,entrust_limit,entrust_stop,ip_address,entrust_order_id,is_follower) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	var last sql.Result
	if tx != nil {
		last, err = tx.Exec(str, order.UserId, order.PositionId, order.ContractCode, order.Side, order.Amount, order.Limit, order.Stop, order.ConditionLimit, order.ConditionStop, order.CreateTime, order.AccountType, order.PlatformID, order.Lever, order.Mode, order.EntrustType, order.EntrustLimit, order.EntrustStop, order.IPAddress, order.EntrustOrderId, order.IsFollower)
	} else {
		last, err = DefaultDB().Exec(str, order.UserId, order.PositionId, order.ContractCode, order.Side, order.Amount, order.Limit, order.Stop, order.ConditionLimit, order.ConditionStop, order.CreateTime, order.AccountType, order.PlatformID, order.Lever, order.Mode, order.EntrustType, order.EntrustLimit, order.EntrustStop, order.IPAddress, order.EntrustOrderId, order.IsFollower)
	}
	if err != nil {
		log.Errorf("InsertPlanCloseOrder fail,%v", err)
		return
	}
	lastId, err = last.LastInsertId()
	return
}

// 更新条件单平仓
func UpdatePlanCloseOrder(tx *sqlx.Tx, order *proto.PlanCloseOrder) error {
	_, err := tx.Exec("UPDATE tb_plan_close_order SET `limit`=?, stop=?, condition_limit=?, condition_stop=?, amount=? WHERE plan_close_order_id=? AND trigger_status=?", order.Limit, order.Stop, order.ConditionLimit, order.ConditionStop, order.Amount, order.PlanCloseOrderId, define.OrderConditionNotTrigger)
	return err
}

func UpdateFollowPositionStop(tx *sqlx.Tx, positionId int64, limit, stop decimal.Decimal) error {
	_, err := tx.Exec("UPDATE tb_follow_position SET `limit`=?, stop=? WHERE id=?", limit, stop, positionId)
	return err
}

////根据计划平仓id修改状态
//func DelCloseOrderByOrderId(tx *sqlx.Tx, orderId int64) (err error) {
//	str := "delete from tb_plan_close_order where plan_close_order_id=?"
//	if tx != nil {
//		_, err = tx.Exec(str, orderId)
//	} else {
//		_, err = DefaultDB().Exec(str, orderId)
//	}
//	return
//}
//
//func DelCloseOrderByPositionId(tx *sqlx.Tx, posId int64) (err error) {
//	str := "delete from tb_plan_close_order where position_id=?"
//	if tx != nil {
//		_, err = tx.Exec(str, posId)
//	} else {
//		_, err = DefaultDB().Exec(str, posId)
//	}
//	return
//}
//
//func DelCloseOrderByContractAndSide(tx *sqlx.Tx, userId int64, code, side string) (err error) {
//	str := "delete from tb_plan_close_order where user_id=? and contract_code=? and side=?"
//	if tx != nil {
//		_, err = tx.Exec(str, userId, code, side)
//	} else {
//		_, err = DefaultDB().Exec(str, userId, code, side)
//	}
//	return
//}

const _updateConditionOrderState = "UPDATE tb_plan_order SET `status`=?, order_time=IF(?=? OR ?=?,?,NULL), cancel_time=IF(?=?,?,NULL) WHERE plan_order_id=? AND `status`=?"

// 更新条件单状态
func UpdateConditionOrderState(tx *sqlx.Tx, id int64, origin, newState int, tm time.Time) error {
	var err error
	if tx != nil {
		_, err = tx.Exec(_updateConditionOrderState, newState, newState, define.OrderCondHadTrigger, newState, define.OrderCondFailed, tm, newState, define.OrderCondCancel, tm, id, origin)
	} else {
		_, err = DefaultDB().Exec(_updateConditionOrderState, newState, newState, define.OrderCondHadTrigger, newState, define.OrderCondFailed, tm, newState, define.OrderCondCancel, tm, id, origin)
	}
	return err
}

const _updatePlanOrderPositionID = "UPDATE tb_plan_order SET position_id=?, account_type=? WHERE plan_order_id=?;"

// UpdatePlanOrderPositionID 更新条件单对应持仓id
func UpdatePlanOrderPositionID(tx *sqlx.Tx, id, positionID int64, accountType define.AccountType) error {
	var err error
	if tx != nil {
		_, err = tx.Exec(_updatePlanOrderPositionID, positionID, accountType, id)
	} else {
		_, err = DefaultDB().Exec(_updatePlanOrderPositionID, positionID, accountType, id)
	}
	return err
}

func CountUserClientId(tx *sqlx.Tx, userId, clientOrderId int64) (count int, err error) {

	str := "SELECT count(1) FROM tb_order where user_id=? and client_order_id=?"
	log.Info(str, zap.Int64("id", userId), zap.Int64("oid", clientOrderId))
	if tx != nil {
		err = tx.Get(&count, str, userId, clientOrderId)
	} else {
		err = DefaultDB().Get(&count, str, userId, clientOrderId)
	}
	return
}

func InsertEntrustOrder(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "INSERT INTO tb_order (id, platform_id, position_id, user_id, contract_code, `offset`, entrust_type,entrust_strategy, mode, order_type, side, price, volume, account_type, lever, asset_lock, trade_volume, trade_price, cost_fee, cost_asset, profit,`state`, create_time,update_time,mark,extra_id,follow_order_id,`limit`,`stop`,limit_amount,stop_amount,trigger_type_limit,trigger_type_stop,entrust_limit,entrust_stop,imei,ip_address,order_client,entrust_type_limit, entrust_type_stop,client_order_id,plan_trigger_price,use_broken_price,broken_contract,expect_price,gift_amount,gift_fee,is_follower) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	data := []interface{}{order.ID, order.PlatformID, order.PositionID, order.UserID, order.ContractCode, order.Offset, order.EntrustType, order.EntrustStrategy, order.Mode, order.OrderType, order.Side, order.Price, order.Volume, order.AccountType, order.Lever, order.AssetLock, order.TradeVolume, order.TradePrice, order.CostFee, order.CostAsset, order.Profit, order.State, order.CreateTime, order.UpdateTime, order.Mark, order.ExtraID, order.FollowOrderID, order.Limit, order.Stop, order.LimitAmount, order.StopAmount, order.TriggerTypeLimit, order.TriggerTypeStop, order.EntrustLimit, order.EntrustStop, order.Imei, order.IpAddress, order.OrderClient, order.EntrustTypeLimit, order.EntrustTypeStop, order.ClientOrderId, order.PlanTriggerPrice, order.IsUseBrokenPrice, order.BrokenContract, order.ExpectPrice, order.GiftAmount, order.GiftFee, order.IsFollower}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

const _baseEntrustOrder = "SELECT id, platform_id, position_id, user_id, contract_code, offset, entrust_type,entrust_strategy, mode, order_type, side, price, volume, account_type, lever, asset_lock, trade_volume, trade_price, cost_fee, cost_asset, state, profit,close_profit,create_time,update_time,mark,extra_id,follow_order_id,`limit`,`stop`,limit_amount,stop_amount,trigger_type_limit,trigger_type_stop,entrust_limit,entrust_stop,imei,ip_address,order_client,last_match_price,client_order_id,entrust_type_limit,entrust_type_stop,expect_price,gift_amount,gift_fee,gift_close,gift_blowing FROM tb_order where 1=1 "

func GetUserEntrustOrderCountForPlanOrder(tx *sqlx.Tx, extra_id, userId int64, offset string) (count int64, err error) {
	str := "SELECT count(*) from tb_order where extra_id=? and user_id=? and offset=?"
	if tx != nil {
		err = tx.Get(&count, str, extra_id, userId, offset)
	} else {
		err = DefaultDB().Get(&count, str, extra_id, userId, offset)
	}
	return
}

func GetUserEntrustOrders(tx *sqlx.Tx, userId int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  user_id=?"
	str += " order by update_time desc limit 100"
	data := []interface{}{userId}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

//获取未完成指定模式的订单个数
func CountEntrustUnFinishOrderByFollow(tx *sqlx.Tx, userId int64) (count int, err error) {
	str := "select count(1) from tb_order where  user_id=? and  (state=0 or state=202) and account_type=? "
	if tx != nil {
		err = tx.Get(&count, str, userId, define.AccountTypeByFollow)
	} else {
		err = DefaultDB().Get(&count, str, userId, define.AccountTypeByFollow)
	}
	return
}

func CountEntrustUnFinishOrderByFull(tx *sqlx.Tx, userId int64) (count int, err error) {
	str := "select count(1) from tb_order where  user_id=? and  (state=0 or state=202) and account_type in (?,?) "
	if tx != nil {
		err = tx.Get(&count, str, userId, define.AccountTypeByFullHouseSplit, define.AccountTypeByFullHouse)
	} else {
		err = DefaultDB().Get(&count, str, userId, define.AccountTypeByFullHouseSplit, define.AccountTypeByFullHouse)
	}
	return
}

func CountEntrustUnFinishOrderByWare(tx *sqlx.Tx, userId int64) (count int, err error) {
	str := "select count(1) from tb_order where  user_id=? and  (state=0 or state=202) and account_type in (?,?) "
	if tx != nil {
		err = tx.Get(&count, str, userId, define.AccountTypeByWareHouseSplit, define.AccountTypeByWareHouse)
	} else {
		err = DefaultDB().Get(&count, str, userId, define.AccountTypeByWareHouseSplit, define.AccountTypeByWareHouse)
	}
	return
}

const _hasEntrustUnFinishOrderByFollow = "SELECT 1 FROM tb_order WHERE user_id=? AND (state=0 OR state=202) AND account_type=? LIMIT 1"

// HasEntrustUnFinishOrderByFollow 获取是否有未完成指定模式的订单
func HasEntrustUnFinishOrderByFollow(tx *sqlx.Tx, userId int64) (bool, error) {
	var (
		flag int8
		err  error
	)
	if tx != nil {
		err = tx.Get(&flag, _hasEntrustUnFinishOrderByFollow, userId, define.AccountTypeByFollow)
	} else {
		err = DefaultDB().Get(&flag, _hasEntrustUnFinishOrderByFollow, userId, define.AccountTypeByFollow)
	}
	if err == sql.ErrNoRows {
		return false, nil
	}

	return flag == 1, err
}

const _hasEntrustUnFinishOrderByFull = "SELECT 1 FROM tb_order WHERE user_id=? AND (state=0 OR state=202) AND account_type IN (?,?) LIMIT 1"

// HasEntrustUnFinishOrderByFull 获取是否有未完成指定模式的订单(不区分分仓)
func HasEntrustUnFinishOrderByFull(tx *sqlx.Tx, userId int64) (bool, error) {
	var (
		flag int8
		err  error
	)
	if tx != nil {
		err = tx.Get(&flag, _hasEntrustUnFinishOrderByFull, userId, define.AccountTypeByFullHouseSplit, define.AccountTypeByFullHouse)
	} else {
		err = DefaultDB().Get(&flag, _hasEntrustUnFinishOrderByFull, userId, define.AccountTypeByFullHouseSplit, define.AccountTypeByFullHouse)
	}
	if err == sql.ErrNoRows {
		return false, nil
	}

	return flag == 1, err
}

const _hasEntrustUnFinishOrderByWare = "SELECT 1 FROM tb_order WHERE user_id=? AND (state=0 OR state=202) AND account_type IN (?,?) LIMIT 1"

// HasEntrustUnFinishOrderByWare 获取是否有未完成指定模式的订单(不区分分仓)
func HasEntrustUnFinishOrderByWare(tx *sqlx.Tx, userId int64) (bool, error) {
	var (
		flag int8
		err  error
	)
	if tx != nil {
		err = tx.Get(&flag, _hasEntrustUnFinishOrderByWare, userId, define.AccountTypeByWareHouseSplit, define.AccountTypeByWareHouse)
	} else {
		err = DefaultDB().Get(&flag, _hasEntrustUnFinishOrderByWare, userId, define.AccountTypeByWareHouseSplit, define.AccountTypeByWareHouse)
	}
	if err == sql.ErrNoRows {
		return false, nil
	}

	return flag == 1, err
}

//获取所有未完成的订单
func ListEntrustUnFinishOrders(tx *sqlx.Tx, codes []string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and contract_code "
	var s bytes.Buffer
	var data []interface{}
	s.WriteString(str)
	s.WriteString(" in (")
	for i, code := range codes {
		if i != len(codes)-1 {
			s.WriteString("?,")
		} else {
			s.WriteString("?")
		}
		data = append(data, code)
	}
	s.WriteString(")")
	log.Infof("sql: %+v", s.String())
	if tx != nil {
		err = tx.Select(&list, s.String(), data...)
	} else {
		err = DefaultDB().Select(&list, s.String(), data...)
	}
	return
}

//获取未完成的没进入撮合的定案
func ListEntrustUnFinishOrdersForRc(tx *sqlx.Tx, codes []string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and srv_mark=0 and  (state=0 or state=202) and contract_code "
	var s bytes.Buffer
	var data []interface{}
	s.WriteString(str)
	s.WriteString(" in (")
	for i, code := range codes {
		if i != len(codes)-1 {
			s.WriteString("?,")
		} else {
			s.WriteString("?")
		}
		data = append(data, code)
	}
	s.WriteString(")")
	log.Infof("sql: %+v", s.String())
	if tx != nil {
		err = tx.Select(&list, s.String(), data...)
	} else {
		err = DefaultDB().Select(&list, s.String(), data...)
	}
	return
}

//获取撮合未完成的限价订单
func ListEntrustUnFinishLimitOrdersForMatch(tx *sqlx.Tx, codes []string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and entrust_type=1 and  (state=0 or state=202) and contract_code "
	var s bytes.Buffer
	var data []interface{}
	s.WriteString(str)
	s.WriteString(" in (")
	for i, code := range codes {
		if i != len(codes)-1 {
			s.WriteString("?,")
		} else {
			s.WriteString("?")
		}
		data = append(data, code)
	}
	s.WriteString(")")
	log.Infof("sql: %+v", s.String())
	if tx != nil {
		err = tx.Select(&list, s.String(), data...)
	} else {
		err = DefaultDB().Select(&list, s.String(), data...)
	}
	return
}

//获取撮合未完成的订单
func ListEntrustUnFinishOrdersForMatch(tx *sqlx.Tx, codes []string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and srv_mark=1 and  (state=0 or state=202) and contract_code "
	var s bytes.Buffer
	var data []interface{}
	s.WriteString(str)
	s.WriteString(" in (")
	for i, code := range codes {
		if i != len(codes)-1 {
			s.WriteString("?,")
		} else {
			s.WriteString("?")
		}
		data = append(data, code)
	}
	s.WriteString(")")
	log.Infof("sql: %+v", s.String())
	if tx != nil {
		err = tx.Select(&list, s.String(), data...)
	} else {
		err = DefaultDB().Select(&list, s.String(), data...)
	}
	return
}

// ListEntrustUnFinishOrdersByUserId 获取用户未完成的订单
func ListEntrustUnFinishOrdersByUserId(tx *sqlx.Tx, userId int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? "
	if tx != nil {
		err = tx.Select(&list, str, userId)
	} else {
		err = DefaultDB().Select(&list, str, userId)
	}
	return
}

//根据订单类似及状态查询委托订单列表
func ListEntrustUnFinishOrdersByCustom(tx *sqlx.Tx, userId int64, code string, orderType []int, state []int) (list []proto.EntrustOrder, err error) {
	var data []interface{}
	str := _baseEntrustOrder + " and user_id=? "
	data = append(data, userId)
	if code != "" {
		str += "and contract_code=?"
		data = append(data, code)
	}
	if len(orderType) == 1 {
		str += "and orderType=?"
		data = append(data, orderType[0])
	}
	if len(orderType) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and orderType in (?)", orderType)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}

	if len(state) == 1 {
		str += "and state=?"
		data = append(data, state[0])
	}
	if len(orderType) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and state in (?)", state)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}

	str += " order by id desc limit 100"

	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

//一键平仓查询未完成委托
func ListEntrustUnFinishOrdersWithFullCloseOut(tx *sqlx.Tx, userId int64, ids []int64, code string, isContainLimitStop bool) (list []proto.EntrustOrder, err error) {
	var data []interface{}
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? and account_type!=3 and `offset`='C' "
	data = append(data, userId)
	if !isContainLimitStop {
		str += " and order_type=0 "
	} else {
		str += " and order_type in (0,2,4) "
	}
	if len(ids) == 1 {
		str += "and id=?"
		data = append(data, ids[0])
	}
	if len(ids) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and id in (?)", ids)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}
	if code != "" {
		str += "and contract_code=?"
		data = append(data, code)
	}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

func ListEntrustUnFinishOrdersByIds(tx *sqlx.Tx, userId int64, ids []int64, code string, isContainLimitStop bool) (list []proto.EntrustOrder, err error) {
	var data []interface{}
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? "
	data = append(data, userId)
	if !isContainLimitStop {
		str += " and order_type=0 "
	} else {
		str += " and order_type in (0,2,4) "
	}
	if len(ids) == 1 {
		str += "and id=?"
		data = append(data, ids[0])
	}
	if len(ids) > 1 {
		var strPart string
		var dataPart []interface{}
		strPart, dataPart, err = sqlx.In(" and id in (?)", ids)
		if err != nil {
			return
		}
		str += strPart
		data = append(data, dataPart...)
	}
	if code != "" {
		str += "and contract_code=?"
		data = append(data, code)
	}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

//查询非跟单外的未完成订单
func ListEntrustUnFinishOrdersByPositions(tx *sqlx.Tx, userId int64, posIds []int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? and account_type!=3 and order_type!=5 and position_id in (?)"
	str, data, err := sqlx.In(str, userId, posIds)
	if err != nil {
		return
	}
	if tx != nil {
		err = tx.Select(&list, str, data...)
	} else {
		err = DefaultDB().Select(&list, str, data...)
	}
	return
}

// ListEntrustUnFinishOrdersByUserIdForFull 获取用户全仓未完成的订单(不包括强平单）
func ListEntrustUnFinishOrdersByUserIdForFull(tx *sqlx.Tx, userId int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? and (account_type=1 or account_type=5) and order_type!=5"
	if tx != nil {
		err = tx.Select(&list, str, userId)
	} else {
		err = DefaultDB().Select(&list, str, userId)
	}
	return
}

// ListEntrustUnFinishOrdersByUserIdAndPositionID 获取用户未完成的订单
func ListEntrustUnFinishOrdersByUserIdAndPositionID(tx *sqlx.Tx, userId, positionId int64) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? and position_id=?"
	if tx != nil {
		err = tx.Select(&list, str, userId, positionId)
	} else {
		err = DefaultDB().Select(&list, str, userId, positionId)
	}
	return
}

// ListEntrustUnFinishOrdersByUserIdAndContractSide 获取用户单方向合约未完成的订单
func ListEntrustUnFinishOrdersByUserIdAndContractSide(tx *sqlx.Tx, userId int64, accountType define.AccountType, contractCode, side string) (list []proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  (state=0 or state=202) and user_id=? and account_type=? and contract_code=? and side=?"
	if tx != nil {
		err = tx.Select(&list, str, userId, accountType, contractCode, side)
	} else {
		err = DefaultDB().Select(&list, str, userId, accountType, contractCode, side)
	}
	return
}

func GetUserUnfinishedEntrustVolume(tx *sqlx.Tx, userId int64, contractCode string) (count int, err error) {
	str := "SELECT ifnull(sum(volume-trade_volume),0)  from tb_order where user_id=?  and contract_code=? and offset='O' and (state=0 or state=202)"
	if tx != nil {
		err = tx.Get(&count, str, userId, contractCode)
	} else {
		err = DefaultDB().Get(&count, str, userId, contractCode)
	}
	return
}

//只限限价单
func GetUserUnfinishedEntrustCount(tx *sqlx.Tx, userId int64) (total int, unDoneOpen map[string]map[string]int, err error) {
	unDoneOpen = make(map[string]map[string]int)

	rows, err := tx.Queryx("SELECT position_id, contract_code, side, `offset` FROM tb_order WHERE user_id=? AND state IN(0, 202) AND entrust_type=1", userId)
	if err != nil {
		return
	}
	defer rows.Close()

	var (
		positionId         int64
		code, side, offset string
	)
	for rows.Next() {
		err = rows.Scan(&positionId, &code, &side, &offset)
		if err != nil {
			return
		}

		total++
		if offset == define.OffsetOpen && positionId == 0 {
			// 统计未完成的新开仓计数
			codeMp, ok := unDoneOpen[code]
			if !ok {
				codeMp = make(map[string]int)
				unDoneOpen[code] = codeMp
			}
			codeMp[side]++
		}
	}
	return
}

//已锁定模式查询订单
func GetUserEntrustOrderWithLock(tx *sqlx.Tx, orderId int64) (order *proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  id=? for update"
	data := []interface{}{orderId}
	o := new(proto.EntrustOrder)
	if tx != nil {
		err = tx.Get(o, str, data...)
	} else {
		err = errors.New("tx is nil")
	}
	if err != nil {
		return
	}
	order = o
	return
}

func GetUserEntrustOrder(tx *sqlx.Tx, orderId int64) (order *proto.EntrustOrder, err error) {
	str := _baseEntrustOrder + " and  id=? "
	data := []interface{}{orderId}
	o := new(proto.EntrustOrder)
	if tx != nil {
		err = tx.Get(o, str, data...)
	} else {
		err = DefaultDB().Get(o, str, data...)
	}
	if err != nil {
		log.Error("GetUserEntrustOrder fail", zap.Error(err), zap.Int64("orderId", orderId))
		if err == sql.ErrNoRows {
			err = nil
			return
		}
		return
	}
	order = o
	return
}

//修改委托订单信息
func UpdateEntrustOrderForTrade(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "update tb_order set asset_lock=?,trade_volume=?,trade_price=?,cost_fee=?,cost_asset=?,`state`=?,update_time=?,position_id=?,last_match_price=?,profit=?,close_profit=?,gift_amount=?,gift_fee=?,gift_close=?,gift_blowing=? where id=?"
	data := []interface{}{order.AssetLock, order.TradeVolume, order.TradePrice, order.CostFee, order.CostAsset, order.State, order.UpdateTime, order.PositionID, order.LastMatchPrice, order.Profit, order.CloseProfit, order.GiftAmount, order.GiftFee, order.GiftClose, order.GiftBlowing, order.ID}
	if order == nil {
		return
	}
	if order.ID == 0 {
		return errors.New("entrust order id invalid")
	}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

//修改委托订单信息
func UpdateEntrustOrderForReleaseLock(tx *sqlx.Tx, order *proto.EntrustOrder) (err error) {
	str := "update tb_order set asset_lock=?,gift_amount=?,`state`=?,update_time=? where id=?"
	data := []interface{}{order.AssetLock, order.GiftAmount, order.State, order.UpdateTime, order.ID}
	if order == nil {
		return
	}
	if order.ID == 0 {
		return errors.New("entrust order id invalid")
	}
	if tx != nil {
		_, err = tx.Exec(str, data...)
	} else {
		_, err = DefaultDB().Exec(str, data...)
	}
	return
}

func UpdateEntrustOrderForCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId)
	}
	return
}

func UpdateEntrustOrderMarkForCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=? and mark!=1 and  (state=0 or state=202) "
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId)
	}
	return
}

func UpdateEntrustOrderMarkForSystemCanceling(tx *sqlx.Tx, orderId int64, mark int) (err error) {
	str := "update tb_order set mark=? where id=? and mark=0 and  (state=0 or state=202) "
	if tx != nil {
		_, err = tx.Exec(str, mark, orderId)
	} else {
		_, err = DefaultDB().Exec(str, mark, orderId)
	}
	return
}

func ListEntrustUnFinishOrdersByForce(tx *sqlx.Tx, duration time.Duration) (list []proto.EntrustOrder, err error) {
	now := time.Now()
	now = now.Add(-duration)
	str := _baseEntrustOrder + " and  (state=0 or state=202) and order_type=5 and create_time<?"
	if err != nil {
		return
	}
	if tx != nil {
		err = tx.Select(&list, str, now)
	} else {
		err = DefaultDB().Select(&list, str, now)
	}
	return
}

func ListEntrustUnFinishOrdersByMarket(tx *sqlx.Tx, duration time.Duration) (list []proto.EntrustOrder, err error) {
	now := time.Now()
	now = now.Add(-duration)
	str := _baseEntrustOrder + " and entrust_type=0 and (state=0 or state=202) and order_type!=5 and create_time<?"
	if err != nil {
		return
	}
	if tx != nil {
		err = tx.Select(&list, str, now)
	} else {
		err = DefaultDB().Select(&list, str, now)
	}
	return
}

func UpdateEntrustOrderForAC(tx *sqlx.Tx, orderId int64, slippage decimal.Decimal) (err error) {
	//str := "update tb_order set ac_slippage=? where id=?"
	str := "INSERT INTO tb_order_extend (order_id, ac_slippage, create_time) VALUES (?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, orderId, slippage, time.Now())
	} else {
		_, err = DefaultDB().Exec(str, orderId, slippage, time.Now())
	}
	return
}
