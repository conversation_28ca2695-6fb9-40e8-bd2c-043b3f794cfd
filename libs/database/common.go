package database

import (
	"bc/libs/define"
	"bc/libs/proto"
)

// 获取版本信息
func GetVersions() ([]proto.VersionInfo, error) {
	var list []proto.VersionInfo
	err := DefaultDB().Select(&list, "SELECT v.platform_id,v.channel_id,v.os_type,v.version,v.version_string,v.force_upgrade,v.link,v.url,v.create_time,tv.max_force AS last_force FROM tb_version_v2 AS v RIGHT JOIN ( SELECT platform_id,channel_id,os_type,MAX( version ) AS max_ver,MAX(IF( force_upgrade, version, 0 )) AS max_force FROM tb_version_v2 WHERE valid AND create_time <= NOW() GROUP BY platform_id,channel_id,os_type ) AS tv ON v.platform_id = tv.platform_id AND v.channel_id = tv.channel_id AND v.os_type = tv.os_type AND v.version = tv.max_ver")
	return list, err
}

// 获取版本更新文案
func GetVersionContent(platformID, version int, osType define.OsType) (map[define.ReqLang]string, error) {
	var content string
	var language define.ReqLang

	rows, err := DefaultDB().Queryx("SELECT language, content FROM tb_version_content WHERE platform_id=? AND os_type=? AND version=?;", platformID, osType, version)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	trans := make(map[define.ReqLang]string)
	for rows.Next() {
		err = rows.Scan(&language, &content)
		if err != nil {
			return nil, err
		}
		trans[language] = content
	}
	return trans, nil
}

// GetCountryInfoByCode 获取地区信息
func GetCountryInfoByCode(code string) (*proto.CountryAreacode, error) {
	info := new(proto.CountryAreacode)
	err := DefaultDB().Get(info, "select id, country_en, country_cn, country_tw, country_code, country_encrypt from tb_country_areacode where country_code=? LIMIT 1", code)
	return info, err
}

// GetValidVersion 获取版本信息
func GetValidVersion() ([]proto.VersionDetail, error) {
	var list []proto.VersionDetail
	err := DefaultDB().Select(&list, "SELECT v.platform_id, v.os_type, v.lang_type, v.content, v.channel_id, tv.max_force AS last_force, v.version, v.version_string, v.force_upgrade, v.url, v.create_time FROM tb_version_detail AS v RIGHT JOIN (SELECT platform_id, os_type, lang_type, channel_id, MAX( version ) AS max_ver, MAX(IF( force_upgrade, version, 0 )) AS max_force FROM tb_version_detail WHERE valid = 1 AND create_time <= NOW() GROUP BY platform_id,os_type,lang_type,channel_id ) AS tv ON v.platform_id = tv.platform_id AND v.os_type = tv.os_type AND v.lang_type = tv.lang_type AND v.channel_id=tv.channel_id AND v.version = tv.max_ver;")
	return list, err
}
