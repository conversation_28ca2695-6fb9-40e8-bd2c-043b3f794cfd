package database

import (
	"context"
	"database/sql"
	"time"

	"bc/libs/define"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
)

const (
	_getGiftCashRecordByActivityID    = "SELECT id, user_id, gift_id, amount, state, create_time, expire_time, receive_time, active_time, recovery_time, update_time, receive_account, imei, ip, after_amount FROM tb_gift_record WHERE user_id=? AND gift_id=? LIMIT 1"
	_getGiftCashRecordByID            = "SELECT id, user_id, gift_id, amount, state, create_time, expire_time, receive_time, active_time, recovery_time, update_time, receive_account, imei, ip, after_amount FROM tb_gift_record WHERE id=? AND user_id=? LIMIT 1"
	_getGiftCashRecordByState         = "SELECT id, user_id, gift_id, amount, state, create_time, expire_time, receive_time, active_time, recovery_time, update_time, receive_account, imei, ip, after_amount FROM tb_gift_record WHERE user_id=? AND state=?"
	_getGiftCashRecordByDone          = "SELECT t.gift_id, t.gift_name, t.amount, t.create_time FROM ((SELECT gift_id, '' AS 'gift_name', amount, receive_time AS 'create_time' FROM tb_gift_record WHERE user_id=? AND state=?) UNION ALL (SELECT gift_id, gift_name, amount, create_time FROM tb_gift_manage_send WHERE user_id=? AND stype=?)) AS t ORDER BY t.create_time DESC"
	_getGiftCashRecordByExceed        = "SELECT gift_id, amount, expire_time AS 'create_time' FROM tb_gift_record WHERE user_id=? AND state=?"
	_insertGiftCashRecord             = "INSERT INTO tb_gift_record (id, user_id, gift_id, amount, state, create_time, expire_time, receive_time, active_time, recovery_time, update_time, receive_account, imei, ip, after_amount) VALUE (:id, :user_id, :gift_id, :amount, :state, :create_time, :expire_time, :receive_time, :active_time, :recovery_time, :update_time, :receive_account, :imei, :ip, :after_amount)"
	_getGiftCashRecordWithLockByID    = "SELECT id, user_id, gift_id, amount, state, create_time, expire_time, receive_time, active_time, recovery_time, update_time, receive_account, imei, ip, after_amount FROM tb_gift_record WHERE id=? FOR UPDATE"
	_updateGiftCashRecordStateByID    = "UPDATE tb_gift_record SET state=?, expire_time=?, receive_time=?, active_time=?, recovery_time=?, update_time=?, imei=?, ip=?, after_amount=? WHERE id=?"
	_updateUsdtAssetGiftCash          = "UPDATE tb_user_account SET total_gift_receive=?, gift_balance=?, gift_available=? WHERE account_id=?"
	_checkHasPositionForGiftCash      = "SELECT 1 FROM tb_position WHERE user_id=? LIMIT 1"
	_getLastTradeTimeForGiftCash      = "SELECT MAX(trade_time) FROM tb_trade WHERE user_id=?"
	_getWaitRecoveryGiftCashRecordIDs = "SELECT id FROM tb_gift_record WHERE user_id=? AND state=? AND recovery_time IS NULL"
	_getGiftCashRecordCountOfDevice   = "SELECT COUNT(id) FROM tb_gift_record WHERE imei=? AND state=?"
	_insertGiftCashManageSendRecord   = "INSERT INTO tb_gift_manage_send (user_id, amount, gift_id, gift_name, stype, cause, source_id, after_amount, manage, create_time) VALUE (:user_id, :amount, :gift_id, :gift_name, :stype, :cause, :source_id, :after_amount, :manage, :create_time)"
)

func GetGiftCashRecordByActivityID(ctx context.Context, userID int64, activityID int) (*proto.GiftRecord, error) {
	var record proto.GiftRecord
	err := DefaultDB().GetContext(ctx, &record, _getGiftCashRecordByActivityID, userID, activityID)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &record, err
}

func GetGiftCashRecordByID(ctx context.Context, userID int64, id string) (*proto.GiftRecord, error) {
	var record proto.GiftRecord
	err := DefaultDB().GetContext(ctx, &record, _getGiftCashRecordByID, id, userID)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &record, err
}

func GetGiftCashRecordByState(ctx context.Context, userID int64, state define.GiftJoinState) ([]proto.GiftRecord, error) {
	var list []proto.GiftRecord
	err := DefaultDB().SelectContext(ctx, &list, _getGiftCashRecordByState, userID, state)
	return list, err
}

// GetGiftCashRecordByDone 仅用于获取记录返回给客户端
func GetGiftCashRecordByDone(ctx context.Context, userID int64) ([]proto.GiftRecordAbstract, error) {
	var list []proto.GiftRecordAbstract
	err := DefaultDB().SelectContext(ctx, &list, _getGiftCashRecordByDone, userID, define.GiftJoinStateDone, userID, define.GiftManageSendTypeGrant)
	return list, err
}

// GetGiftCashRecordByExceed 仅用于获取记录返回给客户端
func GetGiftCashRecordByExceed(ctx context.Context, userID int64) ([]proto.GiftRecordAbstract, error) {
	var list []proto.GiftRecordAbstract
	err := DefaultDB().SelectContext(ctx, &list, _getGiftCashRecordByExceed, userID, define.GiftJoinStateExceed)
	return list, err
}

func InsertGiftCashRecord(ctx context.Context, record proto.GiftRecord) error {
	_, err := DefaultDB().NamedExecContext(ctx, _insertGiftCashRecord, record)
	return err
}

func GetGiftCashRecordWithLockByID(ctx context.Context, tx *sqlx.Tx, id string) (*proto.GiftRecord, error) {
	var record proto.GiftRecord
	err := tx.GetContext(ctx, &record, _getGiftCashRecordWithLockByID, id)
	return &record, err
}

func UpdateGiftCashRecordStateByID(ctx context.Context, tx *sqlx.Tx, state define.GiftJoinState, record *proto.GiftRecord) error {
	_, err := tx.ExecContext(ctx, _updateGiftCashRecordStateByID, state, record.ExpireTime, record.ReceiveTime, record.ActiveTime, record.RecoveryTime, record.UpdateTime, record.Imei, record.IP, record.AfterAmount, record.Id)
	return err
}

func UpdateUsdtAssetGiftCash(ctx context.Context, tx *sqlx.Tx, account *proto.Account) error {
	_, err := tx.ExecContext(ctx, _updateUsdtAssetGiftCash, account.TotalGiftReceive, account.GiftBalance, account.GiftAvailable, account.AccountId)
	return err
}

func CheckHasPositionForGiftCash(ctx context.Context, tx *sqlx.Tx, userID int64) (bool, error) {
	var flag uint8
	err := tx.GetContext(ctx, &flag, _checkHasPositionForGiftCash, userID)
	if err == sql.ErrNoRows {
		err = nil
	}
	return flag != 0, err
}

func GetLastTradeTimeForGiftCash(ctx context.Context, tx *sqlx.Tx, userID int64) (time.Time, error) {
	var tradeTime time.Time
	err := tx.GetContext(ctx, &tradeTime, _getLastTradeTimeForGiftCash, userID)
	return tradeTime, err
}

func GetWaitRecoveryGiftCashRecordIDs(ctx context.Context, tx *sqlx.Tx, userID int64) ([]string, error) {
	var err error
	var ids []string
	if tx != nil {
		err = tx.SelectContext(ctx, &ids, _getWaitRecoveryGiftCashRecordIDs, userID, define.GiftJoinStateDone)
	} else {
		err = DefaultDB().SelectContext(ctx, &ids, _getWaitRecoveryGiftCashRecordIDs, userID, define.GiftJoinStateDone)
	}
	return ids, err
}

func GetGiftCashRecordCountOfDevice(ctx context.Context, tx *sqlx.Tx, device string) (int64, error) {
	var err error
	var count int64
	if tx != nil {
		err = tx.GetContext(ctx, &count, _getGiftCashRecordCountOfDevice, device, define.GiftJoinStateDone)
	} else {
		err = DefaultDB().GetContext(ctx, &count, _getGiftCashRecordCountOfDevice, device, define.GiftJoinStateDone)
	}
	return count, err
}

func InsertGiftCashManageSendRecord(ctx context.Context, tx *sqlx.Tx, record proto.GiftManageSend) error {
	var err error
	if tx != nil {
		_, err = tx.NamedExecContext(ctx, _insertGiftCashManageSendRecord, record)
	} else {
		_, err = DefaultDB().NamedExecContext(ctx, _insertGiftCashManageSendRecord, record)
	}
	return err
}
