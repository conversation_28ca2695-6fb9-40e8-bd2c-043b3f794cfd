/*
@Time : 3/6/20 2:38 下午
<AUTHOR> mocha
@File : index
*/
package database

import (
	"time"

	"bc/libs/log"
	"bc/libs/proto"
)

func InsertIndexDetails(list []proto.IndexDetail) (err error) {
	if len(list) == 0 {
		return nil
	}
	strf := "insert into tb_contract_index_detail(detail_id, index_id, level, buy_price, buy_volume, sell_price, sell_volume) values (?,?,?,?,?,?,?)"
	smt, err := DefaultDB().Preparex(strf)
	if err != nil {
		log.Errorf("InsertIndexDetails get prepare db error,%v", err)
		return
	}
	for _, v := range list {
		_, err = smt.Exec(v.DetailId, v.IndexId, v.Level, v.BuyPrice, v.BuyVolume, v.SellPrice, v.SellVolume)
		if err != nil {
			log.Errorf("InsertIndexDetails,err;%v", err)
		}
	}
	return
}

func StoreIndex(h *proto.IndexHistory) {
	//插入指数价格计算历史
	if h == nil {
		return
	}
	err := InsertIndexHistory(h)
	if err != nil {
		log.Errorf("插入指数历史失败，%+v,err:%v", *h, err)
	}
	////插入指数价格计算详情
	//if len(list) == 0 {
	//	return
	//}
	//err = InsertIndexDetails(list)
	//if err != nil {
	//	log.Errorf("插入指数历史详情失败，%+v,err:%v", *h, list)
	//
	//}
}

func InsertIndexHistory(index *proto.IndexHistory) (err error) {
	//log.Debugf("插入指数价格记录,%+v", index)
	str := "insert into tb_contract_index(index_id, contract_code, contract_index,trade_price, buy_price, sell_price, created_time) values(?,?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, index.IndexId, index.ContractCode, index.ContractIndex, index.TradePrice, index.BuyPrice, index.SellPrice, time.Now())
	if err != nil {
		log.Errorf("InsertIndexHistory fail,%v", err)
	}
	return
}

func InsertFundingRateHistory(h *proto.FundingRateHistory) (err error) {
	log.Debugf("插入标记价格记录,%+v", *h)
	str := "insert into tb_funding_rate_history(contract_code, funding_rate, estimated_rate, created_by) values (?,?,?,?)"
	_, err = DefaultDB().Exec(str, h.ContractCode, h.FundingRate, h.EstimatedRate, h.CreatedBy)
	return
}

func InsertIndexTick(v proto.ContractTick) (err error) {
	str := "insert into tb_contract_tick(id, contract_code, trade_price, buy_price, sell_price, index_price, trade_amount, create_time) values (?,?,?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, v.ID, v.ContractCode, v.TradePrice, v.BuyPrice, v.SellPrice, v.IndexPrice, v.TradeAmount, v.CreateTime)
	if err != nil {
		log.Errorf("InsertIndexTick,err;%v", err)
	}
	return
}
