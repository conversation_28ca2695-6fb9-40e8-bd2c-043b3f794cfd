package database

import (
	"bc/libs/proto"
)

func InsertContractIndicator(t *proto.ContractIndicator) (err error) {
	str := "insert into tb_contract_indicator(contract_code, side, `timestamp`, create_time, price) values (?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, t.ContractCode, t.Side, t.Timestamp, t.CreateTime, t.Price)
	return
}

func InsertContractSupport(t *proto.ContractSupport) (err error) {
	str := "insert into tb_contract_support(contract_code, open_price, close_price, high_price, low_price, volume, `timestamp`, create_time,drag,support) values (?,?,?,?,?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, t.ContractCode, t.OpenPrice, t.ClosePrice, t.HighPrice, t.<PERSON>, t.Volume, t.Timestamp, t.<PERSON>reate<PERSON>, t.<PERSON>ag, t.<PERSON>)
	return
}
