package database

//import "bc/pkg/core/model"

//func GetThirdLegalMerchantsByname(busname string) (*model.ThirdMerchants, error) {
//	var merchants = new(model.ThirdMerchants)
//	err := DefaultDB().Get(merchants, "SELECT bus_id, bus_name, bus_logo, pay_mode, support_legal, currency, market, agreement, agreement_url FROM tb_third_merchants WHERE bus_name=?", busname)
//	return merchants, err
//}
//
//func InserThirdOrder(req *model.ThirdLegalOrder) error {
//	str := "INSERT INTO  tb_third_order set `order_id`=?,user_id=?,bus_name=?,coin_name=?,legal_name=?,coin_amount=?,rates=?,order_type=?,status=?,legal_amount=?,fee=?,address=? ON DUPLICATE KEY UPDATE `status`=?,address=? "
//	_, err := DefaultDB().Exec(str, req.OrderID, req.UserId, req.BusName, req.CoinName, req.LegalName, req.CoinAmount, req.Rates, req.OrderType, req.Status, req.LegalAmount, req.Fee, req.Address, req.Status, req.Address)
//	return err
//}
