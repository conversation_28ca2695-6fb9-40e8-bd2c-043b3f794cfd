package database

import (
	"database/sql"
	"time"

	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

const _queryUser = "SELECT u.user_id, u.user_name, u.nickname, u.introduce, u.nickname_en, u.introduce_en, u.avatar, u.login_passwd, u.fund_passwd, u.invite_code, u.invite_parent, u.last_login_time, u.last_login_ip, u.real_name, u.card_no, u.verify, (u.static_identity != 0) AS 'static_identity', u.last_verify_id, u.rest_fund_time, u.rest_login_time,( u.enable_login != 0) AS 'enable_login', (u.enable_withdraw != 0) AS 'enable_withdraw', (u.enable_trade != 0) AS 'enable_trade', u.forbid_open_close, u.created_time, IFNULL(u.phone,'') AS 'phone', IFNULL(u.email, '') AS 'email', IFNULL(u.spare_email, '') AS 'spare_email', u.totp_secret, (u.login_verify_phone != 0) AS 'login_verify_phone', (u.login_verify_email != 0) AS 'login_verify_email', (u.login_verify_totp != 0) AS 'login_verify_totp', u.trade_verify_fund, u.area_code, u.country_code, (u.follow_approved != 0) AS 'follow_approved', IFNULL(du.is_status, ?) AS 'dealer_state', IFNULL(du.rebate, 0) AS 'rebate', u.platform_id,u.label_id,u.content, (u.is_agent != 0) AS 'is_agent', u.withdraw_verify, (u.trade_approved != 0) AS 'trade_approved', (u.enable_simulator != 0) AS 'enable_simulator', (u.trade_confirm != 0) AS 'trade_confirm', u.change_style, u.profit_style, (u.is_open_api != 0) AS 'is_open_api', (u.show_agent_ratio != 0) AS 'show_agent_ratio', (u.is_show_dealer != 0) AS 'is_show_dealer', (u.is_show_dealer_setting != 0) AS 'is_show_dealer_setting', (u.fast_closeout_confirm != 0) AS 'fast_closeout_confirm', u.is_split_position, u.withdraw_limit, u.limit_state FROM tb_user AS u LEFT JOIN tb_dealer_user AS du ON du.user_id=u.user_id WHERE 1=1"

// 获取用户信息
func GetUserInfoByID(userID int64, tx *sqlx.Tx) (*proto.User, error) {
	var err error
	user := new(proto.User)

	str := _queryUser + " AND u.user_id=?"
	if tx != nil {
		err = tx.Get(user, str, define.DealerStateUnknow, userID)
	} else {
		err = DefaultDB().Get(user, str, define.DealerStateUnknow, userID)
	}
	if user.Verify == define.VerifyManuallyAdd {
		if user.CountryCode == define.ChineseCode {
			user.Verify = define.VerifyFaceVerified
		} else {
			user.Verify = define.VerifyIdNumberVerified
		}
	} else if user.Verify == define.VerifyReset {
		user.Verify = define.VerifyUnverified
	}

	return user, err
}

// 获取用户信息
func GetUserInfoByPhone(phone string, tx *sqlx.Tx) (*proto.User, error) {
	var err error
	user := new(proto.User)

	str := _queryUser + " AND u.phone=?"
	if tx != nil {
		err = tx.Get(user, str, define.DealerStateUnknow, phone)
	} else {
		err = DefaultDB().Get(user, str, define.DealerStateUnknow, phone)
	}
	if user.Verify == define.VerifyManuallyAdd {
		if user.CountryCode == define.ChineseCode {
			user.Verify = define.VerifyFaceVerified
		} else {
			user.Verify = define.VerifyIdNumberVerified
		}
	} else if user.Verify == define.VerifyReset {
		user.Verify = define.VerifyUnverified
	}

	return user, err
}

// 获取用户信息
func GetUserInfoByEmail(email string, tx *sqlx.Tx) (*proto.User, error) {
	var err error
	user := new(proto.User)

	str := _queryUser + " AND u.email=?"
	if tx != nil {
		err = tx.Get(user, str, define.DealerStateUnknow, email)
	} else {
		err = DefaultDB().Get(user, str, define.DealerStateUnknow, email)
	}
	if user.Verify == define.VerifyManuallyAdd {
		if user.CountryCode == define.ChineseCode {
			user.Verify = define.VerifyFaceVerified
		} else {
			user.Verify = define.VerifyIdNumberVerified
		}
	} else if user.Verify == define.VerifyReset {
		user.Verify = define.VerifyUnverified
	}

	return user, err
}

// 添加日志
func RecordOperateLog(uid int64, account string, catalog define.UserOPType, now time.Time, req *define.ReqArg) {
	_, err := DefaultDB().Exec("INSERT tb_user_log SET user_id=?, op_type=?, user_name=?, os_type=?, device=?, device_id=?, lang_type=?, version=?, ip_address=?, created_time=?", uid, catalog, account, req.ReqOs, req.Device, req.DeviceID, req.ReqLang, req.Version, req.ReqIP, now)
	if err != nil {
		log.Error("RecordOperateLog db error",
			zap.Int64("reqID", req.ReqID),
			zap.Int64("userID", uid),
			zap.String("account", account),
			zap.String("ip", req.ReqIP),
			zap.Uint8("os", uint8(req.ReqOs)),
			zap.Uint("catalog", uint(catalog)),
			zap.String("device", req.Device),
			zap.String("deviceID", req.DeviceID),
			zap.Error(err))
		return
	}

	if catalog == define.RecordTypeLogin {
		_, err = DefaultDB().Exec("UPDATE tb_user SET last_login_ip=?, last_login_time=? WHERE user_id=?", req.ReqIP, now, uid)
		if err != nil {
			log.Error("RecordOperateLog update tb_user db error",
				zap.Int64("reqID", req.ReqID),
				zap.Int64("userID", uid),
				zap.String("account", account),
				zap.String("ip", req.ReqIP),
				zap.Uint8("os", uint8(req.ReqOs)),
				zap.Uint("catalog", uint(catalog)),
				zap.String("device", req.Device),
				zap.String("deviceID", req.DeviceID),
				zap.Error(err))
		}
	}
}

func GetUserPayment(tx *sqlx.Tx, id int, userId int64) (p *proto.UserPayment, err error) {
	str := "SELECT id, user_id, `type`, bank_name,bank_branch_name, bank_numb, account_holder, ocr_address, created_time FROM tb_user_bank WHERE id=? and user_id = ?"
	t := new(proto.UserPayment)
	if tx != nil {
		err = tx.Get(t, str, id, userId)
	} else {
		err = DefaultDB().Get(t, str, id, userId)
	}
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
		}
		return
	}
	p = t
	return
}

func ListUserPayments(tx *sqlx.Tx, userId int64) (list []proto.UserPayment, err error) {
	str := "SELECT id, user_id, `type`, bank_name, bank_branch_name, bank_numb, account_holder, ocr_address, created_time FROM tb_user_bank WHERE user_id = ?"
	if tx != nil {
		err = tx.Select(&list, str, userId)
	} else {
		err = DefaultDB().Select(&list, str, userId)
	}
	return
}

func AddUserPayment(tx *sqlx.Tx, p *proto.UserPayment) (id int64, err error) {
	str := "INSERT INTO tb_user_bank (user_id, `type`, bank_name,bank_branch_name, bank_numb, account_holder, ocr_address, created_time) VALUES (?,?,?,?,?,?,?,?)"
	var result sql.Result
	if tx != nil {
		result, err = tx.Exec(str, p.UserId, p.Type, p.BankName, p.BankBranchName, p.BankNumb, p.AccountHolder, p.OcrAddress, p.CreatedTime)
	} else {
		result, err = DefaultDB().Exec(str, p.UserId, p.Type, p.BankName, p.BankBranchName, p.BankNumb, p.AccountHolder, p.OcrAddress, p.CreatedTime)
	}
	if err != nil {
		log.Errorf("AddUserPayment fail,%v", err)
		return
	}
	id, err = result.LastInsertId()
	return
}

func UpdateUserPaymentById(tx *sqlx.Tx, p *proto.UserPayment) (err error) {
	str := "UPDATE tb_user_bank SET  bank_name = ?,bank_branch_name=?, bank_numb = ?, account_holder = ?, ocr_address = ? WHERE id = ? and user_id=?"
	if tx != nil {
		_, err = tx.Exec(str, p.BankName, p.BankBranchName, p.BankNumb, p.AccountHolder, p.OcrAddress, p.ID, p.UserId)
	} else {
		_, err = DefaultDB().Exec(str, p.BankName, p.BankBranchName, p.BankNumb, p.AccountHolder, p.OcrAddress, p.ID, p.UserId)
	}
	if err != nil {
		return
	}
	return
}

func RemoveUserPaymentById(tx *sqlx.Tx, p *proto.UserPayment) (err error) {
	str := "DELETE FROM tb_user_bank WHERE id = ? and user_id=?"
	if tx != nil {
		_, err = tx.Exec(str, p.ID, p.UserId)
	} else {
		_, err = DefaultDB().Exec(str, p.ID, p.UserId)
	}
	if err != nil {
		return
	}
	return
}

func GetUserInfoWithLock(tx *sqlx.Tx, userID int64) (*proto.UserDetail, error) {
	var u proto.UserDetail
	err := tx.Get(&u, "SELECT id, user_id, user_name, nickname, introduce, nickname_en, introduce_en, avatar, login_passwd, fund_passwd, totp_secret, login_verify_phone, login_verify_email, login_verify_totp, trade_verify_fund, invite_code, invite_parent, last_login_time, last_login_ip, real_name, card_no, verify, static_identity, last_verify_id, rest_fund_time, rest_login_time, enable_login, enable_withdraw, enable_trade, forbid_open_close, enable_simulator, withdraw_verify, trade_approved, created_time, phone, email, spare_email, is_agent, agent_time, area_code, country_code, follow_approved, trade_confirm, platform_id, label_id, content, petname, change_style, profit_style, is_open_api, is_child_api, show_agent_ratio, is_show_dealer, is_show_dealer_setting, is_legal_buy, is_legal_sell, user_type, fast_closeout_confirm, is_back_agent, channel_id, is_split_position FROM tb_user WHERE user_id=? FOR UPDATE", userID)
	return &u, err
}
