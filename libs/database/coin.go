/*
@Time : 3/5/20 6:51 下午
<AUTHOR> mocha
@File : coin
*/
package database

import (
	"bc/libs/define"
	"database/sql"
	"fmt"
	"github.com/shopspring/decimal"

	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

const coinBase = "SELECT currency_id, currency_name, wallet_name, full_name, icon, `precision`, withdraw_precision, recharge_enable, withdraw_enable, forward_enable, reverse_enable, third_legal_enable, third_legal_default, need_tag, pay_confirms, miner_fee, min_withdraw, max_withdraw, basic_withdraw, daily_withdraw, withdraw_review, tag, min_transfer, max_transfer, status, can_transfer, multiple, withdraw_time, is_exchange, min_exchange, max_exchange, min_exchange_money, daily_exchange_money, exchange_coin_point, exchange_usdt_point, exchange_fee_rate, exchange_price_range, exchange_price_step, exchange_reverse_price_step, exchange_precision, is_hedge,hedge_warn_threshold, sort_weight, market_sort_weight FROM tb_currency "

func GetSupportCoins(tx *sqlx.Tx) (reply []proto.Coin, err error) {
	if tx != nil {
		err = tx.Select(&reply, coinBase)
	} else {
		err = DefaultDB().Select(&reply, coinBase)
	}
	if err != nil {
		log.Error("GetSupportCoins db error", zap.Error(err))
	}
	return
}

func GetCoinById(tx *sqlx.Tx, id int) (reply *proto.Coin, err error) {
	reply = new(proto.Coin)
	str := utils.StrBuilder(coinBase, " where currency_id=?")
	if tx != nil {
		err = tx.Get(reply, str, id)
	} else {
		err = DefaultDB().Get(reply, str, id)
	}
	if err != nil {
		log.Error("GetCoinById db error", zap.Int("coinID", id), zap.Error(err))
	}
	return
}

func GetCoinByName(tx *sqlx.Tx, name string) (reply *proto.Coin, err error) {
	reply = new(proto.Coin)
	str := utils.StrBuilder(coinBase, " where currency_name=?")
	if tx != nil {
		err = tx.Get(reply, str, name)
	} else {
		err = DefaultDB().Get(reply, str, name)
	}
	if err != nil || err != sql.ErrNoRows {
		log.Error("GetCoinByName db error", zap.Error(err))
	}
	return
}

const _baseMarginCoin = "select currency_id, currency_name, `precision`, min_transfer, max_transfer, status, can_transfer from tb_margin_currency "

func GetMarginCoins(tx *sqlx.Tx) (list []proto.MarginCurrency, err error) {
	if tx != nil {
		err = tx.Select(&list, _baseMarginCoin)
		return
	}
	err = DefaultDB().Select(&list, _baseMarginCoin)
	return
}

func GetMarginCoinById(tx *sqlx.Tx, id int) (reply *proto.MarginCurrency, err error) {
	reply = new(proto.MarginCurrency)
	str := utils.StrBuilder(_baseMarginCoin, " where currency_id=?")
	if tx != nil {
		err = tx.Get(reply, str, id)
	} else {
		err = DefaultDB().Get(reply, str, id)
	}
	if err != nil || err != sql.ErrNoRows {
		log.Error("GetCoinById db error", zap.Error(err))
	}
	return
}

func GetMarginCoinByName(tx *sqlx.Tx, name string) (reply *proto.MarginCurrency, err error) {
	reply = new(proto.MarginCurrency)
	str := utils.StrBuilder(_baseMarginCoin, " where currency_name=?")
	if tx != nil {
		err = tx.Get(reply, str, name)
	} else {
		err = DefaultDB().Get(reply, str, name)
	}
	if err != nil || err != sql.ErrNoRows {
		log.Error("GetCoinByName db error", zap.Error(err))
	}
	return
}

const _getWalletCoinConf = "SELECT wallet_name, coin_id, coin_name, protocol, can_deposit, can_withdraw, min_withdraw, max_withdraw, withdraw_fee, withdraw_review, withdraw_time, withdraw_precision, confirm_num, weight FROM tb_coin_wallet_config WHERE coin_id IN (%s)"

// 获取全部大钱包币种名称对应关系
func GetAllWalletCoinDict(tx *sqlx.Tx) (map[string]map[string]proto.WalletCoinConf, error) {
	// 首先获取全部上架币种列表
	list, err := GetSupportCoins(tx)
	if err != nil {
		return nil, err
	}

	var multipleList []interface{}
	basicM := make(map[string]proto.Coin, len(list))
	dict := make(map[string]map[string]proto.WalletCoinConf, len(list))
	for i := range list {
		basicM[list[i].CurrencyName] = list[i]
		// 多协议的币种放入列表,从另一个表中取数据
		if list[i].Multiple {
			multipleList = append(multipleList, list[i].CurrencyId)
			continue
		}

		// 不是多协议的币种直接放进字典
		if _, ok := dict[list[i].CurrencyName]; !ok {
			dict[list[i].CurrencyName] = make(map[string]proto.WalletCoinConf)
		}
		dict[list[i].CurrencyName][define.WalletCoinConfDefaultProtocol] = proto.WalletCoinConf{
			WalletName:        list[i].WalletName,
			CoinID:            list[i].CurrencyId,
			CoinName:          list[i].CurrencyName,
			CanDeposit:        list[i].RechargeEnable,
			CanWithdraw:       list[i].WithdrawEnable,
			NeedTag:           list[i].NeedTag,
			BasicWithdraw:     list[i].BasicWithdraw,
			DailyWithdraw:     list[i].DailyWithdraw,
			MinWithdraw:       decimal.NewFromFloat(list[i].MinWithdraw),
			MaxWithdraw:       decimal.NewFromFloat(list[i].MaxWithdraw),
			WithdrawFee:       decimal.NewFromFloat(list[i].MinerFee),
			WithdrawReview:    decimal.NewFromFloat(list[i].WithdrawReview),
			WithdrawTime:      list[i].WithdrawTime,
			WithdrawPrecision: list[i].WithdrawPrecision,
			ConfirmNum:        list[i].PayConfirms,
			Protocol:          list[i].Tag,
		}
	}

	if len(multipleList) > 0 {
		// 从tb_coin_wallet_config获取多协议币种的信息
		opStr := "?"
		for i := 1; i < len(multipleList); i++ {
			opStr += ",?"
		}
		queryStr := fmt.Sprintf(_getWalletCoinConf, opStr)

		var rows *sqlx.Rows
		if tx == nil {
			rows, err = DefaultDB().Queryx(queryStr, multipleList...)
		} else {
			rows, err = tx.Queryx(queryStr, multipleList...)
		}
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		for rows.Next() {
			var coin proto.WalletCoinConf
			err = rows.StructScan(&coin)
			if err != nil {
				return nil, err
			}
			if _, ok := dict[coin.CoinName]; !ok {
				dict[coin.CoinName] = make(map[string]proto.WalletCoinConf)
			}
			coin.NeedTag = basicM[coin.CoinName].NeedTag
			coin.BasicWithdraw = basicM[coin.CoinName].BasicWithdraw
			coin.DailyWithdraw = basicM[coin.CoinName].DailyWithdraw
			if coin.Protocol == "" {
				dict[coin.CoinName][define.WalletCoinConfDefaultProtocol] = coin
			} else {
				dict[coin.CoinName][coin.Protocol] = coin
			}
		}
	}
	return dict, nil
}

// 获取全部大钱包币种配置
func GetWalletConfDictFromDB() (map[string]proto.WalletCoinConf, error) {
	// 首先获取全部上架交易对列表
	list, err := GetSupportCoins(nil)
	if err != nil {
		return nil, err
	}

	var multipleList []interface{}
	dict := make(map[string]proto.WalletCoinConf)
	for i := range list {
		if list[i].Multiple {
			multipleList = append(multipleList, list[i].CurrencyId)
			continue
		}
		dict[list[i].WalletName] = proto.WalletCoinConf{
			WalletName:        list[i].WalletName,
			CoinID:            list[i].CurrencyId,
			CoinName:          list[i].CurrencyName,
			CanDeposit:        list[i].RechargeEnable,
			CanWithdraw:       list[i].WithdrawEnable,
			MinWithdraw:       decimal.NewFromFloat(list[i].MinWithdraw),
			MaxWithdraw:       decimal.NewFromFloat(list[i].MaxWithdraw),
			WithdrawFee:       decimal.NewFromFloat(list[i].MinerFee),
			WithdrawReview:    decimal.NewFromFloat(list[i].WithdrawReview),
			WithdrawTime:      list[i].WithdrawTime,
			WithdrawPrecision: list[i].WithdrawPrecision,
			ConfirmNum:        list[i].PayConfirms,
		}
	}

	if len(multipleList) > 0 {
		// 从tb_coin_wallet_config获取多协议币种的信息
		opStr := "?"
		for i := 1; i < len(multipleList); i++ {
			opStr += ",?"
		}
		queryStr := fmt.Sprintf(_getWalletCoinConf, opStr)
		rows, err := DefaultDB().Queryx(queryStr, multipleList...)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		for rows.Next() {
			var coin proto.WalletCoinConf
			err = rows.StructScan(&coin)
			if err != nil {
				return nil, err
			}

			dict[coin.WalletName] = coin
		}
	}
	return dict, nil
}

const _getCoinProtocols = "SELECT wallet_name, coin_id, coin_name, protocol, can_deposit, can_withdraw, min_withdraw, max_withdraw, withdraw_fee, withdraw_review, withdraw_time, withdraw_precision, confirm_num, weight FROM tb_coin_wallet_config WHERE coin_id = ? ORDER BY weight DESC"

func GetCoinProtocols(coinID int) ([]proto.WalletCoinConf, error) {
	protocols := make([]proto.WalletCoinConf, 0)
	err := DefaultDB().Select(&protocols, _getCoinProtocols, coinID)
	return protocols, err
}
