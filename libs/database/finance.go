/*
@Time : 2019-08-09 13:47
<AUTHOR> mocha
@File : dbcoin
*/
package database

import (
	"bc/libs/define"
	"bc/libs/log"
	"go.uber.org/zap"
	"math"

	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
)

//获取资产账户币种钱包
func GetFsAssetWallet(currencyId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=1 and platform_id=?"
	err = DefaultDB().Get(&m, str, currencyId, platformId)
	return
}

//获取法币平台账户钱包
func GetC2cWallet(currencyId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=3 and platform_id=?"
	err = DefaultDB().Get(&m, str, currencyId, platformId)
	return
}

//获取指定币种地址的账户信息
func GetFsWalletByCoinName(address, name string) (*proto.FsWallet, error) {
	m := new(proto.FsWallet)
	str := "select fs.coin_id, fs.coin_name,fs.`type`, fs.amount, fs.amount_lock, ifnull(fs.address, '') address,platform_id from tb_finance_asset fs left join tb_coin_wallet_config cs on cs.coin_name=fs.coin_name where cs.wallet_name=? and fs.address=?"
	err := DefaultDB().Get(m, str, name, address)
	return m, err
}

func GetFsWalletWithLock(tx *sqlx.Tx, coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=1 and platform_id=? for update "
	err = tx.Get(&m, str, coinId, platformId)
	return
}

func GetFsWalletWithLockByWalletType(tx *sqlx.Tx, coinId int, walletType int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, coinId, walletType, platformId)
	return
}

func GetFsC2cAccountWithLock(tx *sqlx.Tx, coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=3 and platform_id=? for update "
	err = tx.Get(&m, str, coinId, platformId)
	return
}

func InsertFsWallet(m proto.FsWallet) (err error) {
	str := "insert ignore into tb_finance_asset(coin_id, coin_name,`type`, amount, amount_lock, address,platform_id) values (?,?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, m.CoinId, m.CoinName, m.Type, m.Amount, m.AmountLock, m.Address, m.PlatformID)
	return
}

func InsertFsWalletWithTx(tx *sqlx.Tx, m proto.FsWallet) (err error) {
	str := "insert ignore into tb_finance_asset(coin_id, coin_name,`type`, amount, amount_lock, address,platform_id) values (?,?,?,?,?,?,?)"
	_, err = tx.Exec(str, m.CoinId, m.CoinName, m.Type, m.Amount, m.AmountLock, m.Address, m.PlatformID)
	return
}

func UpdateFsAssetWalletAddress(platformID, coinId, aType int, address string) (err error) {
	str := "update tb_finance_asset set address=? where coin_id=? and `type`=? and platform_id=?"
	_, err = DefaultDB().Exec(str, address, coinId, aType, platformID)
	return
}

func IncrFsWallet(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_id=? and `type`=1 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinId, platformId)
	return
}

func IncrFsExchangeWallet(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_id=? and `type`=? and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinId, define.WalletTypeExchange, platformId)
	return
}

func IncrFsC2cWallet(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_id=? and `type`=3 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinId, platformId)
	return
}

func GetFsCapital(coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name, `type`, amount, amount_lock, address, mix_charge, mix_with from tb_finance_asset where coin_id=? and `type`=2 and platform_id=?"
	err = DefaultDB().Get(&m, str, coinId, platformId)
	return
}

func GetFsCapitalWithLock(tx *sqlx.Tx, coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock from tb_finance_asset where coin_id=? and `type`=2 and platform_id=? for update "
	err = tx.Get(&m, str, coinId, platformId)
	return
}

func IncrFsCapital(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	var capital float64
	if amount < 0 {
		capital = math.Abs(amount)
	}
	str := "update tb_finance_asset set amount=amount+?,asset_award=asset_award+? where coin_id=? and `type`=2 and platform_id=?"
	_, err = tx.Exec(str, amount, capital, coinId, platformId)
	return
}

func IncrFsCapitalBlowingFee(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	capital := math.Abs(amount)
	str := "update tb_finance_asset set blowing_fee=blowing_fee+? where coin_id=? and `type`=2 and platform_id=?"
	_, err = tx.Exec(str, capital, coinId, platformId)
	if err != nil {
		log.Error("IncrFsCapitalBlowingFee fail", zap.Error(err))
	}
	return
}

func IncrFsCapitalWearAmount(tx *sqlx.Tx, coinId int, amount float64, platformId int) (err error) {
	capital := math.Abs(amount)
	str := "update tb_finance_asset set wear_amount=wear_amount+? where coin_id=? and `type`=2 and platform_id=?"
	_, err = tx.Exec(str, capital, coinId, platformId)
	if err != nil {
		log.Error("IncrFsCapitalWearAmount fail", zap.Error(err))
	}
	return
}

func InsertFsWalletHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (id int64, err error) {
	str := "insert into tb_finance_asset_history(user_id, coin_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,?,now(),?)"
	res, err := tx.Exec(str, r.UserId, r.CoinId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	if err == nil {
		return res.LastInsertId()
	}
	return
}

func UpdateFsWalletHistorySourceID(tx *sqlx.Tx, billID, sourceID int64) (err error) {
	if tx != nil {
		_, err = tx.Exec("UPDATE tb_finance_asset_history SET source_id=? WHERE id=?", sourceID, billID)
	} else {
		_, err = DefaultDB().Exec("UPDATE tb_finance_asset_history SET source_id=? WHERE id=?", sourceID, billID)
	}
	return
}

func InsertFsCapitalHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (err error) {
	str := "insert into tb_finance_capital_history(user_id, coin_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,?,now(),?)"
	_, err = tx.Exec(str, r.UserId, r.CoinId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	return
}

//插入c2c账户资金记录
func InsertC2AccountHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (err error) {
	str := "insert into tb_finance_c2c_history(user_id, coin_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,?,now(),?)"
	_, err = tx.Exec(str, r.UserId, r.CoinId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	return
}

func ExistFsWalletHistory(tx *sqlx.Tx, sourceID int64, opType int) bool {
	str := "select count(1) from tb_finance_asset_history where source_id=? and op_type=?"
	var count int
	_ = tx.Get(&count, str, sourceID, opType)
	return count != 0
}

func ExistFsC2cWalletHistory(tx *sqlx.Tx, sourceID int64, opType int) bool {
	str := "select count(1) from tb_finance_c2c_history where source_id=? and op_type=?"
	var count int
	_ = tx.Get(&count, str, sourceID, opType)
	return count != 0
}

//获取兑币账户钱包
func GetFsExchangeWallet(currencyId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=? and platform_id=?"
	err = DefaultDB().Get(&m, str, currencyId, define.WalletTypeExchange, platformId)
	return
}

func GetFsExchangeAccountWithLock(tx *sqlx.Tx, coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, coinId, define.WalletTypeExchange, platformId)
	return
}

func InsertFsExchangeHistory(tx *sqlx.Tx, r proto.FsWalletHistory) (id int64, err error) {
	str := "insert into tb_finance_exchange_history(user_id, coin_id, coin_name, amount, amount_after, op_type, remark, op_user_name, source_id, create_at,platform_id) VALUES (?,?,?,?,?,?,?,?,?,now(),?)"
	res, err := tx.Exec(str, r.UserId, r.CoinId, r.CoinName, r.Amount, r.AmountAfter, r.OpType, r.Remark, r.OpUserName, r.SourceId, r.PlatformID)
	if err == nil {
		return res.LastInsertId()
	}
	return
}

//获取充提账户钱包
func GetFsChargeWallet(currencyId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name, `type`, amount, amount_lock, mix_charge, mix_with, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=? and platform_id=?"
	err = DefaultDB().Get(&m, str, currencyId, define.WalletTypeCharge, platformId)
	return
}

func GetFsChargeAccountWithLock(tx *sqlx.Tx, coinId int, platformId int) (m proto.FsWallet, err error) {
	str := "select coin_id, coin_name,`type`, amount, amount_lock, mix_charge, mix_with, fee, ifnull(address, '') address,platform_id from tb_finance_asset where coin_id=? and `type`=? and platform_id=? for update "
	err = tx.Get(&m, str, coinId, define.WalletTypeCharge, platformId)
	return
}
