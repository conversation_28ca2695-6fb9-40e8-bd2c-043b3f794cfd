package database

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	//"bc/pkg/core/model"
	"github.com/jmoiron/sqlx"
	//"go.uber.org/zap"
)

const _conditionOrderBase = "SELECT contract_code, plan_order_id, status, side, UNIX_TIMESTAMP(create_time) AS 'create_time', amount, `condition`, trigger_price, IFNULL(UNIX_TIMESTAMP(order_time), 0) AS 'order_time', account_type, lever,mode FROM tb_plan_order WHERE user_id=? AND status IN (?,?,?)"

const _getAllPlanOrder = "(SELECT po.contract_code,po.plan_order_id AS id,po.`status` AS state,po.side,UNIX_TIMESTAMP( po.create_time ) AS 'create_time',po.amount,po.`condition`,po.trigger_price,IFNULL( UNIX_TIMESTAMP( po.order_time ), ? ) AS 'trigger_time',po.account_type,po.lever,? AS trigger_type,po.mode,po.entrust_type,po.entrust_price,po.entrust_strategy FROM tb_plan_order AS po WHERE po.user_id = ? AND po.`status` IN ( ?, ?, ? ) AND (po.order_time BETWEEN ? AND ? OR po.`status` = ?) ORDER BY IFNULL(UNIX_TIMESTAMP(po.order_time),**********) DESC, po.create_time DESC LIMIT ?) UNION ALL (SELECT co.contract_code,co.plan_close_order_id AS id,co.trigger_status AS state,co.side,UNIX_TIMESTAMP( co.create_time ) AS create_time,co.amount,IF( co.trigger_type = ?, co.condition_limit, co.condition_stop ) AS 'condition',IF( co.trigger_type = ?, co.`limit`, co.stop ) AS 'trigger_price',IFNULL( UNIX_TIMESTAMP( co.trigger_time ), ? ) AS trigger_time,co.account_type,co.lever,co.trigger_type,3 as mode,co.entrust_type,IF( co.trigger_type = ?, co.entrust_limit, co.entrust_stop ) AS 'entrust_price',0 as entrust_strategy FROM tb_plan_close_order AS co WHERE co.user_id = ? AND co.trigger_status = ? ORDER BY IFNULL(UNIX_TIMESTAMP(co.trigger_time),**********) DESC, co.create_time DESC LIMIT ?) ORDER BY trigger_time DESC, create_time DESC LIMIT ?"

// 获取用户全部合约条件单
func GetAllContractConditionOrder(userID int64, start, end time.Time) ([]proto.ApiConditionOrder, error) {
	list := make([]proto.ApiConditionOrder, 0)
	err := DefaultDB().Select(&list, _getAllPlanOrder, define.MaxTimestamp, define.OrderTypePlan, userID, define.OrderConditionNotTrigger, define.OrderCondHadTrigger, define.OrderCondFailed, start, end, define.OrderConditionNotTrigger, define.SQLLimitMaxCount, define.OrderTypeLimit, define.OrderTypeLimit, define.MaxTimestamp, define.OrderTypeLimit, userID, define.OrderCondHadTrigger, define.SQLLimitMaxCount, define.ApiHistoryOrderMaxCount)
	return list, err
}

const _getContractPlanOrder = "(SELECT po.contract_code,po.plan_order_id AS id,po.`status` AS state,po.side,UNIX_TIMESTAMP( po.create_time ) AS 'create_time',po.amount,po.`condition`,po.trigger_price,IFNULL( UNIX_TIMESTAMP( po.order_time ), ? ) AS 'trigger_time',po.account_type,po.lever,? AS trigger_type,po.mode,po.entrust_type,po.entrust_price,po.entrust_strategy FROM tb_plan_order AS po WHERE po.user_id = ? AND po.contract_code = ? AND po.`status` IN ( ?, ?, ? ) AND (po.order_time BETWEEN ? AND ? OR po.`status` = ?) ORDER BY IFNULL(UNIX_TIMESTAMP(po.order_time),**********) DESC, po.create_time DESC LIMIT ?) UNION ALL (SELECT co.contract_code,co.plan_close_order_id AS id,co.trigger_status AS state,co.side,UNIX_TIMESTAMP( co.create_time ) AS create_time,co.amount,IF( co.trigger_type = ?, co.condition_limit, co.condition_stop ) AS 'condition',IF( co.trigger_type = ?, co.`limit`, co.stop ) AS 'trigger_price',IFNULL( UNIX_TIMESTAMP( co.trigger_time ), ? ) AS trigger_time,co.account_type,co.lever,co.trigger_type,3 as mode,co.entrust_type,IF( co.trigger_type = ?, co.entrust_limit, co.entrust_stop ) AS 'entrust_price',0 as entrust_strategy FROM tb_plan_close_order AS co WHERE co.user_id = ? AND co.contract_code = ? AND co.trigger_status = ? ORDER BY IFNULL(UNIX_TIMESTAMP(co.trigger_time),**********) DESC, co.create_time DESC LIMIT ?) ORDER BY trigger_time DESC, create_time DESC LIMIT ?"

// 获取用户指定合约条件单
func GetContractConditionOrder(userID int64, contractCode string, start, end time.Time) ([]proto.ApiConditionOrder, error) {
	list := make([]proto.ApiConditionOrder, 0)
	err := DefaultDB().Select(&list, _getContractPlanOrder, define.MaxTimestamp, define.OrderTypePlan, userID, contractCode, define.OrderConditionNotTrigger, define.OrderCondHadTrigger, define.OrderCondFailed, start, end, define.OrderConditionNotTrigger, define.SQLLimitMaxCount, define.OrderTypeLimit, define.OrderTypeLimit, define.MaxTimestamp, define.OrderTypeLimit, userID, contractCode, define.OrderCondHadTrigger, define.SQLLimitMaxCount, define.ApiHistoryOrderMaxCount)
	return list, err
}

const _basePlanOrder = "select plan_order_id, user_id, contract_code, side, offset, amount, status, create_time, order_time, cancel_time, ip_address, imei, order_client, trigger_price, `condition`, account_type, lever,mode from tb_plan_order"

// 获取符合触发条件的条件单
func GetMatchPlanOrders(contractCode string, price float64) (list []proto.ConditionOrder, err error) {
	str := "select plan_order_id, user_id, contract_code, side, offset, amount, status, create_time, order_time, cancel_time, ip_address, imei, order_client, trigger_price, `condition`, account_type, lever from tb_plan_order where contract_code=? and (`condition`=1 and ?>=trigger_price) or (`condition`=2 and ?<=trigger_price)"
	err = DefaultDB().Select(&list, str, contractCode, price, price)
	return
}

// 插入条件单
func InsertPlanOrder(tx *sqlx.Tx, o *proto.ConditionOrder) (err error) {
	str := "insert into tb_plan_order(plan_order_id, user_id, platform_id, contract_code, side, offset, amount, status, create_time, ip_address, imei, order_client, trigger_price, `condition`, account_type, lever, `limit`, `stop`,mode,entrust_type,entrust_price,entrust_strategy) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, o.PlanOrderId, o.UserId, o.PlatformID, o.ContractCode, o.Side, o.Offset, o.Amount, o.Status, time.Now(), o.IpAddress, o.Imei, o.OrderClient, o.TriggerPrice, o.Condition, o.AccountType, o.Lever, o.Limit, o.Stop, o.Mode, o.EntrustType, o.EntrustPrice, o.EntrustStrategy)
		return
	}
	_, err = DefaultDB().Exec(str, o.PlanOrderId, o.UserId, o.PlatformID, o.ContractCode, o.Side, o.Offset, o.Amount, o.Status, time.Now(), o.IpAddress, o.Imei, o.OrderClient, o.TriggerPrice, o.Condition, o.AccountType, o.Lever, o.Limit, o.Stop, o.Mode, o.EntrustType, o.EntrustPrice, o.EntrustStrategy)
	return
}

// 修改计划单触发
func UpdatePlanOrderTrigger(tx *sqlx.Tx, orderId int64, triggleSuccess bool) (err error) {
	var status int
	if triggleSuccess {
		status = 2
	} else {
		status = 3
	}
	str := "update tb_plan_order set status=?,order_time=? where plan_order_id=?"
	if tx != nil {
		_, err = tx.Exec(str, status, time.Now(), orderId)
		return
	}
	_, err = DefaultDB().Exec(str, status, time.Now(), orderId)
	return
}

const _cancelConditionOrderBasic = "SELECT plan_order_id, user_id, contract_code, side, offset, amount, status, create_time 'create_time', IFNULL(order_time, create_time) 'order_time', IFNULL(cancel_time, create_time) 'cancel_time', ip_address, imei, order_client, trigger_price, `condition`, account_type, lever,mode,entrust_type,entrust_price,entrust_strategy FROM tb_plan_order"

//func CancelConditionOrder(userID, orderID int64, contractCode string) (list []proto.ConditionOrder, err error) {
//	var buf strings.Builder
//	buf.WriteString(_cancelConditionOrderBasic)
//	buf.WriteString(" WHERE ")
//	if orderID != 0 {
//		buf.WriteString(fmt.Sprintf(" plan_order_id=%d AND", orderID))
//	}
//	if userID != 0 {
//		buf.WriteString(fmt.Sprintf(" user_id=%d AND", userID))
//	}
//	if len(contractCode) > 0 {
//		buf.WriteString(fmt.Sprintf(" contract_code='%s' AND", contractCode))
//	}
//	buf.WriteString(fmt.Sprintf(" `status`=%d FOR UPDATE", define.OrderConditionNotTrigger))
//
//	str := buf.String()
//	log.Info("CancelConditionOrder", zap.String("sql", str))
//
//	tx, err := Begin()
//	if err != nil {
//		log.Error("CancelConditionOrder open transaction failed",
//			zap.Int64("userID", userID),
//			zap.Int64("orderID", orderID),
//			zap.Error(err))
//		err = define.ErrMsgBusy
//		return
//	}
//	defer CommitTx(tx, &err, orderID, nil)
//
//	list, err = GetConditionOrderAndLock(tx, str)
//	if err != nil {
//		log.Error("CancelConditionOrder GetConditionOrderAndLock failed",
//			zap.Int64("userID", userID),
//			zap.Int64("orderID", orderID),
//			zap.Error(err))
//		err = define.ErrMsgBusy
//		return
//	}
//
//	userMP := make(map[int64]map[string]*model.PlanContactLeverCount)
//	for i := range list {
//		list[i].CancelTime = time.Now()
//		list[i].Status = define.OrderCondCancel
//
//		if _, ok := userMP[list[i].UserId]; !ok {
//			userMP[list[i].UserId] = make(map[string]*model.PlanContactLeverCount)
//			userMP[list[i].UserId][list[i].ContractCode] = new(model.PlanContactLeverCount)
//		}
//		userMP[list[i].UserId][list[i].ContractCode].Lever = list[i].Lever
//		userMP[list[i].UserId][list[i].ContractCode].Count = userMP[list[i].UserId][list[i].ContractCode].Count + 1
//
//		err = UpdateConditionOrderState(tx, list[i].PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondCancel, list[i].CancelTime)
//		if err != nil {
//			log.Error("CancelConditionOrder UpdateConditionOrderState failed",
//				zap.Int64("userID", userID),
//				zap.Int64("orderID", orderID),
//				zap.Error(err))
//			err = define.ErrMsgBusy
//			return
//		}
//	}
//
//	return
//}

// 获取条件单列表
func GetConditionOrderAndLock(tx *sqlx.Tx, sqlStr string) ([]proto.ConditionOrder, error) {
	var list []proto.ConditionOrder
	err := tx.Select(&list, sqlStr)
	return list, err
}

const _conditionOrderForMeetBasic = "SELECT plan_order_id, user_id, platform_id,account_type, contract_code, side, offset, amount, status, create_time 'create_time', IFNULL(order_time, create_time) 'order_time', IFNULL(cancel_time, create_time) 'cancel_time', ip_address, imei, order_client, trigger_price, `condition`, account_type, lever, `limit`, `stop`,mode,entrust_type,entrust_price,entrust_strategy FROM tb_plan_order WHERE 1=1"

//func GetConditionOrderForMeet(contractCode string, price float64) (list []proto.ConditionOrder, err error) {
//	var buf strings.Builder
//	buf.WriteString(_conditionOrderForMeetBasic)
//	buf.WriteString(fmt.Sprintf(" AND contract_code='%s'", contractCode))
//	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))
//	buf.WriteString(fmt.Sprintf(" AND IF(`condition`=%d, trigger_price<=%f, trigger_price>=%f) FOR UPDATE", define.OrderConditionGreaterOrEqual, price, price))
//
//	str := buf.String()
//	//log.Info("GetConditionOrderForMeet", zap.String("sql", str))
//
//	tx, err := Begin()
//	if err != nil {
//		log.Error("GetConditionOrderForMeet open transaction failed",
//			zap.String("contract", contractCode),
//			zap.Float64("price", price),
//			zap.Error(err))
//		return
//	}
//	defer CommitTx(tx, &err, 0, nil)
//
//	list, err = GetConditionOrderAndLock(tx, str)
//	if err != nil {
//		log.Error("GetConditionOrderForMeet GetConditionOrderAndLock failed",
//			zap.String("contract", contractCode),
//			zap.Float64("price", price),
//			zap.Error(err))
//		return
//	}
//
//	now := time.Now()
//	userMP := make(map[int64]map[string]*model.PlanContactLeverCount)
//
//	for i := range list {
//		if _, ok := userMP[list[i].UserId]; !ok {
//			userMP[list[i].UserId] = make(map[string]*model.PlanContactLeverCount)
//			userMP[list[i].UserId][list[i].ContractCode] = new(model.PlanContactLeverCount)
//		}
//		userMP[list[i].UserId][list[i].ContractCode].Lever = list[i].Lever
//		userMP[list[i].UserId][list[i].ContractCode].Count = userMP[list[i].UserId][list[i].ContractCode].Count + 1
//
//		err = UpdateConditionOrderState(tx, list[i].PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondRunning, now)
//		if err != nil {
//			log.Error("GetConditionOrderForMeet UpdateConditionOrderState failed",
//				zap.String("contract", contractCode),
//				zap.Float64("price", price),
//				zap.Error(err))
//			return
//		}
//		list[i].OrderTime = now
//	}
//
//	return
//}

//func GetConditionOrderForMeetV2(contractCode string, ids string) (list []proto.ConditionOrder, err error) {
//	var buf strings.Builder
//	buf.WriteString(_conditionOrderForMeetBasic)
//	buf.WriteString(fmt.Sprintf(" AND plan_order_id IN(%s)", ids))
//	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))
//
//	str := buf.String()
//	//log.Info("GetConditionOrderForMeet", zap.String("sql", str))
//
//	tx, err := Begin()
//	if err != nil {
//		log.Error("GetConditionOrderForMeet open transaction failed",
//			zap.String("contract", contractCode),
//			zap.Error(err))
//		return
//	}
//	defer CommitTx(tx, &err, 0, nil)
//
//	list, err = GetConditionOrderAndLock(tx, str)
//	if err != nil {
//		log.Error("GetConditionOrderForMeet GetConditionOrderAndLock failed",
//			zap.String("contract", contractCode),
//			zap.Error(err))
//		return
//	}
//
//	//now := time.Now()
//	userMP := make(map[int64]map[string]*model.PlanContactLeverCount)
//
//	for i := range list {
//		if _, ok := userMP[list[i].UserId]; !ok {
//			userMP[list[i].UserId] = make(map[string]*model.PlanContactLeverCount)
//			userMP[list[i].UserId][list[i].ContractCode] = new(model.PlanContactLeverCount)
//		}
//		userMP[list[i].UserId][list[i].ContractCode].Lever = list[i].Lever
//		userMP[list[i].UserId][list[i].ContractCode].Count = userMP[list[i].UserId][list[i].ContractCode].Count + 1
//
//		//err = UpdateConditionOrderState(tx, list[i].PlanOrderId, define.OrderConditionNotTrigger, define.OrderCondRunning, now)
//		//if err != nil {
//		//	log.Error("GetConditionOrderForMeet UpdateConditionOrderState failed",
//		//		zap.String("contract", contractCode),
//		//		zap.Error(err))
//		//	return
//		//}
//		// 该字段改为触发时赋值
//		//list[i].OrderTime = now
//	}
//
//	return
//}

// 获取未触发计划单总张数
func GetPlanOrderCount(tx *sqlx.Tx, userID int64) (int, error) {
	var count int
	err := tx.Get(&count, "SELECT COUNT(1) FROM tb_plan_order WHERE user_id=? AND status=?", userID, define.OrderConditionNotTrigger)
	return count, err
}

//func GetAllPlanOrderID(code string, condition int) ([]model.PlanOrderInitData, error) {
//	var list []model.PlanOrderInitData
//	err := DefaultDB().Select(&list, "SELECT plan_order_id, trigger_price,user_id FROM tb_plan_order WHERE contract_code = ? AND `status` = ? AND `condition` = ?", code, define.OrderConditionNotTrigger, condition)
//	return list, err
//}

//func GetStopPriceList(arg *model.StopPriceListArg) ([]model.StopPriceListReply, error) {
//	list := make([]model.StopPriceListReply, 0)
//	rows, err := DefaultDB().Queryx("SELECT plan_close_order_id, contract_code, side, amount, `limit`, `stop`, entrust_limit, entrust_stop, create_time FROM tb_plan_close_order WHERE position_id = ? AND user_id = ? and trigger_status=? ORDER BY plan_close_order_id DESC", arg.PlanOrderID, arg.UserID, define.OrderConditionNotTrigger)
//	if err != nil {
//		return nil, err
//	}
//	defer rows.Close()
//
//	var data model.StopPriceListReply
//	for rows.Next() {
//		err = rows.StructScan(&data)
//		if err != nil {
//			return nil, err
//		}
//
//		data.CreateTimeUnix = data.CreateTime.Unix()
//		list = append(list, data)
//	}
//
//	return list, err
//}

//func GetContractStopPriceList(arg *proto.UserContract) ([]model.StopPriceListReply, error) {
//	list := make([]model.StopPriceListReply, 0)
//	rows, err := DefaultDB().Queryx("SELECT plan_close_order_id, contract_code, side, amount, `limit`, `stop`, entrust_limit, entrust_stop FROM tb_plan_close_order WHERE user_id = ? and trigger_status=? AND IF(''=?, TRUE, contract_code=?) ORDER BY plan_close_order_id DESC", arg.UserID, define.OrderConditionNotTrigger, arg.ContractCode, arg.ContractCode)
//	if err != nil {
//		return nil, err
//	}
//	defer rows.Close()
//
//	var data model.StopPriceListReply
//	for rows.Next() {
//		err = rows.StructScan(&data)
//		if err != nil {
//			return nil, err
//		}
//
//		data.CreateTimeUnix = data.CreateTime.Unix()
//		list = append(list, data)
//	}
//
//	return list, err
//}

// 获取条件平仓单
func GetPlanCloseOrderByPlanIdAndUserId(tx *sqlx.Tx, planID, userID int64) (*proto.PlanCloseOrder, error) {
	var err error
	order := new(proto.PlanCloseOrder)
	str := "SELECT plan_close_order_id, position_id, user_id, contract_code, side, amount, `limit`, stop, create_time, user_id, position_id, contract_code, side, amount, `limit`, `stop`, create_time FROM tb_plan_close_order WHERE plan_close_order_id = ? AND user_id = ? and trigger_status=?"
	if tx != nil {
		err = tx.Get(order, str, planID, userID, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(order, str, planID, userID, define.OrderConditionNotTrigger)
	}
	return order, err
}

const _queryAllTradeList = `
SELECT
    t.trade_id, t.contract_code, c.contract_name, c.contract_name_en, t.side, t.offset, t.price, t.open_avg_price, t.volume, t.trade_value, t.account_type, t.lever, t.trade_amount, t.profit, t.close_profit, t.order_type, t.order_client, t.trade_time, t.is_follow, t.is_trader,t.entrust_order_id,t.entrust_mode,t.entrust_type,t.entrust_volume,t.entrust_status
FROM (
         (
             SELECT
                 nt.trade_id,nt.entrust_order_id, nt.contract_code, nt.side, nt.offset, nt.price, nt.open_avg_price, nt.volume, nt.trade_value, nt.account_type, nt.lever, nt.trade_amount, nt.profit, nt.close_profit, nt.order_type, nt.order_client, nt.trade_time, FALSE AS 'is_follow', FALSE AS 'is_trader',ifnull(t2.mode,3) as entrust_mode,ifnull(t2.entrust_type,0) as entrust_type,ifnull(t2.volume,nt.volume) as entrust_volume,ifnull(t2.state,200) as entrust_status
             FROM
                 tb_trade AS nt left join tb_order AS t2 on nt.entrust_order_id=t2.id
             WHERE
             		nt.user_id = ?
             	AND
            		IF( ? = '', TRUE, nt.contract_code = ? )
             	AND
             		IF( ? = '', TRUE, nt.offset = ?)
             	AND
                	nt.trade_time BETWEEN ? AND ?
             ORDER BY
             	nt.trade_id DESC
             LIMIT ?
         )
         UNION ALL
         (
             SELECT
                 ft.deal_id AS 'trade_id',ft.entrust_order_id, ft.contract_code, ft.side, ft.offset, ft.price, IFNULL(tfph.open_price,ft.price) AS open_avg_price, ft.volume, ft.trade_value, ? AS 'account_type', ft.lever, ft.trade_amount, ft.profit, ft.close_profit, ft.order_type, ft.order_client, ft.trade_time, TRUE AS 'is_follow', ft.is_trader,ifnull(t2.mode,3) as entrust_mode,ifnull(t2.entrust_type,0) as entrust_type,ifnull(t2.volume,ft.volume) as entrust_volume,ifnull(t2.state,200) as entrust_status
             FROM
                 tb_follow_trade AS ft left join tb_order AS t2 on ft.entrust_order_id=t2.id LEFT JOIN tb_follow_position_history tfph on ft.position_id = tfph.id
             WHERE
             		ft.user_id = ?
             	AND
            		IF( ? = '', TRUE, ft.contract_code = ? )
             	AND
             		IF( ? = '', TRUE, ft.offset = ?)
            	AND
                	ft.trade_time BETWEEN ? AND ?
             ORDER BY
                 ft.deal_id DESC
             LIMIT ?
         )
     ) AS t
         LEFT JOIN tb_contract c ON c.contract_code = t.contract_code
ORDER BY
	t.trade_id DESC
LIMIT ?
`

//const _queryAllTradeList = `
//SELECT
//	t.trade_id, t.contract_code, c.contract_name, c.contract_name_en, t.side, t.offset, t.price, t.volume, t.trade_value, t.account_type, t.lever, t.trade_amount, t.profit, t.order_type, t.trade_time, t.is_follow, t.is_trader
//FROM (
//	(
//	SELECT
//		nt.trade_id, nt.contract_code, nt.side, nt.offset, nt.price, nt.volume, nt.trade_value, nt.account_type, nt.lever, nt.trade_amount, nt.profit, nt.order_type, nt.trade_time, FALSE AS 'is_follow', FALSE AS 'is_trader'
//	FROM
//		tb_trade AS nt
//	WHERE
//		nt.user_id = ?
//	AND
//		IF( ? = '', TRUE, nt.contract_code = ? )
//	AND
//	    IF( ? = '', TRUE, nt.offset = ?)
//	ORDER BY
//		nt.trade_id DESC
//	LIMIT ?
//	)
//UNION ALL
//	(
//	SELECT
//		ft.deal_id AS 'trade_id', ft.contract_code, ft.side, ft.offset, ft.price, ft.volume, ft.trade_value, ? AS 'account_type', ft.lever, ft.trade_amount, ft.profit, ft.order_type, ft.trade_time, TRUE AS 'is_follow', ft.is_trader
//	FROM
//		tb_follow_trade AS ft
//	WHERE
//		ft.user_id = ?
//	AND
//		IF( ? = '', TRUE, ft.contract_code = ? )
//	AND
//	    IF( ? = '', TRUE, ft.offset = ?)
//	ORDER BY
//		ft.deal_id DESC
//	LIMIT ?
//	)
//) AS t
//LEFT JOIN tb_contract c ON c.contract_code = t.contract_code
//ORDER BY
//	t.trade_id DESC
//LIMIT ?
//`

//func GetAllTradeList(userID int64, contractCode, offset string, start, end time.Time, page *define.Page) ([]model.ApiAllSoldTrade, error) {
//	list := make([]model.ApiAllSoldTrade, 0)
//	err := DefaultDB().Select(&list, _queryAllTradeList, userID, contractCode, contractCode, offset, offset, start, end, page.Count, define.AccountTypeByFollow, userID, contractCode, contractCode, offset, offset, start, end, page.Count, page.Count)
//	return list, err
//}

func GetConditionOrderByOrderId(tx *sqlx.Tx, ids int64) (order *proto.ConditionOrder, err error) {
	var buf strings.Builder
	buf.WriteString(_conditionOrderForMeetBasic)
	buf.WriteString(fmt.Sprintf(" AND plan_order_id =%v", ids))
	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))

	o := new(proto.ConditionOrder)
	if tx != nil {
		err = tx.Get(o, buf.String())
	} else {
		err = DefaultDB().Get(o, buf.String())
	}
	if err != nil {
		if err != sql.ErrNoRows {
			log.Errorf("GetConditionOrderByOrderId fail,%v,id；%v", err, ids)
			return nil, err
		}
		return nil, nil
	}
	order = o
	return
}

//func GetConditionOrderByOrderId(contractCode string, ids int64) (list []proto.ConditionOrder, err error) {
//	var buf strings.Builder
//	buf.WriteString(_conditionOrderForMeetBasic)
//	buf.WriteString(fmt.Sprintf(" AND plan_order_id =%v", ids))
//	buf.WriteString(fmt.Sprintf(" AND `status`=%d", define.OrderConditionNotTrigger))
//
//	tx, err := Begin()
//	if err != nil {
//		log.Error("GetConditionOrderForMeet open transaction failed",
//			zap.String("contract", contractCode),
//			zap.Error(err))
//		return
//	}
//	defer CommitTx(tx, &err, 0, nil)
//
//	err = tx.Select(&list, buf.String())
//	if err != nil {
//		log.Errorf("GetConditionOrderByOrderId fail,%v", err)
//	}
//
//	return
//}
