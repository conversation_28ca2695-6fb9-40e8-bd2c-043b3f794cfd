package database

import (
	"bc/libs/log"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

const _insertApiErr = "INSERT tb_error_api SET own_day=:own_day, req_id=:req_id, req_uri=:req_uri, err_code=:err_code, err_msg=:err_msg, user_id=:user_id, ip_address=:ip_address, client_os=:client_os, create_time=:create_time"

func InsertApiErrorRecord(record *proto.ApiError) {
	_, err := DefaultDB().NamedExec(_insertApiErr, record)
	if err != nil {
		log.Error("InsertApiErrorRecord db error", zap.Any("record", record), zap.Error(err))
	}
}

const _insertExternalErr = "INSERT tb_error_external SET own_day=:own_day, sys_type=:sys_type, req_id=:req_id, req_uri=:req_uri, err_code=:err_code, err_msg=:err_msg, user_id=:user_id, create_time=:create_time"

func InsertExternalErrorRecord(tx *sqlx.Tx, record *proto.ExternalError) {
	var err error
	if tx == nil {
		_, err = DefaultDB().NamedExec(_insertExternalErr, record)
	} else {
		_, err = tx.NamedExec(_insertExternalErr, record)
	}
	if err != nil {
		log.Error("InsertExternalErrorRecord db error", zap.Any("record", record), zap.Error(err))
	}
}

const _insertSysErr = "INSERT tb_error_sys SET own_day=:own_day, err_type=:err_type, err_msg=:err_msg, contract_code=:contract_code, user_id=:user_id, create_time=:create_time"

func InsertSysErrorRecord(tx *sqlx.Tx, record *proto.SysError) {
	var err error
	if tx == nil {
		_, err = DefaultDB().NamedExec(_insertSysErr, record)
	} else {
		_, err = tx.NamedExec(_insertSysErr, record)
	}
	if err != nil {
		log.Error("InsertSysErrorRecord db error", zap.Any("record", record), zap.Error(err))
	}
}

func InsertUserErrorLog(record *proto.UserError) {
	_, err := DefaultDB().NamedExec("INSERT tb_user_err_log SET req_id=:req_id, user_id=:user_id, user_name=:user_name, op_type=:op_type, err_code=:err_code, err_msg=:err_msg, ip=:ip, device=:device, imei=:imei, os_type=:os_type, version=:version, created_time=:created_time", record)
	if err != nil {
		log.Error("InsertUserErrorLog db failed", zap.Int64("reqID", record.ReqID), zap.Error(err))
	}
}
