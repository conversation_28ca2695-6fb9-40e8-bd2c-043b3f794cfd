package database

import (
	"bc/libs/log"
	"bc/libs/proto"
	"database/sql"
)

const _platformi = "SELECT platform_id, platform_name, down_url, create_time, status_type, superadmin, superadmin_id, platform_code, create_manage FROM tb_platform_info"

func ListPlatformInfo() (list []proto.PlatformInfo, err error) {
	err = DefaultDB().Select(&list, _platformi)
	return
}

func ListValidPlatformInfo() (list []proto.PlatformInfo, err error) {
	err = DefaultDB().Select(&list, _platformi+" where status_type=1")
	return
}

func GetValidPlatformInfo(platformId int) *proto.PlatformInfo {
	var info proto.PlatformInfo
	err := DefaultDB().Get(&info, _platformi+" where platform_id=? AND status_type=1", platformId)
	if err != nil && err != sql.ErrNoRows {
		log.Errorf("GetValidPlatformInfo fail,%v", err)
		return nil
	}
	return &info
}

func IsValidPlatformId(platformId int) bool {
	var count int
	str := "select count(1) from tb_platform_info where platform_id=? and status_type=1"
	err := DefaultDB().Get(&count, str, platformId)
	if err != nil {
		log.Errorf("IsValidPlatformId fail id:%v,%v", platformId, err)
		return false
	}
	return count > 0

}

func GetUserLabelIDByCode(code string) (int, error) {
	var labelID int
	err := DefaultDB().Get(&labelID, "SELECT IF(is_agent, label_id, 0) FROM tb_user WHERE invite_code=?", code)
	return labelID, err
}
