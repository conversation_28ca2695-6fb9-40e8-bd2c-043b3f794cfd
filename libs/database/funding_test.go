package database

import (
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
	"testing"
	"time"
)

func TestFundingDeal(t *testing.T) {
	db, err := sqlx.Open("mysql", "root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
	if err != nil {
		t.Error("InitDefaultDB db connect failed", err)
		return
	}
	tx, err := db.Beginx()
	if err != nil {
		return
	}
	fd := &proto.FundingDeal{
		ID:           1,
		ContractCode: "BTCUSDT",
		FundingRate:  nums.NewFromInt64(1),
		Income:       nums.NewFromFloat(500),
		Giving:       nums.NewFromFloat(300),
		GivingFull:   nums.NewFromFloat(300),
		GivingWare:   nums.NewFromFloat(300),
		GivingFollow: nums.NewFromFloat(300),
		CreateTime:   time.Now(),
	}
	err = InsertFundingDeal(tx, fd)
	if err != nil {
		t.Log(err)
		tx.Rollback()
		return
	}
	tx.Commit()

}
