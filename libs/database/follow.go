package database

import (
	"database/sql"
	"time"

	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//添加跟单账户
func InsertFollowAccount(tx *sqlx.Tx, a *proto.FollowAccount) (err error) {
	str := "INSERT INTO tb_follow_account (account_id, user_id, currency_id, balance, lock_amount, total_profit, total_proceeds, total_principal,day_profit,day_principal,platform_id) VALUES (?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, a.AccountID, a.UserId, a.CurrencyID, a.Balance, a.LockAmount, a.TotalProfit, a.TotalProceeds, a.TotalPrincipal, a.DayProfit, a.DayPrincipal, a.PlatformID)
	} else {
		_, err = tx.Exec(str, a.AccountID, a.UserId, a.CurrencyID, a.Balance, a.LockAmount, a.TotalProfit, a.TotalProceeds, a.TotalPrincipal, a.DayProfit, a.DayPrincipal, a.PlatformID)
	}
	return
}

//查询跟单账户
const _followAccountQuery = "select account_id, user_id, currency_id, balance, lock_amount, total_profit, total_proceeds, total_principal, day_profit, day_principal, platform_id from tb_follow_account"

func GetFollowAccountByUserId(tx *sqlx.Tx, userId int64, currencyId int) (account *proto.FollowAccount, err error) {
	str := _followAccountQuery + " where user_id=? and currency_id=?"
	a := new(proto.FollowAccount)
	if tx == nil {
		err = DefaultDB().Get(a, str, userId, currencyId)
	} else {
		err = tx.Get(a, str, userId, currencyId)
	}
	if err != nil {
		return nil, err
	}
	return a, nil
}

func ListFollowTradeAccount(tx *sqlx.Tx, page, size int) (list []proto.FollowAccount, err error) {
	str := utils.StrBuilder(_followAccountQuery, " limit ?,?")
	if tx != nil {
		err = tx.Select(&list, str, page*size, size)
	} else {
		err = DefaultDB().Select(&list, str, page*size, size)
	}
	if err != nil {
		return nil, err
	}
	return
}

func GetFollowAccountByUserIdWithLocked(tx *sqlx.Tx, userId int64, currencyId int) (account *proto.FollowAccount, err error) {
	str := _followAccountQuery + " where user_id=? and currency_id=? for update"
	a := new(proto.FollowAccount)
	if tx == nil {
		err = DefaultDB().Get(a, str, userId, currencyId)
	} else {
		err = tx.Get(a, str, userId, currencyId)
	}
	if err != nil {
		return nil, err
	}
	return a, nil
}

//以增量方式更新跟单账户交易资金
//balanceIncr-余额增加量 totalProfitIncr-已实现盈亏 total_proceeds-净收益 totalPrincipalIncr-成本
func UpdateFollowAccountWithIncrement(tx *sqlx.Tx, accountId int64, balanceIncr, lockIncr, totalProfitIncr, totalProceedsIncr, totalPrincipalIncr, dayProfitIncr, dayPrincipalIncr, totalClosePrincipalIncr decimal.Decimal) (err error) {
	str := "update tb_follow_account set balance=balance+?,lock_amount=lock_amount+?,total_profit=total_profit+?,total_proceeds=total_proceeds+?,total_principal=total_principal+?,day_profit=day_profit+?,day_principal=day_principal+?,total_close_principal=total_close_principal+? where account_id=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, balanceIncr, lockIncr, totalProfitIncr, totalProceedsIncr, totalPrincipalIncr, dayProfitIncr, dayPrincipalIncr, totalClosePrincipalIncr, accountId)
		return
	}
	_, err = tx.Exec(str, balanceIncr, lockIncr, totalProfitIncr, totalProceedsIncr, totalPrincipalIncr, dayProfitIncr, dayPrincipalIncr, totalClosePrincipalIncr, accountId)
	return
}

// UpdateFollowAccountLockForCanceling 更新用户冻结资产
func UpdateFollowAccountLockForCanceling(tx *sqlx.Tx, accountId int64, balance, lock decimal.Decimal) (err error) {
	str := "update tb_follow_account set balance=?, lock_amount=? where account_id=?"
	if tx == nil {
		// 这里不允许非事务执行
		return define.ErrMsgBusy
	}
	_, err = tx.Exec(str, balance, lock, accountId)
	return
}

func UpdateFollowAccountLockForOpen(tx *sqlx.Tx, accountId int64, balanceIncre decimal.Decimal) (err error) {
	str := "update tb_follow_account set balance=balance+?  where account_id=?"
	if tx == nil {
		// 这里不允许非事务执行
		return define.ErrMsgBusy
	}
	_, err = tx.Exec(str, balanceIncre, accountId)
	return
}

func UpdateFollowAccountLock(tx *sqlx.Tx, accountId int64, lock decimal.Decimal) (err error) {
	str := "update tb_follow_account set lock_amount=? where account_id=?"
	if tx == nil {
		// 这里不允许非事务执行
		return define.ErrMsgBusy
	}
	_, err = tx.Exec(str, lock, accountId)
	return
}

// 更新用户可用资产
func UpdateFollowAccountBalance(tx *sqlx.Tx, accountId int64, newBalance decimal.Decimal) (err error) {
	str := "update tb_follow_account set balance=? where account_id=?"
	if tx == nil {
		// 这里不允许非事务执行
		return define.ErrMsgBusy
	}
	_, err = tx.Exec(str, newBalance, accountId)
	return
}

//初始化账户日统计数据
func UpdateFollowAccountFollowDayInit(tx *sqlx.Tx) (err error) {
	str := "update tb_follow_account set day_profit=0,day_principal=0 where 1=1"
	if tx == nil {
		_, err = DefaultDB().Exec(str)
		return
	}
	_, err = DefaultDB().Exec(str)
	return
}

//新增跟单资金记录
func InsertFollowAccountHistory(tx *sqlx.Tx, h *proto.FollowAccountHistory) (err error) {
	str := "INSERT INTO tb_follow_account_history (id, user_id, position_id, currency_id, currency_name, balance, available, `type`, amount, create_time, ip_address, imei, order_client, margin, account_rights, platform_id,extra_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, h.ID, h.UserId, h.PositionId, h.CurrencyID, h.CurrencyName, h.Balance, h.Available, h.Type, h.Amount, h.CreateTime, h.IpAddress, h.Imei, h.OrderClient, h.Margin, h.AccountRights, h.PlatformID, h.ExtraID)
	} else {
		_, err = tx.Exec(str, h.ID, h.UserId, h.PositionId, h.CurrencyID, h.CurrencyName, h.Balance, h.Available, h.Type, h.Amount, h.CreateTime, h.IpAddress, h.Imei, h.OrderClient, h.Margin, h.AccountRights, h.PlatformID, h.ExtraID)
	}
	return
}

//修改交易员增量数据
func UpdateDealerUserWithIncrementData(tx *sqlx.Tx, d *proto.DealerUser) (err error) {
	str := "update tb_dealer_user set total_amount=total_amount+?,total_profit=total_profit+?,total_loss=total_loss+?,total_profit_count=total_profit_count+?,total_loss_count=total_loss_count+?,total_capital=total_capital+? where user_id=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, d.TotalAmount, d.TotalProfit, d.TotalLoss, d.TotalProfitCount, d.TotalLossCount, d.TotalCapital, d.UserId)
	} else {
		_, err = tx.Exec(str, d.TotalAmount, d.TotalProfit, d.TotalLoss, d.TotalProfitCount, d.TotalLossCount, d.TotalCapital, d.UserId)
	}
	return
}

//获取持仓
const _followPosition = "select id, user_id, contract_code, side, price, volume,volume_lock, force_price, `limit`, `stop`, trader_uid, follow_position_id, lever, init_margin, raw_margin,margin, adjust_margin, float_profit, available, profit_ratio, margin_ratio, contract_index, buy_price, sell_price, commission,open_fee, funding_amount,create_time,platform_id from tb_follow_position "

// GetUserFollowPositions 获取所有持仓
func GetUserFollowPositions(tx *sqlx.Tx, page, size int) (list []proto.FollowPosition, err error) {
	str := utils.StrBuilder(_followPosition + " limit ?,?")
	if tx == nil {
		err = DefaultDB().Select(&list, str, page*size, size)
	} else {
		err = tx.Select(&list, str, page*size, size)
	}
	return
}

func GetFollowPositionById(tx *sqlx.Tx, id int64) (pos *proto.FollowPosition, err error) {
	p := new(proto.FollowPosition)
	str := utils.StrBuilder(_followPosition + " where id=?")
	if tx == nil {
		err = DefaultDB().Get(p, str, id)
	} else {
		err = tx.Get(p, str, id)
	}
	if err != nil {
		return nil, err
	}
	return p, nil
}

func GetFollowPositionByUserAndId(tx *sqlx.Tx, userId, id int64) (pos *proto.FollowPosition, err error) {
	p := new(proto.FollowPosition)
	str := utils.StrBuilder(_followPosition + " where id=? and user_id=?")
	if tx == nil {
		err = DefaultDB().Get(p, str, id, userId)
	} else {
		err = tx.Get(p, str, id, userId)
	}
	if err != nil {
		return nil, err
	}
	return p, nil
}

//查询跟单账户仓位数
func GetFollowPositionCount(tx *sqlx.Tx, userId int64) (count int64, err error) {
	str := "select count(1) from tb_follow_position where user_id=?"
	if tx == nil {
		err = DefaultDB().Get(&count, str, userId)
	} else {
		err = tx.Get(&count, str, userId)
	}
	if err != nil {
		return
	}
	return
}

func GetFollowPositionByUserAndIdWithLock(tx *sqlx.Tx, userId, id int64) (pos *proto.FollowPosition, err error) {
	p := new(proto.FollowPosition)
	str := utils.StrBuilder(_followPosition + " where id=? and user_id=?")
	if tx == nil {
		err = DefaultDB().Get(p, str, id, userId)
	} else {
		str += " for update"
		err = tx.Get(p, str, id, userId)
	}
	if err != nil {
		return nil, err
	}
	return p, nil
}

func GetFollowPositionByIdWithLock(tx *sqlx.Tx, id int64) (pos *proto.FollowPosition, err error) {
	p := new(proto.FollowPosition)
	str := utils.StrBuilder(_followPosition + " where id=? for update")
	if tx == nil {
		err = DefaultDB().Get(p, str, id)
	} else {
		err = tx.Get(p, str, id)
	}
	if err != nil {
		return nil, err
	}
	return p, nil
}

//获取全部持仓
func ListAllPositionByContractCode(tx *sqlx.Tx, code string) (list []proto.FollowPosition, err error) {
	str := _followPosition
	var params []interface{}
	if code != "" {
		str = utils.StrBuilder(str, " where contract_code=?")
		params = append(params, code)
	}
	if tx == nil {
		err = DefaultDB().Select(&list, str, params...)
	} else {
		err = tx.Select(&list, str, params...)
	}
	return
}

//获取全部跟单用户持仓
func ListAllFollowPositions(tx *sqlx.Tx) (list []proto.FollowPosition, err error) {
	str := _followPosition
	var params []interface{}
	str = str + " order by user_id asc"
	if tx == nil {
		err = DefaultDB().Select(&list, str, params...)
	} else {
		err = tx.Select(&list, str, params...)
	}
	return
}

//获取交易持仓下所有跟单持仓
func ListFollowPositionsByTraderPosId(tx *sqlx.Tx, id int64) (list []proto.FollowPosition, err error) {
	str := _followPosition
	str = utils.StrBuilder(str, " where follow_position_id=?")
	if tx == nil {
		err = DefaultDB().Select(&list, str, id)
	} else {
		err = tx.Select(&list, str, id)
	}
	return
}

//获取用户指定合约持仓 codes以逗号分隔
func ListFollowPositionsByCodes(tx *sqlx.Tx, userId int64, codes string) (list []proto.FollowPosition, err error) {
	str := _followPosition
	str = utils.StrBuilder(str, " where user_id=? and contract_code in (?)")
	if tx == nil {
		err = DefaultDB().Select(&list, str, userId, codes)
	} else {
		err = tx.Select(&list, str, userId, codes)
	}
	return
}

func ListUserFollowPositions(tx *sqlx.Tx, userId int64) (list []proto.FollowPosition, err error) {
	str := _followPosition
	str = utils.StrBuilder(str, " where user_id=? for update")
	if tx == nil {
		err = DefaultDB().Select(&list, str, userId)
	} else {
		err = tx.Select(&list, str, userId)
	}
	return
}

func ListFollowAllPositions(tx *sqlx.Tx) (list []proto.FollowPosition, err error) {
	str := _followPosition
	if tx == nil {
		err = DefaultDB().Select(&list, str)
	} else {
		err = tx.Select(&list, str)
	}
	return
}

//新增持仓
func InsertFollowPosition(tx *sqlx.Tx, fp *proto.FollowPosition) (err error) {
	str := "INSERT INTO tb_follow_position (id, user_id, contract_code, side, price, volume, force_price, `limit`, `stop`, trader_uid, follow_position_id, lever, init_margin, margin,raw_margin, adjust_margin, float_profit, available, profit_ratio, margin_ratio, contract_index, buy_price, sell_price, commission,open_fee, funding_amount,create_time,platform_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, fp.Id, fp.UserId, fp.ContractCode, fp.Side, fp.Price, fp.Volume, fp.ForcePrice, fp.Limit, fp.Stop, fp.TraderUid, fp.FollowPositionId, fp.Lever, fp.InitMargin, fp.Margin, fp.RawMargin, fp.AdjustMargin, fp.FloatProfit, fp.Available, fp.ProfitRatio, fp.MarginRatio, fp.ContractIndex, fp.BuyPrice, fp.SellPrice, fp.Commission, fp.OpenFee, fp.FundingAmount, fp.CreateTime, fp.PlatformID)
	} else {
		_, err = tx.Exec(str, fp.Id, fp.UserId, fp.ContractCode, fp.Side, fp.Price, fp.Volume, fp.ForcePrice, fp.Limit, fp.Stop, fp.TraderUid, fp.FollowPositionId, fp.Lever, fp.InitMargin, fp.Margin, fp.RawMargin, fp.AdjustMargin, fp.FloatProfit, fp.Available, fp.ProfitRatio, fp.MarginRatio, fp.ContractIndex, fp.BuyPrice, fp.SellPrice, fp.Commission, fp.OpenFee, fp.FundingAmount, fp.CreateTime, fp.PlatformID)
	}
	return
}

func UpdateFollowPositionForcePrice(tx *sqlx.Tx, id int64, forcePrice decimal.Decimal) (err error) {
	str := "update tb_follow_position set force_price=? where id=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, forcePrice, id)
	} else {
		_, err = tx.Exec(str, forcePrice, id)
	}
	return
}

func UpdateFollowPosition(tx *sqlx.Tx, fp *proto.FollowPosition) (err error) {
	str := "update tb_follow_position set price=?,volume=?,volume_lock=?,commission=?,open_fee=?,init_margin=?,margin=?,raw_margin=?,profit_ratio=?,margin_ratio=?,float_profit=?,close_fee=?,close_profit=? where id=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, fp.Price, fp.Volume, fp.VolumeLock, fp.Commission, fp.OpenFee, fp.InitMargin, fp.Margin, fp.RawMargin, fp.ProfitRatio, fp.MarginRatio, fp.FloatProfit, fp.CloseFee, fp.CloseProfit, fp.Id)
	} else {
		_, err = tx.Exec(str, fp.Price, fp.Volume, fp.VolumeLock, fp.Commission, fp.OpenFee, fp.InitMargin, fp.Margin, fp.RawMargin, fp.ProfitRatio, fp.MarginRatio, fp.FloatProfit, fp.CloseFee, fp.CloseProfit, fp.Id)
	}
	return
}

func UpdateFollowPositionForFundingRate(tx *sqlx.Tx, posId int64, forcePrice, ProfitRate, marginRate, fundingRate decimal.Decimal) (err error) {
	str := "UPDATE tb_follow_position SET  force_price = ?, margin =margin+ ?,  profit_ratio = ?, margin_ratio = ?, funding_amount =funding_amount+ ? WHERE id = ? "
	if tx == nil {
		_, err = DefaultDB().Exec(str, forcePrice, fundingRate, ProfitRate, marginRate, fundingRate, posId)
	} else {
		_, err = tx.Exec(str, forcePrice, fundingRate, ProfitRate, marginRate, fundingRate, posId)
	}
	return
}

//删除仓位
func DelFollowPosition(tx *sqlx.Tx, posId int64) (err error) {
	str := "DELETE FROM tb_follow_position WHERE id = ?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, posId)
		return
	}
	_, err = tx.Exec(str, posId)
	return
}

////修改止盈止损
//func UpdatePositionLimitStopPriceById(tx *sqlx.Tx, limit, stop decimal.Decimal, id int64) (err error) {
//	str := "update tb_follow_position set limit=?,stop=? where id=?"
//	if tx == nil {
//		_, err = DefaultDB().Exec(str, limit, stop, id)
//	} else {
//		_, err = tx.Exec(str, limit, stop, id)
//	}
//	return
//}
const baseDealerFollow = "SELECT id, trader_uid, follow_uid,currency_id, contract, follow_status, quantity, proportion, stop_loss, stop_profit, max_amount, already_amount, wait_profit, wait_loss, total_actual_amount, already_profit, already_loss,withholding,start_audit_time,platform_id FROM tb_dealer_follow"

//查询用户跟随关系
func GetDealerFollow(tx *sqlx.Tx, followUid, traderUid int64) (u *proto.DealerFollow, err error) {
	df := new(proto.DealerFollow)
	str := baseDealerFollow + " WHERE trader_uid = ? and  follow_uid=?"
	if tx == nil {
		err = DefaultDB().Get(df, str, traderUid, followUid)
	} else {
		err = tx.Get(df, str, traderUid, followUid)
	}
	if err != nil {
		return nil, err
	}
	return df, nil
}

//查询交易员所有跟单合约用户
func ListDealerFollows(tx *sqlx.Tx, traderUid int64, contract string) (list []proto.DealerFollow, err error) {
	str := baseDealerFollow + " WHERE trader_uid = ? and follow_status=1 and INSTR(contract,?)>0"
	//str := baseDealerFollow + " WHERE trader_uid = ? and contract like concat('%',?,'%')"
	if tx == nil {
		err = DefaultDB().Select(&list, str, traderUid, contract)
	} else {
		err = tx.Select(&list, str, traderUid, contract)
	}
	return
}

//查询所有跟随关系
func ListAllDealerFollows(tx *sqlx.Tx) (list []proto.DealerFollow, err error) {
	str := baseDealerFollow + " WHERE 1=1"
	if tx == nil {
		err = DefaultDB().Select(&list, str)
	} else {
		err = tx.Select(&list, str)
	}
	return
}

//添加交易员关系
func InsertDealerFollow(tx *sqlx.Tx, f *proto.DealerFollow) (err error) {
	str := "INSERT INTO tb_dealer_follow (id, trader_uid, follow_uid,currency_id, contract, follow_status, quantity, proportion, stop_loss, stop_profit, max_amount, already_amount, wait_profit, wait_loss, total_actual_amount, already_profit, already_loss,start_audit_time,platform_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, f.ID, f.TraderUid, f.FollowUid, f.CurrencyId, f.Contract, f.FollowStatus, f.Quantity, f.StopLoss, f.StopProfit, f.MaxAmount, f.AlreadyAmount, f.WaitLoss, f.WaitLoss, f.TotalActualAmount, f.AlreadyProfit, f.AlreadyLoss, f.StartAuditTime, f.PlatformID)
	} else {
		_, err = tx.Exec(str, f.ID, f.TraderUid, f.FollowUid, f.CurrencyId, f.Contract, f.FollowStatus, f.Quantity, f.StopLoss, f.StopProfit, f.MaxAmount, f.AlreadyAmount, f.WaitLoss, f.WaitLoss, f.TotalActualAmount, f.AlreadyProfit, f.AlreadyLoss, f.StartAuditTime, f.PlatformID)
	}
	return
}

//更新交易员关系表
func UpdateDealerFollow(tx *sqlx.Tx, ic *proto.DealerFollow) (err error) {
	str := "UPDATE tb_dealer_follow SET   follow_status = ?, quantity = ?, proportion = ?, stop_loss = ?, stop_profit = ?, max_amount = ?, already_amount = ?, wait_profit = ?, wait_loss = ?, total_actual_amount = ?, already_profit = ?, already_loss = ?,withholding=? WHERE trader_uid=? and follow_uid=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, ic.FollowStatus, ic.Quantity, ic.Proportion, ic.StopLoss, ic.StopProfit, ic.MaxAmount, ic.AlreadyAmount, ic.WaitProfit, ic.WaitLoss, ic.TotalActualAmount, ic.AlreadyProfit, ic.AlreadyLoss, ic.Withholding, ic.TraderUid, ic.FollowUid)
	} else {
		_, err = tx.Exec(str, ic.FollowStatus, ic.Quantity, ic.Proportion, ic.StopLoss, ic.StopProfit, ic.MaxAmount, ic.AlreadyAmount, ic.WaitProfit, ic.WaitLoss, ic.TotalActualAmount, ic.AlreadyProfit, ic.AlreadyLoss, ic.Withholding, ic.TraderUid, ic.FollowUid)
	}
	return
}

//修改跟单者已使用资金
func UpdateFollowAlreadyUsed(tx *sqlx.Tx, followUid, traderUid int64, money decimal.Decimal) (err error) {
	str := "update tb_dealer_follow set already_amount=already_amount+?,total_amount=total_amount+? where follow_uid=? and trader_uid=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, money, money, followUid, traderUid)
	} else {
		_, err = tx.Exec(str, money, money, followUid, traderUid)
	}
	return
}

//修改平仓已使用资金
func UpdateFollowAlreadyUsedForClose(tx *sqlx.Tx, followUid, traderUid int64, money decimal.Decimal) (err error) {
	str := "update tb_dealer_follow set already_amount=already_amount-? where follow_uid=? and trader_uid=? "
	if tx == nil {
		_, err = DefaultDB().Exec(str, money, followUid, traderUid)
	} else {
		_, err = tx.Exec(str, money, followUid, traderUid)
	}
	return
}

//更新平仓时收益
func UpdateDealerFollowWithClosePos(tx *sqlx.Tx, profit, loss, withholding decimal.Decimal, traderUid, followUid int64) (err error) {
	str := "UPDATE tb_dealer_follow SET   wait_profit =wait_profit+ ?, wait_loss =wait_loss+ ?, already_profit =already_profit+ ?, already_loss =already_loss+ ?,withholding=withholding+? WHERE trader_uid=? and follow_uid=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, profit, loss, profit, loss, withholding, traderUid, followUid)
		return
	}
	_, err = tx.Exec(str, profit, loss, profit, loss, withholding, traderUid, followUid)
	return
}

func UpdateDealerFollowWithSettle(tx *sqlx.Tx, profit, loss, withholding, brokerage decimal.Decimal, traderUid, followUid int64, setterTime time.Time) (err error) {
	str := "UPDATE tb_dealer_follow SET   wait_profit =wait_profit+ ?, wait_loss =wait_loss+ ?, withholding=withholding+?,total_actual_amount=total_actual_amount+?,start_audit_time=? WHERE trader_uid=? and follow_uid=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, profit, loss, withholding, brokerage, setterTime, traderUid, followUid)
		return
	}
	_, err = tx.Exec(str, profit, loss, withholding, brokerage, setterTime, traderUid, followUid)
	return
}

//结算后重置收益
func RecoverDealerFollowWaitProfit(tx *sqlx.Tx, actualProfit decimal.Decimal, traderUid, followUid int64) (err error) {
	str := "UPDATE tb_dealer_follow SET   wait_profit = 0, wait_loss = 0, total_actual_amount = total_actual_amount+? WHERE trader_uid=? and follow_uid=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, actualProfit, traderUid, followUid)
		return
	}
	_, err = tx.Exec(str, actualProfit, traderUid, followUid)
	return
}

//插入成交记录
func InsertFollowTradeRecord(tx *sqlx.Tx, t *proto.FollowTrade) (err error) {
	str := "INSERT INTO tb_follow_trade (deal_id, user_id, trader_uid,follow_position_id, order_id, position_id, contract_code, side, `offset`, price, open_avg_price, volume, trade_value, commission, back_profit, lever, trade_amount, profit, close_profit, order_type, `limit`, `stop`, trade_time, balance, available, total_profit, broke_price, buy_price, sell_price, is_trader,platform_id,entrust_order_id,trade_remark,blowing_fee,raw_blowing_fee,extra_id, ip_address, imei, order_client, raw_close_profit, raw_fee,is_maker) VALUES (:deal_id, :user_id, :trader_uid,:follow_position_id, :order_id, :position_id, :contract_code, :side, :offset, :price, :open_avg_price, :volume, :trade_value, :commission, :back_profit, :lever, :trade_amount, :profit, :close_profit, :order_type, :limit, :stop, :trade_time, :balance, :available, :total_profit, :broke_price, :buy_price, :sell_price, :is_trader,:platform_id,:entrust_order_id,:trade_remark,:blowing_fee,:raw_blowing_fee,:extra_id, :ip_address, :imei, :order_client,:raw_close_profit,:raw_fee,:is_maker)"
	if tx == nil {
		_, err = DefaultDB().NamedExec(str, t)
	} else {
		_, err = tx.NamedExec(str, t)
	}
	return
}

func ListFollowTrades(lastId int64) (list []proto.FollowTrade, err error) {
	str := "SELECT deal_id, user_id, trader_uid,follow_position_id, order_id, position_id, contract_code, side, `offset`, price, volume, trade_value, commission, back_profit, lever, trade_amount, profit, close_profit, order_type, `limit`, `stop`, trade_time, balance, available, total_profit, broke_price, buy_price, sell_price, is_trader,platform_id FROM tb_follow_trade  WHERE deal_id>? order by deal_id desc"
	err = DefaultDB().Select(&list, str, lastId)
	return
}

//插入交易员与跟单者清算
func InsertDealerAudit(tx *sqlx.Tx, r *proto.DealerAudit) (err error) {
	str := "INSERT INTO tb_dealer_audit ( trader_uid, follow_uid, side_type, wait_profit, wait_loss, actual_amount, own_at, create_time,withholding,rebate,start_audit_time,platform_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, r.TraderUid, r.FollowUid, r.SideType, r.WaitProfit, r.WaitLoss, r.ActualAmount, r.OwnAt, r.CreateTime, r.Withholding, r.Rebate, r.StartAuditTime, r.PlatformID)
		return
	}
	_, err = tx.Exec(str, r.TraderUid, r.FollowUid, r.SideType, r.WaitProfit, r.WaitLoss, r.ActualAmount, r.OwnAt, r.CreateTime, r.Withholding, r.Rebate, r.StartAuditTime, r.PlatformID)
	return
}

//查询交易者日统计表
func GetDealerRecordByOwnDay(tx *sqlx.Tx, traderUid int64, day time.Time) (r *proto.DealerDaySummary, err error) {
	trs := new(proto.DealerDaySummary)
	str := "SELECT trader_uid, profit, loss, get_profit, number, total_profit, total_capital, follow_count, profit_count, loss_count, trading_volume, own_at, create_time,platform_id FROM tb_dealer_day_summary WHERE trader_uid = ? and own_at=date(?) "
	if tx == nil {
		err = DefaultDB().Get(trs, str, traderUid, day)
	} else {
		err = tx.Get(trs, str, traderUid, day)
	}
	if err != nil {
		return nil, err
	}
	return trs, nil
}

//生成日统计表
func ProductDealerSummary(tx *sqlx.Tx) (err error) {
	str := "insert into tb_dealer_day_summary(trader_uid, profit, loss, get_profit, number, total_profit, total_capital, follow_count, profit_count, loss_count, trading_volume, own_at, create_time,platform_id) select trader_uid, 0, 0, 0, 0, 0, 0, follow_count, 0, 0, 0, now(), now(),platform_id from tb_dealer_day_summary where own_at=date(date_add(now(),interval -1 day ))"
	if tx == nil {
		_, err = DefaultDB().Exec(str)
		return
	}
	_, err = tx.Exec(str)
	return
}

//插入带单者日统计表
func InsertDealerDaySummary(tx *sqlx.Tx, r *proto.DealerDaySummary) (err error) {
	str := "INSERT INTO tb_dealer_day_summary (trader_uid, profit, loss, get_profit, `number`, total_profit, total_capital, follow_count, profit_count, loss_count, trading_volume, own_at, create_time,platform_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, r.TraderUid, r.Profit, r.Loss, r.GetProfit, r.Number, r.TotalProfit, r.TotalCapital, r.FollowCount, r.ProfitCount, r.LossCount, r.TradingVolume, r.OwnAt, r.CreateTime, r.PlatformID)
		return
	}
	_, err = tx.Exec(str, r.TraderUid, r.Profit, r.Loss, r.GetProfit, r.Number, r.TotalProfit, r.TotalCapital, r.FollowCount, r.ProfitCount, r.LossCount, r.TradingVolume, r.OwnAt, r.CreateTime, r.PlatformID)
	return
}

//修改带单者昨日累计佣金
func UpdateTraderActualProfit(tx *sqlx.Tx, traderUid int64, ownAt time.Time, profit decimal.Decimal) (err error) {
	str := "UPDATE tb_dealer_day_summary SET  get_profit=get_profit+? WHERE trader_uid = ? and own_at=date(?)"
	if tx == nil {
		_, err = DefaultDB().Exec(str, profit, traderUid, ownAt)
		return
	}
	_, err = tx.Exec(str, profit, traderUid, ownAt)
	return
}

//以增量修改带单日结算表，getprofit实际分佣金额
func UpdateDealerDaySummaryWithIncrement(tx *sqlx.Tx, r *proto.DealerDaySummary) (affectRows int64, err error) {
	r.GetProfit = decimal.Zero
	str := "UPDATE tb_dealer_day_summary SET  profit = profit+?, loss =loss+ ?, get_profit =get_profit+ ?, `number` = `number`+?, total_profit = total_profit+?, total_capital =total_capital+ ?, follow_count =follow_count+ ?, profit_count =profit_count+ ?, loss_count =loss_count+ ?, trading_volume =trading_volume+ ? WHERE trader_uid = ? and own_at=date(?)"
	var result sql.Result
	if tx == nil {
		result, err = DefaultDB().Exec(str, r.Profit, r.Loss, r.GetProfit, r.Number, r.TotalProfit, r.TotalCapital, r.FollowCount, r.ProfitCount, r.LossCount, r.TradingVolume, r.TraderUid, r.OwnAt)
		return
	} else {
		result, err = tx.Exec(str, r.Profit, r.Loss, r.GetProfit, r.Number, r.TotalProfit, r.TotalCapital, r.FollowCount, r.ProfitCount, r.LossCount, r.TradingVolume, r.TraderUid, r.OwnAt)
	}
	if err != nil {
		return 0, err
	}
	affectRows, err = result.RowsAffected()
	if err != nil {
		return 0, err
	}
	return
}

const _getDealerUser = "SELECT user_id, rebate, is_status, limit_number, create_time, IFNULL(audit_time, CONVERT(?, DATETIME)) AS 'audit_time', create_manage, total_amount, total_profit, total_loss, total_profit_count, total_loss_count, total_capital FROM tb_dealer_user WHERE user_id=?"

// 获取交易员信息
func GetDealerUser(tx *sqlx.Tx, userID int64) (*proto.DealerUser, error) {
	var err error
	var dealer proto.DealerUser
	if tx != nil {
		err = tx.Get(&dealer, _getDealerUser, define.DefaultTime, userID)
	} else {
		err = DefaultDB().Get(&dealer, _getDealerUser, define.DefaultTime, userID)
	}
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &dealer, err
}

func FollowAccountExist(uid int64, coinID int) bool {
	var count int8
	err := DefaultDB().Get(&count, "SELECT COUNT(1) FROM tb_follow_account WHERE user_id = ? AND currency_id = ?", uid, coinID)
	if err != nil {
		log.Error("FollowAccountExist db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
		return false
	}
	return count != 0
}

func CreateFollowAccount(uid int64, coinID int, platformID int) {
	var initBalance float64
	if conf.IsSimulate() {
		initBalance = conf.InitBalance()
	}
	_, err := DefaultDB().Exec("INSERT tb_follow_account SET user_id=?, currency_id=?, balance=?,platform_id=?", uid, coinID, initBalance, platformID)
	if err != nil {
		// 插入失败是钱包已经创建过,忽略
		log.Error("CreateFollowAccount insert db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
	}
}

//插入跟单账户持仓资金费用历史
func InsertFollowFundingHistory(tx *sqlx.Tx, f *proto.FollowFundingRateHistory) (err error) {
	str := "INSERT INTO tb_follow_funding_rate_history (id, user_id, position_id, amount, side, before_margin, funding_rate, index_price, create_time) VALUES (?,?,?,?,?,?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, f.ID, f.UserId, f.PositionId, f.Amount, f.Side, f.BeforeMargin, f.FundingRate, f.IndexPrice, f.CreateTime)
	}
	return
}

const queryDealerContractLeverMarkSql = "SELECT IFNULL(lever,0) AS 'buy_lever', IFNULL(lever,0) AS 'sell_lever' FROM tb_dealer_lever WHERE user_id=? AND contract_code=? ORDER BY update_time DESC LIMIT 1"

//获取用户模式及杠杆标记
func GetDealerContractMark(userId int64, contractCode string) (*proto.DealerLever, error) {
	mark := new(proto.DealerLever)
	err := DefaultDB().Get(mark, queryDealerContractLeverMarkSql, userId, contractCode)
	if err == sql.ErrNoRows {
		err = nil
	}
	return mark, err
}

const _updateDealerContractLever = "INSERT tb_dealer_lever SET user_id=?, contract_code=?, side=?, lever=?, update_time=? ON DUPLICATE KEY UPDATE lever=?, update_time=?"

// 设置或更新交易员默认杠杆
func UpdateDealerContractLever(tx *sqlx.Tx, userId int64, contractCode, side string, lever int) (err error) {
	now := time.Now().UnixNano() / 1e6 // 毫秒时间戳
	if tx != nil {
		_, err = tx.Exec(_updateDealerContractLever, userId, contractCode, side, lever, now, lever, now)
		return
	}
	_, err = DefaultDB().Exec(_updateDealerContractLever, userId, contractCode, side, lever, now, lever, now)
	return
}

const _insertFollowPositionHistory = "INSERT INTO tb_follow_position_history (id, user_id, contract_code, close_order_type, entrust_type, side, open_price, volume, trader_uid, close_price, follow_position_id, lever, init_margin, close_profit, open_commission, funding_amount, withholding, open_time, close_time, close_commission,platform_id) VALUES (:id, :user_id, :contract_code, :close_order_type, :entrust_type, :side, :open_price, :volume, :trader_uid, :close_price, :follow_position_id, :lever, :init_margin, :close_profit, :open_commission, :funding_amount, :withholding, :open_time, :close_time, :close_commission,:platform_id)"

// 入库历史带单跟单
func InsertFollowPositionHistory(tx *sqlx.Tx, record *proto.FollowPositionHistory) (err error) {
	if tx != nil {
		_, err = tx.NamedExec(_insertFollowPositionHistory, record)
	} else {
		_, err = DefaultDB().NamedExec(_insertFollowPositionHistory, record)
	}
	return
}

func InsertNoticeMessage(tx *sqlx.Tx, msg *proto.NotifyMessage) (err error) {
	str := "INSERT INTO tb_message (id, sender_id, sender_nickname, receiver_id, category, title, content, language_type, create_time) VALUES (:id,:sender_id,:sender_nickname,:receiver_id,:category,:title,:content,:language_type,:create_time)"
	if tx == nil {
		_, err = DefaultDB().NamedExec(str, msg)
	} else {
		_, err = tx.NamedExec(str, msg)
	}
	return
}

func ListDealerDaySummary(tx *sqlx.Tx, ownday string) (list []proto.DealerDaySummary, err error) {
	str := "SELECT trader_uid, profit, loss, get_profit, number, total_profit, total_capital, follow_count, profit_count, loss_count, trading_volume, own_at, create_time,platform_id FROM tb_dealer_day_summary where own_at=?"
	if tx == nil {
		err = DefaultDB().Select(&list, str, ownday)
	} else {
		err = tx.Select(&list, str, ownday)
	}
	return
}

func UpdateDealerTradeCount(tx *sqlx.Tx, traderUid int64, code string, count int) (err error) {
	str := "INSERT INTO tb_dealer_trade_count (trader_id, contract_code, trade_count) VALUES (?,?,?) ON DUPLICATE KEY UPDATE  trade_count =trade_count + ?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, traderUid, code, count, count)
	} else {
		_, err = tx.Exec(str, traderUid, code, count, count)
	}
	return
}

func GetFollowConfig(tx *sqlx.Tx) (*proto.FollowConf, error) {
	var err error
	var c proto.FollowConf
	str := "SELECT min_multiple, max_multiple, min_number, max_number, trader_total_position, follow_total_position, trader_lately_position, trader_lately_history, show_follow_users FROM tb_follow_config LIMIT 1"
	if tx == nil {
		err = DefaultDB().Get(&c, str)
	} else {
		err = tx.Get(&c, str)
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return &c, err
}

func UpdateFollowPositionWithCloseEntrust(tx *sqlx.Tx, id, volume int64) (err error) {
	str := "update tb_follow_position set volume_lock=volume_lock+? where id=? "
	if tx != nil {
		_, err = tx.Exec(str, volume, id)
	} else {
		_, err = DefaultDB().Exec(str, volume, id)
	}
	return
}
