package database

import (
	"database/sql"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

const _userAccountbase = "select account_id,account_type, user_id, currency_id, balance,lock_amount,available, diff,blowing_fee, total_profit,platform_id,warning_risk_rate,gift_available,gift_balance,total_gift_receive,gift_used,gift_lock_amount from tb_user_account "

// GetUserAccountByLock 获取交易账户资产并锁定
func GetUserAccountByLock(tx *sqlx.Tx, userId int64, currencyId int) (account *proto.Account, err error) {
	ac := new(proto.Account)
	str := _userAccountbase + " where user_id=? and currency_id=? for update"
	if tx != nil {
		err = tx.Get(ac, str, userId, currencyId)
	} else {
		err = DefaultDB().Get(ac, str, userId, currencyId)
	}
	if err != nil {
		return nil, err
	}
	account = ac
	return
}

//获取全仓账户并锁定
func GetUserAccountByUserIDWithLock(tx *sqlx.Tx, userId int64) (account *proto.Account, err error) {
	ac := new(proto.Account)
	str := _userAccountbase + " where user_id=? for update"
	if tx != nil {
		err = tx.Get(ac, str, userId)
	} else {
		err = DefaultDB().Get(ac, str, userId)
	}
	if err != nil {
		return nil, err
	}
	account = ac
	return
}

// 查询用户当前持仓模式
func GetCurrentAccountType(tx *sqlx.Tx, userId int64) (accountType int, err error) {
	str := "select account_type from tb_position where user_id=? limit 1"
	if tx != nil {
		err = tx.Get(&accountType, str, userId)
	} else {
		err = DefaultDB().Get(&accountType, str, userId)
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

// 查询指定仓模式计划单数量
func CountUserPlanByAccountType(tx *sqlx.Tx, userId int64, accountType define.AccountType) (count int, err error) {
	str := "select count(1) from tb_plan_order where user_id=? and account_type=? and `status`=?"
	if tx != nil {
		err = tx.Get(&count, str, userId, accountType, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(&count, str, userId, accountType, define.OrderConditionNotTrigger)
	}
	return
}

const _hasUserPlanByAccountType = "SELECT 1 FROM tb_plan_order WHERE user_id=? AND account_type=? AND `status`=? LIMIT 1"

// HasUserPlanByAccountType 查询是否有指定仓模式计划单(未完成计划委托不需要进行分仓判断)
func HasUserPlanByAccountType(tx *sqlx.Tx, userId int64, accountType define.AccountType) (bool, error) {
	var (
		flag int8
		err  error
	)
	if tx != nil {
		err = tx.Get(&flag, _hasUserPlanByAccountType, userId, accountType, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(&flag, _hasUserPlanByAccountType, userId, accountType, define.OrderConditionNotTrigger)
	}
	if err == sql.ErrNoRows {
		return false, nil
	}

	return flag == 1, err
}

// 查询指定仓模式持仓数量
func CountUserPositionByAccountType(tx *sqlx.Tx, userId int64, accountType define.AccountType) (count int, err error) {
	str := "select count(1) from tb_position where user_id=? and account_type=?"
	if tx != nil {
		err = tx.Get(&count, str, userId, accountType)
	} else {
		err = DefaultDB().Get(&count, str, userId, accountType)
	}
	return
}

const _hasUserPositionByAccountType = "select 1 from tb_position where user_id=? and account_type IN(?,?) LIMIT 1"

// HasUserPositionByAccountType 查询是否有指定仓位模式的持仓,不区分是否分仓
func HasUserPositionByAccountType(tx *sqlx.Tx, userId int64, accountType define.AccountType) (bool, error) {
	params := []interface{}{userId}
	if accountType == define.AccountTypeByFullHouse {
		params = append(params, define.AccountTypeByFullHouse, define.AccountTypeByFullHouseSplit)
	} else {
		params = append(params, define.AccountTypeByWareHouse, define.AccountTypeByWareHouseSplit)
	}

	var (
		flag int8
		err  error
	)
	if tx != nil {
		err = tx.Get(&flag, _hasUserPositionByAccountType, params...)
	} else {
		err = DefaultDB().Get(&flag, _hasUserPositionByAccountType, params...)
	}
	if err == sql.ErrNoRows {
		return false, nil
	}

	return flag == 1, err
}

const _getPositionOrderLever = "SELECT account_type, side, lever FROM tb_position WHERE user_id=? AND contract_code=? GROUP BY account_type, side"

// CheckPositionLever 查询持仓杠杆
func CheckPositionLever(tx *sqlx.Tx, userId int64, contractCode string) (bool, bool, int, int, int, error) {
	var err error
	var rows *sqlx.Rows
	if tx != nil {
		rows, err = tx.Queryx(_getPositionOrderLever, userId, contractCode)
	} else {
		rows, err = DefaultDB().Queryx(_getPositionOrderLever, userId, contractCode)
	}
	if err != nil {
		return false, false, 0, 0, 0, err
	}
	defer rows.Close()

	var (
		side                                        string
		longExist, shortExist                       bool
		accountType                                 define.AccountType
		tmpLever, crossLever, longLever, shortLever int
	)

	for rows.Next() {
		err = rows.Scan(&accountType, &side, &tmpLever)
		if err != nil {
			return false, false, 0, 0, 0, err
		}
		if accountType == define.AccountTypeByFullHouse || accountType == define.AccountTypeByFullHouseSplit {
			crossLever = tmpLever
		} else {
			if side == define.OrderBuy {
				longLever = tmpLever
			} else {
				shortLever = tmpLever
			}
		}

		if side == define.OrderBuy {
			longExist = true
		} else {
			shortExist = true
		}
	}
	return longExist, shortExist, crossLever, longLever, shortLever, nil
}

const _getPlanOrderLever = "SELECT account_type, side, lever FROM tb_plan_order WHERE user_id=? AND contract_code=? AND `status`=? GROUP BY account_type, side"

// CheckPlanLeverByCode 查询计划单杠杆
func CheckPlanLeverByCode(tx *sqlx.Tx, userId int64, contractCode string) (bool, bool, int, int, int, error) {
	var err error
	var rows *sqlx.Rows
	if tx != nil {
		rows, err = tx.Queryx(_getPlanOrderLever, userId, contractCode, define.OrderConditionNotTrigger)
	} else {
		rows, err = DefaultDB().Queryx(_getPlanOrderLever, userId, contractCode, define.OrderConditionNotTrigger)
	}
	if err != nil {
		return false, false, 0, 0, 0, err
	}
	defer rows.Close()

	var (
		side                                        string
		longExist, shortExist                       bool
		accountType                                 define.AccountType
		tmpLever, crossLever, longLever, shortLever int
	)

	for rows.Next() {
		err = rows.Scan(&accountType, &side, &tmpLever)
		if err != nil {
			return false, false, 0, 0, 0, err
		}
		if accountType == define.AccountTypeByFullHouse || accountType == define.AccountTypeByFullHouseSplit {
			crossLever = tmpLever
		} else {
			if side == define.OrderBuy {
				longLever = tmpLever
			} else {
				shortLever = tmpLever
			}
		}

		if side == define.OrderBuy {
			longExist = true
		} else {
			shortExist = true
		}
	}
	return longExist, shortExist, crossLever, longLever, shortLever, nil
}

const _getEntrustOrderLever = "SELECT account_type, `offset`, side, lever FROM tb_order WHERE user_id=? AND contract_code=? AND state IN(?,?) GROUP BY account_type, `offset`, side"

// CheckEntrustLeverByCode 查询委托单杠杆
func CheckEntrustLeverByCode(tx *sqlx.Tx, userId int64, contractCode string) (bool, bool, int, int, int, error) {
	var err error
	var rows *sqlx.Rows
	if tx != nil {
		rows, err = tx.Queryx(_getEntrustOrderLever, userId, contractCode, define.OrderStatusDefault, define.OrderStatusPart)
	} else {
		rows, err = DefaultDB().Queryx(_getEntrustOrderLever, userId, contractCode, define.OrderStatusDefault, define.OrderStatusPart)
	}
	if err != nil {
		return false, false, 0, 0, 0, err
	}
	defer rows.Close()

	var (
		side, offset                                string
		longExist, shortExist                       bool
		accountType                                 define.AccountType
		tmpLever, crossLever, longLever, shortLever int
	)

	for rows.Next() {
		err = rows.Scan(&accountType, &offset, &side, &tmpLever)
		if err != nil {
			return false, false, 0, 0, 0, err
		}
		if accountType == define.AccountTypeByFullHouse || accountType == define.AccountTypeByFullHouseSplit {
			crossLever = tmpLever
		} else {
			if offset == define.OffsetOpen {
				if side == define.OrderBuy {
					longLever = tmpLever
				} else {
					shortLever = tmpLever
				}
			} else {
				if side == define.OrderBuy {
					shortLever = tmpLever
				} else {
					longLever = tmpLever
				}
			}
		}

		if offset == define.OffsetOpen {
			if side == define.OrderBuy {
				longExist = true
			} else {
				shortExist = true
			}
		} else {
			if side == define.OrderBuy {
				shortExist = true
			} else {
				longExist = true
			}
		}
	}
	return longExist, shortExist, crossLever, longLever, shortLever, nil
}

// 查询计划单账户模式
func CountUserPlanAccountType(tx *sqlx.Tx, userId int64) (accountType int, err error) {
	str := "select account_type from tb_plan_order where user_id=? and `status`=? limit 1"
	if tx != nil {
		err = tx.Get(&accountType, str, userId, define.OrderConditionNotTrigger)
	} else {
		err = DefaultDB().Get(&accountType, str, userId, define.OrderConditionNotTrigger)
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

// 查询持仓账户模式
func CountUserPositionAccountType(tx *sqlx.Tx, userId int64) (accountType int, err error) {
	str := "select account_type from tb_position where user_id=? limit 1"
	if tx != nil {
		err = tx.Get(&accountType, str, userId)
	} else {
		err = DefaultDB().Get(&accountType, str, userId)
	}
	if err == sql.ErrNoRows {
		err = nil
	}
	return
}

// 查询计划单杠杆
func CountUserPlanLever(tx *sqlx.Tx, userId int64, accountType define.AccountType, contractCode string) (buyLever, sellLever int, err error) {
	if accountType == define.AccountTypeByFullHouse || accountType == define.AccountTypeByFullHouseSplit {
		// 不区分方向
		str := "select `lever` from tb_plan_order where user_id=? and `status`=? and contract_code=? and account_type=? limit 1"
		if tx != nil {
			err = tx.Get(&buyLever, str, userId, define.OrderConditionNotTrigger, contractCode, accountType)
		} else {
			err = DefaultDB().Get(&buyLever, str, userId, define.OrderConditionNotTrigger, contractCode, accountType)
		}
		if err == nil {
			sellLever = buyLever
		} else if err == sql.ErrNoRows {
			err = nil
		}
	} else {
		// 查询两个方向
		str := "(select side,`lever` from tb_plan_order where user_id=? and `status`=? and contract_code=? and side=? and account_type=? limit 1) union all (select side,`lever` from tb_plan_order where user_id=? and `status`=? and contract_code=? and side=? and account_type=? limit 1)"
		var rows *sql.Rows
		if tx != nil {
			rows, err = tx.Query(str, userId, define.OrderConditionNotTrigger, contractCode, define.OrderBuy, accountType, userId, define.OrderConditionNotTrigger, contractCode, define.OrderSell, accountType)
		} else {
			rows, err = DefaultDB().Query(str, userId, define.OrderConditionNotTrigger, contractCode, define.OrderBuy, accountType, userId, define.OrderConditionNotTrigger, contractCode, define.OrderSell, accountType)
		}
		if err != nil {
			return
		}
		defer rows.Close()

		var (
			lever int
			side  string
		)
		for rows.Next() {
			err = rows.Scan(&side, &lever)
			if err != nil {
				return
			}

			if side == define.OrderBuy {
				buyLever = lever
			} else {
				sellLever = lever
			}
		}
	}
	return
}

// 查询持仓杠杆
func CountUserPositionLever(tx *sqlx.Tx, userId int64, accountType define.AccountType, contractCode string) (buyLever, sellLever int, err error) {
	if accountType == define.AccountTypeByFullHouse || accountType == define.AccountTypeByFullHouseSplit {
		// 不区分方向
		str := "select `lever` from tb_position where user_id=? and contract_code=? and account_type=? limit 1"
		if tx != nil {
			err = tx.Get(&buyLever, str, userId, contractCode, accountType)
		} else {
			err = DefaultDB().Get(&buyLever, str, userId, contractCode, accountType)
		}
		if err == nil {
			sellLever = buyLever
		} else if err == sql.ErrNoRows {
			err = nil
		}
	} else {
		// 查询两个方向
		str := "(select side,`lever` from tb_position where user_id=? and contract_code=? and side=? and account_type=? limit 1) union all (select side,`lever` from tb_position where user_id=? and contract_code=? and side=? and account_type=? limit 1)"
		var rows *sql.Rows
		if tx != nil {
			rows, err = tx.Query(str, userId, contractCode, define.OrderBuy, accountType, userId, contractCode, define.OrderSell, accountType)
		} else {
			rows, err = DefaultDB().Query(str, userId, contractCode, define.OrderBuy, accountType, userId, contractCode, define.OrderSell, accountType)
		}
		if err != nil {
			return
		}
		defer rows.Close()

		var (
			lever int
			side  string
		)
		for rows.Next() {
			err = rows.Scan(&side, &lever)
			if err != nil {
				return
			}

			if side == define.OrderBuy {
				buyLever = lever
			} else {
				sellLever = lever
			}
		}
	}
	return
}

func CreateAllWallet(tx *sqlx.Tx, uid int64, platformId int) error {
	_, err := tx.Exec("INSERT INTO tb_user_wallet (user_id, currency_id, balance, withdraw_lock,platform_id) (SELECT ?, currency_id, 0, 0,? FROM tb_currency WHERE `status`=?)", uid, platformId, define.CoinStateNormal)
	return err
}

func WalletExist(uid int64, coinID int) bool {
	var count int8
	err := DefaultDB().Get(&count, "SELECT COUNT(1) FROM tb_user_wallet WHERE user_id = ? AND currency_id = ?", uid, coinID)
	if err != nil {
		log.Error("WalletExist db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
		return false
	}
	return count != 0
}

const _checkMissWalletCoin = "SELECT currency_id FROM tb_user_wallet WHERE user_id=?"

func CheckMissWalletCoin(uid int64, coinIDs ...int) ([]int, error) {
	if len(coinIDs) == 0 {
		return nil, nil
	}
	rows, err := DefaultDB().Queryx(_checkMissWalletCoin, uid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var cid int
	dict := make(map[int]define.Placeholder)
	for rows.Next() {
		err = rows.Scan(&cid)
		if err != nil {
			return nil, err
		}
		dict[cid] = define.PlaceholderEntity
	}

	var diff []int
	for i := range coinIDs {
		if _, ok := dict[coinIDs[i]]; !ok {
			diff = append(diff, coinIDs[i])
		}
	}
	return diff, nil
}

func CreateWallet(uid int64, coinID int, platformId int) {
	_, err := DefaultDB().Exec("INSERT tb_user_wallet SET user_id=?, currency_id=?, balance=0, withdraw_lock=0,platform_id=?", uid, coinID, platformId)
	if err != nil {
		// 插入失败是钱包已经创建过,忽略
		log.Error("CreateWallet insert db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
	}
}

func CreateAllAccount(tx *sqlx.Tx, uid int64, platformId int) error {
	var initBalance float64
	if conf.IsSimulate() {
		initBalance = conf.InitBalance()
	}
	_, err := tx.Exec("INSERT INTO tb_user_account (user_id, currency_id, balance, available,platform_id) (SELECT ?, currency_id, ?, ?,? FROM tb_margin_currency WHERE `status`=?)", uid, initBalance, initBalance, platformId, define.CoinStateNormal)
	return err
}

func CreateAllFollow(tx *sqlx.Tx, uid int64, platformId int) error {
	var initBalance float64
	if conf.IsSimulate() {
		initBalance = conf.InitBalance()
	}
	_, err := tx.Exec("INSERT INTO tb_follow_account (user_id, currency_id, balance,platform_id) (SELECT ?, currency_id, ?,? FROM tb_margin_currency WHERE `status`=?)", uid, initBalance, platformId, define.CoinStateNormal)
	return err
}

func AccountExist(uid int64, coinID int) bool {
	var count int8
	err := DefaultDB().Get(&count, "SELECT COUNT(1) FROM tb_user_account WHERE user_id = ? AND currency_id = ?", uid, coinID)
	if err != nil {
		log.Error("AccountExist db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
		return false
	}
	return count != 0
}

func CreateAccount(uid int64, coinID int, platformId int) {
	var initBalance float64
	if conf.IsSimulate() {
		initBalance = conf.InitBalance()
	}
	_, err := DefaultDB().Exec("INSERT tb_user_account SET user_id=?, currency_id=?, balance=?, available=?,platform_id=?", uid, coinID, initBalance, initBalance, platformId)
	if err != nil {
		// 插入失败是钱包已经创建过,忽略
		log.Error("CreateWallet insert db error", zap.Int64("uid", uid), zap.Int("coinID", coinID), zap.Error(err))
	}
}

const _userPosition = "select id, user_id, contract_code, side, price, volume, volume_lock,force_price, `limit`, stop, account_type, lever,init_margin,raw_margin, margin,adjust_margin, float_profit, available, profit_ratio, margin_ratio, contract_index, buy_price, sell_price, commission,platform_id,margin_gift,raw_margin_gift,cur_open_time,release_type from tb_position "

//获取用户持有
func GetUserPositionForLock(tx *sqlx.Tx, userId int64, contractCode string, side string) (u []proto.UserPosition, err error) {
	str := _userPosition + " where user_id=? and contract_code=? and side=? order by volume desc"
	if tx != nil {
		str += " for update"
		err = tx.Select(&u, str, userId, contractCode, side)
	} else {
		err = DefaultDB().Select(&u, str, userId, contractCode, side)
	}
	if err != nil {
		return
	}
	return
}

//获取用户持仓(使用持仓方向)
func GetUserPositionByPositionSide(tx *sqlx.Tx, userId int64, contractCode, side string) (u []proto.UserPosition, err error) {
	str := _userPosition + " where user_id=? and contract_code=? and side=? order by volume desc for update"
	err = tx.Select(&u, str, userId, contractCode, side)
	if err != nil {
		log.Infof("GetUserPositionByPositionSide fail,userId:%v,code: %s,side；%v,err:%v", userId, contractCode, side, err)
		return
	}
	return
}

func GetUserPositionNumsBySide(tx *sqlx.Tx, userId int64, contractCode, side string) (nums int, err error) {
	str := "select count(1) from tb_position where user_id=? and contract_code=? and side=? "
	err = tx.Get(&nums, str, userId, contractCode, side)
	if err != nil {
		log.Infof("GetUserPositionNumsBySide fail,userId:%v,code: %s,side；%v,err:%v", userId, contractCode, side, err)
		return
	}
	return
}

// 获取持仓的合约
func GetUserPositionContractCode(userId, positionId int64) (string, error) {
	var code string
	err := DefaultDB().Get(&code, "SELECT contract_code FROM tb_position WHERE id=? AND user_id=? LIMIT 1", positionId, userId)
	return code, err
}

//获取用户持仓
func GetUserPositionByPositionId(tx *sqlx.Tx, userId int64, positionId int64) (u *proto.UserPosition, err error) {
	p := new(proto.UserPosition)
	str := _userPosition + " where user_id=? and id=? for update"
	err = tx.Get(p, str, userId, positionId)
	if err != nil {
		log.Infof("GetUserPositionByPositionId fail,userId:%v,positionId；%v,err:%v", userId, positionId, err)
		return
	}
	u = p
	return
}

//锁定用户全仓持仓
func ListUserFullPositionByUserIdWithLock(tx *sqlx.Tx, uid int64) (list []proto.UserPosition, err error) {
	str := _userPosition + " where  user_id=? and (account_type=? or account_type=?)  for update"
	err = tx.Select(&list, str, uid, define.AccountTypeByFullHouse, define.AccountTypeByFullHouseSplit)
	if err != nil {
		log.Infof("GetUserPositionByUserIdWithLock fail,userId:%v,user_id；%v,err:%v", uid, err)
		return
	}
	return
}

func ListUserPositionByUserIdWithLock(tx *sqlx.Tx, uid int64) (list []proto.UserPosition, err error) {
	str := _userPosition + " where  user_id=?  for update"
	err = tx.Select(&list, str, uid)
	if err != nil {
		log.Infof("ListUserPositionByUserIdWithLock fail,userId:%v,user_id；%v,err:%v", uid, err)
		return
	}
	return
}

// 获取用户是否还有持仓
func CheckUserPositionExist(tx *sqlx.Tx, uid int64) (exist bool, err error) {
	var count int
	err = tx.Get(&count, "SELECT 1 FROM tb_position WHERE user_id=? LIMIT 1", uid)
	if err == sql.ErrNoRows {
		return false, nil
	}
	return true, err
}

//锁定指定持仓
func GetUserPositionByPositionIdWithLock(tx *sqlx.Tx, positionId int64) (u *proto.UserPosition, err error) {
	p := new(proto.UserPosition)
	str := _userPosition + " where  id=? for update"
	err = tx.Get(p, str, positionId)
	if err != nil {
		log.Infof("GetUserPositionByPositionId fail,userId:%v,positionId；%v,err:%v", positionId, err)
		return
	}
	u = p
	return
}

func GetUserPositionByPositionIdNoLock(tx *sqlx.Tx, userId int64, positionId int64) (u *proto.UserPosition, err error) {
	p := new(proto.UserPosition)
	str := _userPosition + " where user_id=? and id=? "
	if tx != nil {
		err = tx.Get(p, str, userId, positionId)
	} else {
		err = DefaultDB().Get(p, str, userId, positionId)
	}
	if err != nil {
		log.Infof("GetUserPositionByPositionId fail,userId:%v,positionId；%v,err:%v", userId, positionId, err)
		if err == sql.ErrNoRows {
			err = nil
			return
		}
		return
	}
	u = p
	return
}

//根据positionId 查看用户持仓
func GetPositionByPositionId(tx *sqlx.Tx, positionId int64) (u *proto.UserPosition, err error) {
	p := new(proto.UserPosition)
	str := _userPosition + " where id=?"
	if tx != nil {
		err = tx.Get(p, str, positionId)

	} else {
		err = DefaultDB().Get(p, str, positionId)
	}
	if err != nil {
		log.Infof("GetPositionByPositionId fail,positionId；%v,err:%v", positionId, err)
		return
	}
	u = p
	return
}

const _poistionByCoin = "select tb.id, tb.user_id, tb.contract_code, tb.side, tb.price, tb.volume,tb.volume_lock, tb.force_price, tb.`limit`, tb.stop, tb.account_type,tb.init_margin,tb.adjust_margin, tb.lever,tb.margin, tb.float_profit, tb.available, tb.profit_ratio, tb.margin_ratio, tb.contract_index, tb.buy_price, tb.sell_price, tb.commission,tb.platform_id from tb_position tb left join tb_contract tc on tb.contract_code = tc.contract_code "

//获取指定币种用户持仓
func GetPositionByUserCoinId(tx *sqlx.Tx, userId int64, coinId int) (list []proto.UserPosition, err error) {
	str := _poistionByCoin + " where user_id=? and tc.coin_id=?"
	if tx != nil {
		err = tx.Select(&list, str, userId, coinId)
		return
	}
	err = DefaultDB().Select(&list, str, userId, coinId)
	return
}

//获取指定币种用户持仓
func GetPositionsByCoinId(tx *sqlx.Tx, coinId int) (list []proto.UserPosition, err error) {
	str := _poistionByCoin + " where tc.coin_id=?"
	if tx != nil {
		err = tx.Select(&list, str, coinId)
		return
	}
	err = DefaultDB().Select(&list, str, coinId)
	return
}

func UpdateUsrFullStop(tx *sqlx.Tx, positionId int64, limit, stop float64) (err error) {
	//log.Infof("pid:%v,l:%v，s:%v", positionId, limit, stop)
	str := "update tb_position set `limit`=?,stop=? where id=?"
	_, err = tx.Exec(str, limit, stop, positionId)
	return
}

func UpdateUsrPositonFull(tx *sqlx.Tx, positionId int64, limit float64) (err error) {
	//log.Infof("pid:%v,l:%v，s:%v", positionId, limit, stop)
	str := "update tb_position set `limit`=? where id=?"
	_, err = tx.Exec(str, limit, positionId)
	return
}

func UpdateUsrPostionForFunding(tx *sqlx.Tx, positionId int64, margin float64) (err error) {
	//log.Infof("pid:%v,l:%v，s:%v", positionId, limit, stop)
	str := "update tb_position set margin=? where id=?"
	_, err = tx.Exec(str, margin, positionId)
	return
}

func UpdateUsrPositonStop(tx *sqlx.Tx, positionId int64, stop float64) (err error) {
	//log.Infof("pid:%v,l:%v，s:%v", positionId, limit, stop)
	str := "update tb_position set stop=? where id=?"
	_, err = tx.Exec(str, stop, positionId)
	return
}

func GetUserPositionByContract(tx *sqlx.Tx, userId int64, contractCode string) (list []proto.UserPosition, err error) {
	str := _userPosition + " where user_id=? and contract_code=?"
	if tx == nil {
		err = DefaultDB().Select(&list, str, userId, contractCode)
	} else {
		err = tx.Select(&list, str, userId, contractCode)
	}
	return
}

//func GetUserPositionByContractAndSide(tx *sqlx.Tx, userId int64, contractCode string,side string) (u *proto.UserPosition, err error) {
//	str := _userPosition + " where user_id=? and contract_code=?"
//	if tx == nil {
//		err = DefaultDB().Select(&list, str, userId, contractCode)
//	} else {
//
//		err = tx.Select(&list, str, userId, contractCode)
//	}
//	return
//}

func GetUserPositions(tx *sqlx.Tx, page, size int) (list []proto.UserPosition, err error) {
	str := "select tp.id, tp.user_id, tp.contract_code, side,tp.raw_margin,tp.price, tp.volume,tp.volume_lock, tp.force_price, tp.`limit`, tp.stop, tp.account_type, tp.lever, tp.margin,tp.init_margin,tp.adjust_margin, tp.float_profit, tp.available, tp.profit_ratio, tp.margin_ratio, tp.contract_index, tp.buy_price, tp.sell_price, tp.commission,tc.par_value,tp.platform_id,tp.margin_gift,release_type from tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code limit ?,?"
	if tx == nil {
		err = DefaultDB().Select(&list, str, page*size, size)
	} else {
		err = tx.Select(&list, str, page*size, size)
	}
	return
}

func GetUserPositionsByUserID(tx *sqlx.Tx, userId int64) (list []proto.UserPosition, err error) {
	str := "select tp.id, tp.user_id, tp.contract_code, side,tp.raw_margin, tp.price, tp.volume,tp.volume_lock, tp.force_price, tp.`limit`, tp.stop, tp.account_type, tp.lever, tp.margin,tp.init_margin,tp.adjust_margin, tp.float_profit, tp.available, tp.profit_ratio, tp.margin_ratio, tp.contract_index, tp.buy_price, tp.sell_price, tp.commission,tc.par_value,tp.platform_id,margin_gift from tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code  where tp.user_id=?"
	if tx == nil {
		err = DefaultDB().Select(&list, str, userId)
	} else {
		err = tx.Select(&list, str, userId)
	}
	return
}

func GetUserPositionsByUserIDWithLock(tx *sqlx.Tx, userId int64) (list []proto.UserPosition, err error) {
	str := "select tp.id, tp.user_id, tp.contract_code, side,tp.raw_margin, tp.price, tp.volume,tp.volume_lock, tp.force_price, tp.`limit`, tp.stop, tp.account_type, tp.lever, tp.margin,tp.init_margin,tp.adjust_margin, tp.float_profit, tp.available, tp.profit_ratio, tp.margin_ratio, tp.contract_index, tp.buy_price, tp.sell_price, tp.commission,tc.par_value,tp.platform_id,tp.margin_gift from tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code  where tp.user_id=? for update"
	if tx != nil {
		err = tx.Select(&list, str, userId)
	} else {
		err = DefaultDB().Select(&list, str, userId)
	}
	return
}

func GetUserPositionsByContractCode(code string) (list []proto.UserPosition, err error) {
	str := _userPosition + " contract_code=?"
	err = DefaultDB().Select(&list, str, code)
	return
}

func InsertUserPosition(tx *sqlx.Tx, p *proto.UserPosition) (err error) {
	str := "insert into tb_position(id, user_id, contract_code, side, price, volume,volume_lock, force_price, `limit`, stop, account_type, lever,raw_margin, margin,init_margin,adjust_margin, float_profit, available, profit_ratio, margin_ratio, contract_index, buy_price, sell_price,commission,open_fee,platform_id,margin_gift,raw_margin_gift,cur_open_time,release_type) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"
	_, err = tx.Exec(str, p.Id, p.UserId, p.ContractCode, p.Side, p.Price, p.Volume, p.VolumeLock, p.ForcePrice, p.Limit, p.Stop, p.AccountType, p.Lever, p.RawMargin, p.Margin, p.InitMargin, p.AdjustMargin, p.FloatProfit, p.Available, p.ProfitRatio, p.MarginRatio, p.ContractIndex, p.BuyPrice, p.SellPrice, p.Commission, p.OpenFee, p.PlatformID, p.MarginGift, p.RawMarginGift, p.CurOpenTime, p.ReleaseType)
	return
}

// UpdatePosition 更新用户持仓
func UpdatePosition(tx *sqlx.Tx, u *proto.UserPosition) (err error) {
	str := "update tb_position set price=?,volume=?,volume_lock=?,force_price=?,raw_margin=?,margin=?,init_margin=?,adjust_margin=?,float_profit=?,available=?,profit_ratio=?,margin_ratio=?,contract_index=?,buy_price=?,sell_price=?,commission=?,raw_margin=?,open_fee=?,margin_gift=?,raw_margin_gift=?,cur_open_time=? where id=?"
	_, err = tx.Exec(str, u.Price, u.Volume, u.VolumeLock, u.ForcePrice, u.RawMargin, u.Margin, u.InitMargin, u.AdjustMargin, u.FloatProfit, u.Available, u.ProfitRatio, u.MarginRatio, u.ContractIndex, u.BuyPrice, u.SellPrice, u.Commission, u.RawMargin, u.OpenFee, u.MarginGift, u.RawMarginGift, u.CurOpenTime, u.Id)
	return
}

func UpdatePositionWithCloseEntrust(tx *sqlx.Tx, id, volume int64) (err error) {
	str := "update tb_position set volume_lock=volume_lock+? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, volume, id)
	} else {
		_, err = DefaultDB().Exec(str, volume, id)
	}
	return
}

func UpdatePositionWithCloseEntrustFor0ver(tx *sqlx.Tx, id int64, volume int64) (err error) {
	str := "update tb_position set volume_lock=volume_lock-? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, volume, id)
	} else {
		_, err = DefaultDB().Exec(str, volume, id)
	}
	return
}

func UpdateFollowPositionWithCloseEntrustFor0ver(tx *sqlx.Tx, id int64, volume int64) (err error) {
	str := "update tb_follow_position set volume_lock=volume_lock-? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, volume, id)
	} else {
		_, err = DefaultDB().Exec(str, volume, id)
	}
	return
}

func UpdatePositionForcePrice(id int64, forcePrice float64) (err error) {
	str := "update tb_position set force_price=? where id=?"
	_, err = DefaultDB().Exec(str, forcePrice, id)
	return
}

func UpdateContractPositionForcePrice(userId int64, code string, forcePrice float64) (err error) {
	str := "update tb_position set force_price=? where user_id=? and contract_code=?"
	_, err = DefaultDB().Exec(str, forcePrice, userId, code)
	return
}

func UpdatePositionCustom(tx *sqlx.Tx, initMargin, margin, adjust float64, u *proto.UserPosition) (err error) {
	log.Debugf("UpdatePositionCustom,initMargin；%v,margin；%v,adjust:%v,u:%+v", initMargin, margin, adjust, *u)
	str := "update tb_position set force_price=?,margin=margin+?,init_margin=init_margin+?,adjust_margin=adjust_margin+?,float_profit=?,profit_ratio=?,margin_ratio=? where id=? and margin>=? and init_margin>=? and adjust_margin>=?"
	r, err := tx.Exec(str, u.ForcePrice, margin, initMargin, adjust, u.FloatProfit, u.ProfitRatio, u.MarginRatio, u.Id, math.Abs(margin), math.Abs(initMargin), math.Abs(adjust))
	if err != nil {
		log.Errorf("UpdatePositionCustom fail,%v", err)
		return
	}
	n, err := r.RowsAffected()
	if n == 0 {
		err = errors.New("update affect row nums:0")
	}
	return
}

//func UpdateUserPosition(tx *sqlx.Tx, side string, volume int, price float64, nominalPrice float64, forcePrice float64, initMargin float64, fee float64, profitRatio float64, roic float64, floatProfit float64, userId int64, code string) (err error) {
//	str := "update tb_position set side=?,volume=?,price=?,nominal_close_price=?,force_close_price=?,init_margin=?,commission=?,profit_ratio=?,float_profit=?,roic=? where user_id=? and contract_code=?"
//	_, err = tx.Exec(str, side, volume, price, nominalPrice, forcePrice, initMargin, fee, profitRatio, floatProfit, roic, userId, code)
//	return
//}

func UpdateForcePrice(tx *sqlx.Tx, positonId int64, forcePrice float64) (err error) {
	str := "update tb_position set force_price=? where id=?"
	if tx != nil {
		_, err = tx.Exec(str, forcePrice, positonId)
	} else {
		_, err = DefaultDB().Exec(str, forcePrice, positonId)

	}
	return
}

func UpdateFloatProfit(tx *sqlx.Tx, userId int64, code string, floatProft float64) (err error) {
	str := "update tb_position set float_profit=? where user_id=? and contract_code=?"
	_, err = tx.Exec(str, userId, code)
	return
}

func DelUserPosition(tx *sqlx.Tx, positionId int64) (err error) {
	str := "delete from tb_position where id=?"
	_, err = tx.Exec(str, positionId)
	return
}

const _getAssetDetail = "SELECT coin_id, coin_name, balance, position_margin,platform_id FROM tb_user_wallet WHERE user_id=? AND coin_id=?"

func GetAssetDetail(userID int64, coinID int) (*proto.ApiAsset, error) {
	asset := new(proto.ApiAsset)
	err := DefaultDB().Get(&asset, _getAssetDetail, userID, coinID)
	if err != nil {
		log.Error("GetAssetDetail db error", zap.Int64("userID", userID), zap.Int("coinID", coinID), zap.Error(err))
	}
	return asset, err
}

func GetUserWallet(userID int64, coinID int) (*proto.UserWallet, error) {
	wallet := new(proto.UserWallet)
	err := DefaultDB().Get(wallet, "SELECT user_wallet_id, user_id, currency_id, balance, withdraw_lock, address, platform_id FROM tb_user_wallet WHERE user_id = ? AND currency_id = ? ", userID, coinID)
	return wallet, err
}

func GetUserWalletWithLock(tx *sqlx.Tx, userID int64, coinID int) (*proto.UserWallet, error) {
	wallet := new(proto.UserWallet)
	err := tx.Get(wallet, "SELECT user_wallet_id, user_id, currency_id, balance, withdraw_lock, address, platform_id FROM tb_user_wallet WHERE user_id = ? AND currency_id = ?  FOR UPDATE", userID, coinID)
	return wallet, err
}

//插入资金费率历史
func InsertFundingRateHitory(tx *sqlx.Tx, h *proto.FundingRateHistory) (err error) {
	str := "insert into tb_funding_rate_history(contract_code, funding_rate, estimated_rate, created_by) values (?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, h.ContractCode, h.FundingRate, h.EstimatedRate, h.CreatedBy)
		return
	}
	_, err = DefaultDB().Exec(str, h.ContractCode, h.FundingRate, h.EstimatedRate, h.CreatedBy)
	return
}

//资产费用总和统计
func InsertFundingFee(tx *sqlx.Tx, h *proto.FundingFee) (err error) {
	str := "insert into tb_funding_fee(contract_code, coin_id, coin_name, funding_rate, `interval`, buy_to_sell, sell_to_buy, total_amount,created_by,position_value_diff,funding_type) VALUES (?,?,?,?,?,?,?,?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, h.ContractCode, h.CoinId, h.CoinName, h.FundingRate, h.Interval, h.BuyToSell, h.SellToBuy, h.TotalAmount, h.CreatedBy, h.PositionValueDiff, h.FundingType)
		return
	}
	_, err = DefaultDB().Exec(str, h.ContractCode, h.CoinId, h.CoinName, h.FundingRate, h.Interval, h.BuyToSell, h.SellToBuy, h.TotalAmount, h.CreatedBy, h.PositionValueDiff, h.FundingType)
	return
}

//增加资产费用详情
func InsertFundingFeeDetail(tx *sqlx.Tx, h *proto.FundingFeeDetail) (err error) {
	str := "insert into tb_funding_fee_detail(user_id, contract_code, coin_id, coin_name, funding_rate, amount, before_balance, before_close_profit, after_balance, after_close_profit, created_by,position_value_diff,funding_type) values (?,?,?,?,?,?,?,?,?,?,?,?,?)"
	if tx != nil {
		_, err = tx.Exec(str, h.UserId, h.ContractCode, h.CoinId, h.CoinName, h.FundingRate, h.Amount, h.BeforeBalance, h.BeforeCloseProfit, h.AfterBalance, h.AfterCloseProfit, h.CreatedBy, h.BuyPosiVolume, h.FundingType)
		return
	}
	_, err = DefaultDB().Exec(str, h.UserId, h.ContractCode, h.CoinId, h.CoinName, h.FundingRate, h.Amount, h.BeforeBalance, h.BeforeCloseProfit, h.AfterBalance, h.AfterCloseProfit, h.CreatedBy, h.BuyPosiVolume, h.FundingType)
	return
}

func UpdateFloatProfitAndProfitRate(marketPrice decimal.Decimal, contractCode string) (err error) {
	str := `update tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code left join tb_user_account ta on tc.coin_id=ta.currency_id and ta.user_id=tp.user_id
set float_profit=volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?),
    profit_ratio=volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)/tp.init_margin,
    margin_ratio=if(tp.account_type=1 or tp.account_type=5,if(tp.side = 'B', power(?,2)-power(tp.force_price,2),power(?,2)-power(tp.force_price,2))/(power(tp.price,2)-power(tp.force_price,2)),(tp.margin + volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)) /(volume * tc.par_value * if(tp.side = 'B', ?, ?))),
    tp.available=ta.available
     where tp.contract_code = ? and 1=1`
	_, err = DefaultDB().Exec(str, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, marketPrice, contractCode)
	//_, err = DefaultDB().Exec(str, sellPrice, buyPrice, sellPrice, buyPrice, sellPrice, buyPrice, sellPrice, buyPrice, sellPrice, buyPrice, contractCode)
	return
}

func UpdateFloatProfitAndProfitRateByFollow(buyPrice, sellPrice float64, contractCode string) (err error) {
	str := `update tb_follow_position tp left join tb_contract tc on tp.contract_code = tc.contract_code left join tb_follow_account ta on tc.coin_id=ta.currency_id and ta.user_id=tp.user_id
set float_profit=volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?),
    profit_ratio=volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)/tp.init_margin,
    margin_ratio=(tp.margin + volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)) /(volume * tc.par_value * if(tp.side = 'B', ?, ?)),
    tp.available=ta.balance
where tp.contract_code = ? and 1=1`
	_, err = DefaultDB().Exec(str, sellPrice, buyPrice, sellPrice, buyPrice, sellPrice, buyPrice, sellPrice, buyPrice, contractCode)
	return
}

//获取指定币种的交易对
func GetMarginCoinContracts(tx *sqlx.Tx, coinId int) (list []string, err error) {
	str := "select contract_code from tb_contract where coin_id=? and !delisted"
	if tx != nil {
		err = tx.Select(&list, str, coinId)
		return
	}
	err = DefaultDB().Select(&list, str, coinId)
	return
}

func GetCoinMargin(tx *sqlx.Tx, userId int64, contractCode string, buyPrice, sellPrice float64) (margin string, err error) {
	str := "select ifnull(sum(tp.margin),0) as margin,ifnull(sum(volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?))) from  tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code where tp.contract_code=? and tp.user_id=?"
	if tx != nil {
		err = tx.Get(&margin, str, sellPrice, buyPrice, contractCode, userId)
		return
	}
	err = DefaultDB().Get(&margin, str, sellPrice, buyPrice, contractCode, userId)
	return
}

func GetCoinMarginStat(tx *sqlx.Tx, userId int64, contractCode string, marketPrice decimal.Decimal) (margin proto.AssetStat, err error) {
	str := "select ifnull(sum(tp.margin),0) as margin,ifnull(sum(tp.margin_gift),0) as margin_gift,ifnull(sum(tp.commission),0) as commission,ifnull(sum(volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)),0) as float_profit from  tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code where tp.contract_code=? and tp.user_id=?"
	if tx != nil {
		err = tx.Get(&margin, str, marketPrice, marketPrice, contractCode, userId)
		return
	}
	err = DefaultDB().Get(&margin, str, marketPrice, marketPrice, contractCode, userId)
	return
}

func GetFollowCoinMarginStat(tx *sqlx.Tx, userId int64, contractCode string, marketPrice decimal.Decimal) (margin proto.AssetStat, err error) {
	str := "select ifnull(sum(tp.margin),0) as margin,ifnull(sum(tp.commission),0) as commission,ifnull(sum(volume * tc.par_value * if(tp.side = 'B', ? - tp.price, tp.price - ?)),0) as float_profit from  tb_follow_position tp left join tb_contract tc on tp.contract_code = tc.contract_code where tp.contract_code=? and tp.user_id=?"
	if tx != nil {
		err = tx.Get(&margin, str, marketPrice, marketPrice, contractCode, userId)
		return
	}
	err = DefaultDB().Get(&margin, str, marketPrice, marketPrice, contractCode, userId)
	return
}

func GetCoinInitMarginStat(tx *sqlx.Tx, userID int64, coinID int) (margins []proto.InitMarginStat, err error) {
	str := "select tp.contract_code, tp.init_margin, tp.lever from tb_position tp right join tb_contract tc on tc.coin_id = ? and tp.contract_code = tc.contract_code where user_id = ?"
	if tx != nil {
		err = tx.Select(&margins, str, coinID, userID)
	} else {
		err = DefaultDB().Select(&margins, str, coinID, userID)
	}
	return
}

func GetFollowCoinInitMarginStat(tx *sqlx.Tx, userID int64, coinID int) (margins []proto.InitMarginStat, err error) {
	str := "select tp.contract_code, tp.init_margin, tp.lever from tb_follow_position tp right join tb_contract tc on tc.coin_id = ? and tp.contract_code = tc.contract_code where user_id = ?"
	if tx != nil {
		err = tx.Select(&margins, str, coinID, userID)
	} else {
		err = DefaultDB().Select(&margins, str, coinID, userID)
	}
	return
}

func UpdateContractForcePrice(tx *sqlx.Tx, userId int64, coinId int, available float64) (err error) {
	str := `update tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code 
set force_price=truncate(case tp.side when 'B' then if(tp.price-?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value)>0,tp.price-?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value),0) else if((tp.price+?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker)>0,(tp.price+?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker),0) end ,tc.digit),tp.available=?
where tc.coin_id=? and tp.user_id=?`
	if tx != nil {
		_, err = tx.Exec(str, available, available, available, available, available, coinId, userId)
		return
	}
	_, err = DefaultDB().Exec(str, available, available, available, available, available, coinId, userId)
	return
}

//update tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code
//	set force_price=case tp.side when 'B' then if(tp.price-?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value)>0,tp.price-?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value),0) else if((tp.price+?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker)>0,(tp.price+?*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker),0) end ,tp.available=?
//	where tc.coin_id=? and tp.user_id=?  ta.available*

////根据币资产，修改持仓强平价
//func UpdateCoinForcePrice(tx *sqlx.Tx, userId int64, coinId int) (err error) {
//	str := `update tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code left join tb_user_account ta on tc.coin_id=ta.currency_id and ta.user_id=tp.user_id
//	set force_price=truncate(case tp.side when 'B' then if(tp.price-ta.available*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value)>0,tp.price-ta.available*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value),0) else if((tp.price+ta.available*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker)>0,(tp.price+ta.available*(1-tc.maintenance_margin_ratio)/(tp.volume*tc.par_value))/(1+tc.fee_taker),0) end ,tc.digit),tp.available=ta.available
//	where tc.coin_id=? and tp.user_id=?`
//	if tx != nil {
//		_, err = tx.Exec(str, coinId, userId)
//		return
//	}
//	_, err = DefaultDB().Exec(str, coinId, userId)
//	return
//}

//func UpdateFloatProfitAndProfitRate(buyPrice,sellPrice,indexPrice float64, dealPrice float64, contractCode string) (err error) {
//	str := `
//update tb_position tp left join tb_contract tc on tp.contract_code = tc.contract_code
//set float_profit=volume*tc.par_value*if(tp.side='B',?-tp.price,tp.price-?),profit_ratio=if(tp.side='B',1,-1)*(1-tp.price/?),margin_ratio=if(tp.side='B',1,-1)*(1-tp.price/?)*tp.lever where tp.contract_code=?`
//	_, err = DefaultDB().Exec(str,  sellPrice,buyPrice, dealPrice, dealPrice, contractCode)
//	return
//}

func HasAssertRecord(tx *sqlx.Tx, id string, billType define.WalletBillType, status define.WalletBillState) (bool, error) {
	var (
		err   error
		exist bool
	)
	if status == define.WalletBillStateAll {
		err = tx.Get(&exist, "select if(count(bill_id) > 0, true, false) from tb_user_wallet_bill where order_id=? and type=?", id, billType)
	} else {
		err = tx.Get(&exist, "select if(count(bill_id) > 0, true, false) from tb_user_wallet_bill where order_id=? and type=? and status=?", id, billType, status)
	}
	return exist, err
}

const _insertWalletBill = "INSERT tb_user_wallet_bill SET bill_id=:bill_id, user_id=:user_id, `type`=:type, `status`=:status, currency_id=:currency_id, currency_name=:currency_name, tag=:tag, amount=:amount, from_addr=:from_addr, to_addr=:to_addr, tx=:tx, confirm_num=:confirm_num, balance=:balance, lock_amount=:lock_amount, commission=:commission, remarks=:remarks, created_time=:created_time, order_id=IF(:order_id!='',:order_id,NULL), ip_address=:ip_address, imei=:imei, order_client=:order_client, approved_time=:approved_time,platform_id=:platform_id"

func InsertWalletBill(tx *sqlx.Tx, bill *proto.UserWalletBill) (int64, error) {
	result, err := tx.NamedExec(_insertWalletBill, bill)
	if err != nil {
		return 0, err
	}
	id, err := result.LastInsertId()
	return id, err
}

const _getWalletBillSql = "SELECT bill_id, user_id, type, `status`, currency_id, currency_name, tag, amount, from_addr, to_addr, tx, confirm_num, balance, lock_amount, commission, remarks, created_time, IFNULL(approver,'') approver, IFNULL(approved_time, now()) approved_time, IFNULL(order_id, '') order_id, ip_address, imei, order_client,platform_id FROM tb_user_wallet_bill"

func GetBillByOrderIDLock(tx *sqlx.Tx, orderID string, billType define.WalletBillType) (*proto.UserWalletBill, error) {
	record := new(proto.UserWalletBill)
	str := _getWalletBillSql + " WHERE order_id=? AND type=? FOR UPDATE"
	err := tx.Get(record, str, orderID, billType)
	if err == nil {
		record.CreatedTimeUnix = record.CreatedTime.Unix()
		record.ApprovedTimeUnix = record.ApprovedTime.Unix()
	}
	return record, err
}

func GetBillByBillIDLock(tx *sqlx.Tx, billID int64) (*proto.UserWalletBill, error) {
	record := new(proto.UserWalletBill)
	str := _getWalletBillSql + " WHERE bill_id=? FOR UPDATE"
	err := tx.Get(record, str, billID)
	if err == nil {
		record.CreatedTimeUnix = record.CreatedTime.Unix()
		record.ApprovedTimeUnix = record.ApprovedTime.Unix()
	}
	return record, err
}

func UpdateUserWalletAddress(userID int64, coinID int, address string) error {
	_, err := DefaultDB().Exec("UPDATE tb_user_wallet SET address = ? WHERE user_id = ? AND currency_id = ?", address, userID, coinID)
	return err
}

func UpdateWalletByNewData(tx *sqlx.Tx, account *proto.UserWallet) error {
	_, err := tx.Exec("UPDATE tb_user_wallet SET balance=?, withdraw_lock=? WHERE user_wallet_id=?", account.Balance, account.WithdrawLock, account.UserWalletId)
	return err
}

func ModifyMarginByNewData(tx *sqlx.Tx, positionID int64, newMargin, newAdjustMargin, forcePrice float64, rawMargin, marginGift, rawMarginGift decimal.Decimal) error {
	_, err := tx.Exec("UPDATE tb_position SET adjust_margin=?, margin=?,raw_margin=?, margin_gift=?, raw_margin_gift=?, force_price=? WHERE id=?", newAdjustMargin, newMargin, rawMargin, marginGift, rawMarginGift, forcePrice, positionID)
	return err
}

//
func IncrUserBalance(tx *sqlx.Tx, userID int64, coinID int, amount float64, withdraw bool) error {
	var err error
	if withdraw {
		// 转出时, amount为负数,所以反向操作
		_, err = tx.Exec("UPDATE tb_user_wallet SET balance = balance + ?, day_withdraw = day_withdraw - ?, total_withdraw = total_withdraw - ? WHERE user_id = ? AND coin_id = ?", amount, amount, amount, userID, coinID)
	} else {
		// 转入
		_, err = tx.Exec("UPDATE tb_user_wallet SET balance = balance + ?, day_recharge = day_recharge + ?, total_recharge = total_recharge + ? WHERE user_id = ? AND coin_id = ?", amount, amount, amount, userID, coinID)
	}
	return err
}

const tradeAccountSql = "select t1.account_id,t1.account_type, t1.user_id, t1.currency_id, t1.balance,t1.lock_amount, t1.available, t1.diff, t1.total_profit,platform_id,t1.warning_risk_rate,t1.gift_available,t1.gift_balance,t1.total_gift_receive,t1.gift_used from tb_user_account t1 left join tb_margin_currency t2 on t1.currency_id=t2.currency_id "

// GetTradeAccount 获取交易账户资产
func GetTradeAccount(tx *sqlx.Tx, userId int64, coinName string) (account *proto.Account, err error) {
	ac := new(proto.Account)
	str := utils.StrBuilder(tradeAccountSql, "where t1.user_id=? and t2.currency_name=?")
	if tx != nil {
		err = tx.Get(ac, str, userId, coinName)
	} else {
		err = DefaultDB().Get(ac, str, userId, coinName)
	}
	if err != nil {
		return nil, err
	}
	account = ac
	return
}

//获取交易账户资产
func ListTradeAccount(tx *sqlx.Tx, coinName string, page, size int) (list []proto.Account, err error) {
	str := utils.StrBuilder(tradeAccountSql, "where t2.currency_name=? limit ?,?")
	if tx != nil {
		err = tx.Select(&list, str, coinName, page*size, size)
	} else {
		err = DefaultDB().Select(&list, str, coinName, page*size, size)
	}
	if err != nil {
		return nil, err
	}
	return
}

// 使用钱包币种名获取充币地址
func GetDepositAddressWithWalletName(userID int64, platformID int, name string) (string, error) {
	var address string
	err := DefaultDB().Get(&address, "SELECT address FROM tb_user_deposit_address WHERE user_id=? AND wallet_name=? AND platform_id=?", userID, name, platformID)
	if err == sql.ErrNoRows {
		err = nil
	}
	return address, err
}

const _createUserDepositAddr = "INSERT tb_user_deposit_address SET user_id=?, platform_id=?, coin_id=?, coin_name=?, wallet_name=?, protocol=?, address=?, update_time=? ON DUPLICATE KEY UPDATE address=?, update_time=?"

// 创建用户充币地址
func CreateUserDepositAddr(coin proto.WalletCoinConf, userID int64, platformID int, addr string) error {
	_, err := DefaultDB().Exec(_createUserDepositAddr, userID, platformID, coin.CoinID, coin.CoinName, coin.WalletName, coin.Protocol, addr, time.Now(), addr, time.Now())
	if err != nil {
		log.Errorf("CreateUserDepositAddr insert error, userID:%d, walletName:%s, addr:%s, err:%v", userID, coin.WalletName, addr, err)
	}
	return err
}

//修复用户总资产
func UpdateUserBalanceByAccountId(tx *sqlx.Tx, balance decimal.Decimal, accountId int) (err error) {
	str := "update tb_user_account set balance=? where account_id=?"
	_, err = tx.Exec(str, balance, accountId)
	return
}

func UpdateWalletAssetByNewData(tx *sqlx.Tx, account *proto.WalletAsset) error {
	_, err := tx.Exec("UPDATE tb_user_wallet SET balance=?, withdraw_lock=? WHERE user_wallet_id=?", account.Balance, account.WithdrawLock, account.UserWalletId)
	return err
}

func GetUserWithdrawAddrHasDefault(userID int64, coinName string) (bool, error) {
	var exist bool
	err := DefaultDB().Get(&exist, "SELECT TRUE FROM tb_user_withdraw_address WHERE user_id=? AND coin_name=? AND is_default=1 LIMIT 1", userID, coinName)
	if err == sql.ErrNoRows {
		err = nil
	}
	return exist, err
}

func GetUserWithdrawAddrList(userID int64, coinName, protocol string) ([]proto.UserWithdrawAddress, error) {
	list := make([]proto.UserWithdrawAddress, 0, define.MaxPageCount)
	err := DefaultDB().Select(&list, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE user_id=? AND coin_name=? AND coin_protocol=? ORDER BY is_default DESC, create_time DESC LIMIT ?", userID, coinName, protocol, define.MaxPageCount)
	return list, err
}

func GetUserWithdrawDefaultAddr(tx *sqlx.Tx, userID int64, coinName string) (*proto.UserWithdrawAddress, error) {
	var err error
	var data proto.UserWithdrawAddress
	if tx != nil {
		err = tx.Get(&data, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE user_id=? AND coin_name=? AND is_default LIMIT ?", userID, coinName, 1)
	} else {
		err = DefaultDB().Get(&data, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE user_id=? AND coin_name=? AND is_default LIMIT ?", userID, coinName, 1)
	}
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &data, err
}

func UpdateWithdrawAddrDefault(tx *sqlx.Tx, id int64, isDefault bool) error {
	_, err := tx.Exec("UPDATE tb_user_withdraw_address SET is_default=? WHERE id=?", isDefault, id)
	return err
}

func GetUserWithdrawAddrRecordWithAddr(tx *sqlx.Tx, userID int64, addr string) (*proto.UserWithdrawAddress, error) {
	var err error
	var record proto.UserWithdrawAddress
	if tx != nil {
		err = tx.Get(&record, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE user_id=? AND addr=? LIMIT 1", userID, addr)
	} else {
		err = DefaultDB().Get(&record, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE user_id=? AND addr=? LIMIT 1", userID, addr)
	}
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &record, err
}

func GetUserWithdrawAddrRecord(tx *sqlx.Tx, id, userID int64) (*proto.UserWithdrawAddress, error) {
	var err error
	var record proto.UserWithdrawAddress
	if tx != nil {
		err = tx.Get(&record, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE id=? AND user_id=? LIMIT 1", id, userID)
	} else {
		err = DefaultDB().Get(&record, "SELECT id, user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time FROM tb_user_withdraw_address WHERE id=? AND user_id=? LIMIT 1", id, userID)
	}
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &record, err
}

func DeleteWithdrawAddr(tx *sqlx.Tx, id int64) error {
	_, err := tx.Exec("DELETE FROM tb_user_withdraw_address WHERE id=?", id)
	return err
}

func InsertWithdrawAddr(tx *sqlx.Tx, record *proto.UserWithdrawAddress) (int64, error) {
	res, err := tx.NamedExec("INSERT tb_user_withdraw_address (user_id, coin_name, coin_protocol, coin_tag, addr, addr_alias, is_default, is_trust, create_time) VALUE (:user_id, :coin_name, :coin_protocol, :coin_tag, :addr, :addr_alias, :is_default, :is_trust, :create_time)", record)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func UpdateWithdrawAddr(tx *sqlx.Tx, record *proto.UserWithdrawAddress) error {
	_, err := tx.Exec("UPDATE tb_user_withdraw_address SET coin_tag=?, addr=?, addr_alias=?, is_default=?, is_trust=? WHERE id=?", record.CoinTag, record.Addr, record.AddrAlias, record.IsDefault, record.IsTrust, record.ID)
	return err
}

func GetUserTradeAsset(tx *sqlx.Tx, userId int64, currencyId int) (account *proto.TradeAsset, err error) {
	var ac proto.TradeAsset
	str := _userAccountbase + " where user_id=? and currency_id=?"
	if tx != nil {
		err = tx.Get(&ac, str, userId, currencyId)
	} else {
		err = DefaultDB().Get(&ac, str, userId, currencyId)
	}
	return &ac, err
}

const _getUserMultipleWallet = "SELECT user_wallet_id, user_id, currency_id, balance, withdraw_lock, platform_id FROM tb_user_wallet WHERE user_id = ?"

func GetUserMultipleWallet(tx *sqlx.Tx, userID int64, coinIDs ...int) ([]proto.WalletAsset, error) {
	as := make([]proto.WalletAsset, 0, len(coinIDs))
	if len(coinIDs) == 0 {
		return as, nil
	}

	query, params, err := sqlx.In(" AND currency_id IN(?)", coinIDs)
	if err != nil {
		return nil, err
	}

	query = _getUserMultipleWallet + query
	params = append([]interface{}{userID}, params...)
	if tx != nil {
		err = tx.Select(&as, query, params...)
	} else {
		err = DefaultDB().Select(&as, query, params...)
	}
	return as, err
}

func GetUserMultipleWalletWithLock(tx *sqlx.Tx, userID int64, coinIDs ...int) ([]proto.WalletAsset, error) {
	as := make([]proto.WalletAsset, 0, len(coinIDs))
	if len(coinIDs) == 0 {
		return as, nil
	}

	query, params, err := sqlx.In(" AND currency_id IN(?)", coinIDs)
	if err != nil {
		return nil, err
	}

	query = _getUserMultipleWallet + query + " FOR UPDATE"
	params = append([]interface{}{userID}, params...)
	err = tx.Select(&as, query, params...)
	return as, err
}

func GetWalletBillByOrderIDAndType(tx *sqlx.Tx, orderID int64, bt define.WalletBillType) (*proto.UserWalletBill, error) {
	var bill proto.UserWalletBill
	err := tx.Get(&bill, "SELECT bill_id, user_id, platform_id, type, currency_name, `status`, currency_id, tag, amount, from_addr, to_addr, tx, confirm_num, balance, lock_amount, commission, remarks, created_time, approved_time, order_id FROM tb_user_wallet_bill WHERE order_id=? AND type=? LIMIT 1", orderID, bt)
	return &bill, err
}

func UpdateWalletBillState(tx *sqlx.Tx, billID int64, newState define.WalletBillState, now time.Time) error {
	_, err := tx.Exec("update tb_user_wallet_bill set `status`=?, approved_time=? where bill_id=?", newState, now, billID)
	if err != nil {
		return err
	}
	return nil
}

const _getBillByUserIDSql = "SELECT bill_id, user_id, type, `status`, currency_id, currency_name, tag, amount, from_addr, to_addr, tx, confirm_num, balance, commission, remarks, created_time, IFNULL(approver,'') approver, IFNULL(approved_time, created_time) approved_time, IFNULL(order_id, '') order_id FROM tb_user_wallet_bill WHERE user_id=?"

func GetBillByUserID(tx *sqlx.Tx, userID int64, coinID int, billType define.WalletBillType, billState define.WalletBillState, startTime, endTime time.Time, page define.Page) ([]proto.UserWalletBillV2, error) {
	var buf strings.Builder
	buf.WriteString(_getBillByUserIDSql)

	if coinID != 0 {
		buf.WriteString(fmt.Sprintf(" AND currency_id = %d", coinID))
	}

	if billType != define.WalletBillTypeAll {
		buf.WriteString(" AND type in( 0")

		var current = 1
		for billType > 0 {
			if billType&1 == 1 {
				buf.WriteString(fmt.Sprintf(",%d", current))
			}
			billType >>= 1
			current <<= 1
		}
		buf.WriteString(" )")
	}
	if billState > define.WalletBillStateAll {
		buf.WriteString(fmt.Sprintf(" AND status = %d", billState))
	} else if billState < define.WalletBillStateAll {
		buf.WriteString(fmt.Sprintf(" AND status != %d", 0-billState))
	}

	if !startTime.IsZero() {
		buf.WriteString(fmt.Sprintf(" AND created_time >= '%s'", startTime.Format(define.TimeFormatNormal)))
	}
	if !endTime.IsZero() {
		buf.WriteString(fmt.Sprintf(" AND created_time <= '%s'", endTime.Format(define.TimeFormatNormal)))
	}
	buf.WriteString(" ORDER BY approved_time DESC LIMIT ?, ?")
	log.Info("GetBillByUserID", zap.String("sql", buf.String()))

	var err error
	var rows *sqlx.Rows
	if tx != nil {
		rows, err = tx.Queryx(buf.String(), userID, page.Page*page.Count, page.Count)
	} else {
		rows, err = DefaultDB().Queryx(buf.String(), userID, page.Page*page.Count, page.Count)
	}
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var bill proto.UserWalletBillV2
	list := make([]proto.UserWalletBillV2, 0, define.MaxPageCount)
	for rows.Next() {
		err = rows.StructScan(&bill)
		if err != nil {
			return nil, err
		}
		bill.CreatedTimeUnix = bill.CreatedTime.Unix()
		bill.ApprovedTimeUnix = bill.ApprovedTime.Unix()
		list = append(list, bill)
	}

	return list, err
}

func GetUserPositionGiftMargin(tx *sqlx.Tx, userId int64, contractCode, side string) (u decimal.Decimal, err error) {
	str := "select ifnull(sum(ifnull(raw_margin_gift,0)),0) from tb_position where user_id = ? and contract_code =? and side=?"
	err = tx.Get(&u, str, userId, contractCode, side)
	if err != nil {
		log.Error("GetUserPositionGiftMargin fail", zap.Error(err), zap.Int64("userId", userId), zap.String("code", contractCode), zap.String("side", side))
		return
	}
	return
}

func GetOrderGiftMargin(tx *sqlx.Tx, userId int64, contractCode, side string) (u decimal.Decimal, err error) {
	str := "select ifnull(sum(gift_amount),0) from tb_order where user_id = ? and contract_code =? and side=? and state IN(0, 202) "
	err = tx.Get(&u, str, userId, contractCode, side)
	if err != nil {
		log.Error("GetOrderGiftMargin fail", zap.Error(err), zap.Int64("userId", userId), zap.String("code", contractCode), zap.String("side", side))
		return
	}
	return
}

const (
	_hasPositionForSide = "select 1 from tb_position where user_id = ? and contract_code =? and side=? LIMIT 1"
	_hasOrderForSide    = "select 1 from tb_order where user_id = ? and contract_code =? and side=? and offset=? and state IN(0, 202) LIMIT 1"
)

func HasPositionForSide(tx *sqlx.Tx, userId int64, contractCode, side string) (bool, error) {
	var flag int8
	err := tx.Get(&flag, _hasPositionForSide, userId, contractCode, side)
	if err == sql.ErrNoRows {
		err = nil
	}
	return flag == 1, err
}

func HasOrderForSide(tx *sqlx.Tx, userId int64, contractCode, side string) (bool, error) {
	var flag int8
	err := tx.Get(&flag, _hasOrderForSide, userId, contractCode, side, define.OffsetOpen)
	if err == sql.ErrNoRows {
		err = nil
	}
	return flag == 1, err
}

//修改交易账户余额
func UpdateUserAccountByAccountId(tx *sqlx.Tx, acountId int, increBalance, increAvailable, lockAmount, haveProfit decimal.Decimal, giftBalanceIncre, giftAvailableIncre, giftUsed, giftLockIncr decimal.Decimal) (err error) {
	str := "update tb_user_account set balance=balance+?,available=available+?,lock_amount=lock_amount+?,total_profit=total_profit+?,gift_balance=gift_balance+?,gift_available=gift_available+?,gift_used=gift_used+?,gift_lock_amount=gift_lock_amount+? where account_id=?"
	_, err = tx.Exec(str, increBalance, increAvailable, lockAmount, haveProfit, giftBalanceIncre, giftAvailableIncre, giftUsed, giftLockIncr, acountId)
	return
}

func UpdateUserAccountForProfit(tx *sqlx.Tx, accountId int, newBalance, newAvailable, diff, totalProfit, blowingFee, giftBalance, giftAvailable, giftUsed decimal.Decimal) (err error) {
	str := "update tb_user_account set balance=?,available=?,diff=?,total_profit=?,blowing_fee=?,gift_available=?,gift_balance=?,gift_used=? where account_id=?"
	_, err = tx.Exec(str, newBalance, newAvailable, diff, totalProfit, blowingFee, giftAvailable, giftBalance, giftUsed, accountId)
	return
}

func InsertFundingFail(tx *sqlx.Tx, ff *proto.FundingFail) (err error) {
	str := "INSERT INTO tb_funding_fail (id, user_id, account_type, currency_id, balance, available, diff, platform_id, gift_available, gift_balance, amount, create_time, lock_amount) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)"
	_, err = tx.Exec(str, ff.Id, ff.UserId, ff.AccountType, ff.CurrencyId, ff.Balance, ff.Available, ff.Diff, ff.PlatformID, ff.GiftAvailable, ff.GiftBalance, ff.Amount, ff.CreateTime, ff.LockAmount)
	return
}
