package database

import (
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"bytes"
	"database/sql"
	"github.com/jmoiron/sqlx"
)

const (
	_labelSql  = "SELECT t1.id, t1.label_id, t1.contract_code, t1.max_lever, t1.max_order_volume, t1.min_order_volume, t1.max_posi_volume, t1.fee, t1.funding, t1.slippage,t1.min_slippage, t1.status_stype, t1.risk_rate FROM tb_label_info  t1 left join tb_label t2 on t1.label_id=t2.id "
	_labelUser = "SELECT t1.id, t1.label_id, t1.contract_code, t1.max_lever, t1.max_order_volume, t1.min_order_volume, t1.max_posi_volume, t1.fee, t1.funding, t1.slippage,t1.min_slippage, t1.status_stype, t1.risk_rate from tb_label_info t1 left join tb_label t2  on t1.label_id=t2.id left join tb_user t3 on t1.label_id=t3.label_id "
)

func GetUserLabelByContractAndUserId(tx *sqlx.Tx, userId int64, code string) (*proto.UserLabel, error) {
	var (
		err  error
		list []proto.UserLabel
	)
	var s bytes.Buffer
	var args []interface{}
	s.WriteString(_labelUser)
	if code != "" {
		s.WriteString("where t2.status_type=1 and t3.user_id=? and contract_code=? and t1.status_stype=1 order by t2.id desc limit 1")
		args = append(args, userId, code)
	} else {
		s.WriteString("where t2.status_type=1 and t3.user_id=? and t1.status_stype=1 order by t2.id desc limit 1")
		args = append(args, userId)

	}
	if tx != nil {
		err = tx.Select(&list, s.String(), args...)
	} else {
		err = DefaultDB().Select(&list, s.String(), args...)
	}
	if err != nil {
		log.Errorf("GetUserLabelByContractAndUserId fail,%v", err)
		return nil, err
	}
	size := len(list)
	if size > 0 {
		if size > 1 {
			log.Errorf("数据有误，用户指定合约多条数据,userId:%v,code:%v", userId, code)
		}
		return &list[0], nil
	}
	return nil, nil
}

func GetUserLabelByContract(tx *sqlx.Tx, labelId int, code string) (*proto.UserLabel, error) {
	str := utils.StrBuilder(_labelSql, " where t2.status_type=1 and t1.label_id=? and contract_code=? and t1.status_stype=1 order by t2.id desc LIMIT 1")

	var err error
	var label proto.UserLabel
	if tx != nil {
		err = tx.Get(&label, str, labelId, code)
	} else {
		err = DefaultDB().Get(&label, str, labelId, code)
	}
	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &label, err
}

func ListUserLabelByLabelId(tx *sqlx.Tx, labelId int) (list []proto.UserLabel, err error) {
	str := utils.StrBuilder(_labelSql, " where t2.status_type=1 and t1.label_id=? and t1.status_stype=1 order by t2.id desc")
	if tx != nil {
		err = tx.Select(&list, str, labelId)
		return
	}
	err = DefaultDB().Select(&list, str, labelId)
	return
}

func GetLabelConfDictByLabelID(tx *sqlx.Tx, labelId int) (dict map[string]proto.UserLabel, err error) {
	str := utils.StrBuilder(_labelSql, " where t2.status_type=1 and t1.label_id=? and t1.status_stype=1")

	var rows *sqlx.Rows
	if tx != nil {
		rows, err = tx.Queryx(str, labelId)
	} else {
		rows, err = DefaultDB().Queryx(str, labelId)
	}
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var label proto.UserLabel
	dict = make(map[string]proto.UserLabel)
	for rows.Next() {
		err = rows.StructScan(&label)
		if err != nil {
			return nil, err
		}
		dict[label.ContractCode] = label
	}
	return
}
