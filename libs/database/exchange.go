package database

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"database/sql"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"strings"
	"time"
)

const _insertExchangeOrder = "INSERT tb_exchange_order (id, user_id, from_coin_id, from_coin_name, from_amount, to_coin_id, to_coin_name, to_amount, original_price, exchange_price, price, fee, fee_rate, real_amount, hedge_amount, refund_amount, hedge_order_id, state, os_type, imei, ip_addr, create_time, update_time,platform_id) VALUE (:id, :user_id, :from_coin_id, :from_coin_name, :from_amount, :to_coin_id, :to_coin_name, :to_amount, :original_price, :exchange_price, :price, :fee, :fee_rate, :real_amount, :hedge_amount, :refund_amount, :hedge_order_id, :state, :os_type, :imei, :ip_addr, :create_time, :update_time,:platform_id)"

func InsertExchangeOrder(tx *sqlx.Tx, order *proto.ExchangeOrder) error {
	_, err := tx.NamedExec(_insertExchangeOrder, order)
	return err
}

const _updateExchangeOrderByNewData = "UPDATE tb_exchange_order SET fee=:fee, real_amount=:real_amount, hedge_amount=:hedge_amount, refund_amount=:refund_amount, hedge_order_id=:hedge_order_id, state=:state, update_time=:update_time WHERE id=:id"

func UpdateExchangeOrderByNewData(tx *sqlx.Tx, order *proto.ExchangeOrder) error {
	_, err := tx.NamedExec(_updateExchangeOrderByNewData, order)
	return err
}

const _getExchangeOrderByID = "SELECT id, user_id, from_coin_id, from_coin_name, from_amount, to_coin_id, to_coin_name, to_amount, price, fee, fee_rate, real_amount, hedge_amount, refund_amount, hedge_order_id, state, os_type, imei, ip_addr, create_time, update_time,platform_id FROM tb_exchange_order WHERE id=? LIMIT 1 FOR UPDATE"

func GetExchangeOrderByID(tx *sqlx.Tx, orderID string) (*proto.ExchangeOrder, error) {
	var order proto.ExchangeOrder
	err := tx.Get(&order, _getExchangeOrderByID, orderID)
	return &order, err
}

const _getExchangeOrderHistory = "SELECT id, from_coin_name, to_coin_name, from_amount, to_amount, price, real_amount, refund_amount, fee, update_time, state FROM tb_exchange_order WHERE user_id=?"

func GetExchangeOrderHistory(userID int64, state define.ExchangeOrderState) ([]proto.ExchangeOrderHistory, error) {
	var buf strings.Builder
	buf.WriteString(_getExchangeOrderHistory)

	switch state {
	case define.ExchangeOrderStateFinish:
		buf.WriteString(fmt.Sprintf(" AND state IN(%d,%d)", define.ExchangeOrderStateCancel, define.ExchangeOrderStateDone))
	case define.ExchangeOrderStatePending, define.ExchangeOrderStateCancel, define.ExchangeOrderStateDone:
		buf.WriteString(fmt.Sprintf(" AND state = %d", state))
	}
	buf.WriteString(" ORDER BY id DESC LIMIT ?")

	rows, err := DefaultDB().Queryx(buf.String(), userID, define.MaxPageCount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var record proto.ExchangeOrderHistory
	list := make([]proto.ExchangeOrderHistory, 0, define.MaxPageCount)
	for rows.Next() {
		err = rows.StructScan(&record)
		if err != nil {
			return nil, err
		}
		record.UpdateTimeUnix = record.UpdateTime.Unix()
		list = append(list, record)
	}

	return list, nil
}

const _exchangeHedgeBase = "SELECT exchange_order_id, coin_name, contract_code, hedge_source, hedge_order_id, entrust_type, side, price, amount, trade_price, trade_amount, trade_money, state, update_time, create_time FROM tb_exchange_hedge_order "

func GetExchangeHedgeOrder(tx *sqlx.Tx, exchangeOrderId int64) (order *proto.ExchangeHedgeOrder, err error) {
	str := _exchangeHedgeBase + "WHERE exchange_order_id = ? "
	o := new(proto.ExchangeHedgeOrder)
	if tx == nil {
		err = DefaultDB().Get(o, str, exchangeOrderId)
	} else {
		str += " for update"
		err = tx.Get(o, str, exchangeOrderId)
	}
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
			return
		}
		log.Error("GetExchangeHedgeOrder fail", zap.Error(err))
	}
	order = o
	return
}

//查询指定源未完成的订单
func ListUnFinishExchangeHedgeOrders(tx *sqlx.Tx, source define.ExchangeHedgeSource) (list []proto.ExchangeHedgeOrder, err error) {
	str := _exchangeHedgeBase + "WHERE hedge_source = ? and (state=? or state=?) order by hedge_order_id asc"
	if tx == nil {
		err = DefaultDB().Select(&list, str, source, define.ExchangeHedgeStateNew, define.ExchangeHedgeStatePartTrade)
	} else {
		err = tx.Select(&list, str, source, define.ExchangeHedgeStateNew, define.ExchangeHedgeStatePartTrade)
	}
	if err != nil {
		log.Error("ListUnFinishExchangeHedgeOrders fail", zap.Error(err))
	}
	return
}

//修改对冲订单成交状态
func UpdateExchangeHedgeOrderState(tx *sqlx.Tx, exchangeOrderId int64, state define.ExchangeHedgeState, tradePrice, tradeAmount, tradeMoney decimal.Decimal, updateTime time.Time) (err error) {
	str := "update tb_exchange_hedge_order set state=?,trade_price=?,trade_amount=?,trade_money=?,update_time=? where exchange_order_id=?"
	if tx == nil {
		_, err = DefaultDB().Exec(str, state, tradePrice, tradeAmount, tradeMoney, updateTime, exchangeOrderId)
	} else {
		_, err = tx.Exec(str, state, tradePrice, tradeAmount, tradeMoney, updateTime, exchangeOrderId)
	}
	return
}

const _insertExchangeHedgeOrder = "INSERT INTO tb_exchange_hedge_order (exchange_order_id, coin_name, contract_code, hedge_source, hedge_order_id, entrust_type, side, price, amount, trade_price, trade_amount, trade_money, `state`, update_time, create_time) VALUES (:exchange_order_id,:coin_name,:contract_code,:hedge_source,:hedge_order_id,:entrust_type,:side,:price,:amount,:trade_price,:trade_amount,:trade_money,:state,:update_time,:create_time)"

//对冲订单入库
func InsertExchangeHedgeOrder(tx *sqlx.Tx, order *proto.ExchangeHedgeOrder) (err error) {
	if tx == nil {
		_, err = DefaultDB().NamedExec(_insertExchangeHedgeOrder, order)
	} else {
		_, err = tx.NamedExec(_insertExchangeHedgeOrder, order)
	}
	return
}

const _exchangeHedgeConfig = "SELECT config_id, hedge_source, hedge_account, public_key, private_key, hint_phrase, create_time, update_time, is_test FROM tb_exchange_hedge_config "

func GetExchangeHedgeConfig(tx *sqlx.Tx) (list []proto.ExchangeHedgeConfig, err error) {
	if tx == nil {
		err = DefaultDB().Select(&list, _exchangeHedgeConfig)
	} else {
		err = tx.Select(&list, _exchangeHedgeConfig)
	}
	return
}
