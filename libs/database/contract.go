/*
@Time : 2019-12-30 11:50
<AUTHOR> mocha
@File : symbol
*/
package database

import (
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

const symbolsBase = "select contract_id, contract_name, contract_name_en, contract_code, contract_icon, contract_type, step, digit, par_value, min_order_volume, max_order_volume, max_posi_volume, open_order_enable, close_order_enable, coin_id, coin_name, price_coin_name, fee_taker,fee_maker, order_by, delisted, maintenance_margin_ratio, full_lever, part_lever,follow_lever, index_precision, recommend, is_show,ifnull(min_trader_volume,0) as min_trader_volume,ifnull(max_trader_volume,0) as max_trader_volume,is_follow,slippage,price_step,unit_trade,label_max_order_volume,label_max_posi_volume,label_fee,label_funding,label_slippage,label_lever,label_min_risk_rate,blowing_up_fee,max_side_amount,limit_buy_price,limit_sell_price,lock_float_factor,redundancy_factor,price_protect_factor,ioc_limit,ioc_buy_limit,ioc_sell_limit,max_ioc_factor,is_maintenance,fee_taker_depth,fee_maker_depth,ac_min_slippage,ac_max_slippage,ac_seconds, trade_factor, max_trade_factor, net_pos_switch from tb_contract"

//获取所有在线交易对
func GetOnlineSymbols() ([]proto.Contract, error) {
	return GetOnlineContractsTx(nil)
}

func GetOnlineContractsTx(tx *sqlx.Tx) (reply []proto.Contract, err error) {
	if tx != nil {
		err = tx.Select(&reply, utils.StrBuilder(symbolsBase, " where delisted=? order by order_by desc"), 0)
	} else {
		err = DefaultDB().Select(&reply, symbolsBase+" where delisted=? order by order_by desc", 0)
	}
	if err != nil {
		return nil, err
	}
	for i, contract := range reply {
		lRisk, err := ListLeverRiskByContractID(tx, contract.ContractId)
		if err != nil {
			return nil, err
		}
		reply[i].LeverRisk = lRisk
	}
	return
}

//获取所有支持交易对
func GetSupportContract() ([]proto.Contract, error) {
	return GetSupportContractsTx(nil)
}

func GetSupportContractsTx(tx *sqlx.Tx) (reply []proto.Contract, err error) {
	str := utils.StrBuilder(symbolsBase, " order by order_by desc")
	if tx != nil {
		err = tx.Select(&reply, str)
	} else {
		err = DefaultDB().Select(&reply, str)
	}
	if err != nil {
		log.Error("GetSuppportSymbols db error", zap.Error(err))
		return nil, err
	}
	for i, contract := range reply {
		lRisk, err := ListLeverRiskByContractID(tx, contract.ContractId)
		if err != nil {
			return nil, err
		}
		reply[i].LeverRisk = lRisk
	}
	return
}

//获取指定id交易对信息
func GetContractById(symbolID int) (*proto.Contract, error) {
	return GetContractByIdTx(nil, symbolID)
}

func GetContractByIdTx(tx *sqlx.Tx, symbolId int) (reply *proto.Contract, err error) {
	reply = new(proto.Contract)
	str := utils.StrBuilder(symbolsBase, " where contract_id=?")
	if tx != nil {
		err = tx.Get(reply, str, symbolId)
	} else {
		err = DefaultDB().Get(reply, str, symbolId)
	}
	if err != nil {
		log.Error("GetContractById db error", zap.Error(err))
	}
	lRisk, err := ListLeverRiskByContractID(tx, reply.ContractId)
	if err != nil {
		return nil, err
	}
	reply.LeverRisk = lRisk

	return
}

//获取指定code交易对信息
func GetContractByCode(contractCode string) (*proto.Contract, error) {
	return GetContractByCodeTx(nil, contractCode)
}

func GetContractByCodeTx(tx *sqlx.Tx, contractCode string) (reply *proto.Contract, err error) {
	reply = new(proto.Contract)
	str := utils.StrBuilder(symbolsBase, " where contract_code=?")
	if tx != nil {
		err = tx.Get(reply, str, contractCode)
	} else {
		err = DefaultDB().Get(reply, str, contractCode)
	}
	if err != nil {
		log.Errorf("GetContractById db error,code:%v,err:%v", contractCode, err)
		return nil, err
	}
	lRisk, err := ListLeverRiskByContractID(tx, reply.ContractId)
	if err != nil {
		return nil, err
	}
	reply.LeverRisk = lRisk
	return
}

func GetContractMarketConfig() (list []proto.MarketConfig, err error) {
	str := "select id, api_name, api_status, contract_codes, notice_switch, warning_threshold from tb_market_config"
	err = DefaultDB().Select(&list, str)
	return
}

func UpdateContractMarketConfigById(id int64, apiStatus int) (err error) {
	str := "update tb_market_config set api_status=? where id=?"
	_, err = DefaultDB().Exec(str, apiStatus, id)
	return
}

func UpdateContractMarketConfig(apiStatus int) (err error) {
	str := "update tb_market_config set api_status=? where 1=1"
	_, err = DefaultDB().Exec(str, apiStatus)
	return
}

//查询合约杠杆风险度
func ListLeverRiskByContractID(tx *sqlx.Tx, contractId int) (list []proto.ContractLeverRisk, err error) {
	str := "SELECT id, contract_id, `level`, min_lever, max_lever, risk_rate, create_time FROM tb_contract_lever_risk WHERE  contract_id = ? order by `level` asc"
	if tx != nil {
		err = tx.Select(&list, str, contractId)
	} else {
		err = DefaultDB().Select(&list, str, contractId)
	}
	if err != nil {
		log.Errorf("ListLeverRiskByContractID id:%v,fail,%v", contractId, err)
		return nil, err
	}
	return
}

func ListContractDepthConfig() (list []proto.ContractDepth, err error) {
	str := "SELECT contract_id, contract_code, bait_warn_level, distance, hold_threshold, level_factor, pos_move_factor, max_adjust_factor, max_exe_signal, buy_factor, sell_factor, bucket_base, bucket_pin_factor, bucket_price_update, bucket_threshold, bucket_amount, bucket_max_amount, pre_bucket_base, max_bucket_base, base_factor, min_24h_amount, max_24h_amount, bucket_base_duration, depth_level_start, depth_level_end, depth_spay_money, extreme_switch, extreme_range_limit, extreme_level_inc, extreme_level_limit, large_trade_count, large_trade_factor, extreme_netpos, extreme_price_change, trade_diff_warn, depth_min_rand, depth_max_rand, index_invalid_value, reason_range, departure_factor, index_diff_switch, index_ref_value, index_exception_adjust, depth_copy_source, bait_level_pre_value, depth_level_adjust_factor, max_trade_level, bait_price_adjust, bait_amount_adjust, single_base_level, single_max_level, single_adjust, funds_protect, continue_protect_factor, spot_exception_time, spot_exception_threshold, exception_check_factor, depth_switch, is_depth_switch, force_hop, rc_deal_max_volume FROM tb_contract_depth "
	err = DefaultDB().Select(&list, str)
	return
}

func UpdateContractDepthForResetBucketBaseDuration(code string) (err error) {
	str := "update tb_contract_depth set bucket_base_duration=0 where contract_code=?"
	_, err = DefaultDB().Exec(str, code)
	return
}

func UpdateDepthPriceSwitch(code string, s int) (err error) {
	str := "update tb_contract_depth set depth_switch=? where contract_code=?"
	_, err = DefaultDB().Exec(str, s, code)
	return
}
