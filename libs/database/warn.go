package database

import "bc/libs/proto"

//查询系统配置的报警邮件列表
func GetWarnEmailsByHedge() (emails []string, err error) {
	str := "select email from tb_hedge_mail where ctype=1"
	err = DefaultDB().Select(&emails, str)
	return
}

//获取行情报警邮件
func GetWarnEmailsByMarket() (emails []string, err error) {
	str := "select email from tb_hedge_mail where ctype=2"
	err = DefaultDB().Select(&emails, str)
	return
}

//插入报警列表
func InsertWarnNotice(notice proto.WarnNotice) (err error) {
	str := "insert into tb_hedge_notice(notice_type, notice_content, notice_status, created_by, hedge_account, contract_code) values (?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, notice.NoticeType, notice.NoticeContent, notice.NoticeStatus, notice.CreatedBy, notice.HedgeAccount, notice.ContractCode)
	return
}

func InsertWarnNoticeWithMarket(notice proto.WarnNotice) (err error) {
	str := "insert into tb_market_notice(notice_type, notice_content, notice_status, created_by, hedge_account, contract_code) values (?,?,?,?,?,?)"
	_, err = DefaultDB().Exec(str, notice.NoticeType, notice.NoticeContent, notice.NoticeStatus, notice.CreatedBy, notice.HedgeAccount, notice.ContractCode)
	return
}

const _getHedgeCautionReceiver = "SELECT receiver FROM tb_hedge_caution_receiver"

func GetHedgeCautionReceiver() ([]string, error) {
	var list []string
	err := DefaultDB().Select(&list, _getHedgeCautionReceiver)
	return list, err
}
