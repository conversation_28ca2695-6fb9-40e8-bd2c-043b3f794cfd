package msg

import (
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"net"
	"net/smtp"
	"strings"

	"bc/libs/conf"
	"bc/libs/log"
	"bc/libs/utils"
)

func SendCautionEmail(subject, content string) {
	SendEmailToListWithExMail(utils.StrBuilderBySep(" ", conf.LocalName(), subject), content, conf.CautionReceiver()...)
}

// 提币预警邮件
func SendWithdrawCautionEmail(subject, content string) {
	//SendEmailToListWithExMail(subject, content, conf.WithdrawCautionReceiver()...)
	for _, addr := range conf.WithdrawCautionReceiver() {
		SendEmailByMsgServiceIgnore(0, addr, subject, content)
	}
}

//发送兑币账户报警
func SendExchangeCautionEmail(subject, content string) {
	for _, addr := range conf.ExchangeCautionReceiver() {
		SendEmailByMsgServiceIgnore(0, addr, subject, content)
	}
}

func SendNotifyHedgeEmail(emails []string, subject, content string) {
	for _, email := range emails {
		_ = SendEmailWithExmail(email, utils.StrBuilderBySep(" ", conf.LocalName(), subject), content)
	}
	//SendEmailToListWithExMail(utils.StrBuilderBySep(" ", conf.LocalName(), subject), content, emails...)
}

func SendEmailToList(subject, content string, addresses ...string) {
	if len(addresses) == 0 {
		return
	}

	for i := range addresses {
		toAddr := addresses[i]
		_ = SendEmail(toAddr, subject, content)
	}
}

func SendEmailToListWithExMail(subject, content string, addresses ...string) {
	if len(addresses) == 0 {
		return
	}

	for i := range addresses {
		toAddr := addresses[i]
		_ = SendEmailWithExmail(toAddr, subject, content)
	}
}

func SendEmail(to, subject, content string) (err error) {
	// if strings.HasSuffix(to, "qbtest.cn") || !check.EmailAddrV2(to) {
	if strings.HasSuffix(to, "qbtest.cn") {
		// 测试邮箱或邮箱地址无效,不需要实际发送邮件
		return
	}
	//subject = utils.StrBuilderBySep(" ", conf.MailPrefix(), subject)

	//resp, err := SendEmailWithAliDM(to, subject, content)
	resp, err := SendEmailByMSGService(to, subject, content)
	if err != nil {
		log.Errorf("[SendMail] to:%s,subject:%v,err:%v", to, subject, err)
	} else {
		log.Infof("[SendMail] to:%s, subject:%s,resp:%s", to, subject, resp)
	}
	return err
}

// 普通方式
const (
	HOST       = "smtp.transmail.com"
	ServerAddr = "smtp.transmail.com:465"
	USER       = "emailapikey"                                                                                                                                      //发送邮件的邮箱
	PASSWORD   = "wSsVR61+rBP3XfgvnDX/dO1qkVkEAlz3QRl8igOp73H8HfjCpcdvk0SdBgP2GvdLFmZvQTER9eh8mhgD0TYM2Yh5yQsGCCiF9mqRe1U4J3x17qnvhDzIW2pemxaJK40NxAtonWRoEsok+g==" //发送邮件邮箱的密码(panda-server)
	FROM       = "<EMAIL>"
)

func SendEmailWithExmail(to, subject, content string) (err error) {
	auth := smtp.PlainAuth("", USER, PASSWORD, HOST)
	b64 := base64.StdEncoding
	subjectBase64 := fmt.Sprintf("=?UTF-8?B?%s?=", b64.EncodeToString([]byte(subject)))
	str := "From: " + FROM + "\r\nTo: " + to + "\r\nSubject: " + subjectBase64 + "\r\n" + "Content-Type: text/plain;charset=UTF-8" +
		"\r\n" + "Content-Transfer-Encoding: base64" + "\r\n\r\n" + b64.EncodeToString([]byte(content)) /*
		var content_type string
		if mailtype == "html" {
			content_type = "Content-Type: text/" + mailtype + "; charset=UTF-8"
		} else {
			content_type = "Content-Type: text/plain" + "; charset=UTF-8"
		}
		str := "To: " + to + "\r\nFrom: " + FROM + "\r\nSubject: " + subject + "\r\n" + content_type + "\r\n\r\n" + content
	*/
	err = SendMailUsingTLS(
		ServerAddr,
		auth,
		USER,
		[]string{to},
		[]byte(str),
	)
	log.Infof("[SendMail] to：%s, content:%s, err: %v", to, content, err)
	return
}

//return a smtp client
func Dial(addr string) (*smtp.Client, error) {
	conn, err := tls.Dial("tcp", addr, nil)
	if err != nil {
		return nil, err
	}
	//分解主机端口字符串
	host, _, _ := net.SplitHostPort(addr)
	return smtp.NewClient(conn, host)
}

//参考net/smtp的func SendMail()
//使用net.Dial连接tls(ssl)端口时,smtp.NewClient()会卡住且不提示err
//len(to)>1时,to[1]开始提示是密送
func SendMailUsingTLS(addr string, auth smtp.Auth, from string,
	to []string, msg []byte) (err error) {

	//create smtp client
	c, err := Dial(addr)
	if err != nil {
		return err
	}
	defer c.Close()

	if auth != nil {
		if ok, _ := c.Extension("AUTH"); ok {
			if err = c.Auth(auth); err != nil {
				return err
			}
		}
	}

	if err = c.Mail(from); err != nil {
		return err
	}

	for _, addr := range to {
		if err = c.Rcpt(addr); err != nil {
			return err
		}
	}

	w, err := c.Data()
	if err != nil {
		return err
	}

	_, err = w.Write(msg)
	if err != nil {
		return err
	}

	err = w.Close()
	if err != nil {
		return err
	}
	return c.Quit()
}
