/*
	内部接口,用于内部服务调用,用于操作资产
*/

package base_wallet_rpc

import (
	"context"

	"bc/libs/define"
	"bc/libs/generic"
	"bc/libs/proto"
	"bc/libs/xplugins"
)

const (
	RPCAdminWithdraw        = "AdminWithdraw"        // 管理员提币
	RPCAdminWithdrawOperate = "AdminWithdrawOperate" // 管理员提币审核
	RPCBackendCWSBalance    = "BackendCWSBalance"    // 管理员获取当前大钱包资产
	RPCCheckAsset           = "CheckAsset"           // 检查资产账户(不存在则初始化账户)
)

func AdminWithdraw(ctx context.Context, reqID int64, arg *proto.AdminWithdrawArg) error {
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(ctx, RPCAdminWithdraw, arg, &define.Reply{}, span)
}

func AdminWithdrawOperate(ctx context.Context, reqID int64, arg *proto.WithdrawOperateArg) error {
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(ctx, RPCAdminWithdrawOperate, arg, &define.Reply{}, span)
}

func BackendCWSBalance(ctx context.Context, reqID int64) (map[string]string, error) {
	bs := make(map[string]string)
	reply := define.Reply{Data: bs}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCBackendCWSBalance, &define.NoneArg{}, &reply, span)
	if err != nil {
		return nil, err
	}
	return bs, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func CheckAsset(ctx context.Context, reqID, userID int64, platformID int, assetType define.AssetType, coins ...string) error {
	bs := make(map[string]string)

	arg := CheckAssetArg{
		UserID:     userID,
		PlatformID: platformID,
		AssetType:  assetType,
		CoinNames:  coins,
	}
	reply := define.Reply{Data: bs}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCCheckAsset, &arg, &reply, span)
	if err != nil {
		return err
	}

	return generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}
