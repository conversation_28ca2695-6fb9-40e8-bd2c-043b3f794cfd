package base_third_rpc

import "github.com/shopspring/decimal"

type CreateDepositAddrArg struct {
	CoinName string `json:"coin_name"` // 币种名
	Label    string `json:"label"`     // 用户标识
	NeedTag  bool   `json:"need_tag"`  // 是否需要标签
}

type SubmitWithdrawArg struct {
	CoinName string          `json:"coin_name"` // 币种
	ToAddr   string          `json:"to_addr"`   // 目标地址
	Amount   decimal.Decimal `json:"amount"`    // 数量
	LocalID  int64           `json:"local_id"`  // 本地id
	NeedTag  bool            `json:"need_tag"`  // 是否需要标签
}

type WithdrawResult struct {
	Id       int64 `json:"id"`       // 提币记录id
	Internal bool  `json:"internal"` // 是否是内部地址
}

type VerifyAddrArg struct {
	CoinName  string `json:"coin_name"`  // 币种
	Addr      string `json:"addr"`       // 地址
	ShareAddr string `json:"share_addr"` // 共享地址
}

type VerifyCallbackSignArg struct {
	Params    string `json:"params"`    // 参数字符串
	Signature string `json:"signature"` // 签名串
}

type VerifyCaptchaArg struct {
	CaptchaID string  `json:"captcha_id"` // 验证id
	Validate  string  `json:"validate"`   // 验证数据
	UserIDs   []int64 `json:"user_ids"`   // 用户id列表
}
