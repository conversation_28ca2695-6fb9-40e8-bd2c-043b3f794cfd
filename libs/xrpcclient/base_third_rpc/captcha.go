package base_third_rpc

import (
	"context"

	"bc/libs/define"
	"bc/libs/generic"
	"bc/libs/log"
	"bc/libs/xplugins"

	"go.uber.org/zap"
)

const (
	RPCGetYiDunCaptchaID = "GetYiDunCaptchaID" // 获取图形验证码ID
	RPCVerifyCaptcha     = "VerifyCaptcha"     // 校验图形验证码
)

func GetYiDunCaptchaID(ctx context.Context, reqID int64) (string, error) {
	var captchaID string
	reply := define.Reply{Data: &captchaID}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetYiDunCaptchaID, new(define.NoneArg), &reply, span)
	if err != nil {
		log.Error("GetYiDunCaptchaID rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return "", err
	}
	return captchaID, generic.If[error](reply.Ret != define.ErrCodeNone, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg), nil)
}

func VerifyCaptcha(ctx context.Context, reqID int64, captchaID, validate string, users ...int64) bool {
	arg := VerifyCaptchaArg{
		CaptchaID: captchaID,
		Validate:  validate,
		UserIDs:   users,
	}
	var isValid bool
	reply := define.Reply{Data: &isValid}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCVerifyCaptcha, &arg, &reply, span)
	if err != nil {
		log.Error("VerifyCaptcha rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return false
	}
	if reply.Ret != define.ErrCodeNone {
		log.Error("VerifyCaptcha rpc call result code error",
			zap.Int64("reqID", reqID), zap.Int("code", reply.Ret), zap.String("msg", reply.Msg))
	}
	return isValid
}
