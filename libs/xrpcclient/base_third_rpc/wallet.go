/*
	用于内部服务调用,与大钱包进行交互
*/

package base_third_rpc

import (
	"context"

	"bc/libs/define"
	"bc/libs/generic"
	"bc/libs/log"
	"bc/libs/xplugins"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

const (
	RPCCreateDepositAddr  = "CreateDepositAddr"  // 创建充币地址
	RPCSubmitWithdraw     = "SubmitWithdraw"     // 向钱包提交提币
	RPCVerifyAddr         = "VerifyAddr"         // 验证地址正确性
	RPCGetWalletBalance   = "GetWalletBalance"   // 获取钱包资产
	RPCVerifyCallbackSign = "VerifyCallbackSign" // 验证钱包回调参数签名
)

func CreateDepositAddr(ctx context.Context, reqID int64, coinName, label string, needTag bool) (string, error) {
	arg := CreateDepositAddrArg{
		CoinName: coinName,
		Label:    label,
		NeedTag:  needTag,
	}
	var addr string
	reply := define.Reply{Data: &addr}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCCreateDepositAddr, &arg, &reply, span)
	if err != nil {
		log.Error("CreateDepositAddr rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return "", err
	}
	return addr, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func SubmitWithdraw(ctx context.Context, reqID int64, coinName, toAddr string, amount decimal.Decimal, localID int64, needTag bool) (*WithdrawResult, error) {
	arg := SubmitWithdrawArg{
		CoinName: coinName,
		ToAddr:   toAddr,
		Amount:   amount,
		LocalID:  localID,
		NeedTag:  needTag,
	}
	var result WithdrawResult
	reply := define.Reply{Data: &result}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCSubmitWithdraw, &arg, &reply, span)
	if err != nil {
		log.Error("SubmitWithdraw rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return &result, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func VerifyAddr(ctx context.Context, reqID int64, coinName, addr, shareAddr string) bool {
	arg := VerifyAddrArg{
		CoinName:  coinName,
		Addr:      addr,
		ShareAddr: shareAddr,
	}
	var isValid bool
	reply := define.Reply{Data: &isValid}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCVerifyAddr, &arg, &reply, span)
	if err != nil {
		log.Error("VerifyAddr rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return false
	}
	if reply.Ret != define.ErrCodeNone {
		log.Error("VerifyAddr rpc call result code error",
			zap.Int64("reqID", reqID), zap.Int("code", reply.Ret), zap.String("msg", reply.Msg))
	}
	return isValid
}

func GetWalletBalance(ctx context.Context, reqID int64) (map[string]decimal.Decimal, error) {
	assets := make(map[string]decimal.Decimal)
	reply := define.Reply{Data: assets}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetWalletBalance, new(define.NoneArg), &reply, span)
	if err != nil {
		log.Error("GetWalletBalance rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return assets, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func VerifyCallbackSign(ctx context.Context, reqID int64, params, signature string) bool {
	arg := VerifyCallbackSignArg{
		Params:    params,
		Signature: signature,
	}
	var isValid bool
	reply := define.Reply{Data: &isValid}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCVerifyCallbackSign, &arg, &reply, span)
	if err != nil {
		log.Error("VerifyCallbackSign rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return false
	}
	if reply.Ret != define.ErrCodeNone {
		log.Error("VerifyCallbackSign rpc call result code error",
			zap.Int64("reqID", reqID), zap.Int("code", reply.Ret), zap.String("msg", reply.Msg))
	}
	return isValid
}
