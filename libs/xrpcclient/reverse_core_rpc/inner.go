package reverse_core_rpc

import (
	"context"

	"bc/libs/define"
	"bc/libs/generic"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/xplugins"

	"go.uber.org/zap"
)

const (
	RPCIsNotHasUndoneOrder = "IsNotHasUndoneOrder" // 判断当前是否没有未完成订单
)

func IsNotHasUndoneOrder(ctx context.Context, reqID int64, userID int64) error {
	arg := proto.UserArg{
		UserID: userID,
	}
	var reply define.Reply

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCIsNotHasUndoneOrder, &arg, &reply, span)
	if err != nil {
		log.Error("IsNotHasUndoneOrder rpc call fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		return err
	}
	return generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}
