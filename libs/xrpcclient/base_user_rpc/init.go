package base_user_rpc

import (
	"context"
	"time"

	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/xplugins"
	"bc/libs/xrpc"
	"github.com/smallnest/rpcx/client"
	"github.com/smallnest/rpcx/protocol"
)

type Client struct {
	cli *xrpc.Client
	ctx context.Context
}

var cli = new(Client)

func InitUserClient(ctx context.Context) {
	option := &client.DefaultOption
	option.SerializeType = protocol.JSON
	co := xrpc.ClientOption{
		BasePath:    define.BasePath,
		ServiceName: define.BaseServerNameUser,
		NodeName:    conf.LocalName(),
		Discovery:   conf.Discovery(),
		Option:      option,
		PoolSize:    5,
	}
	cli.ctx = ctx
	cli.cli = xrpc.NewXClient(co)
}

func Close() {
	cli.cli.ClosePool()
}

func (c *Client) Call(ctx context.Context, method string, arg interface{}, reply *define.Reply, span *xplugins.SpanValues) error {
	select {
	case <-c.ctx.Done():
		return define.ErrMsgServerNotAvailable
	default:
	}

	if ctx == nil {
		ctx = c.ctx
	}
	_ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()

	return c.cli.Call(context.WithValue(_ctx, xplugins.WrapRequestKey, span), method, arg, reply)
}
