package base_user_rpc

import (
	"time"

	"bc/libs/alias"
	"bc/libs/define"

	"github.com/shopspring/decimal"
)

type InnerUserArg struct {
	UserID     int64 `json:"user_id"`     // 用户id
	PlatformID int   `json:"platform_id"` // 平台id
	ForceDB    bool  `json:"force_db"`    // 是否强制从数据库获取,默认优先从缓存获取
}

type InnerUser struct {
	UserID          int64                    `db:"user_id" json:"user_id"`                     // 用户id
	UserName        string                   `db:"user_name" json:"user_name"`                 // 用户名
	Nickname        string                   `db:"nickname" json:"nickname"`                   // 中文昵称
	NicknameEn      string                   `db:"nickname_en" json:"nickname_en"`             // 英文昵称
	Introduce       string                   `db:"introduce" json:"introduce"`                 // 中文介绍
	IntroduceEn     string                   `db:"introduce_en" json:"introduce_en"`           // 英文介绍
	Avatar          string                   `db:"avatar" json:"avatar"`                       // 头像
	LoginPasswd     string                   `db:"login_passwd" json:"login_passwd"`           // 登录密码
	FundPasswd      string                   `db:"fund_passwd" json:"fund_passwd"`             // 资金密码
	InviteCode      string                   `db:"invite_code" json:"invite_code"`             // 邀请码
	InviteParent    string                   `db:"invite_parent" json:"invite_parent"`         // 上级邀请链
	RealName        string                   `db:"real_name" json:"real_name"`                 // 用户真实姓名
	CardNo          string                   `db:"card_no" json:"card_no"`                     // 证件号码
	Phone           string                   `db:"phone" json:"phone"`                         // 手机号
	Email           string                   `db:"email" json:"email"`                         // 邮箱
	SpareEmail      string                   `db:"spare_email" json:"spare_email"`             // 备用邮箱
	TotpSecret      string                   `db:"totp_secret" json:"totp_secret"`             // totp验证器私钥
	AreaCode        string                   `db:"area_code" json:"area_code"`                 // 地区区号
	CountryCode     string                   `db:"country_code" json:"country_code"`           // 国家代码
	Content         string                   `db:"content" json:"content"`                     // 备注
	Rebate          decimal.Decimal          `db:"rebate" json:"rebate"`                       // 分佣比例
	ForbidOpenClose uint8                    `db:"forbid_open_close" json:"forbid_open_close"` // 开平仓权限 0: 都不禁止 1: 禁止开仓 2: 禁止平仓
	TradeVerifyFund uint8                    `db:"trade_verify_fund" json:"trade_verify_fund"` // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
	ProfitStyle     int                      `db:"profit_style" json:"profit_style"`           // 盈亏计算方式 0-使用标记价格 1-使用成交价格
	WithdrawVerify  int                      `db:"withdraw_verify" json:"withdraw_verify"`     // 提币活体验证状态
	Verify          define.UserVerifyState   `db:"verify" json:"verify"`                       // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
	PlatformID      int                      `db:"platform_id" json:"platform_id"`             // 平台id
	LabelID         int                      `db:"label_id" json:"label_id"`                   // 标签id
	DealerState     define.DealerState       `db:"dealer_state" json:"dealer_state"`           // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
	WithdrawLimit   define.UserWithdrawLimit `db:"withdraw_limit" json:"withdraw_limit"`       // 提币限制标记 1-不限制 2-提币限制 3-取消提币限制
	EnableLogin     alias.NumBool            `db:"enable_login" json:"enable_login"`           // 是否可登录
	EnableWithdraw  alias.NumBool            `db:"enable_withdraw" json:"enable_withdraw"`     // 是否可提现
	EnableTrade     alias.NumBool            `db:"enable_trade" json:"enable_trade"`           // 是否可交易
	IsAgent         alias.NumBool            `db:"is_agent" json:"is_agent"`                   // 是否是代理
	IsOpenAPI       alias.NumBool            `db:"is_open_api" json:"is_open_api,omitempty"`   // 是否有权限开通api
	LimitState      define.UserLimitState    `db:"limit_state" json:"limit_state"`             // 限制状态
	CreatedTime     time.Time                `db:"created_time" json:"created_time"`           // 创建时间
}

func (u *InnerUser) IsTrader() bool {
	return u.DealerState == define.DealerStateNormal
}

type InnerUserLabelArg struct {
	InnerUserArg
	ContractCode string `json:"contract_code"` // 合约Code
}

type InnerUserLabel struct {
	ID             int             `db:"id" json:"id"`
	LabelID        int             `db:"label_id" json:"label_id"`                 // 标签id
	ContractCode   string          `db:"contract_code" json:"contract_code"`       // 合约代码
	MaxLever       int             `db:"max_lever" json:"max_lever"`               // 最大杠杆
	MaxOrderVolume decimal.Decimal `db:"max_order_volume" json:"max_order_volume"` // 单笔最大下单量
	MinOrderVolume decimal.Decimal `db:"min_order_volume" json:"min_order_volume"` // 单笔最小下单量
	MaxPosiVolume  decimal.Decimal `db:"max_posi_volume" json:"max_posi_volume"`   // 最大持仓张数
	Fee            decimal.Decimal `db:"fee" json:"fee"`                           // 手续费
	Funding        decimal.Decimal `db:"funding" json:"funding"`                   // 资金费用
	MaxSlippage    decimal.Decimal `db:"slippage" json:"slippage"`                 // 最大滑点价差
	MinSlippage    decimal.Decimal `db:"min_slippage" json:"min_slippage"`         // 最小滑点
	RiskRate       decimal.Decimal `db:"risk_rate" json:"risk_rate"`               // 风险率加点
}

type InnerTopUser struct {
	Petname string `db:"petname" json:"petname"` // 顶级用户昵称
}

type InnerUpdateUserWithdrawLimitArg struct {
	InnerUserArg
	NewState define.UserWithdrawLimit `json:"new_state"` // 新状态
}
