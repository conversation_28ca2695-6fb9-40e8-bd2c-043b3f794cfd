/*
	内部接口,用于内部服务调用,获取用户数据
*/

package base_user_rpc

import (
	"context"

	"bc/libs/define"
	"bc/libs/generic"
	"bc/libs/proto"
	"bc/libs/xplugins"
)

const (
	RPCGetInnerUserByID        = "GetInnerUserByID"        // 通过用户id获取用户信息
	RPCGetInnerUserLabels      = "GetInnerUserLabels"      // 获取用户所有合约的标签配置
	RPCGetInnerUserLabelByCode = "GetInnerUserLabelByCode" // 获取用户指定合约的标签配置
	RPCAdminUserState          = "AdminUserState"          // 用户状态更新
	RPCGetTopUserByID          = "GetTopUserByID"          // 获取顶级代理用户信息
	RPCUpdateUserWithdrawLimit = "UpdateUserWithdrawLimit" // 更新用户提币限制状态
)

func GetInnerUserByID(ctx context.Context, reqID, userID int64, forceDB bool) (*InnerUser, error) {
	arg := &InnerUserArg{
		UserID:  userID,
		ForceDB: forceDB,
	}
	var user InnerUser
	reply := define.Reply{Data: &user}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetInnerUserByID, arg, &reply, span)
	if err != nil {
		return nil, err
	}

	return &user, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func GetInnerUserLabels(ctx context.Context, reqID, userID int64, forceDB bool) (map[string]InnerUserLabel, error) {
	arg := &InnerUserArg{
		UserID:  userID,
		ForceDB: forceDB,
	}
	labels := make(map[string]InnerUserLabel)
	reply := define.Reply{Data: labels}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetInnerUserLabels, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return labels, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func GetInnerUserLabelByCode(ctx context.Context, reqID, userID int64, contractCode string, forceDB bool) (*InnerUserLabel, error) {
	arg := &InnerUserLabelArg{
		InnerUserArg: InnerUserArg{
			UserID:  userID,
			ForceDB: forceDB,
		},
		ContractCode: contractCode,
	}
	var label InnerUserLabel
	reply := define.Reply{Data: &label}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetInnerUserLabelByCode, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return &label, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func AdminUserState(ctx context.Context, reqID, userID int64) error {
	arg := &proto.UserArg{UserID: userID}
	span := xplugins.NewSpan().SetRequestID(reqID)

	return cli.Call(ctx, RPCAdminUserState, arg, &define.Reply{}, span)
}

func GetTopUserByID(ctx context.Context, reqID, userID int64) (*InnerTopUser, error) {
	arg := &proto.UserArg{UserID: userID}
	var top InnerTopUser
	reply := define.Reply{Data: &top}

	span := xplugins.NewSpan().SetRequestID(reqID)

	err := cli.Call(ctx, RPCGetTopUserByID, arg, &reply, span)
	if err != nil {
		return nil, err
	}
	return &top, generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}

func UpdateUserWithdrawLimit(ctx context.Context, reqID, userID int64, platformID int, newState define.UserWithdrawLimit) error {
	arg := &InnerUpdateUserWithdrawLimitArg{
		InnerUserArg: InnerUserArg{
			UserID:     userID,
			PlatformID: platformID,
			ForceDB:    true,
		},
		NewState: newState,
	}

	span := xplugins.NewSpan().SetRequestID(reqID)

	var reply define.Reply
	err := cli.Call(ctx, RPCUpdateUserWithdrawLimit, arg, &reply, span)
	if err != nil {
		return err
	}
	return generic.If[error](reply.Ret == define.ErrCodeNone, nil, define.NewReplyErrorWithMsg(reply.Ret, reply.Msg))
}
