package xplugins

import (
	"context"

	"github.com/smallnest/rpcx/share"
)

type ClientPlugin struct{}

func (p *ClientPlugin) PreCall(ctx context.Context, servicePath, serviceMethod string, args interface{}) error {
	v := ctx.Value(WrapRequestKey)
	if v == nil {
		return nil
	}

	span, ok := v.(*SpanValues)
	if ok {
		if rpcxContext, ok := ctx.(*share.Context); ok {
			metadata := rpcxContext.Value(share.ReqMetaDataKey)
			if metadata != nil {
				m := metadata.(map[string]string)
				for k, v := range span.GetValues() {
					m[k] = v
				}
			} else {
				metadata = span.GetValues()
			}
			rpcxContext.SetValue(share.ReqMetaDataKey, metadata)

			if rpcxContext.Value(share.ResMetaDataKey) == nil {
				rpcxContext.SetValue(share.ResMetaDataKey, make(map[string]string))
			}
		}
	}
	return nil
}

func (p *ClientPlugin) PostCall(ctx context.Context, servicePath, serviceMethod string, args interface{}, reply interface{}, err error) error {
	span, err := getSpanContextFromResContext(ctx)
	if err != nil || span == nil {
		return nil
	}

	v := ctx.Value(WrapRequestKey)
	if v == nil {
		return nil
	}

	resSpan, ok := v.(*SpanValues)
	if ok {
		resSpan.SetResponseNode(span.GetResponseNode())
	}

	return nil
}

func getSpanContextFromResContext(ctx context.Context) (*SpanValues, error) {
	span := NewSpan()
	if rpcxContext, ok := ctx.(*share.Context); ok {
		reqMeta, ok := rpcxContext.Value(share.ResMetaDataKey).(map[string]string)
		if !ok {
			return nil, nil
		}
		return convertSpanValues(reqMeta), nil
	}
	return span, nil
}
