package xplugins

import (
	"context"

	"github.com/smallnest/rpcx/protocol"
	"github.com/smallnest/rpcx/share"
)

type ServerPlugin struct{}

func (p ServerPlugin) PreHandleRequest(ctx context.Context, r *protocol.Message) error {
	span, err := getSpanContextFromReqContext(ctx)
	if err != nil || span == nil {
		return nil
	}

	if rpcxContext, ok := ctx.(*share.Context); ok {
		rpcxContext.SetValue(WrapResponseKey, span)
	}

	return nil
}

func (p ServerPlugin) PostCall(ctx context.Context, serviceName, methodName string, args, reply interface{}) (interface{}, error) {
	v := ctx.Value(WrapResponseKey)
	if v == nil {
		return reply, nil
	}

	span, ok := v.(*SpanValues)
	if ok {
		if rpcxContext, ok := ctx.(*share.Context); ok {
			resSpan, ok := rpcxContext.Value(share.ResMetaDataKey).(map[string]string)
			if ok {
				for key, val := range span.GetValues() {
					resSpan[key] = val
				}
			}
		}
	}
	return reply, nil
}

func getSpanContextFromReqContext(ctx context.Context) (*SpanValues, error) {
	reqMeta, ok := ctx.Value(share.ReqMetaDataKey).(map[string]string)
	if !ok {
		return nil, nil
	}

	return convertSpanValues(reqMeta).SetResponseNode(_nodeName), nil
}
