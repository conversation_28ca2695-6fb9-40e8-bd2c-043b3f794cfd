package proto

import (
	"bc/libs/define"
	"time"
)

// 登陆请求参数
type C2CLoginArg struct {
	OrderID int64 `json:"order_id"` // 本地订单id
}

// 登陆返回值
type C2CLoginReply struct {
	Token string `json:"token"` // token
}

type LegalOrder struct {
	OrderID        int64                  `json:"order_id" db:"order_id"`             // 本地订单号
	CoinID         int                    `json:"coin_id" db:"coin_id"`               // 币种id
	CoinName       string                 `json:"coin_name" db:"coin_name"`           // 币种名称
	UserID         int64                  `json:"user_id" db:"user_id"`               // 用户id
	OrderType      int                    `json:"order_type" db:"order_type"`         // 订单类型 1-出售 2-购买
	State          define.LegalOrderState `json:"state" db:"state"`                   // 订单状态 0-卖单待审核 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8-后台取消
	Amount         string                 `json:"amount" db:"amount"`                 // 订单数量
	LegalAmount    string                 `json:"legal_amount" db:"legal_amount"`     // 法币数量
	PlatformPrice  string                 `json:"platform_price" db:"platform_price"` // 汇率
	Balance        string                 `json:"balance" db:"balance"`               // 剩余账户资产
	LockAmount     string                 `json:"lock_amount" db:"lock_amount"`       // 冻结资产
	AvailAmount    string                 `json:"avail_amount" db:"avail_amount"`     // 可用数量
	CreateTime     int64                  `json:"create_time" db:"create_time"`       // 创建时间
	CloseTime      int64                  `json:"close_time" db:"close_time"`         // 完成时间
	PayOrderID     int64                  `json:"pay_order_id" db:"pay_order_id"`     // 三方交易id
	IPAddress      string                 `json:"ip_address" db:"ip_address"`         // 下单ip
	IMEI           string                 `json:"imei" db:"imei"`                     // 下单设备标识
	OrderClient    define.OsType          `json:"order_client" db:"order_client"`     // 下单设备类型
	PaymentId      int                    `db:"payment_id" json:"payment_id"`         //用户银行卡ID
	PaymentType    int                    `db:"payment_type" json:"payment_type"`     //收款类型 1-银行 2-支付宝 3-微信
	BankName       string                 `db:"bank_name" json:"bank_name"`           //开户行
	BankBranchName string                 `db:"bank_branch_name" json:"bank_branch_name"`
	BankNumb       string                 `db:"bank_numb" json:"bank_numb"`           //收款账户
	AccountHolder  string                 `db:"account_holder" json:"account_holder"` //收款人
	OcrAddress     string                 `db:"ocr_address" json:"ocr_address"`       //ocr地址
	AuditManager   string                 `db:"audit_manager" json:"audit_manager"`   //
	AuditTime      time.Time              `db:"audit_time" json:"audit_time"`
}

type ApiLegalOrderArg struct {
	State define.LegalOrderApiState `json:"state"` // 订单状态 1-进行中 2-已完成 3-已取消
}

type ApiLegalOrder struct {
	OrderID        int64                  `json:"order_id" db:"order_id"`             // 本地订单号
	CoinID         int                    `json:"coin_id" db:"coin_id"`               // 币种id
	CoinName       string                 `json:"coin_name" db:"coin_name"`           // 币种名称
	OrderType      int                    `json:"order_type" db:"order_type"`         // 订单类型 1-出售 2-购买
	State          define.LegalOrderState `json:"state" db:"state"`                   // 订单状态 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8:后台审核失败
	Amount         string                 `json:"amount" db:"amount"`                 // 订单数量
	LegalAmount    string                 `json:"legal_amount" db:"legal_amount"`     // 法币数量
	CreateTime     int64                  `json:"create_time" db:"create_time"`       // 创建时间
	CloseTime      int64                  `json:"close_time" db:"close_time"`         // 完成时间
	PlatformPrice  string                 `db:"platform_price" json:"platform_price"` //平台汇率
	PaymentType    int                    `db:"payment_type" json:"payment_type"`     //收款类型 1-银行 2-支付宝
	BankName       string                 `db:"bank_name" json:"bank_name"`           //开户行
	BankBranchName string                 `db:"bank_branch_name" json:"bank_branch_name"`
	BankNumb       string                 `db:"bank_numb" json:"bank_numb"`           //收款账户
	AccountHolder  string                 `db:"account_holder" json:"account_holder"` //收款人
	OcrAddress     string                 `db:"ocr_address" json:"ocr_address"`       //ocr地址
}

type ApiLegalOrderLimit struct {
	ValueMin  string `json:"value_min"`  // 最小下单价值
	ValueMax  string `json:"value_max"`  // 最大下单价值
	LegalRate string `json:"legal_rate"` // 单价汇率
}

type ApiLegalPaymentArg struct {
	ID             int    `db:"id" json:"id"`
	UserId         int64  `db:"user_id" json:"user_id"`
	Type           int    `db:"type" json:"type"`
	BankName       string `db:"bank_name" json:"bank_name"`
	BankBranchName string `db:"bank_branch_name" json:"bank_branch_name"`
	BankNumb       string `db:"bank_numb" json:"bank_numb"`
	AccountHolder  string `db:"account_holder" json:"account_holder"`
	OcrAddress     string `db:"ocr_address" json:"ocr_address"`
}

type ApiExchangeRate struct {
	ExchangeRate string `json:"exchange_rate"`
	BusinessID   int64  `json:"business_id"`
	RateToken    string `json:"rate_token"`
	Amount       string `json:"amount"`
	AmountCn     string `json:"amount_cn"`
}
