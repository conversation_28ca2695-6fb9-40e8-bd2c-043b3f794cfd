package proto

import (
	"bc/libs/okex.v5"
	"github.com/shopspring/decimal"
	"time"
)

type HedgeAsset struct {
	HistoryId              int       `db:"history_id" json:"history_id"`                             //记录id
	GroupID                int       `db:"group_id" json:"group_id"`                                 //组id
	HedgeAccount           string    `db:"hedge_account" json:"hedge_account"`                       //对冲子账号
	ContractAccount        string    `db:"contract_account" json:"contract_account"`                 //合约账户
	Available              float64   `db:"available" json:"available"`                               //可用数量
	UsedAmount             float64   `db:"used_amount" json:"used_amount"`                           //已用数量
	Balance                float64   `db:"balance" json:"balance"`                                   //账户权益
	CloseProfit            float64   `db:"close_profit" json:"close_profit"`                         //已实现盈亏
	UncloseProfit          float64   `db:"unclose_profit" json:"unclose_profit"`                     //未实现盈亏
	LockAmount             float64   `db:"lock_amount" json:"lock_amount"`                           //冻结数量
	MarginRatio            float64   `db:"margin_ratio" json:"margin_ratio"`                         //保证金率
	MaintenanceMarginRatio float64   `db:"maintenance_margin_ratio" json:"maintenance_margin_ratio"` //维持保证金率
	CreatedAt              time.Time `db:"created_at" json:"created_at"`                             //创建时间
}

type HedgeConfig struct {
	ConfigId     int       `db:"config_id" json:"config_id"`         //配置id
	GroupID      int       `db:"group_id" json:"group_id"`           //组id
	HedgeAccount string    `db:"hedge_account" json:"hedge_account"` //对冲子账号
	CreatedAt    time.Time `db:"created_at" json:"created_at"`       //创建时间
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`       //修改时间
	ApiStatus    int       `db:"api_status" json:"api_status"`       //api状态
	PublicKey    string    `db:"public_key" json:"public_key"`       //公钥
	PrivateKey   string    `db:"private_key" json:"private_key"`     //私钥
	HintPhrase   string    `db:"hint_phrase" json:"hint_phrase"`     //短语
	OrderBy      int       `db:"order_by" json:"order_by"`           //排序
	IsSim        bool
}

//max_position_vol, `min_available`, margin_ratio, notice_switch
type HedgeContractConfig struct {
	ConfigId        int       `db:"config_id" json:"config_id"`         //配置id
	GroupID         int       `db:"group_id" json:"group_id"`           // 组id
	HedgeAccount    string    `db:"hedge_account" json:"hedge_account"` //对冲子账号
	Lever           int       `db:"lever" json:"lever"`
	ContractCode    string    `db:"contract_code" json:"contract_code"`
	MarginDirection int       `db:"margin_direction" json:"margin_direction"`
	ContractType    int       `db:"contract_type" json:"contract_type"` //0:永续，1：交割合约
	AccountType     int       `db:"account_type" json:"account_type"`
	HedgeStatus     int       `db:"hedge_status" json:"hedge_status"` //对冲状态
	CreatedAt       time.Time `db:"created_at" json:"created_at"`

	MaxPositionVol int     `db:"max_position_vol" json:"max_position_vol"`
	MinAvailable   float64 `db:"min_available" json:"min_available"`
	MarginRatio    float64 `db:"margin_ratio" json:"margin_ratio"`
	NoticeSwitch   bool    `db:"notice_switch" json:"notice_switch"`

	RiskExposure     decimal.Decimal `db:"risk_exposure" json:"risk_exposure"`           // 风险敞口币值
	MinExposureRatio decimal.Decimal `db:"min_exposure_ratio" json:"min_exposure_ratio"` // 最小敞口保留比例
	HedgeRatio       decimal.Decimal `db:"hedge_ratio" json:"hedge_ratio"`               // 对冲比例
	HedgingAdjust    decimal.Decimal `db:"hedging_adjust" json:"hedging_adjust"`         // 对冲下单价格调整比例
}

type HedgePosition struct {
	HedgeAccount    string            `db:"hedge_account" json:"hedge_account"`       //对冲子账号
	ContractAccount string            `db:"contract_account" json:"contract_account"` //合约账户
	Volume          int               `db:"volume" json:"volume"`                     //持仓数量
	Side            okex.PositionSide `json:"side"`                                   // 持仓方向
	ForcePrice      float64           `db:"force_price" json:"force_price"`           //强平价格
	LongValue       float64           `db:"long_value" json:"long_value"`             //持多价值
	ShortValue      float64           `db:"short_value" json:"short_value"`           //持空价值
	LongPrice       float64           `db:"long_price" json:"long_price"`             //持多开仓均价
	ShortPrice      float64           `db:"short_price" json:"short_price"`           //持空开仓均价
	UnclosePrice    float64           `db:"unclose_profit" json:"unclose_price"`      //未实现盈亏
}

type OkExPosition struct {
	ContractAccount string `db:"contract_account" json:"contract_account"` //合约账户
	Volume          int    `db:"volume" json:"volume"`
	Lever           int    `db:"lever" json:"lever"`
	Side            string `json:"side"`
}

type HedgePositionHistory struct {
	PositionId      int     `db:"position_id" json:"position_id"`           //持仓id
	HedgeAccount    string  `db:"hedge_account" json:"hedge_account"`       //对冲子账号
	ContractAccount string  `db:"contract_account" json:"contract_account"` //合约账户
	ForcePrice      float64 `db:"force_price" json:"force_price"`           //强平价格
	LongValue       float64 `db:"long_value" json:"long_value"`             //持多价值
	ShortValue      float64 `db:"short_value" json:"short_value"`           //持空价值
	LongPrice       float64 `db:"long_price" json:"long_price"`             //持多开仓均价
	ShortPrice      float64 `db:"short_price" json:"short_price"`           //持空开仓均价
	UncloseProfit   float64 `db:"unclose_profit" json:"unclose_profit"`     //未实现盈亏
	CreatedAt       int64   `db:"created_at" json:"created_at"`             //记录时间
}
