package proto

import "github.com/shopspring/decimal"

type UserPositionStat struct {
	TotalFloatProfit    decimal.Decimal //总未实现影库
	TotalInitMargin     decimal.Decimal //总用户初始保证金
	TotalRealInitMargin decimal.Decimal //合约实际处理保证金（实际初始保证金=（空头开仓价值+多头开仓价值）/合约支持的最大杠杆倍数）
	TotalMaintainMargin decimal.Decimal //总维持保证金
	TotalCloseFee       decimal.Decimal //总预扣平仓费用
	RiskRate            decimal.Decimal //最高风险率
	ConStatMap          map[string]UserContractStat
}

type UserContractStat struct {
	UserID         int64
	Code           string
	BuyWorth       decimal.Decimal //多价值
	SellWorth      decimal.Decimal //空价值
	FloatProfit    decimal.Decimal
	PerValue       decimal.Decimal
	RealInitMargin decimal.Decimal //实际保证金
	MaintainMargin decimal.Decimal //维持保证金
	MaintainRate   decimal.Decimal //维持保证金率
	InitMargin     decimal.Decimal //合约初始保证金累计
	TotalMargin    decimal.Decimal //总保证金余额(包括手续费）
	CloseFee       decimal.Decimal //总平仓手续费
	NetPosVolume   int
	Lever          int             //用户持仓杠杆
	MaxFullLever   int             //合约最大可用杠杆
	RiskRate       decimal.Decimal //最高风险率
	Precision      int32
	ForcePrice     decimal.Decimal
	BrokPrice      decimal.Decimal
	Index          ContractMark //合约标记价格统计
	AvgPriceBuy    decimal.Decimal
	AvgPriceSell   decimal.Decimal
}
