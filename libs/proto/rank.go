package proto

type IncreaseRank struct {
	ContractCode   string  `json:"contract_code" db:"contract_code"`       // 合约代码
	ContractName   string  `json:"contract_name" db:"contract_name"`       // 合约名称
	ContractNameEn string  `json:"contract_name_en" db:"contract_name_en"` // 合约英文名称
	Index          float64 `json:"index" db:"index"`                       // 合约指数
	IndexStr       string  `json:"index_str" db:"index_str"`               // 合约指数(字符串)
	IncreaseRatio  float64 `json:"increase_ratio" db:"increase_ratio"`     // 合约涨幅
}

type PersonRank struct {
	ContractCode   string  `json:"contract_code" db:"contract_code"`       // 合约代码
	ContractName   string  `json:"contract_name" db:"contract_name"`       // 合约名称
	ContractNameEn string  `json:"contract_name_en" db:"contract_name_en"` // 合约英文名称
	Index          float64 `json:"index" db:"index"`                       // 合约指数
	IndexStr       string  `json:"index_str" db:"index_str"`               // 合约指数(字符串)
	Amount         int64   `json:"amount" db:"amount"`                     // 人数
}
