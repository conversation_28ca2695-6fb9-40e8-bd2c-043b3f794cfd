package proto

import (
	"time"

	"bc/libs/define"
)

type ClientErrorArg struct {
	List []ClientErrorLog `json:"list"`
}

type ClientErrorLog struct {
	ErrorID    int64         `json:"error_id" db:"error_id"`       // 记录id
	ErrorType  uint16        `json:"error_type" db:"error_type"`   // 错误类型 1-本地异常 2-api response 3-Ws response ... 其它http错误码
	ErrorMsg   string        `json:"error_msg" db:"error_msg"`     // 报错内容
	Os         define.OsType `json:"os" db:"os"`                   // 设备类型（1: android 2: iOS 3: WEB 4: H5 7: 管理后台)
	Version    int           `db:"version" json:"version"`         // app版本号
	SystemOs   string        `json:"system_os" db:"system_os"`     // 操作系统
	DeviceName string        `json:"device_name" db:"device_name"` // 设备型号
	IPAddress  string        `json:"ip_address" db:"ip_address"`   // ip地址
	Location   string        `json:"location" db:"location"`       // 所在地
	UserID     int64         `db:"user_id" json:"user_id"`         // 用户id
	ErrorTime  int64         `json:"error_time" db:"error_time"`   // 错误产生时间
	SubmitTime time.Time     `json:"submit_time" db:"submit_time"` // 提交时间
}

type AppDownloadSubmitArg struct {
	Os           define.OsType `json:"os"`            // 设备类型（1: android 2: iOS)
	DownloadMode int           `json:"download_mode"` // 下载模式 1-极速下载 2-本地下载 3-本地更新 4-备用下载
	AppVersion   string        `json:"app_version"`   // 下载版本号
}

type CollectDomainDelayArg struct {
	List []CollectDomainDelaySubArg `json:"list"`
}

type CollectDomainDelaySubArg struct {
	Domain string `json:"domain"` // 域名
	Delay  uint   `json:"delay"`  // 请求耗时(超时传999999)
}

type CollectDomainDelay struct {
	UserID        int64         `db:"user_id" json:"user_id"`               // 用户id
	UserName      string        `db:"user_name" json:"user_name"`           // 用户名
	OsType        define.OsType `db:"os_type" json:"os_type"`               // 设备类型
	IMEI          string        `db:"imei" json:"imei"`                     // 设备标识
	IPAddress     string        `db:"ip_address" json:"ip_address"`         // ip地址
	IPLocation    string        `db:"ip_location" json:"ip_location"`       // ip位置
	FastestDomain string        `db:"fastest_domain" json:"fastest_domain"` // 最快的域名
	FastestDelay  uint          `db:"fastest_delay" json:"fastest_delay"`   // 最快的域名耗时(毫秒)
	SlowestDomain string        `db:"slowest_domain" json:"slowest_domain"` // 最慢的域名
	SlowestDelay  uint          `db:"slowest_delay" json:"slowest_delay"`   // 最慢的域名耗时(毫秒)
	RawData       string        `db:"raw_data" json:"raw_data"`             // 原始数据
	CreateTime    time.Time     `db:"create_time" json:"create_time"`       // 创建时间
}
