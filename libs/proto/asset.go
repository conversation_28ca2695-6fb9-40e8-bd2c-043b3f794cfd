package proto

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
	"time"
)

type ApiAssetRecord struct {
	CoinID    int    `json:"coin_id" db:"coin_id"`     // 币种id
	CoinName  string `json:"coin_name" db:"coin_name"` // 币种名
	Type      int    `json:"type" db:"type"`           // 类型
	Amount    string `json:"amount" db:"amount"`       // 活动数量
	Balance   string `json:"balance" db:"balance"`     // 剩余账户资产
	Available string `json:"available" db:"available"` // 剩余可用余额
	CreateBy  int64  `json:"create_by" db:"create_by"` // 发生时间
}

type ApiAsset struct {
	CoinID           int    `json:"coin_id" db:"coin_id"`                 // 币种id
	CoinName         string `json:"coin_name" db:"coin_name"`             // 币种名
	Balance          string `json:"balance" db:"balance"`                 // 账户资产
	Available        string `json:"available" db:"available"`             // 可用余额
	Deposit          string `json:"deposit" db:"deposit"`                 // 保证金余额
	OrderMargin      string `json:"order_margin" db:"order_margin"`       // 委托保证金
	PositionMargin   string `json:"position_margin" db:"position_margin"` // 仓位保证金
	UnrealizedProfit string `json:"unrealized_profit" db:"float_profit"`  // 未实现盈亏
	CloseProfit      string `json:"close_profit" db:"total_close_profit"` // 已实现盈亏
	HoldVolume       int    `json:"hold_volume" db:"volume"`              // 持有张数
	RepayRate        string `json:"repay_rate" db:"roic"`                 // 回报率
	CanTransfer      bool   `json:"can_transfer" db:"can_transfer"`       // 是否支持划转
	MinRecharge      string `json:"min_recharge" db:"min_recharge"`       // 最小转入数量
	MaxWithdraw      string `json:"max_withdraw" db:"max_withdraw"`       // 最大转出数量
}

type UserWalletSummary struct {
	CoinID           int     `json:"coin_id" db:"coin_id"`                 // 币种id
	CoinName         string  `json:"coin_name" db:"coin_name"`             // 币种名称
	Balance          float64 `json:"balance" db:"balance"`                 // 账户资产
	Available        float64 `json:"available" db:"available"`             // 可用余额
	OrderMargin      float64 `json:"order_margin" db:"order_margin"`       // 委托保证金
	DepositMargin    float64 `json:"deposit_margin" db:"deposit_margin"`   // 保证金余额
	PositionMargin   float64 `json:"position_margin" db:"position_margin"` // 仓位保证金
	UnrealizedProfit float64 `json:"unrealized_profit" db:"float_profit"`  // 未实现盈亏
	CloseProfit      float64 `json:"close_profit" db:"total_close_profit"` // 已实现盈亏
	CanTransfer      bool    `json:"can_transfer" db:"can_transfer"`       // 是否支持划转
	MinRecharge      string  `json:"min_recharge" db:"min_recharge"`       // 最小转入数量
	MaxWithdraw      string  `json:"max_withdraw" db:"max_withdraw"`       // 最大转出数量
}

type ApiTransferArg struct {
	CoinID       int          `json:"coin_id"`
	TransferType TransferType `json:"transfer_type"` // 划转类型
	Amount       string       `json:"amount"`        // 划转数量
	YiDunRequest
}

type TransferType int8

const (
	TransferTypeAsset2Trade  TransferType = iota // 0-资金账户->交易账户
	TransferTypeTrade2Asset                      // 1-交易账户->资金账户
	TransferTypeAsset2Follow                     // 2-资金账户->跟单账户
	TransferTypeFollow2Asset                     // 3-跟单账户->资金账户
	TransferTypeTrade2Follow                     // 4-交易账户->跟单账户
	TransferTypeFollow2Trade                     // 5-跟单账户->交易账户
)

type AssetStat struct {
	Margin      float64         `db:"margin"`
	MarginGift  decimal.Decimal `db:"margin_gift"`
	Commission  float64         `db:"commission"`
	FloatProfit float64         `db:"float_profit"`
}

type InitMarginStat struct {
	ContractCode string `db:"contract_code"` // 合约代码
	InitMargin   string `db:"init_margin"`   // 起始保证金
	Lever        int    `db:"lever"`         // 杠杆
}

// 提现请求参数
type WithdrawArg struct {
	VerifyID int64           `json:"verify_id"` // 校验id
	CoinName string          `json:"coin_name"` // 币种名称
	Amount   decimal.Decimal `json:"amount"`    // 提现金额
	Address  string          `json:"address"`   // 到账地址
	Tag      string          `json:"tag"`       // 币种tag
	Protocol string          `json:"protocol"`  // 协议
	Code     string          `json:"code"`      // 短信验证码
	SafeVerifyCode
}

type DepositAddress struct {
	CoinID  int    `json:"coin_id" db:"coin_id"` // 币种id
	Address string `json:"address" db:"address"` // 地址
}

type WalletBillArg struct {
	//CoinID   int                   `json:"coin_id"`   // 币种id
	BillType int `json:"bill_type"` // 记录类型
}

type AssetDetail struct {
	CoinID          int                `json:"coin_id"`           // 币种id
	CoinName        string             `json:"coin_name"`         // 币种名称
	TotalBalance    float64            `json:"total_balance"`     // 总资产
	TotalBalanceCNY decimal.Decimal    `json:"total_balance_cny"` // 总资产(cny)
	Trade           TradeWalletDetail  `json:"trade"`             // 交易账户
	Wallet          WalletDetail       `json:"wallet"`            // 资产账户
	Follow          FollowWalletDetail `json:"follow"`            // 跟单账户
}

type TradeWalletDetail struct {
	Total       float64         `json:"total"`        // 总资产
	TotalCNY    decimal.Decimal `json:"total_cny"`    // 总资产(CNY)
	Rights      float64         `json:"rights"`       // 权益
	Float       float64         `json:"float"`        // 未实现盈亏
	Margin      float64         `json:"margin"`       // 仓位保证金
	TotalMargin decimal.Decimal `json:"total_margin"` // 总保证金 初始保证金+手续费
	Balance     decimal.Decimal `json:"balance"`      // 账户资产
	Available   float64         `json:"available"`    // 可用余额
	CanTransfer float64         `json:"can_transfer"` // 可划转余额
	LockAmount  decimal.Decimal `json:"lock_amount"`  // 委托冻结
	Diff        decimal.Decimal `json:"diff"`         // 待结算差值
	BlowingRate decimal.Decimal `json:"blowing_rate"` // 平仓风险率
	RiskRate    decimal.Decimal `json:"risk_rate"`    // 账户风险率
}

type WalletDetail struct {
	Total       float64         `json:"total"`        // 总资产
	TotalCNY    decimal.Decimal `json:"total_cny"`    // 总资产(CNY)
	Available   float64         `json:"available"`    // 可用余额
	Frozen      float64         `json:"frozen"`       // 冻结数量
	CanWithdraw string          `json:"can_withdraw"` // 可提现余额
}

type FollowWalletDetail struct {
	Rights       decimal.Decimal `json:"rights"`         // 权益
	Total        decimal.Decimal `json:"total"`          // 总资产
	TotalCNY     decimal.Decimal `json:"total_cny"`      // 总资产(CNY)
	TotalLegal   decimal.Decimal `json:"total_legal"`    // 总折合法币
	TotalLegalV2 decimal.Decimal `json:"total_legal_v2"` // 总折合法币
	Available    decimal.Decimal `json:"available"`      // 可用余额
	Margin       decimal.Decimal `json:"margin"`         // 保证金
	Float        decimal.Decimal `json:"float"`          // 未实现盈亏
	CanTransfer  decimal.Decimal `json:"can_transfer"`   // 可划转余额
	LockAmount   decimal.Decimal `json:"lock_amount"`    // 委托冻结
}

type BrokerageBillArg struct {
	Mode define.WalletBillType `json:"mode"` // 记录类型
}

type AssetDetailArg struct {
	Mode define.AssetMode `db:"mode" json:"mode"` // 资产类型 0-全部 1-资产账户 2-交易账户 3=跟单账户
	UserSymbol
}

type GetWithdrawAddrListArg struct {
	UserID   int64  `json:"user_id"`   // 用户id
	CoinName string `json:"coin_name"` // 币种名称
	Protocol string `json:"protocol"`  // 协议
}

type GetWithdrawAddrListReply struct {
	HasDefault bool                  `db:"has_default" json:"has_default"`
	List       []UserWithdrawAddress `db:"list" json:"list"`
}

type ModifyWithdrawAddrArg struct {
	VerifyID int64 `json:"verify_id"`
	UserWithdrawAddress
}

type DelWithdrawAddrArg struct {
	ID     int64 `json:"id"`      // 记录id
	UserID int64 `json:"user_id"` // 用户id
}

type UserWithdrawAddress struct {
	ID           int64  `db:"id" json:"id"`                       // 记录id
	UserID       int64  `db:"user_id" json:"user_id"`             // 用户id
	CoinName     string `db:"coin_name" json:"coin_name"`         // 币种名称
	CoinProtocol string `db:"coin_protocol" json:"coin_protocol"` // 币种协议
	CoinTag      string `db:"coin_tag" json:"coin_tag"`           // 币种tag
	Addr         string `db:"addr" json:"addr"`                   // 提币地址
	AddrAlias    string `db:"addr_alias" json:"addr_alias"`       // 地址别名
	IsDefault    bool   `db:"is_default" json:"is_default"`       // 是否是默认地址
	IsTrust      bool   `db:"is_trust" json:"is_trust"`           // 是否是信任地址
	IsDefaultNum uint8  `json:"is_default_num,omitempty"`         // 是否是默认地址
	IsTrustNum   uint8  `json:"is_trust_num,omitempty"`           // 是否是信任地址
	CreateTime   int64  `db:"create_time" json:"create_time"`     // 创建时间戳
}

func (a *UserWithdrawAddress) Parse() {
	a.IsDefault = a.IsDefaultNum != 0
	a.IsTrust = a.IsTrustNum != 0
}
func (a *UserWithdrawAddress) Check() bool {
	return !(len(a.Addr) == 0 || len(a.AddrAlias) == 0)
}

type TradeAsset struct {
	AccountId        int             `db:"account_id" json:"account_id"`                 // 账户id
	AccountType      int             `db:"account_type" json:"account_type"`             // 账户模式
	CurrencyId       int             `db:"currency_id" json:"currency_id"`               // 币种id
	PlatformID       int             `db:"platform_id" json:"platform_id"`               // 平台id
	UserId           int64           `db:"user_id" json:"user_id"`                       // 用户id
	Available        decimal.Decimal `db:"available" json:"available"`                   // 可用余额
	LockAmount       decimal.Decimal `db:"lock_amount" json:"lock_amount"`               // 委托冻结保证金
	Diff             decimal.Decimal `db:"diff" json:"diff"`                             // 账户差值
	BlowingFee       decimal.Decimal `db:"blowing_fee" json:"blowing_fee"`               // 全仓爆仓手续费计算
	Balance          decimal.Decimal `db:"balance" json:"balance"`                       // 账户资产
	TotalProfit      decimal.Decimal `db:"total_profit" json:"total_profit"`             // 累计已实现盈亏
	WarningRiskRate  decimal.Decimal `db:"warning_risk_rate" json:"warning_risk_rate"`   // 爆仓预警风险率
	TotalGiftReceive decimal.Decimal `db:"total_gift_receive" json:"total_gift_receive"` // 累计入账赠金
	GiftAvailable    decimal.Decimal `db:"gift_available" json:"gift_available"`         // 赠金可用
	GiftBalance      decimal.Decimal `db:"gift_balance" json:"gift_balance"`             // 赠金
	GiftUsed         decimal.Decimal `db:"gift_used" json:"gift_used"`                   // 累计使用赠金
	GiftLockAmount   decimal.Decimal `db:"gift_lock_amount" json:"gift_lock_amount"`     // 赠金委托冻结
}

// WalletAsset 用户资产信息
type WalletAsset struct {
	UserWalletId int64           `db:"user_wallet_id" json:"user_wallet_id"` // 钱包id
	UserId       int64           `db:"user_id" json:"user_id"`               // 用户id
	CurrencyId   int             `db:"currency_id" json:"currency_id"`       // 币种id
	PlatformID   int             `db:"platform_id" json:"platform_id"`       // 平台id
	Balance      decimal.Decimal `db:"balance" json:"balance"`               // 资产可用数量
	WithdrawLock decimal.Decimal `db:"withdraw_lock" json:"withdraw_lock"`   // 提币冻结数量
}

type MultipleWallet struct {
	TotalUSDT    decimal.Decimal  `json:"total_usdt"`     // 总折合USDT
	TotalLegal   decimal.Decimal  `json:"total_legal"`    // 总折合法币(固定cny)
	TotalLegalV2 decimal.Decimal  `json:"total_legal_v2"` // 总折合法币
	List         []WalletDetailV2 `json:"list"`           // 账户列表
}

type WalletDetailV2 struct {
	CoinID         int             `db:"coin_id" json:"coin_id"`           // 币种id
	CoinName       string          `db:"coin_name" json:"coin_name"`       // 币种名
	Balance        decimal.Decimal `db:"balance" json:"balance"`           // 可用资产
	Frozen         decimal.Decimal `db:"frozen" json:"frozen"`             // 冻结
	CanWithdraw    decimal.Decimal `db:"can_withdraw" json:"can_withdraw"` // 可提币数量
	EquivalentUSDT decimal.Decimal `db:"equivalent_usdt" json:"-"`         // 折合USDT
	SortWeight     float64         `json:"-"`                              // 排序权重
}

type TradeWalletDetailV2 struct {
	Total            decimal.Decimal `json:"total"`              // 总资产
	TotalLegal       decimal.Decimal `json:"total_legal"`        // 总折合法币
	TotalLegalV2     decimal.Decimal `json:"total_legal_v2"`     // 总折合法币
	Rights           decimal.Decimal `json:"rights"`             // 权益
	Float            decimal.Decimal `json:"float"`              // 未实现盈亏
	Margin           decimal.Decimal `json:"margin"`             // 仓位保证金
	TotalMargin      decimal.Decimal `json:"total_margin"`       // 总保证金 初始保证金+手续费
	Balance          decimal.Decimal `json:"balance"`            // 账户资产
	Available        decimal.Decimal `json:"available"`          // 可用余额
	CanTransfer      decimal.Decimal `json:"can_transfer"`       // 可划转余额
	LockAmount       decimal.Decimal `json:"lock_amount"`        // 委托冻结
	Diff             decimal.Decimal `json:"diff"`               // 待结算差值
	BlowingRate      decimal.Decimal `json:"blowing_rate"`       // 平仓风险率
	RiskRate         decimal.Decimal `json:"risk_rate"`          // 账户风险率
	TotalGiftReceive decimal.Decimal `json:"total_gift_receive"` // 累计入账赠金
	GiftAvailable    decimal.Decimal `json:"gift_available"`     // 赠金可用
	GiftBalance      decimal.Decimal `json:"gift_balance"`       // 赠金
	GiftUsed         decimal.Decimal `json:"gift_used"`          // 累计使用赠金
	GiftLockAmount   decimal.Decimal `json:"gift_lock_amount"`   // 赠金委托冻结
	MarginGift       decimal.Decimal `json:"margin_gift"`        // 赠金仓位保证金
}

type UsdTradeAsset struct {
	AccountId       int             `db:"account_id" json:"account_id"`               // 账户id
	AccountType     int             `db:"account_type" json:"account_type"`           // 账户模式
	PlatformID      int             `db:"platform_id" json:"platform_id"`             // 平台id
	CurrencyId      int             `db:"currency_id" json:"currency_id"`             // 币种id
	CurrencyName    string          `db:"currency_name" json:"currency_name"`         // 币种名称
	UserId          int64           `db:"user_id" json:"user_id"`                     // 用户id
	Available       decimal.Decimal `db:"available" json:"available"`                 // 可用余额
	LockAmount      decimal.Decimal `db:"lock_amount" json:"lock_amount"`             // 委托冻结保证金
	Diff            decimal.Decimal `db:"diff" json:"diff"`                           // 账户差值
	BlowingFee      decimal.Decimal `db:"blowing_fee" json:"blowing_fee"`             // 全仓爆仓手续费计算
	Balance         decimal.Decimal `db:"balance" json:"balance"`                     // 账户资产
	TotalProfit     decimal.Decimal `db:"total_profit" json:"total_profit"`           // 累计已实现盈亏
	WarningRiskRate decimal.Decimal `db:"warning_risk_rate" json:"warning_risk_rate"` // 爆仓预警风险率
}

type AssetDetailV2 struct {
	TotalUSDT    decimal.Decimal     `json:"total_usdt"`     // 总折合USDT
	TotalLegal   decimal.Decimal     `json:"total_legal"`    // 总折合法币
	TotalLegalV2 decimal.Decimal     `json:"total_legal_v2"` // 总折合法币
	Wallet       MultipleWallet      `json:"wallet"`         // 资产账户
	Trade        TradeWalletDetailV2 `json:"trade"`          // 交易账户
	Follow       FollowWalletDetail  `json:"follow"`         // 跟单账户
}

type WalletAssetBillArg struct {
	BillType  define.WalletBillType  `json:"bill_type"`  // 记录类型
	CoinName  string                 `json:"coin_name"`  // 币种
	State     define.WalletBillState `json:"state"`      // 状态 充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账
	LimitDays int                    `json:"limit_days"` // 数据限制天数(当前仅支持7天或30天)
}

type UserWalletBillV2 struct {
	BillId           string                 `db:"bill_id" json:"bill_id"`             // 记录id
	UserId           int64                  `db:"user_id" json:"user_id"`             //用户标识
	Amount           decimal.Decimal        `db:"amount" json:"amount"`               //数量
	Commission       decimal.Decimal        `db:"commission" json:"-"`                //提现手续费
	Balance          decimal.Decimal        `db:"balance" json:"balance"`             //用户账户可用数量
	LockAmount       decimal.Decimal        `db:"lock_amount" json:"lock_amount"`     //冻结数量
	PlatformID       int                    `db:"platform_id" json:"platform_id"`     //平台id
	CurrencyId       int                    `db:"currency_id" json:"currency_id"`     //币种id
	CurrencyName     string                 `db:"currency_name" json:"currency_name"` //币种名称
	Tx               string                 `db:"tx" json:"tx"`                       //交易哈希
	Type             define.WalletBillType  `db:"type" json:"type"`                   //1：充值  2：提现  4：划转到交易账户 8：从交易账户转入
	Status           define.WalletBillState `db:"status" json:"status"`               //充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账
	CreatedTime      time.Time              `db:"created_time" json:"-"`              //创建时间
	CreatedTimeUnix  int64                  `json:"created_time"`                     //创建时间
	ApprovedTime     time.Time              `db:"approved_time" json:"-"`             //提现审核时间
	ApprovedTimeUnix int64                  `json:"approved_time"`                    //提现审核时间
	ConfirmNum       int                    `db:"confirm_num" json:"-"`               //充提币确认数
	Approver         string                 `db:"approver" json:"-"`                  //审核人
	FromAddr         string                 `db:"from_addr" json:"-"`                 //来源地址
	Imei             string                 `db:"imei" json:"-"`                      //设备识别码
	IpAddress        string                 `db:"ip_address" json:"-"`                //委托客户IP
	OrderClient      define.OsType          `db:"order_client" json:"-"`              //委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	OrderId          string                 `db:"order_id" json:"-"`                  //充值为空 提现大钱包CWSId 其它为定单编号
	Remarks          string                 `db:"remarks" json:"-"`                   //提现备注
	Tag              string                 `db:"tag" json:"tag"`                     //币种标签 如usdt的ERC20
	ToAddr           string                 `db:"to_addr" json:"to_addr"`             //目标地址
}
