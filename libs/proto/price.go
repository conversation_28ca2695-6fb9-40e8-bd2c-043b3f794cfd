package proto

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type LegalPrice struct {
	Rate       decimal.Decimal `db:"rate" json:"rate"`
	UpdateTime int64           `db:"update_time" json:"update_time"`
}

type CoinLegalPrice struct {
	USD        decimal.Decimal `json:"usd"`         // 美元价格
	CNY        decimal.Decimal `json:"cny"`         // 人民币价格
	KRW        decimal.Decimal `json:"krw"`         // 韩元价格
	VND        decimal.Decimal `json:"vnd"`         // 越南盾价格
	IDR        decimal.Decimal `json:"idr"`         // 印尼盾价格
	RUB        decimal.Decimal `json:"rub"`         // 卢布价格
	EUR        decimal.Decimal `json:"eur"`         // 欧元价格
	UpdateTime int64           `json:"update_time"` // 更新时间
}

func (p CoinLegalPrice) Rate(lang define.ReqLang) decimal.Decimal {
	switch lang {
	case define.ReqLangCN:
		return p.CNY
	case define.ReqLangKR:
		return p.KRW
	case define.ReqLangVN:
		return p.VND
	case define.ReqLangID:
		return p.IDR
	case define.ReqLangRU:
		return p.RUB
	case define.ReqLangDE:
		return p.EUR
	default:
		return p.USD
	}
}
