package proto

import (
	"bc/libs/convert"
	"bc/libs/define"
	"time"

	"bc/libs/nums"
	"github.com/shopspring/decimal"
)

type ApiOrderOpenArgs struct {
	NToken       string             `json:"ncr"`
	ContractCode string             `json:"contract_code"`
	HoldType     define.AccountType `json:"hold_type"`
	Side         string             `json:"side"`
	Amount       int                `json:"amount"` //下单张数
	Leverage     int                `json:"leverage"`
	FuturePrice  decimal.Decimal    `json:"future_price"`
	Limit        decimal.Decimal    `json:"limit"` //计划平仓价格
	Stop         decimal.Decimal    `json:"stop"`
	FundPassword string             `json:"fund_password"` // 资金密码
	PositionID   string             `json:"position_id"`   // 持仓id(指定仓位加仓用)

	EntrustType int             `db:"entrust_type" json:"entrust_type"` //委托类型 0-市价 1-限价
	Price       decimal.Decimal `db:"price" json:"entrust_price"`       //委托价格;限价单传递价格
	Mode        int             `db:"mode" json:"mode"`                 //下单模式 1-对手价 2-最优3挡 3-最优5挡

}

func (oa *ApiOrderOpenArgs) Convert() *OrderOpenArgs {
	return &OrderOpenArgs{
		NToken:       oa.NToken,
		ContractCode: oa.ContractCode,
		HoldType:     oa.HoldType,
		Side:         oa.Side,
		Amount:       oa.Amount,
		Leverage:     oa.Leverage,
		Limit:        nums.Float(oa.Limit),
		Stop:         nums.Float(oa.Stop),
		EntrustType:  oa.EntrustType,
		Price:        oa.Price,
		Mode:         oa.Mode,
		PositionId:   convert.String2Int64(oa.PositionID),
	}
}

type OrderCancelArgs struct {
	RequestId int64          `json:"request_id"`
	Lang      define.ReqLang `json:"lang"`
	UserId    int64          `json:"user_id"`
	ID        int64          `json:"id"` //委托订单id
}

//订单列表与合约联查
type OrderBatchCancelArgs struct {
	RequestId    int64          `json:"request_id"`
	Lang         define.ReqLang `json:"lang"`
	UserId       int64          `json:"user_id"`
	OrderIds     []int64        `json:"order_ids"` //委托订单列表
	ContractCode string         `json:"code"`      //查询指定合约
}

type OrderBatchOpenArgs struct {
	RequestId int64
	Lang      define.ReqLang
	UserId    int64 `json:"user_id"`

	NToken       string `json:"ncr"`
	ContractCode string `json:"contract_code"` //合约名

	Orders []OrderOpenArgs `json:"orders"` //委托订单列表

	IpAddress  string            `json:"ip_address"`
	DeviceId   string            `json:"device_id"`
	APPID      int               `json:"appid"`
	AppVersion define.AppVersion `json:"app_version"` // APP版本
}

//"contract_code": "BTCUSD", // 合约code
//"hold_type": 1, // 持仓类型 1:全仓 2:逐仓
//"side": "B", // 方向 B买 S卖
//"leverage": 10 // 杠杠倍数
type OrderOpenArgs struct {
	RequestId  int64
	Lang       define.ReqLang
	AppVersion define.AppVersion // 版本,小于 1263 不支持分仓
	NToken     string            `json:"ncr"`

	PlanOrderId  int64
	ContractCode string             `json:"contract_code"`
	HoldType     define.AccountType `json:"hold_type"` //1-全仓 2-逐仓
	Side         string             `json:"side"`
	Amount       int                `json:"amount"` //下单张数
	Leverage     int                `json:"leverage"`
	OrderType    int                //订单类型 0: 用户委托 1：计划单 2：止盈单 4：止损单 5：强平单

	UserId int64

	IpAddress          string
	DeviceId           string
	APPID              int
	Limit              float64         `json:"limit"`                          //设置用户止盈触发价
	Stop               float64         `json:"stop"`                           //用户止损触发价
	EntrustLimit       decimal.Decimal `json:"entrust_limit"`                  //止盈限价委托执行价
	EntrustStop        decimal.Decimal `json:"entrust_stop"`                   //止损限价委托执行 价
	TriggerType        int             `db:"trigger_type" json:"trigger_type"` //止盈止损触发标的 0-成交价,1-标记价格
	TriggerEntrustType int             `json:"trigger_entrust_type"`           //止盈止损触发委托类型
	TriggerPrice       decimal.Decimal `json:"trigger_price"`                  //计划单实际触发价格

	EntrustType       int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy   int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 1-fok 2-ioc 3-maker
	Price             decimal.Decimal `db:"price" json:"entrust_price"`               //委托价格;限价单传递价格
	Mode              int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡
	ClientOrderId     int64           `db:"client_order_id" json:"client_order_id"`
	IsDepthBatchPlace bool            //是否铺单下单 （铺单用户批量下单，不直接进行撮合委托）

	PositionId int64 `json:"position_id"` //加仓传递持仓id
}

type OrderOpenRsp struct {
	Order     *EntrustOrder `json:"order"`
	Balance   string        `json:"-"`
	Available string        `json:"available,omitempty"`
}

type OrderOpenReply struct {
	Ret           int           `json:"ret"`
	Msg           string        `json:"msg"`
	Order         *EntrustOrder `json:"order"`
	Available     string        `json:"available,omitempty"`
	ClientOrderId int64         `db:"client_order_id" json:"client_order_id"`
}

type OrderBatchOpenRsp struct {
	Orders []OrderOpenReply `json:"orders"`
}

type OrderCloseRsp struct {
	Order *EntrustOrder
}

//"position_id": 123456, // 持仓id
//"close_typ":1,//平仓类型，1-部分平仓 2-全部平仓(忽略amount)
//"amount": 1,//平仓张数
type OrderCloseArgs struct {
	Lang         define.ReqLang
	AppVersion   define.AppVersion
	NToken       string `json:"ncr"`
	PositionId   int64  `json:"position_id"`
	PosId        string `json:"pos_id"`        //备用，PositionId字符串类型
	ContractCode string `json:"contract_code"` // 合约代码(下平仓委托时使用)
	CloseType    int    `json:"close_type"`    //1-部分平仓 2-全部平仓
	Amount       int    `json:"amount"`
	Side         string `json:"side"` // 持仓方向

	OrderType int `json:"order_type"` //订单类型 0: 用户下单 1：计划单 2：止盈单 4：止损单 5：强平单
	//Offset    string //offset O-OPEN C-CLOSE
	//PlanOrderId int64
	RequestId                                            int64
	UserId                                               int64
	IpAddress                                            string
	DeviceId                                             string
	APPID                                                int
	CloseId                                              string `json:"close_id"`
	PlanCloseOrderId                                     int64
	TriggerPrice                                         decimal.Decimal
	TriggerBuyPrice, TriggerSellPrice, TriggerForcePrice float64

	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 1-fok 2-ioc 3-maker
	Price           decimal.Decimal `db:"price" json:"entrust_price"`               //委托价格;限价单传递价格
	Mode            int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡

	IsAdminOp bool //是否管理平台操作,后台操作为true

	ForceCloseID int64 `json:"force_close_id"` //强平流水id

	ClientOrderId int64 `db:"client_order_id" json:"client_order_id"`
	IsFastClose   bool  //是否一键全平
}

// FullForceTrigger 全仓强平触发
type FullForceTrigger struct {
	List      []UserPosition
	UserId    int64
	Code      string
	RiskRate  decimal.Decimal
	BuyPrice  float64
	SellPrice float64
}

type PlanHistory struct {
	ID              int64           `db:"id" json:"id"`
	OrderId         int64           `db:"order_id" json:"order_id"`
	OrderType       int             `db:"order_type" json:"order_type"`
	OrderTime       time.Time       `db:"order_time" json:"order_time"`
	OrderCondition  int             `db:"order_condition" json:"order_condition"`
	OrderCancelTime time.Time       `db:"order_cancel_time" json:"order_cancel_time"`
	TriggerTime     time.Time       `db:"trigger_time" json:"trigger_time"`
	TriggerPrice    decimal.Decimal `db:"trigger_price" json:"trigger_price"`
	OrderPrice      decimal.Decimal `db:"order_price" json:"order_price"`
	Amount          int             `db:"amount" json:"amount"`
	PositionId      int64           `db:"position_id" json:"position_id"`
	Side            string          `db:"side" json:"side"`
	Lever           string          `db:"lever" json:"lever"`
	AccountType     int             `db:"account_type" json:"account_type"`
	Offset          string          `db:"offset" json:"offset"`
	Status          int             `db:"trigger_status" json:"status"`
	CreateTime      time.Time       `db:"create_time" json:"create_time"`
}

type OrderStopUpdateArgs struct {
	OrderID          int64           `json:"order_id"`                                   // 委托订单id
	LimitAmount      int             `db:"limit_amount" json:"limit_amount"`             // 止盈张数
	StopAmount       int             `db:"stop_amount" json:"stop_amount"`               // 止损张数
	EntrustTypeLimit int             `db:"entrust_type_limit" json:"entrust_type_limit"` // 止盈委托类型 0-市价 1-限价
	EntrustTypeStop  int             `db:"entrust_type_stop" json:"entrust_type_stop"`   // 止损委托类型 0-市价 1-限价
	TriggerTypeLimit int             `db:"trigger_type_limit" json:"trigger_type_limit"` // 止盈触发标的 0-成交价 1-标记价格
	TriggerTypeStop  int             `db:"trigger_type_stop" json:"trigger_type_stop"`   // 止损触发标的 0-成交价 1-标记价格
	Limit            decimal.Decimal `db:"limit" json:"limit"`                           // 止盈价格
	Stop             decimal.Decimal `db:"stop" json:"stop"`                             // 止损价格
	EntrustLimit     decimal.Decimal `db:"entrust_limit" json:"entrust_limit"`           // 止盈委托执行价
	EntrustStop      decimal.Decimal `db:"entrust_stop" json:"entrust_stop"`             // 止损委托执行价
}
