/*
@Time : 3/7/20 10:18 上午
<AUTHOR> mocha
@File : order
*/
package proto

import (
	"bc/libs/crypto"
	"bc/libs/nums"
	"fmt"
	"github.com/shopspring/decimal"
	"time"

	"bc/libs/define"
)

type ComplexPrice struct {
	ContractCode      string          `json:"contract_code"`
	Price             decimal.Decimal `json:"Price,omitempty"` //标记价格
	PriceStr          string          `json:"price"`
	SpotIndexPrice    decimal.Decimal `json:"spot_index_price_original,omitempty"` //现货指数价格
	SpotIndexPriceStr string          `json:"spot_index_price,omitempty"`          //现货指数价格
	Amount            decimal.Decimal `json:"amount,omitempty"`
	TS                int64           `json:"ts"`
}

type MatchOrder struct {
	Id             int64           `json:"id"`               //成交id
	EntrustOrderId int64           `json:"entrust_order_id"` //委托id
	ContractCode   string          `json:"contract_code"`
	Side           string          `json:"side"`
	TradeVolume    int             `json:"trade_volume"`
	TradePrice     float64         `json:"price"`
	TradePriceCn   string          `json:"trade_price_cn"`
	TradePriceStr  string          `json:"trade_price"`
	TradeTime      time.Time       `json:"trade_time"`
	IsBaitTrade    bool            `json:"is_bait_trade"`
	IsClose        bool            `json:"is_close"`
	IsForceOrder   bool            `json:"is_force_order"`
	PerValue       float64         `json:"per_value"` //面值
	Slippage       decimal.Decimal `json:"slippage"`
	MatchPrice
}

//{
//"digit":2,
//"contract_code":"btc/usdt", //交易对名称
//"buy":[
//{
//"price": "39.20", //价格
//"amount": "1000" //数量,张
//"total_amount":1500,//累计张
//}
//],
//"sell":[
//{
//"price": "40.20",
//"amount": "1000"
//"total_amount":1500,
//}
//]}
type DepthTradeContainer struct {
	DepthContainer
	CurDepthPrice decimal.Decimal `json:"cur_depth_price"`
	BuyMax        decimal.Decimal `json:"buy_max"`  //鱼饵档位最高价
	SellMax       decimal.Decimal `json:"sell_max"` //鱼饵档位最高价
	BuyFirst      decimal.Decimal `json:"buy_first"`
	SellFirst     decimal.Decimal `json:"sell_first"`
}

type DepthContainer struct {
	//Id           int64   `json:"id"`
	TS           int64   `json:"ts"`
	Level        int32   `json:"level"`
	Digit        int32   `json:"digit"`
	PriceLimit   int32   `json:"price_limit"`
	ContractCode string  `json:"contract_code"`
	Buy          []Depth `json:"buy"`
	Sell         []Depth `json:"sell"`
	//BuyFirst     decimal.Decimal `json:"buy_first"`
	//SellFirst    decimal.Decimal `json:"sell_first"`
	//BuyThree     decimal.Decimal `json:"buy_three"`
	//SellThree    decimal.Decimal `json:"sell_three"`
	//BuyFive      decimal.Decimal `json:"buy_five"`
	//SellFive     decimal.Decimal `json:"sell_five"`
	//CreateTime   time.Time       `json:"create_time"`
	//Deal         *DealInfo       `json:"-"`
}

type DepthInfo struct {
	BuyFirst  decimal.Decimal
	SellFirst decimal.Decimal
	BuyThree  decimal.Decimal
	SellThree decimal.Decimal
	BuyFive   decimal.Decimal
	SellFive  decimal.Decimal
}

type DepthItem struct {
	Price  decimal.Decimal `json:"price"`
	Volume decimal.Decimal `json:"volume"`
}

func (r *DepthContainer) GetFirstPrice() (buy, sell *DepthItem) {
	if len(r.Buy) > 0 {
		price := r.Buy[0].PriceD
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(r.Buy[0].Price)
		}
		buy = &DepthItem{
			Price:  price,
			Volume: nums.NewFromInt64(r.Buy[0].Amount),
		}
	}

	if len(r.Sell) > 0 {
		price := r.Sell[0].PriceD
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(r.Sell[0].Price)
		}
		sell = &DepthItem{
			Price:  price,
			Volume: nums.NewFromInt64(r.Sell[0].Amount),
		}
	}
	return
}

func (r *DepthContainer) GetDepthSimpleInfo() (ds *DepthInfo) {
	ds = &DepthInfo{}
	for index, item := range r.Buy {
		price := item.PriceD
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(item.Price)
		}
		if index == 0 {
			ds.BuyFirst = price
		}
		if index < 3 {
			ds.BuyThree = price
		}
		if index < 5 {
			ds.BuyFive = price
		}
	}
	for index, item := range r.Sell {
		price := item.PriceD
		if price.LessThanOrEqual(decimal.Zero) {
			price = nums.NewFromString(item.Price)
		}
		if index == 0 {
			ds.SellFirst = price
		}
		if index < 3 {
			ds.SellThree = price
		}
		if index < 5 {
			ds.SellFive = price
		}
	}
	return
}

func (r *DepthContainer) Hash() string {
	return crypto.Md5(fmt.Sprintf("%v,%v", r.Buy, r.Sell))
}

type Depth struct {
	Price       string          `json:"price"`
	PriceD      decimal.Decimal `json:"price_d"`
	Amount      int64           `json:"amount"`
	TotalAmount int64           `json:"total_amount"`
	CanTrade    bool            `json:"depth_factor"`    //是否可交易档位
	IsBaitLevel bool            `json:"bait_warn_level"` //是否是警戒档位
}

//推送给客户端
type DepthContainerPub struct {
	TS           int64      `json:"ts"`
	Level        int32      `json:"level"`
	Digit        int32      `json:"digit"`
	PriceLimit   int32      `json:"price_limit"`
	ContractCode string     `json:"contract_code"`
	Buy          []DepthPub `json:"buy"`
	Sell         []DepthPub `json:"sell"`
}

type DepthPub struct {
	Price       string          `json:"price"`
	PriceD      decimal.Decimal `json:"-"`
	Amount      int64           `json:"amount"`
	TotalAmount int64           `json:"total_amount"`
}

type ApiOrderPlaceArg struct {
	ReqID        int64         `json:"req_id"`        // 请求id
	UserID       int64         `json:"user_id"`       // 用户id
	ContractCode string        `json:"contract_code"` // 合约code
	OrderType    int           `json:"order_type"`    // 订单烈性 1:限价单 2:最优价 3:条件单
	HoldType     int           `json:"hold_type"`     // 持仓类型 1:全仓 2:逐仓
	Side         string        `json:"side"`          // 方形
	Price        string        `json:"price"`         // 价格
	Amount       int           `json:"amount"`        // 数量
	Leverage     int           `json:"leverage"`      // 杠杠
	IPAddr       string        `json:"ip_addr"`       // 委托客户ip
	IEMI         string        `json:"iemi"`          // 设备识别码
	Os           define.OsType `json:"os"`            // 设备类型
}

type ApiOrderPlaceReply struct {
	OrderID int64 `json:"order_id" db:"order_id"` // 订单id
	ApiAsset
}

type ApiOrderCancelArg struct {
	ReqID        int64  `json:"req_id"`        // 请求id
	UserID       int64  `json:"user_id"`       // 用户id
	ContractCode string `json:"contract_code"` // 合约code
	OrderID      int64  `json:"order_id"`      // 订单id
	OrderType    int8   `json:"order_type"`    // 订单类型 1-委托 2-条件单
}

type ApiOrderLiquidateArg struct {
	ReqID        int64         `json:"req_id"`        // 请求id
	UserID       int64         `json:"user_id"`       // 用户id
	ContractCode string        `json:"contract_code"` // 合约code
	Amount       int           `json:"amount"`        // 数量
	IPAddr       string        `json:"ip_addr"`       // 委托客户ip
	IEMI         string        `json:"iemi"`          // 设备识别码
	Os           define.OsType `json:"os"`            // 设备类型
}

type ApiAddDepositArg struct {
	ReqID        int64         `json:"req_id"`                     // 请求id
	UserID       int64         `json:"user_id"`                    // 用户id
	ContractCode string        `json:"contract_code"`              // 合约code
	Amount       string        `json:"amount"`                     // 数量
	IPAddress    string        `db:"ip_address" json:"ip_address"` // ip地址
	IMEI         string        `db:"imei" json:"imei"`             // 设备识别码
	OS           define.OsType `db:"os" json:"os"`                 // 客户端类型
}

type BackendUserContractArg struct {
	ContractCode string `json:"contract_code"` // 合约code
}

type ForceLiquidateArg struct {
	UserID       int64  `json:"user_id"`       // 用户id
	ContractCode string `json:"contract_code"` // 合约code
}

type LiquidateData struct {
	UserID      int64  `json:"user_id" db:"user_id"`           // 用户id
	AccountType int    `json:"account_type" db:"account_type"` // 账户类型
	Side        string `json:"side" db:"side"`                 // 持仓方向
	Volume      int    `json:"volume" db:"volume"`             // 持仓张数
	Lever       int    `json:"lever" db:"lever"`               // 杠杆倍数
}

//"contract_code": "BTCUSD", // 合约code
//"change_ratio": "12.23", // 当日涨跌幅 12.23%
//"change":"-0.237",//涨幅
//"high_price": "3000",//最高价(请与最新成交比较，取大）
//"low_price": "3000",//最低价（请于最新成交比较，取小）
type ContractApply struct {
	ContractCode string `json:"contract_code"`
	ChangeRatio  string `json:"change_ratio"`
	Change       string `json:"change"`
	HighPrice    string `json:"high_price"`
	LowPrice     string `json:"low_price"`
	TradeV24h    string `json:"trade_24h"`
}

type UserContractMark struct {
	AccountType int `json:"account_type" db:"account_type"`
	CrossLever  int `db:"cross_lever" json:"cross_lever"`
	LongLever   int `db:"long_lever" json:"long_lever"`
	ShortLever  int `db:"short_lever" json:"short_lever"`
}

type CloseOrderEvent struct {
	Event int
	Data  PlanCloseOrder
}

type PlanCloseOrder struct {
	ReqLang          define.ReqLang     `json:"req_lang"`
	PlanCloseOrderId int64              `db:"plan_close_order_id" json:"plan_close_order_id"`
	AccountType      define.AccountType `db:"account_type"` //账户类 0-普通 1-跟单
	PositionId       int64              `db:"position_id" json:"position_id"`
	UserId           int64              `db:"user_id" json:"user_id"`
	ContractCode     string             `db:"contract_code" json:"contract_code"`
	Side             string             `db:"side" json:"side"`
	Amount           int                `db:"amount" json:"amount"`
	Limit            float64            `db:"limit"`
	ConditionLimit   int                `db:"condition_limit" `
	Stop             float64            `db:"stop"`
	ConditionStop    int                `db:"condition_stop"`
	CreateTime       time.Time          `db:"create_time" json:"create_time"`
	PlatformID       int                `db:"platform_id" json:"platform_id"` //平台id
	Lever            int                `db:"lever" json:"lever"`
	TriggerType      int                `db:"trigger_type" json:"trigger_type"` //1-止盈 2-止损
	TriggerPrice     decimal.Decimal    `db:"trigger_price" json:"trigger_price"`
	TriggerTime      time.Time          `db:"trigger_time" json:"trigger_time"`
	TriggerStatus    int                `db:"trigger_status" json:"trigger_status"`
	Mode             int                `db:"mode" json:"mode"`
	EntrustType      int                `db:"entrust_type" json:"entrust_type"`
	EntrustLimit     decimal.Decimal    `db:"entrust_limit" json:"entrust_limit"` // 止盈委托价(0下市价单 大于0下限价单)
	EntrustStop      decimal.Decimal    `db:"entrust_stop" json:"entrust_stop"`   // 止损委托价(0下市价单 大于0下限价单)
	IPAddress        string             `db:"ip_address" json:"ip_address"`
	EntrustOrderId   int64              `db:"entrust_order_id" json:"entrust_order_id"`
	IsFollower       bool               `db:"is_follower" json:"is_follower"` // 持仓者是否是跟随者
}

type PlanBasic struct {
	PlanCloseOrderId int64           `db:"plan_close_order_id" json:"plan_close_order_id"`
	AccountType      int             `db:"account_type"` //账户类 0-普通 1-跟单
	PositionId       int64           `db:"position_id" json:"position_id"`
	UserId           int64           `db:"user_id" json:"user_id"`
	ContractCode     string          `db:"contract_code" json:"contract_code"`
	Side             string          `db:"side" json:"side"`
	Amount           int             `db:"amount" json:"amount"`
	Price            decimal.Decimal `db:"limit"`
	Condition        int             `db:"condition" `
	Lever            int             `db:"lever" json:"lever"`
	TriggerPrice     decimal.Decimal `db:"trigger_price" json:"trigger_price"`
	TriggerTime      time.Time       `db:"trigger_time" json:"trigger_time"`
}

type ContractTick struct {
	ID           int64     `json:"id"`
	ContractCode string    `json:"contract_code"`
	TradePrice   float64   `json:"trade_price"`
	TradeAmount  int64     `json:"trade_amount"`
	BuyPrice     float64   `json:"buy_price"`
	SellPrice    float64   `json:"sell_price"`
	IndexPrice   float64   `json:"index_price"`
	CreateTime   time.Time `json:"create_time"`
}

type AllLiquidateArg struct {
	UserID       int64  `json:"user_id" db:"user_id"`
	ContractCode string `json:"contract_code" db:"contract_code"`
	FundPassword string `json:"fund_password"` // 资金密码
}

//撮合成交价信息
type MatchPrice struct {
	TakeLevel   int             `json:"take_level"`
	MatchPrice  decimal.Decimal `json:"match_price"`
	MatchVolume decimal.Decimal `json:"match_volume"`
	TradePrice  decimal.Decimal `json:"avg_price"`        //撮合成交价
	TradeVolume int64           `json:"trade_volume"`     //成交数量
	Status      int             `json:"status"`           //成交状态
	DepthPrice  decimal.Decimal `json:"depth_base_price"` //铺单基准价
	BuyFirst    decimal.Decimal `json:"buy_first"`        //当前买一
	SellFirst   decimal.Decimal `json:"sell_first"`       //当前卖一
	BuyMax      decimal.Decimal `json:"buy_max"`          //鱼饵档位最高价
	SellMax     decimal.Decimal `json:"sell_max"`         //鱼饵档位最高价
	//TradeList []MatchDetail `json:"list"`
	Depth DepthTradeContainer
}

type MatchDetail struct {
	TradePrice  decimal.Decimal `json:"price"`        //撮合成交价
	TradeVolume int64           `json:"trade_volume"` //成交数量
	Ts          int64           `json:"ts"`           //成交时间
}
