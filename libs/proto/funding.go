package proto

import (
	"github.com/shopspring/decimal"
	"time"
)

type FundingDeal struct {
	ID           int64           `db:"id" json:"id"`
	ContractCode string          `db:"contract_code" json:"contract_code"`
	FundingRate  decimal.Decimal `db:"funding_rate" json:"funding_rate"`
	Income       decimal.Decimal `db:"income" json:"income"`
	Giving       decimal.Decimal `db:"giving" json:"giving"`
	GivingFull   decimal.Decimal `db:"giving_full" json:"giving_full"`
	GivingWare   decimal.Decimal `db:"giving_ware" json:"giving_ware"`
	GivingFollow decimal.Decimal `db:"giving_follow" json:"giving_follow"`
	CreateTime   time.Time       `db:"create_time" json:"create_time"`
}
