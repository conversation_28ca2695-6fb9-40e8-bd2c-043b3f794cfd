package proto

import (
	"time"

	"bc/libs/define"
)

type ManageOperationLog struct {
	ID         int                           `db:"id" json:"id"`
	Stype      define.ManageOperationLogType `db:"stype" json:"stype"`             // 1 修改标签，2添加代理，3修改用户信息，4添加备注，5添加标签，6添加标签信息，7身份审核，8审核提币，9修改标签信息，10发放返佣，11 删除用户标签，12 添加用户风控组，13 更新用户风控组，15 设置用户风控白名单，14 删除用户风控白名单，16 修改用户风控组信息，17 设置用户提币审核限制，18 解除用户提币限制
	UserID     int64                         `db:"user_id" json:"user_id"`         // 目标
	PlatformID int                           `db:"platform_id" json:"platform_id"` // 平台id
	ManageID   int                           `db:"manage_id" json:"manage_id"`     // 管理员id
	Manage     string                        `db:"manage" json:"manage"`           // 修改人
	Conten     string                        `db:"conten" json:"conten"`           // 修改参数
	CreatTime  time.Time                     `db:"creat_time" json:"creat_time"`   // 操作时间
}
