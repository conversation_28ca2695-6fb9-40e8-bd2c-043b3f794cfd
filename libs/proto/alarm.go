package proto

import (
	"bc/libs/crypto"
	"bc/libs/utils"
	"strconv"
	"time"
)

const (
	AlarmTypeSpotIndexMain                 = 1001 //BTCUSDT合约现货指数采集报警
	AlarmTypeSpotIndexSecondary            = 1002 //非BTCUSDT合约现货指数采集报警
	AlarmTypeSpotIndexFetchException       = 2001 //现货指数获取异常报警 （前后两次计算出的现货指数价格差距过大，ABS[(本次－前次)/前次]≥0.1）
	AlarmTypeSpotIndexCannotFetch          = 2002 //现货指数无法获取报警
	AlarmTypeSpotIndexPriceProtect         = 2003 //最新价格保护报警
	AlarmTypeDepthPriceReferPriceException = 3001 //铺单参照系价格获取失败报警
	AlarmTypeMarkPriceAvgException         = 3050 //标记价格与盘口中间价差距报警

	AlarmTypeDepthReferException  = 4001 //铺单参照系盘口异常报警
	AlarmTypeDepthNetPosException = 4002 //净持仓异常报警

	AlarmTypeFastSideException = 5001 //快速单边市报警
	AlarmTypeContinueException = 5002 //持续冲击报警

	AlarmTypeSpotExceptionRecord = 6001 //持续冲击报警
)

type Alarm struct {
	ID         int64     `json:"id"` //id
	Code       string    `json:"code"`
	AlarmLevel int       `json:"alarm_level"` //报警级别
	Type       int       `json:"type"`        //报警类别
	SubType    int       `json:"-"`           //子类型
	Content    string    `json:"content"`     //报警内容
	Time       time.Time `json:"time"`
}

func (a *Alarm) HashCode() string {
	if a == nil {
		return ""
	}
	s := utils.StrBuilderByHyphen(strconv.Itoa(a.AlarmLevel), strconv.Itoa(a.Type), strconv.Itoa(a.SubType), a.Content)
	return crypto.Md5(s)
}
