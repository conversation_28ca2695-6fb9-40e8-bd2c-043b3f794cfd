package proto

import (
	"bc/libs/alias"
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type ApiContractList struct {
	ContractCode           string          `json:"contract_code"`                                    // 合约code
	ContractName           string          `json:"contract_name"`                                    // 合约中文名
	ContractNameEn         string          `json:"contract_name_en"`                                 // 合约英文名
	ContractIcon           string          `json:"contract_icon"`                                    // 合约图标
	ContractType           int8            `json:"contract_type"`                                    // 合约类型 0:币本位 1:金本位
	IndexPrecision         int32           `json:"index_precision"`                                  // 合约指数价格精度
	Digit                  int32           `json:"digit"`                                            // 精度
	Price                  string          `json:"price"`                                            // 成交价格
	PriceCNY               string          `json:"price_cny"`                                        // 成交价格(人民币)
	IndexPrice             string          `json:"index_price"`                                      // 指数价格
	ChangeRatio            string          `json:"change_ratio"`                                     // 涨跌幅百分比
	Change                 string          `json:"change"`                                           // 涨跌
	BuyCount               int64           `json:"buy_count"`                                        // 看涨人数
	SellCount              int64           `json:"sell_count"`                                       // 看跌人数
	Delisted               alias.NumBool   `json:"delisted"`                                         // 是否已下架
	IsFollow               alias.NumBool   `json:"is_follow"`                                        // 是否可带单开仓
	PriceCoinName          string          `json:"price_coin_name"`                                  // 币种名称
	ParValue               string          `json:"par_value"`                                        // 面值
	TradeVolume            decimal.Decimal `json:"trade_volume"`                                     // 成交量
	FeeMaker               decimal.Decimal `json:"maker_fee" db:"fee_maker"`                         // maker手续费率
	FeeTaker               float64         `json:"fee_taker"`                                        // 手续费
	IOCLimit               decimal.Decimal `db:"ioc_limit" json:"ioc_limit"`                         //ioc委托限价
	IOCBuyLimit            decimal.Decimal `db:"ioc_buy_limit" json:"ioc_buy_limit"`                 //ioc买方价格限制
	IOCSellLimit           decimal.Decimal `db:"ioc_sell_limit" json:"ioc_sell_limit"`               //ioc卖方价格限制
	IsMaintenance          alias.NumBool   `db:"is_maintenance" json:"is_maintenance"`               //是否系统维护
	MaintenanceMarginRatio decimal.Decimal `json:"hold_deposit_ratio" db:"maintenance_margin_ratio"` // 维持保证金率
	FullLever              []int           `json:"full_lever" db:"full_lever"`                       // 全仓杠杆倍率
	Leverages              []int           `json:"leverages" db:"leverages"`                         // 逐仓杠杆倍数
	FollowLever            []int           `db:"follow_lever" json:"follow_lever"`                   // 跟单杠杆倍数
	LockFloatFactor        decimal.Decimal `db:"lock_float_factor" json:"lock_float_factor"`         //资产冻结上浮系数
	RedundancyFactor       decimal.Decimal `db:"redundancy_factor" json:"redundancy_factor"`         //市价冗余系数
	HighPrice              decimal.Decimal `json:"high_price"`                                       // 最高价
	LowPrice               decimal.Decimal `json:"low_price"`                                        // 最低价
}

type ApiContractDetail struct {
	ContractCode           string          `json:"contract_code" db:"contract_code"`                 // 合约code
	ContractName           string          `json:"contract_name" db:"contract_name"`                 // 合约中文名
	ContractNameEn         string          `json:"contract_name_en" db:"contract_name_en"`           // 合约英文名
	ContractType           int8            `json:"contract_type" db:"contract_type"`                 // 合约类型 0:币本位 1:金本位
	Digit                  int32           `json:"digit" db:"digit"`                                 // 合约精度
	IndexPrecision         int32           `json:"index_precision" db:"index_precision"`             // 合约指数价格精度
	MarkPrecision          int32           `json:"mark_precision" db:"mark_precision"`               // 标记价格精度
	Step                   float64         `json:"step,string" db:"step"`                            // 下单价格步长
	CoinId                 int             `json:"coin_id" db:"coin_id"`                             // 结算币种id
	CoinName               string          `json:"coin_name" db:"coin_name"`                         // 结算币种名
	MarketName             string          `json:"market_name"`                                      // 计价币种名
	PriceCoinName          string          `json:"price_coin_name" db:"price_coin_name"`             // 计价币种名
	ParValue               string          `json:"par_value" db:"par_value"`                         // 合约面值
	MinOrderVolume         int             `json:"min_order_volume" db:"min_order_volume"`           // 单笔最小下单量 张
	MaxOrderVolume         int             `json:"max_order_volume" db:"max_order_volume"`           // 单笔最大下单量 张
	FeeMaker               decimal.Decimal `json:"maker_fee" db:"fee_maker"`                         // maker手续费率
	FeeTaker               float64         `json:"taker_fee,string" db:"fee_taker"`                  // taker手续费率
	Delisted               alias.NumBool   `json:"delisted" db:"delisted"`                           // 是否下架 false-上架,true-下架
	MaintenanceMarginRatio float64         `json:"hold_deposit_ratio" db:"maintenance_margin_ratio"` // 维持保证金率

	FullLever       []int           `json:"full_lever" db:"full_lever"`               // 全仓杠杆倍率
	Leverages       []int           `json:"leverages" db:"leverages"`                 // 逐仓杠杆倍数
	FollowLever     []int           `db:"follow_lever" json:"follow_lever"`           // 跟单杠杆倍数
	MinTraderVolume int             `db:"min_trader_volume" json:"min_trader_volume"` // 交易员最小下单张数
	MaxTraderVolume int             `db:"max_trader_volume" json:"max_trader_volume"` // 交易员最小下单张数
	IsFollow        alias.NumBool   `db:"is_follow" json:"is_follow"`                 // 是否可下带单
	FundFee         string          `json:"fund_fee" db:"fund_fee"`                   // 资金费率
	EstimateFundFee string          `json:"estimate_fund_fee" db:"estimate_fund_fee"` // 预测资金费率
	IndexPrice      decimal.Decimal `json:"index_price" db:"index_price"`             // 指数价格
	IndexPriceCny   decimal.Decimal `json:"index_price_cny" db:"index_price_cny"`     // 指数折合人民币价格
	BuyPrice        string          `json:"buy_price" db:"buy_price"`                 // 买入指数价格
	SellPrice       string          `json:"sell_price" db:"sell_price"`               // 卖出指数价格
	HighPrice       string          `json:"high_price"`                               // 最高价
	LowPrice        string          `json:"low_price"`                                // 最低价
	ChangeDaily     string          `json:"change_daily" db:"change_daily"`           // 当日涨跌幅
	Change24h       string          `json:"change_24h" db:"change_24h"`               // 24小时涨跌幅
	Change8h        string          `json:"change_8h" db:"change_8h"`                 // 8小时涨跌幅
	Change4h        string          `json:"change_4h" db:"change_4h"`                 // 4小时涨跌幅
	Change2h        string          `json:"change_2h" db:"change_2h"`                 // 2小时涨跌幅
	Change1h        string          `json:"change_1h" db:"change_1h"`                 // 1小时涨跌幅
	Change30m       string          `json:"change_30m" db:"change_30m"`               // 30分钟涨跌幅
	Change10m       string          `json:"change_10m" db:"change_10m"`               // 10分钟涨跌幅
	ChangeVolume    string          `json:"change_volume" db:"change_volume"`         // 当日涨跌量
	Trade24h        string          `json:"trade_24h"`
	IOCLimit        decimal.Decimal `db:"ioc_limit" json:"ioc_limit"`           //ioc委托限价
	IOCBuyLimit     decimal.Decimal `db:"ioc_buy_limit" json:"ioc_buy_limit"`   //ioc买方价格限制
	IOCSellLimit    decimal.Decimal `db:"ioc_sell_limit" json:"ioc_sell_limit"` //ioc卖方价格限制

	LockFloatFactor  decimal.Decimal `db:"lock_float_factor" json:"lock_float_factor"` //资产冻结上浮系数
	RedundancyFactor decimal.Decimal `db:"redundancy_factor" json:"redundancy_factor"` //市价冗余系数

	IsMaintenance alias.NumBool `db:"is_maintenance" json:"is_maintenance"` //是否系统维护
}

type ApiContractDepthArg struct {
	ContractCode string `json:"contract_code"` // 合约code
	Level        int    `json:"level"`         // 深度等级
}

type ApiContractDepth struct {
	ContractCode string  `json:"contract_code"` // 合约code
	Level        int     `json:"level"`         // 深度等级
	UpdateTime   int64   `json:"update_time"`   // 最后更新时间
	Buy          []Depth `json:"buy"`           // 看多单
	Sell         []Depth `json:"sell"`          // 看空单
}

type ApiNormalKLineArg struct {
	ContractCode string `json:"contract_code"` // 合约code
	Duration     string `json:"duration"`      // k线时间段
	StartTime    int64  `json:"start_time"`    // 开始时间
	Count        int    `json:"count"`         // 条数
}

type ApiTradingViewKLineArg struct {
	ContractCode string `json:"contract_code"` // 合约code
	Duration     string `json:"duration"`      // k线时间段
	StartTime    int64  `json:"start_time"`    // 开始时间
	EndTime      int64  `json:"end_time"`      // 结束时间
}

type ApiKLineData struct {
	ContractCode   string  `json:"contract_code"`    // 合约code
	ContractName   string  `json:"contract_name"`    // 合约中文名
	ContractNameEn string  `json:"contract_name_en"` // 合约英文名
	Duration       string  `json:"duration"`         // k线时间段
	ServerTime     int64   `json:"server_time"`      // 服务器时间
	List           []KLine `json:"list"`             // k线数据
}

type ApiContractChange struct {
	ContractCode   string `json:"contract_code"`    // 合约cod
	ContractName   string `json:"contract_name"`    // 合约中文名
	ContractNameEn string `json:"contract_name_en"` // 合约英文名
	CurrentPrice   string `json:"current_price"`    // 当前价格
	PriceCNY       string `json:"cny_price"`        // 折合人民币价格
	Change         string `json:"change"`           // 日涨跌量
	ChangeRatio    string `json:"change_ratio"`     // 日涨跌幅
	HighPrice      string `json:"high_price"`       // 日最高价
	LowPrice       string `json:"low_price"`        // 日最低价
}

type ApiRecentOrder struct {
	TradeID      int64  `json:"trade_id" db:"trade_id"`           // 订单id
	ContractCode string `json:"contract_code" db:"contract_code"` // 合约code
	Price        string `json:"price" db:"price"`                 // 价格
	Volume       int    `json:"volume" db:"volume"`               // 数量
	Side         string `json:"side" db:"side"`                   // 成交方向
	OrderTime    int64  `json:"order_time" db:"order_time"`       // 成交时间
}

type QuoteChange struct {
	ContractCode   string          `json:"contract_code" db:"contract_code"`       // 合约代码
	ContractName   string          `json:"contract_name" db:"contract_name"`       // 合约名称
	ContractNameEn string          `json:"contract_name_en" db:"contract_name_en"` // 合约英文名称
	Index          float64         `json:"index" db:"index"`                       // 合约指数
	IndexStr       string          `json:"index_str" db:"index_str"`               // 合约指数(字符串)
	IndexCNY       decimal.Decimal `db:"index_cny" json:"index_cny"`               // 人民币指数价格
	QuoteChange    string          `json:"quote_change" db:"quote_change"`         // 合约涨跌幅
	Kline          []float64       `db:"kline" json:"kline"`                       // k线数据
}

type ContractPageArg struct {
	ContractCode string `json:"contract_code"` // 合约代码
	define.Page
}

type HourFactor struct {
	A decimal.Decimal `json:"a"`
	B decimal.Decimal `json:"b"`
}

type APISymbolMarketReply struct {
	List []APISymbolMarket `json:"list" extensions:"x-order=01"` // 数据列表
}

type APISymbolMarket struct {
	Symbol      string          `json:"symbol" example:"BTCUSDT" extensions:"x-order=01"`                            // 合约/交易对
	Icon        string          `json:"icon" example:"https://www.a.cn/btc.png" extensions:"x-order=02"`             // 图标
	Price       decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=03"`        // 最新价
	ChangeRatio decimal.Decimal `json:"change_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 涨跌幅
	HighPrice   decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`   // 最高价
	LowPrice    decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=06"`    // 最低价
	TradeVolume decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 成交量
}
