package proto

import (
	"time"

	"bc/libs/define"
)

type VersionInfo struct {
	PlatformID    int                       `db:"platform_id" json:"platform_id"`       // 平台id
	ChannelID     int                       `db:"channel_id" json:"channel_id"`         // 渠道id
	OSType        define.OsType             `db:"os_type" json:"os_type"`               // 平台类型
	Version       int                       `db:"version" json:"version"`               // 数字版本号
	VersionString string                    `db:"version_string" json:"version_string"` // 文字版本号
	ForceUpgrade  bool                      `db:"force_upgrade" json:"force_upgrade"`   // 是否强制升级
	Link          string                    `db:"link" json:"link"`                     // 下载页面
	URL           string                    `db:"url" json:"url"`                       // 下载链接
	CreateTime    time.Time                 `db:"create_time" json:"create_time"`       // 更新时间
	LastForce     int                       `db:"last_force" json:"last_force"`         // 最后一个强制更新版本
	Content       map[define.ReqLang]string `json:"content"`                            // 更新文案
}

type CoinNameArg struct {
	CoinName string `json:"coin_name"` // 币种名称
}

type ShareResourceCacheDelete map[int]map[int8]map[define.ReqLang]struct{}

func (s ShareResourceCacheDelete) DelLang(shareType int, shareLevel int8, lang define.ReqLang) {
	tm, ok := s[shareType]
	if !ok {
		return
	}

	lm, ok := tm[shareLevel]
	if !ok {
		return
	}

	delete(lm, lang)
}

func (s ShareResourceCacheDelete) Final() ShareResourceCacheDelete {
	for st, tm := range s {
		for sl, lm := range tm {
			if len(lm) == 0 {
				delete(tm, sl)
			}
		}
		if len(tm) == 0 {
			delete(s, st)
		}
	}
	return s
}

func NewShareResourceCacheDelete() ShareResourceCacheDelete {
	m := make(ShareResourceCacheDelete)
	for shareType := define.ShareTypeHold; shareType < define.ShareTypeNonsupport; shareType++ {
		m[shareType] = make(map[int8]map[define.ReqLang]struct{})
		for _, level := range define.ShareResourceLevels {
			m[shareType][level] = make(map[define.ReqLang]struct{})
			for lang := define.ReqLangCN; lang < define.ReqLangNonSupport; lang++ {
				m[shareType][level][lang] = struct{}{}
			}
		}
	}
	return m
}
