package proto

import (
	"database/sql"
	"time"

	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type GiftInfoList struct {
	Id               int                              `db:"id" json:"id"`                               // 赠金活动id
	TimeLimit        int                              `db:"time_limit" json:"time_limit"`               // 时间限制 小时
	CloseCount       int                              `db:"close_count" json:"close_count"`             // 平仓次数
	TraderDay        int                              `db:"trader_day" json:"trader_day"`               // 有效交易天数
	Category         define.GiftActivityType          `db:"category" json:"category"`                   // 1-首次充值，2-首次交易，3-新手交易量达标，4-活跃用户，5-充值返利，6-邀请好友
	State            define.GiftActivityState         `db:"state" json:"state"`                         // 状态 0-未启用 1-启用
	Partake          define.GiftPartake               `db:"partake" json:"partake"`                     // 参与者 0-全部用户 1-非代理用户-2代理
	AmountLimit      decimal.Decimal                  `db:"amount_limit" json:"amount_limit"`           // 有效金额限制
	GiftAmount       decimal.Decimal                  `db:"gift_amount" json:"gift_amount"`             // 赠金数量
	FriendsRecharge  decimal.Decimal                  `db:"friends_recharge" json:"friends_recharge"`   // 好友充值数量
	ExceedTime       sql.NullTime                     `db:"exceed_time" json:"exceed_time"`             // 过期时间
	CreateTime       time.Time                        `db:"create_time" json:"create_time"`             // 创建时间
	ReceiveExceed    int                              `db:"receive_exceed" json:"receive_exceed"`       // 领取超时
	ActivationExceed int                              `db:"activation_exceed" json:"activation_exceed"` // 激活超时
	TradeExceed      int                              `db:"trade_exceed" json:"trade_exceed"`           // 交易回收超时
	Trans            map[define.ReqLang]GiftInfoTrans `json:"trans"`                                    // 多语言内容
}

func (g *GiftInfoList) TransEntity(lang define.ReqLang) GiftInfoTrans {
	trans, ok := g.Trans[lang]
	if !ok {
		return g.Trans[define.ReqLangEN]
	}
	return trans
}

type GiftInfoText struct {
	Id       int            `db:"id" json:"id"`               // 赠金活动id
	LangType define.ReqLang `db:"lang_type" json:"lang_type"` // 语言类型
	GiftInfoTrans
}

type GiftInfoTrans struct {
	Title   string `db:"title" json:"title"`     // 活动标题
	Content string `db:"content" json:"content"` // 活动说明
}

type GiftRecord struct {
	Id             string               `db:"id"`              // 记录id
	UserId         int64                `db:"user_id"`         // 用户id
	GiftId         int                  `db:"gift_id"`         // 活动id
	Amount         decimal.Decimal      `db:"amount"`          // 数量
	State          define.GiftJoinState `db:"state"`           // 参与状态 1-未开始 2-进行中 3-待领取 4-已领取 5-已过期
	CreateTime     time.Time            `db:"create_time"`     // 创建时间
	ExpireTime     sql.NullTime         `db:"expire_time"`     // 失效时间
	ReceiveTime    sql.NullTime         `db:"receive_time"`    // 领取时间
	ActiveTime     sql.NullTime         `db:"active_time"`     // 激活时间
	RecoveryTime   sql.NullTime         `db:"recovery_time"`   // 回收时间
	UpdateTime     time.Time            `db:"update_time"`     // 最后更新时间
	ReceiveAccount define.AssetType     `db:"receive_account"` // 领取账户
	Imei           string               `db:"imei"`            // 领取设备
	IP             string               `db:"ip"`              // ip地址
	AfterAmount    decimal.Decimal      `db:"after_amount"`    // 领取赠金余额
}

type GiftRecordAbstract struct {
	GiftName   string          `db:"gift_name"`   // 活动名称
	GiftId     int             `db:"gift_id"`     // 活动id
	Amount     decimal.Decimal `db:"amount"`      // 数量
	CreateTime sql.NullTime    `db:"create_time"` // 创建时间
}

type GiftManageSend struct {
	ID          int                        `db:"id" json:"id"`
	UserID      int64                      `db:"user_id" json:"user_id"`           // 用户id
	GiftID      int                        `db:"gift_id" json:"gift_id"`           // 赠金活动id
	GiftName    string                     `db:"gift_name" json:"gift_name"`       // 赠金名称
	Stype       define.GiftManageSendType  `db:"stype" json:"stype"`               // 1发放 0收回
	Cause       define.GiftManageSendCause `db:"cause" json:"cause"`               // 原因
	SourceID    int64                      `db:"source_id" json:"source_id"`       // 对应资金记录id
	Amount      decimal.Decimal            `db:"amount" json:"amount"`             // 数量
	AfterAmount decimal.Decimal            `db:"after_amount" json:"after_amount"` // 赠金余额
	Manage      string                     `db:"manage" json:"manage"`             // 管理员
	CreateTime  time.Time                  `db:"create_time" json:"create_time"`   // 创建时间
}
