package proto

import "github.com/shopspring/decimal"

type AccountArg struct {
	Account string `json:"account"`
}

// 注册请求参数
type RegisterArg struct {
	Account         string `json:"account"`          // 账号
	InviteCode      string `json:"invite_code"`      // 邀请码
	ChannelID       string `json:"channel_id"`       // 渠道id
	Code            string `json:"code"`             // 短信验证码
	AreaCode        string `json:"areacode"`         // 区号
	CountryCode     string `json:"country_code"`     // 国家区号
	Password        string `json:"password"`         // 登录密码
	QuicklyRegister uint8  `json:"quickly_register"` // 是否是快速注册 0-不是 1-是(不强制设置密码)
}

type RegisterReply struct {
	Token              string          `json:"token,omitempty"`      // token
	Reward             decimal.Decimal `json:"reward"`               // 奖励
	HasPassword        bool            `json:"has_password"`         // 是否已经设置了登录密码
	HasFundPassword    bool            `json:"has_fund_password"`    // 是否已经设置了资金密码
	HasTotpBind        bool            `json:"has_totp_bind"`        // 是否已经绑定了验证器
	LegalWithdrawLimit bool            `json:"legal_withdraw_limit"` // 是否因入金限制了提币
	User
}

// 登陆请求参数
type LoginArg struct {
	Account  string `json:"account"`  // 账号
	Code     string `json:"code"`     // 短信验证码
	Password string `json:"password"` // 登录密码
	SafeVerifyCode
}

// 登陆返回值
type LoginReply struct {
	Token              string `json:"token,omitempty"`      // token
	HasPassword        bool   `json:"has_password"`         // 是否已经设置了登录密码
	HasFundPassword    bool   `json:"has_fund_password"`    // 是否已经设置了资金密码
	HasTotpBind        bool   `json:"has_totp_bind"`        // 是否已经绑定了验证器
	LegalWithdrawLimit bool   `json:"legal_withdraw_limit"` // 是否因入金限制了提币
	User
}

// 找回密码请求参数
type ForgetPwdArg struct {
	Account     string `json:"account"`      // 账号
	NewPassword string `json:"new_password"` // 新密码
	SafeVerifyCode
}

type AccountChange struct {
	UserId    int64                `json:"user_id"`
	RiskRate  string               `json:"risk_rate"`
	ForceList []ContractForcePrice `json:"force_list"`
}

type UserContractForce struct {
	UserId int64                `json:"user_id"`
	List   []ContractForcePrice `json:"list"`
}

type ContractForcePrice struct {
	PositionId   int64  `json:"position_id,omitempty"`
	UserId       int64  `json:"user_id"`
	ContractCode string `json:"contract_code"`
	ForcePrice   string `json:"force_price,omitempty"`
}

type PositionDynamic struct {
	PositionId   int64  `json:"position_id,omitempty"`
	UserId       int64  `json:"user_id"`
	ContractCode string `json:"contract_code"`
	ForcePrice   string `json:"force_price,omitempty"`
	ProfitRatio  string `json:"profit_ratio,omitempty"`
	FloatProfit  string `json:"float_profit,omitempty"`
}

type UserPosList struct {
	UserId      int64             `json:"user_id"`
	AccountType int               `json:"account_type"` //0-合约账户
	List        []PositionDynamic `json:"list"`
}

type PositionUpdate struct {
	Action   string       `json:"action"`
	Position UserPosition `json:"position"`
}

type FollowPositionUpdate struct {
	Action   string         `json:"action"`
	Position FollowPosition `json:"position"`
}
