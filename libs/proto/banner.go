package proto

type TbBanner struct {
	End     int64   `db:"end" json:"end"`           //结束时间
	Id      int     `db:"id" json:"id"`             //轮播图id
	Image   string  `db:"image" json:"image"`       //中文图
	ImageEn string  `db:"image_en" json:"image_en"` //英文图
	Limit   int     `db:"limit" json:"limit"`       //客户端限定 0 不限制, 1 ios, 2 android
	Link    string  `db:"link" json:"link"`         //中文跳转地址
	LinkEn  string  `db:"link_en" json:"link_en"`   //英文跳转地址
	Start   int64   `db:"start" json:"start"`       //开始时间
	Title   string  `db:"title" json:"title"`       //标题
	Valid   int     `db:"valid" json:"valid"`       //是否有效
	Weight  float64 `db:"weight" json:"weight"`     //显示权重
}

type Notice struct {
	Abstract   string  `db:"abstract" json:"abstract"`       //中文摘要
	AbstractEn string  `db:"abstract_en" json:"abstract_en"` //英文摘要
	Content    string  `db:"content" json:"content"`         //中文正文
	ContentEn  string  `db:"content_en" json:"content_en"`   //英文正文
	CreatedAt  int64   `db:"created_at" json:"created_at"`   //发布时间
	Id         int     `db:"id" json:"id"`                   //公告id
	IsTop      int     `db:"is_top" json:"is_top"`           //是否置顶
	Title      string  `db:"title" json:"title"`             //中文标题
	TitleEn    string  `db:"title_en" json:"title_en"`       //英文标题
	Weight     float64 `db:"weight" json:"weight"`           //权重,值越大排位越靠前
}
