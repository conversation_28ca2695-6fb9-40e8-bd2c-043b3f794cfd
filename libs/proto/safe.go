package proto

import (
	"bc/libs/define"
	"time"
)

type SafeVerifyCode struct {
	PhoneCode      string `json:"phone_code"`       // 两步验证 短信验证码
	EmailCode      string `json:"email_code"`       // 两步验证 邮箱验证码
	SpareEmailCode string `json:"spare_email_code"` // 两步验证 备用邮箱验证码
	TotpCode       string `json:"totp_code"`        // 两步验证 令牌验证码
	FundPassword   string `json:"fund_password"`    // 两步验证 资金密码
}

func (svc *SafeVerifyCode) IsTwoStep() bool {
	// 目前资金密码只在提现中使用,不需要加入判断中
	return len(svc.PhoneCode) > define.EmptyStrSize ||
		len(svc.EmailCode) > define.EmptyStrSize ||
		len(svc.SpareEmailCode) > define.EmptyStrSize ||
		len(svc.TotpCode) > define.EmptyStrSize
}

type UserLog struct {
	SeqID       int               `db:"seq_id" json:"seq_id"`
	UserID      int64             `db:"user_id" json:"user_id"`           // 用户id
	OpType      define.UserOPType `db:"op_type" json:"op_type"`           // 操作类型 0-登录 1-注册 2-找回登录密码 3-设置登录密码 4-修改登录密码 5-修改资金密码 6-修改手机号 7-修改邮箱 8-提现申请
	UserName    string            `db:"user_name" json:"user_name"`       // 用户名
	OsType      define.OsType     `db:"os_type" json:"os_type"`           // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	Device      string            `db:"device" json:"device"`             // 设备名称
	DeviceID    string            `db:"device_id" json:"device_id"`       // 设备识别码
	LangType    define.ReqLang    `db:"lang_type" json:"lang_type"`       // 语言类型 0-简中 1-英文 2-繁中 3-韩语 4-日语
	Version     string            `db:"version" json:"version"`           // app版本
	IpAddress   string            `db:"ip_address" json:"ip_address"`     // IP地址
	CreatedTime time.Time         `db:"created_time" json:"created_time"` // 操作时间
}
