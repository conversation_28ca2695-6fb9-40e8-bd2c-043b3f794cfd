package proto

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
	"time"
)

type ApiCurrentPosition struct {
	TotalRights float64           `json:"total_rights" db:"total_rights"` // 总权益
	TotalFloat  float64           `json:"total_float" db:"total_float"`   // 总浮动盈亏
	List        []CurrentPosition `json:"list"`                           // 持仓列表
}

type CurrentPosition struct {
	PositionId     int64              `db:"id" json:"position_id"`                    // 用户持仓ID
	ContractCode   string             `json:"contract_code" db:"contract_code"`       // 合约code
	ContractName   string             `json:"contract_name" db:"contract_name"`       // 中文合约名
	ContractNameEn string             `json:"contract_name_en" db:"contract_name_en"` // 英文合约名
	Side           string             `json:"side" db:"side"`                         // 合约方向
	Price          float64            `db:"price" json:"price"`                       // 持仓均价
	PriceStr       string             `json:"price_str"`
	ParValue       decimal.Decimal    `db:"par_value" json:"par_value"`     // 合约面值
	Volume         int                `db:"volume" json:"volume"`           // 持仓张数
	VolumeLock     int                `db:"volume_lock" json:"volume_lock"` //持仓冻结额张数
	ForcePrice     float64            `db:"force_price" json:"force_price"` // 强平价
	ForcePriceStr  string             `json:"force_price_str"`
	Limit          float64            `db:"limit" json:"limit"`                   // 止盈价
	Stop           float64            `db:"stop" json:"stop"`                     // 止损价
	AccountType    define.AccountType `json:"account_type" db:"account_type"`     // 持仓类型 1全仓 2逐仓
	Lever          int                `json:"lever" db:"lever"`                   // 杠杆倍数
	Margin         float64            `db:"margin" json:"margin"`                 // 保证金
	MarginGift     decimal.Decimal    `db:"margin_gift" json:"margin_gift"`       // 保证金币种赠金
	AdjustMargin   float64            `db:"adjust_margin" json:"adjust_margin"`   // 调整保证金
	InitMargin     float64            `db:"init_margin" json:"init_margin"`       // 初始保证金
	FloatProfit    float64            `db:"float_profit" json:"float_profit"`     // 未实现盈亏
	Available      float64            `db:"available" json:"available"`           // 可用余额
	ProfitRatio    float64            `db:"profit_ratio" json:"profit_ratio"`     // 盈亏率
	MarginRatio    float64            `db:"margin_ratio" json:"margin_ratio"`     // 保证金率
	ContractIndex  float64            `db:"contract_index" json:"contract_index"` // 合约指数
	BuyPrice       float64            `db:"buy_price" json:"buy_price"`           // 买入价
	SellPrice      float64            `db:"sell_price" json:"sell_price"`         // 卖出价
	UserId         int64              `db:"user_id" json:"user_id"`               // 用户id
	Commission     float64            `db:"commission" json:"commission"`         // 手续费
	ShareResource  *ShareResource     `json:"share_resource"`                     // 分享资源
	PlatformID     int                `db:"platform_id" json:"platform_id"`
	CurrentPrice   decimal.Decimal    `db:"current_price" json:"current_price"` // 当前价
}

type ApiSoldTrade struct {
	ContractName   string          `json:"contract_name" db:"contract_name"`       // 中文合约名
	ContractNameEn string          `json:"contract_name_en" db:"contract_name_en"` // 英文合约名
	ParValue       decimal.Decimal `db:"par_value" json:"par_value"`               // 合约面值
	ProfitStr      string          `json:"profit_str"`
	CloseProfitStr string          `json:"close_profit_str"`
	Trade
}

type ApiConditionOrder struct {
	ContractCode    string          `json:"contract_code" db:"contract_code"`       // 合约code
	ContractName    string          `json:"contract_name" db:"contract_name"`       // 中文合约名
	ContractNameEn  string          `json:"contract_name_en" db:"contract_name_en"` // 英文合约名
	ParValue        decimal.Decimal `db:"par_value" json:"par_value"`               // 合约面值
	PlanOrderID     int64           `json:"plan_order_id" db:"id"`                  // 条件单id
	Status          int             `json:"status" db:"state"`                      // 条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败）
	Side            string          `json:"side" db:"side"`                         // 委托方向
	CreateTime      int64           `json:"create_time" db:"create_time"`           // 提交时间
	Volume          int             `json:"volume" db:"amount"`                     // 下单张数
	Condition       int8            `json:"condition" db:"condition"`               // 条件 1 >=, 2 <=
	TriggerPrice    string          `json:"trigger_price" db:"trigger_price"`       // 触发价格
	OrderTime       int64           `json:"order_time" db:"trigger_time"`           // 触发时间 10位秒时间戳 未触发时为0
	AccountType     int8            `json:"account_type" db:"account_type"`         // 账户模式 1全仓 2逐仓
	Lever           int             `json:"lever" db:"lever"`                       // 杠杆倍数
	TriggerType     int             `db:"trigger_type" json:"trigger_type"`         // 触发类型 1-条件单 2-止盈单 4-止损单
	Mode            int             `db:"mode" json:"mode"`                         //委托类型，1-对手价，2-最优3挡 3-最后5挡
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"`
}

type FloatProfitParams struct {
	ContractCode string  `db:"contract_code"` // 合约code
	HoldVolume   int     `db:"volume"`        // 持仓数
	Side         string  `db:"side"`          // 方向
	AvaPrice     string  `db:"price"`         // 开仓均价
	Roic         float64 `db:"roic"`          // 回报率
}

type UserOrderState struct {
	AccountType int `json:"account_type" db:"account_type"` // 账户类型
	Lever       int `json:"lever" db:"lever"`               // 杠杠倍数
	HoldBuy     int `json:"hold_buy" db:"hold_buy"`         // 多单持仓数
	HoldSell    int `json:"hold_sell" db:"hold_sell"`       // 空单持仓数
	UnsoldBuy   int `json:"unsold_buy" db:"unsold_buy"`     // 多单未成交数
	UnsoldSell  int `json:"unsold_sell" db:"unsold_sell"`   // 空单未成交数
}

type LevelArg struct {
	Level     int8 `json:"level"`      // 等级
	ShareType int  `json:"share_type"` // 分享类型
}

type ShareResource struct {
	ImageBackground string `json:"image_background"` // 背景图
	TitleColor      string `json:"title_color"`      // 标题颜色
	TitleVertical   uint16 `json:"title_vertical"`   // 标题垂直位置
	ContentColor    string `json:"content_color"`    // 内容区文字颜色
	TitleText       string `json:"title_text"`       // 标题内容
	TitleSize       uint16 `json:"title_size"`       // 标题字号
}

type ShareImage struct {
	ID              int            `json:"id" db:"id"`
	LanguageType    define.ReqLang `db:"language_type" json:"language_type"`       // 语言
	ImageBackground string         `json:"image_background" db:"image_background"` // 背景图
	TitleColor      string         `json:"title_color" db:"title_color"`           // 标题文本颜色
	TitleVertical   uint16         `db:"title_vertical" json:"title_vertical"`     // 标题垂直位置
	ContentColor    string         `json:"content_color" db:"content_color"`       // 内容区文字颜色
	ShareType       int            `json:"share_type" db:"share_type"`             // 分享类型 0-持仓分享 1-成交记录分享
	LevelType       int8           `json:"level_type" db:"level_type"`             // 等级类型
}

type ShareText struct {
	ID           int            `json:"id" db:"id"`
	LanguageType define.ReqLang `db:"language_type" json:"language_type"` // 语言
	TitleText    string         `json:"title_text" db:"title_text"`       // 标题内容
	TitleSize    uint16         `json:"title_size" db:"title_size"`       // 标题字号
	ShareType    int            `json:"share_type" db:"share_type"`       // 分享类型 0-持仓分享 1-成交记录分享
	LevelType    int8           `json:"level_type" db:"level_type"`       // 等级类型
}

type EntrustSoldArg struct {
	UserContract

	Offset      string             `json:"offset"`       // O-开仓 C-平仓
	AccountType define.AccountType `json:"account_type"` // 仓位模式 0-全部 1：全仓 2：逐仓 3：跟单-带单 4：跟单-跟单 5：全仓-分仓 6：逐仓-分仓
	LimitDays   int                `json:"limit_days"`   // 数据限制天数 当前仅支持7天或30天
}

type UserOrder struct {
	UserID       int64  `db:"user_id" json:"user_id"`
	OrderID      int64  `db:"order_id" json:"order_id"`
	ContractCode string `db:"contract_code" json:"contract_code"`
}

// EntrustOrder 委托订单
type EntrustOrder struct {
	ID              int64              `db:"id" json:"order_id"`
	UserID          int64              `db:"user_id" json:"user_id"`
	PlatformID      int                `db:"platform_id" json:"platform_id"`     //平台id
	ContractCode    string             `db:"contract_code" json:"contract_code"` //合约
	PositionID      int64              `db:"position_id" json:"position_id"`     //平仓持仓id
	EntrustType     int                `db:"entrust_type" json:"entrust_type"`   //委托类型 0-市价 1-限价
	EntrustStrategy int                `db:"entrust_strategy" json:"entrust_strategy"`
	Offset          string             `db:"offset" json:"offset"`             //O-开仓 C-平仓
	Mode            int                `db:"mode" json:"mode"`                 //下单模式 1-对手价 2-最优3挡 3-最优5挡
	Side            string             `db:"side" json:"side"`                 //委托方向 B/S 平仓为持仓反方向
	OrderType       int                `db:"order_type" json:"order_type"`     //订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	Price           decimal.Decimal    `db:"price" json:"entrust_price"`       //委托价格;限价单传递价格
	Volume          int                `db:"volume" json:"volume"`             //委托张数
	AccountType     define.AccountType `db:"account_type" json:"account_type"` //账户类型 1-全仓 2-逐仓 3-跟单
	Lever           int                `db:"lever" json:"lever"`               //杠杠

	AssetLock decimal.Decimal `db:"asset_lock" json:"asset_lock"` //账户锁定金额
	/* 成交信息*/
	TradeVolume    int             `db:"trade_volume" json:"trade_volume"`         //成交张数
	TradePrice     decimal.Decimal `db:"trade_price" json:"trade_price"`           //成交均价
	LastMatchPrice decimal.Decimal `db:"last_match_price" json:"last_match_price"` //最后撮合价
	CostFee        decimal.Decimal `db:"cost_fee" json:"cost_fee"`                 //成交手续费
	CostAsset      decimal.Decimal `db:"cost_asset" json:"cost_asset"`             //成交资产
	CloseProfit    decimal.Decimal `db:"close_profit" json:"close_profit"`         //平仓盈亏
	Profit         decimal.Decimal `db:"profit" json:"profit"`                     //成交资产

	State         int       `db:"state"`                                  //状态
	CreateTime    time.Time `db:"create_time" json:"create_time"`         //委托时间
	UpdateTime    time.Time `db:"update_time" json:"update_time"`         //更新时间
	Mark          int       `db:"mark" json:"mark"`                       //用户标记 1-用户标记撤销
	ExtraID       int64     `db:"extra_id" json:"extra_id"`               //源id
	FollowOrderID int64     `db:"follow_order_id" json:"follow_order_id"` //跟随委托单ID
	TriggerType   int       `db:"trigger_type" json:"trigger_type"`       //止盈止损触发标的 0-成交价,1-标记价格
	LimitAmount   int       `db:"limit_amount" json:"limit_amount"`       // 止盈张数
	StopAmount    int       `db:"stop_amount" json:"stop_amount"`         // 止损张数

	TriggerTypeLimit int `db:"trigger_type_limit" json:"trigger_type_limit"` // 止盈触发标的 0-成交价,1-标记价格
	TriggerTypeStop  int `db:"trigger_type_stop" json:"trigger_type_stop"`   // 止损触发标的 0-成交价,1-标记价格
	EntrustTypeLimit int `db:"entrust_type_limit" json:"entrust_type_limit"` // 止盈委托类型 0-市价 1-限价
	EntrustTypeStop  int `db:"entrust_type_stop" json:"entrust_type_stop"`   // 止损委托类型 0-市价 1-限价

	TriggerEntrustType int             `db:"trigger_entrust_type" json:"trigger_entrust_type"` //委托类型 0-市价 1-限价
	Limit              decimal.Decimal `db:"limit" json:"limit"`                               // 止盈价格
	Stop               decimal.Decimal `db:"stop" json:"stop"`                                 // 止损价格
	EntrustLimit       decimal.Decimal `db:"entrust_limit" json:"entrust_limit"`               // 止盈委托执行价
	EntrustStop        decimal.Decimal `db:"entrust_stop" json:"entrust_stop"`                 // 止损委托执行价

	PlanTriggerPrice float64 `db:"plan_trigger_price" json:"plan_trigger_price"` //止盈止损触发价

	Imei        string `db:"imei" json:"-"`         //设备识别码
	IpAddress   string `db:"ip_address" json:"-"`   //客户IP
	OrderClient int    `db:"order_client" json:"-"` //委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)

	ClientOrderId int64 `db:"client_order_id" json:"client_order_id"`

	ExpectPrice      decimal.Decimal `db:"expect_price" json:"expect_price"`         //预计成交价
	IsUseBrokenPrice bool            `db:"use_broken_price" json:"use_broken_price"` //是否使用的破产价格
	BrokenContract   bool            `db:"broken_contract" json:"broken_contract" `  //是否爆仓合约
	IsFollower       bool            `db:"is_follower" json:"is_follower"`           // 是否是跟随者

	GiftAmount  decimal.Decimal `db:"gift_amount" json:"gift_amount"`
	GiftFee     decimal.Decimal `db:"gift_fee" json:"gift_fee"`
	GiftClose   decimal.Decimal `db:"gift_close" json:"gift_close"`
	GiftBlowing decimal.Decimal `db:"gift_blowing" json:"gift_blowing"`
}

func (o *EntrustOrder) IsFinished() bool {
	return o.State != define.OrderStatusPart && o.State != define.OrderStatusDefault
}

func (o *EntrustOrder) IsForce() bool {
	return o.EntrustType == define.EntrustTypeForce || o.OrderType == define.OrderTypeForceCloseOut
}

func (o *EntrustOrder) IsEntrustMarket() bool {
	return o.EntrustType == define.EntrustTypeMarket || (o.EntrustType == define.EntrustTypeForce && o.Price.Equal(decimal.Zero))
}

func (o *EntrustOrder) IsLimitStop() bool {
	return o.OrderType == define.OrderTypeLimit || o.OrderType == define.OrderTypeStop || o.OrderType == define.OrderTypeFollowLimit || o.OrderType == define.OrderTypeFollowStop
}

func (o *EntrustOrder) TradeMatchType(isProtocal bool) int {
	if isProtocal {
		return define.EntrustTradeProtocol
	}
	if o.IsEntrustMarket() {
		return define.EntrustTradeMarket
	}
	return define.EntrustTradeLimit
}

// MatchType 生成撮合服务支持的类型
func (o *EntrustOrder) MatchType() int {
	orderType := o.OrderType
	if orderType == define.OrderTypeLimit || orderType == define.OrderTypeStop || orderType == define.OrderTypeFollowLimit || orderType == define.OrderTypeFollowStop {
		return define.MatchTypeLimitStop
	}
	if orderType == define.OrderTypeForceCloseOut {
		return define.MatchTypeForce
	}
	return define.MatchTypeDefault
}

// GetUserAccountType 根据订单持仓类型获取用户账户类型
func (o *EntrustOrder) GetUserAccountType() int {
	return o.AccountType.GetUserAccountType()
}

type CurrentEntrustOrder struct {
	OrderID         string          `db:"order_id" json:"order_id"`             // 订单id
	ContractCode    string          `db:"contract_code" json:"contract_code"`   // 合约代码
	ContractName    string          `db:"contract_name" json:"contract_name"`   // 合约名称
	Side            string          `db:"side" json:"side"`                     // 多空方向 B-多 S-空
	Offset          string          `db:"offset" json:"offset"`                 // 开平方向 O-开 C-平
	AccountType     int             `db:"account_type" json:"account_type"`     // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `db:"lever" json:"lever"`                   // 账户杠杆
	State           int             `db:"state" json:"state"`                   // 成交状态 等待成交 部分成交
	Mode            int             `db:"mode" json:"mode"`                     // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	TradeVolume     int             `db:"trade_volume" json:"trade_volume"`     // 成交数量
	EntrustVolume   int             `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	EntrustTime     time.Time       `db:"entrust_time" json:"-"`                // 委托时间
	EntrustTimeUnix int64           `json:"entrust_time"`                       // 委托时间
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 委托价格
	Limit           decimal.Decimal `db:"limit" json:"limit"`                   // 止盈价格
	Stop            decimal.Decimal `db:"stop" json:"stop"`                     // 止损价
	ParValue        decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
}

type CurrentPlanEntrustOrder struct {
	OrderID         string          `db:"order_id" json:"order_id"`             // 订单id
	ContractCode    string          `db:"contract_code" json:"contract_code"`   // 合约代码
	ContractName    string          `db:"contract_name" json:"contract_name"`   // 合约名称
	Side            string          `db:"side" json:"side"`                     // 多空方向 B-多 S-空
	AccountType     int             `db:"account_type" json:"account_type"`     // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `db:"lever" json:"lever"`                   // 账户杠杆
	Condition       int             `db:"condition" json:"condition"`           // 触发条件 1 >= 2 <=
	EntrustMode     int             `db:"entrust_mode" json:"entrust_mode"`     // 执行类型
	EntrustVolume   int             `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	EntrustTime     time.Time       `db:"entrust_time" json:"-"`                // 委托时间
	EntrustTimeUnix int64           `json:"entrust_time"`                       // 委托时间
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 执行价格
	TriggerPrice    decimal.Decimal `db:"trigger_price" json:"trigger_price"`   // 触发价格
	Limit           decimal.Decimal `db:"limit" json:"limit"`                   // 止盈价格
	Stop            decimal.Decimal `db:"stop" json:"stop"`                     // 止损价
	ParValue        decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
}

type HistoryEntrustOrder struct {
	OrderID         string          `db:"order_id" json:"order_id"`             // 订单id
	ContractCode    string          `db:"contract_code" json:"contract_code"`   // 合约代码
	ContractName    string          `db:"contract_name" json:"contract_name"`   // 合约名称
	Side            string          `db:"side" json:"side"`                     // 多空方向 B-多 S-空
	Offset          string          `db:"offset" json:"offset"`                 // 开平方向 O-开 C-平
	AccountType     int             `db:"account_type" json:"account_type"`     // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	Mode            int             `db:"mode" json:"mode"`                     // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	OrderClient     define.OsType   `db:"order_client" json:"order_client"`     // 下单设备 1 android端 2 ios端 3 web 4 h5端 5 openApi 6 系统
	Lever           int             `db:"lever" json:"lever"`                   // 账户杠杆
	State           int             `db:"state" json:"state"`                   // 成交状态 101-未成交已撤 200-全部成交 201-部分成交已撤
	Source          int             `db:"source" json:"source"`                 // 成交来源
	TradeVolume     int             `db:"trade_volume" json:"trade_volume"`     // 成交数量
	EntrustVolume   int             `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	EntrustTime     time.Time       `db:"entrust_time" json:"-"`                // 委托时间
	EntrustTimeUnix int64           `json:"entrust_time"`                       // 委托时间
	UpdateTime      time.Time       `db:"update_time" json:"-"`                 // 更新时间
	UpdateTimeUnix  int64           `json:"update_time"`                        // 更新时间
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 委托价格
	TradePrice      decimal.Decimal `db:"trade_price" json:"trade_price"`       // 成交均价
	Limit           decimal.Decimal `db:"limit" json:"limit"`                   // 止盈价格
	Stop            decimal.Decimal `db:"stop" json:"stop"`                     // 止损价
	CloseProfit     decimal.Decimal `db:"close_profit" json:"close_profit"`     // 收益
	ParValue        decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
}

type HistoryPlanEntrustOrder struct {
	OrderID         string          `db:"order_id" json:"order_id"`             // 订单id
	ContractCode    string          `db:"contract_code" json:"contract_code"`   // 合约代码
	ContractName    string          `db:"contract_name" json:"contract_name"`   // 合约名称
	Side            string          `db:"side" json:"side"`                     // 多空方向 B-多 S-空
	AccountType     int             `db:"account_type" json:"account_type"`     // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `db:"lever" json:"lever"`                   // 账户杠杆
	State           int             `db:"state" json:"state"`                   // 状态 2-触发成功 3-触发成功,委托失败
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`     // 委托类型 0-市价 1-限价
	TriggerIndex    int             `db:"trigger_index" json:"trigger_index"`   // 触发标的 0-成交价 1-标记价格
	TriggerType     int             `db:"trigger_type" json:"trigger_type"`     // 类型 1-计划单 2-止盈 4-止损
	EntrustMode     int             `db:"entrust_mode" json:"entrust_mode"`     // 执行模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustVolume   int             `db:"entrust_volume" json:"entrust_volume"` // 委托数量
	EntrustTime     time.Time       `db:"entrust_time" json:"-"`                // 委托时间
	EntrustTimeUnix int64           `json:"entrust_time"`                       // 委托时间
	UpdateTime      time.Time       `db:"update_time" json:"-"`                 // 更新时间
	UpdateTimeUnix  int64           `json:"update_time"`                        // 更新时间
	TriggerPrice    decimal.Decimal `db:"trigger_price" json:"trigger_price"`   // 触发价格
	EntrustPrice    decimal.Decimal `db:"entrust_price" json:"entrust_price"`   // 执行价格
	Limit           decimal.Decimal `db:"limit" json:"limit"`                   // 止盈价格
	Stop            decimal.Decimal `db:"stop" json:"stop"`                     // 止损价
	ParValue        decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
}

type HistoryEntrustOrderShareResource struct {
	ContractCode  string          `db:"contract_code" json:"contract_code"`   // 合约代码
	Side          string          `db:"side" json:"side"`                     // 多空方向 B-平空 S-平多
	OpenAvgPrice  decimal.Decimal `db:"open_avg_price" json:"open_avg_price"` // 开仓均价
	TradePrice    decimal.Decimal `db:"trade_price" json:"trade_price"`       // 成交均价
	CloseProfit   decimal.Decimal `db:"close_profit" json:"close_profit"`     // 收益
	ProfitRatio   decimal.Decimal `db:"profit_ratio" json:"profit_ratio"`     // 收益率
	CurrentPrice  decimal.Decimal `json:"current_price"`                      // 当前最新价
	ParValue      decimal.Decimal `db:"par_value" json:"par_value"`           // 合约面值
	ShareResource *ShareResource  `json:"share_resource"`                     // 分享资源

	AccountType define.AccountType `db:"account_type" json:"-"` // 账户类型
	TradeVolume decimal.Decimal    `db:"trade_volume" json:"-"` // 成交张数
	Lever       decimal.Decimal    `db:"lever" json:"-"`        // 杠杆
}
