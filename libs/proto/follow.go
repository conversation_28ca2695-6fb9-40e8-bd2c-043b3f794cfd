package proto

import (
	"bc/libs/define"
	"bc/libs/formul"
	"bc/libs/log"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"
	"time"
)

type FollowAccount struct {
	AccountID      int64           `db:"account_id" json:"account_id"`
	UserId         int64           `db:"user_id" json:"user_id"`
	Balance        decimal.Decimal `db:"balance" json:"balance"`
	LockAmount     decimal.Decimal `db:"lock_amount" json:"lock_amount"`
	CurrencyID     int             `db:"currency_id" json:"currency_id"`
	TotalProfit    decimal.Decimal `db:"total_profit" json:"total_profit"`
	TotalProceeds  decimal.Decimal `db:"total_proceeds" json:"total_proceeds"`
	TotalPrincipal decimal.Decimal `db:"total_principal" json:"total_principal"`
	DayProfit      decimal.Decimal `db:"day_profit" json:"day_profit"`
	DayPrincipal   decimal.Decimal `db:"day_principal" json:"day_principal"`
	PlatformID     int             `db:"platform_id" json:"platform_id"`
}

type FollowAccountHistory struct {
	ID            int64                  `db:"id" json:"id"`
	UserId        int64                  `db:"user_id" json:"user_id"`
	PositionId    int64                  `db:"position_id" json:"position_id"`
	CurrencyID    int                    `db:"currency_id" json:"currency_id"`
	CurrencyName  string                 `db:"currency_name" json:"currency_name"`
	Balance       decimal.Decimal        `db:"balance" json:"balance"`
	Available     decimal.Decimal        `db:"available" json:"available"`
	Type          define.AccountBillType `db:"type" json:"type"`
	Amount        decimal.Decimal        `db:"amount" json:"amount"`
	CreateTime    time.Time              `db:"create_time" json:"create_time"`
	IpAddress     string                 `db:"ip_address" json:"ip_address"`
	Imei          string                 `db:"imei" json:"imei"`
	OrderClient   int                    `db:"order_client" json:"order_client"`
	Margin        decimal.Decimal        `db:"margin" json:"margin"`
	AccountRights decimal.Decimal        `db:"account_rights" json:"account_rights"`
	PlatformID    int                    `db:"platform_id" json:"platform_id"`
	ExtraID       int64                  `db:"extra_id" json:"extra_id,omitempty"`
}

// select user_id, contract_code,coin_id, side, volume, price, nominal_close_price, account_type, lever, force_close_price, init_margin, position_margin, adjust_margin, float_profit, profit_ratio, roic, commission from tb_position where user_id=? and contract_code=?
type FollowPosition struct {
	Id            int64           `db:"id" json:"id"`                         //用户持仓ID
	UserId        int64           `db:"user_id" json:"user_id"`               //
	ContractCode  string          `db:"contract_code" json:"contract_code"`   //合约代码
	Side          string          `db:"side" json:"side"`                     //B买S卖
	Volume        int             `db:"volume" json:"volume"`                 //持仓张数
	VolumeLock    int             `db:"volume_lock" json:"volume_lock"`       //委托锁定
	ContractIndex float64         `db:"contract_index" json:"contract_index"` //合约指数
	Lever         int             `db:"lever" json:"lever"`                   //杠杆
	Limit         decimal.Decimal `db:"limit" json:"limit"`                   //止盈价
	Stop          decimal.Decimal `db:"stop" json:"stop"`                     //止损价

	TraderUid        int64 `db:"trader_uid" json:"trader_uid"`                 //交易员用户id
	FollowPositionId int64 `db:"follow_position_id" json:"follow_position_id"` //跟随持仓id

	Price        decimal.Decimal `db:"price" json:"price"`                 //持仓均价
	RawMargin    decimal.Decimal `db:"raw_margin" json:"raw_margin"`       //原始保证金
	Margin       decimal.Decimal `db:"margin" json:"margin"`               //保证金
	InitMargin   decimal.Decimal `db:"init_margin" json:"init_margin"`     //初始保证金
	AdjustMargin decimal.Decimal `db:"adjust_margin" json:"adjust_margin"` //调整保证金
	MarginVolume decimal.Decimal `json:"margin_volume"`                    //仓位保证金（带浮动盈亏）
	Commission   decimal.Decimal `db:"commission" json:"commission"`       //手续费
	OpenFee      decimal.Decimal `db:"open_fee" json:"open_fee"`           //开仓手续费
	CloseFee     decimal.Decimal `db:"close_fee" json:"close_fee"`         //累计平仓手续费
	CloseProfit  decimal.Decimal `db:"close_profit" json:"close_profit"`   //累计平仓盈亏
	ProfitRatio  decimal.Decimal `db:"profit_ratio" json:"profit_ratio"`   //盈亏率
	BuyPrice     decimal.Decimal `db:"buy_price" json:"buy_price"`         //买入价
	SellPrice    decimal.Decimal `db:"sell_price" json:"sell_price"`       //卖出价

	ForcePrice  decimal.Decimal `db:"force_price" json:"force_price"`
	FloatProfit decimal.Decimal `db:"float_profit" json:"float_profit"` //未实现盈亏
	MarginRatio decimal.Decimal `db:"margin_ratio" json:"margin_ratio"` //保证金率

	FundingAmount decimal.Decimal `db:"funding_amount" json:"funding_amount"` //资金费用

	PerValue decimal.Decimal `db:"par_value" json:"per_value"`

	Available  decimal.Decimal `db:"available" json:"-"` //开仓时可用
	CreateTime time.Time       `db:"create_time" json:"create_time"`
	PlatformID int             `db:"platform_id" json:"platform_id"`

	UpdateTime time.Time `db:"update_time" json:"update_time"` // 更新时间
}

// 设置浮动盈亏，收益率，保证金率
func (u *FollowPosition) SetDynamicData(perValue, complexPrice decimal.Decimal) {
	floatProfit := formul.GetFloatProfit(u.Side, u.Volume, perValue, u.Price, complexPrice)
	u.FloatProfit = floatProfit.Truncate(define.MarginPrecision)
	u.MarginVolume = u.Margin.Add(floatProfit).Truncate(define.MarginPrecision)
	u.MarginRatio = formul.GetMarginRateByWareHouse(u.Side, u.Margin, floatProfit, perValue, complexPrice, u.Volume).Truncate(4)
	if u.InitMargin.Equal(decimal.Zero) {
		log.Infof("[warning]保证金为0,phold:%+v", u)
		return
	}
	u.ProfitRatio = formul.GetForcastProfitRate(floatProfit, u.Margin)
}

type FollowTrade struct {
	DealId           int64           `db:"deal_id" json:"deal_id"`
	UserId           int64           `db:"user_id" json:"user_id"`
	TraderUid        int64           `db:"trader_uid" json:"trader_uid"`                 //交易员id
	FollowPositionId int64           `db:"follow_position_id" json:"follow_position_id"` //跟随持仓id
	PositionId       int64           `db:"position_id" json:"position_id"`
	OrderId          int64           `db:"order_id" json:"order_id"`
	ContractCode     string          `db:"contract_code" json:"contract_code"`
	Side             string          `db:"side" json:"side"`
	Offset           string          `db:"offset" json:"offset"`
	Price            decimal.Decimal `db:"price" json:"price"`
	OpenAvgPrice     decimal.Decimal `db:"open_avg_price" json:"open_avg_price"`
	Volume           int             `db:"volume" json:"volume"`
	TradeValue       decimal.Decimal `db:"trade_value" json:"trade_value"`
	Commission       decimal.Decimal `db:"commission" json:"commission"`
	BackProfit       decimal.Decimal `db:"back_profit" json:"back_profit"`
	Lever            int             `db:"lever" json:"lever"`
	TradeAmount      decimal.Decimal `db:"trade_amount" json:"trade_amount"`
	Profit           decimal.Decimal `db:"profit" json:"profit"`
	CloseProfit      decimal.Decimal `db:"close_profit" json:"close_profit"`
	OrderType        int             `db:"order_type" json:"order_type"`
	Limit            decimal.Decimal `db:"limit" json:"limit"`
	Stop             decimal.Decimal `db:"stop" json:"stop"`
	TradeTime        time.Time       `db:"trade_time" json:"trade_time"`
	Balance          decimal.Decimal `db:"balance" json:"balance"`
	Available        decimal.Decimal `db:"available" json:"available"`
	TotalProfit      decimal.Decimal `db:"total_profit" json:"total_profit"`
	BrokePrice       decimal.Decimal `db:"broke_price" json:"broke_price"`
	BuyPrice         decimal.Decimal `db:"buy_price" json:"buy_price"`
	SellPrice        decimal.Decimal `db:"sell_price" json:"sell_price"`
	IsTrader         bool            `db:"is_trader" json:"is_trader"`
	PlatformID       int             `db:"platform_id" json:"platform_id"`
	EntrustOrderId   int64           `db:"entrust_order_id" json:"entrust_order_id"`
	TradeRemark      int             `db:"trade_remark" json:"trade_remark"`
	BlowingFee       decimal.Decimal `db:"blowing_fee" json:"blowing_fee"`
	RawBlowingFee    decimal.Decimal `db:"raw_blowing_fee" json:"raw_blowing_fee"`
	RawCloseFee      decimal.Decimal `db:"raw_fee" json:"raw_fee"`                   //原始手续费
	RawCloseProfit   decimal.Decimal `db:"raw_close_profit" json:"raw_close_profit"` //原始平仓盈亏
	ExtraID          int64           `db:"extra_id" json:"extra_id,omitempty"`       // 额外标记id
	IpAddress        string          `db:"ip_address" json:"ip_address"`
	Imei             string          `db:"imei" json:"imei"`
	OrderClient      int             `db:"order_client" json:"order_client"`

	MatchType int   `db:"match_type" json:"match_type"` //撮合类型 0-市价 1-限价 2-协议成交
	MatchID   int64 `db:"match_id" json:"match_id"`     //撮合ID
	IsMaker   bool  `db:"is_maker" json:"is_maker"`
}

// 交易员跟单清算表
type DealerAudit struct {
	ID             int64           `db:"id" json:"id"`
	TraderUid      int64           `db:"trader_uid" json:"trader_uid"`
	FollowUid      int64           `db:"follow_uid" json:"follow_uid"`
	SideType       int             `db:"side_type" json:"side_type"` //0-退回跟随着，1-给交易员结算
	Rebate         decimal.Decimal `db:"rebate" json:"rebate"`
	WaitProfit     decimal.Decimal `db:"wait_profit" json:"wait_profit"`     //待结算盈利金额
	WaitLoss       decimal.Decimal `db:"wait_loss" json:"wait_loss"`         //待结算亏损金额
	Withholding    decimal.Decimal `db:"withholding" json:"withholding"`     //预扣佣金
	ActualAmount   decimal.Decimal `db:"actual_amount" json:"actual_amount"` //实际结算金额
	OwnAt          time.Time       `db:"own_at" json:"own_at"`
	CreateTime     time.Time       `db:"create_time" json:"create_time"`
	StartAuditTime time.Time       `db:"start_audit_time" json:"start_audit_time"`
	PlatformID     int             `db:"platform_id" json:"platform_id"`
}

// 结算统计表
type DealerDaySummary struct {
	TraderUid     int64           `db:"trader_uid" json:"trader_uid"`       //交易员id
	Profit        decimal.Decimal `db:"profit" json:"profit"`               //当日带单盈利
	Loss          decimal.Decimal `db:"loss" json:"loss"`                   //当日带单坤算资金
	GetProfit     decimal.Decimal `db:"get_profit" json:"get_profit"`       //当日累计所得佣金
	Number        int             `db:"number" json:"number"`               //当日带单张数
	TotalProfit   decimal.Decimal `db:"total_profit" json:"total_profit"`   //当日平仓盈亏
	TotalCapital  decimal.Decimal `db:"total_capital" json:"total_capital"` //当天平仓保证金
	ProfitCount   int             `db:"profit_count" json:"profit_count"`
	LossCount     int             `db:"loss_count" json:"loss_count"`
	TradingVolume decimal.Decimal `db:"trading_volume" json:"trading_volume"`
	FollowCount   decimal.Decimal `db:"follow_count" json:"follow_count"` //跟单人数
	OwnAt         time.Time       `db:"own_at" json:"own_at"`
	CreateTime    time.Time       `db:"create_time" json:"create_time"`
	PlatformID    int             `db:"platform_id" json:"platform_id"`
}

type DealerFollow struct {
	ID                int64           `db:"id" json:"id"`
	TraderUid         int64           `db:"trader_uid" json:"trader_uid"`
	FollowUid         int64           `db:"follow_uid" json:"follow_uid"`
	CurrencyId        int             `db:"currency_id" json:"currency_id"`
	Contract          string          `db:"contract" json:"contract"`
	FollowStatus      int             `db:"follow_status" json:"follow_status"`
	Quantity          decimal.Decimal `db:"quantity" json:"quantity"`     //跟随下单量
	Proportion        decimal.Decimal `db:"proportion" json:"proportion"` //跟随下单比列
	StopLoss          decimal.Decimal `db:"stop_loss" json:"stop_loss"`
	StopProfit        decimal.Decimal `db:"stop_profit" json:"stop_profit"`
	MaxAmount         decimal.Decimal `db:"max_amount" json:"max_amount"` //授权资金
	AlreadyAmount     decimal.Decimal `db:"already_amount" json:"already_amount"`
	ToatalAmount      decimal.Decimal `db:"toatal_amount" json:"toatal_amount"`
	WaitProfit        decimal.Decimal `db:"wait_profit" json:"wait_profit"`
	WaitLoss          decimal.Decimal `db:"wait_loss" json:"wait_loss"`                     //待结算亏损资金
	TotalActualAmount decimal.Decimal `db:"total_actual_amount" json:"total_actual_amount"` //累计奋勇资金
	AlreadyProfit     decimal.Decimal `db:"already_profit" json:"already_profit"`           //累计盈利资金
	AlreadyLoss       decimal.Decimal `db:"already_loss" json:"already_loss"`               //累计亏损资金
	Withholding       decimal.Decimal `db:"withholding" json:"withholding"`                 //预扣佣金
	StartAuditTime    time.Time       `db:"start_audit_time" json:"start_audit_time"`
	PlatformID        int             `db:"platform_id" json:"platform_id"`
}

type DealerPeopleConfig struct {
	ID     int64 `db:"id" json:"id"`
	Amount int   `db:"amount" json:"amount"`
	Number int   `db:"number" json:"number"`
}

type DealerUser struct {
	PlatformID       int                `db:"platform_id" json:"platform_id"`
	UserId           int64              `db:"user_id" json:"user_id"`
	Rebate           decimal.Decimal    `db:"rebate" json:"rebate"`
	IsStatus         define.DealerState `db:"is_status" json:"is_status"`
	FollowNumber     int                `db:"follow_number" json:"follow_number"`
	LimitNumber      int                `db:"limit_number" json:"limit_number"`
	Phone            string             `db:"phone" json:"phone"`
	CreateTime       time.Time          `db:"create_time" json:"create_time"`
	AuditTime        time.Time          `db:"audit_time" json:"audit_time"`
	CreateManage     string             `db:"create_manage" json:"create_manage"`
	TotalAmount      decimal.Decimal    `db:"total_amount" json:"total_amount"`
	TotalProfit      decimal.Decimal    `db:"total_profit" json:"total_profit"`
	TotalLoss        decimal.Decimal    `db:"total_loss" json:"total_loss"`
	TotalProfitCount int                `db:"total_profit_count" json:"total_profit_count"`
	TotalLossCount   int                `db:"total_loss_count" json:"total_loss_count"`
	TotalCapital     decimal.Decimal    `db:"total_capital" json:"total_capital"`
}

type DealerWeightConfig struct {
	Id              int             `db:"id" json:"id"`
	DayNumber       int             `db:"day_number" json:"day_number"`
	DayProfit       decimal.Decimal `db:"day_profit" json:"day_profit"`
	TotalProfit     decimal.Decimal `db:"total_profit" json:"total_profit"`
	Winning         decimal.Decimal `db:"winning" json:"winning"`
	Traders         decimal.Decimal `db:"traders" json:"traders"`
	TotalProfitRate decimal.Decimal `db:"total_profit_rate" json:"total_profit_rate"`
}

type FollowOrderOpenArgs struct {
	Lang define.ReqLang `json:"lang"`
	//"contract_code": "BTCUSD", // 合约code
	//"hold_type": 1, // 持仓类型 1:全仓 2:逐仓
	//"side": "B", // 方向 B买 S卖
	//"future_price": "10000.01", //预估成交价
	//"pay_money": "37.02", // 购买的金额
	//"leverage": 10 // 杠杠倍数
	NToken       string `json:"ncr"`
	ContractCode string `json:"contract_code"`
	//HoldType     int    `json:"hold_type"` //全仓逐仓
	Side        string `json:"side"`
	FuturePrice string `json:"future_price"`
	Amount      int    `json:"amount"` //下单张数
	//PayMoney     string `json:"pay_money"`
	Leverage int `json:"leverage"` //杠杠
	//IsTrader         bool  `json:"is_trader"`          //是否是带单者,下单身份
	//TraderUid        int64 `json:"trader_uid"`         //跟随交易员用户id
	//FollowPositionId int64 `json:"follow_position_id"` //跟随交易员持仓id
	FollowOrderId int64 `json:"follow_order_id"` //跟随交易员委托id

	OrderType int `json:"order_type"` //订单类型 0: 用户委托 1：计划单 2：止盈单 4：止损单 5：强平单
	//Offset   string          `json:"trade_type"` //offset 开仓平仓
	PlanOrderId  int64           `json:"plan_order_id"`
	RequestId    int64           `json:"request_id"`
	UserId       int64           `json:"user_id"`
	IpAddress    string          `json:"ip_address"`
	DeviceId     string          `json:"device_id"`
	APPID        int             `json:"appid"`
	Limit        decimal.Decimal `json:"limit"` //计划平仓价格
	Stop         decimal.Decimal `json:"stop"`
	EntrustLimit decimal.Decimal `json:"entrust_limit"`                  //止盈限价委托价
	EntrustStop  decimal.Decimal `json:"entrust_stop"`                   //止损限价委托价
	TriggerType  int             `db:"trigger_type" json:"trigger_type"` //止盈止损触发标的 0-成交价,1-标记价格

	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 1-fok 2-ioc 3-maker
	Price           decimal.Decimal `db:"price" json:"price"`                       //委托价格;限价单传递价格
	Mode            int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡

	TriggerEntrustType int             `json:"trigger_entrust_type"` //触发委托类型
	TriggerPrice       decimal.Decimal `json:"trigger_price"`        //条件单触发价
	ClientOrderId      int64           `db:"client_order_id" json:"client_order_id"`
}

type FollowOrderOpenRsp struct {
	Order     *EntrustOrder `json:"order"` //委托订单
	Balance   string        `json:"-"`
	Available string        `json:"available,omitempty"`
}

type FollowOrderCloseArgs struct {
	ReqLang    define.ReqLang `json:"req_lang"`
	NToken     string         `json:"ncr"`
	PositionId int64          `json:"position_id"`

	OrderType         int             `json:"order_type"` //订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单 6-条件平仓 7：带单平仓 8-带单止盈 9-带单止损
	CloseType         int             //0-用户主动平仓，1-跟随交易员平仓
	RequestId         int64           `json:"request_id"`
	UserId            int64           `json:"user_id"`
	IpAddress         string          `json:"ip_address"`
	DeviceId          string          `json:"device_id"`
	APPID             int             `json:"appid"`
	PlanCloseOrderId  int64           `json:"plan_close_order_id"`
	TriggerPrice      decimal.Decimal `json:"trigger_price"`
	TriggerBuyPrice   float64         `json:"trigger_buy_price"`
	TriggerSellPrice  float64         `json:"trigger_sell_price"`
	TriggerForcePrice decimal.Decimal `json:"trigger_force_price"`
	//IsTrader          bool            `json:"is_trader"`     //是否是带单者
	EntrustType     int             `db:"entrust_type" json:"entrust_type"`         //委托类型 0-市价 1-限价
	EntrustStrategy int             `db:"entrust_strategy" json:"entrust_strategy"` //委托策略 1-fok 2-ioc 3-maker
	Price           decimal.Decimal `db:"price" json:"price"`                       //委托价格;限价单传递价格
	Mode            int             `db:"mode" json:"mode"`                         //下单模式 1-对手价 2-最优3挡 3-最优5挡
	IsAdminOp       bool            //是否管理平台操作,后台操作为true
	FollowOrderId   int64           `json:"follow_order_id"` //跟随交易员的委托id
	ForceCloseID    int64           `json:"force_close_id"`

	ClientOrderId int64  `db:"client_order_id" json:"client_order_id"`
	Srv           string `json:"srv"`
}

type FollowFundingRateHistory struct {
	ID           int64           `db:"id" json:"id"`
	UserId       int64           `db:"user_id" json:"user_id"`
	PositionId   int64           `db:"position_id" json:"position_id"`
	Amount       decimal.Decimal `db:"amount" json:"amount"`
	BeforeMargin decimal.Decimal `db:"before_margin" json:"before_margin"`
	Side         string          `db:"side" json:"side"`
	FundingRate  decimal.Decimal `db:"funding_rate" json:"funding_rate"`
	IndexPrice   decimal.Decimal `db:"index_price" json:"index_price"`
	CreateTime   time.Time       `db:"create_time" json:"create_time"`
}

type DealerLever struct {
	BuyLever  int `json:"buy_lever" db:"buy_lever"`
	SellLever int `json:"sell_lever" db:"sell_lever"`
}

type FollowPositionHistory struct {
	ID               int64           `db:"id" json:"id"`                                 // 持仓id
	UserID           int64           `db:"user_id" json:"user_id"`                       // 用户id
	ContractCode     string          `db:"contract_code" json:"contract_code"`           // 合约代码
	Side             string          `db:"side" json:"side"`                             // 方向 B买S卖
	CloseOrderType   int             `db:"close_order_type" json:"close_order_type"`     // 平仓类型
	EntrustType      int             `db:"entrust_type" json:"entrust_type"`             // 委托类型 0-市价 1-限价
	Volume           int             `db:"volume" json:"volume"`                         // 张数
	TraderUID        int64           `db:"trader_uid" json:"trader_uid"`                 // 交易员uid
	FollowPositionID int64           `db:"follow_position_id" json:"follow_position_id"` // 跟随上级订单id
	Lever            int             `db:"lever" json:"lever"`                           // 杠杆倍数
	OpenPrice        decimal.Decimal `db:"open_price" json:"open_price"`                 // 开仓价
	ClosePrice       decimal.Decimal `db:"close_price" json:"close_price"`               // 平仓价
	InitMargin       decimal.Decimal `db:"init_margin" json:"init_margin"`               // 初始保证金
	CloseProfit      decimal.Decimal `db:"close_profit" json:"close_profit"`             // 平仓盈亏
	OpenCommission   decimal.Decimal `db:"open_commission" json:"open_commission"`       // 开仓手续费
	CloseCommission  decimal.Decimal `db:"close_commission" json:"close_commission"`     // 平仓手续费
	FundingAmount    decimal.Decimal `db:"funding_amount" json:"funding_amount"`         // 资金费用
	Withholding      decimal.Decimal `db:"withholding" json:"withholding"`               // 预扣分佣
	OpenTime         time.Time       `db:"open_time" json:"open_time"`                   // 开仓时间
	CloseTime        time.Time       `db:"close_time" json:"close_time"`                 // 平仓时间
	PlatformID       int             `db:"platform_id" json:"platform_id"`
}

type FollowConf struct {
	MinMultiple          decimal.Decimal `db:"min_multiple" json:"min_multiple"`                     // 最小跟单比例(单笔)
	MaxMultiple          decimal.Decimal `db:"max_multiple" json:"max_multiple"`                     // 最大跟单比例(单笔)
	MinNumber            int             `db:"min_number" json:"min_number"`                         // 最小跟单张数(单笔)
	MaxNumber            int             `db:"max_number" json:"max_number"`                         // 最大跟单张数(单笔)
	TraderTotalPosition  int             `db:"trader_total_position" json:"trader_total_position"`   // 交易员总持仓限制
	FollowTotalPosition  int             `db:"follow_total_position" json:"follow_total_position"`   // 跟随着总持仓数
	TraderLatelyPosition int             `db:"trader_lately_position" json:"trader_lately_position"` // 交易员持仓数据显示
	TraderLatelyHistory  int             `db:"trader_lately_history" json:"trader_lately_history"`   // 交易员历史数据条数
	ShowFollowUsers      int             `db:"show_follow_users" json:"show_follow_users"`           // 跟随着盈利从大到小人数
}

type FollowLog struct {
	ID         int64                `db:"id" json:"id"`
	UserID     int64                `db:"user_id" json:"user_id"`         // 当前用户id
	TargetID   int64                `db:"target_id" json:"target_id"`     // 目标用户id
	OpType     define.FollowLogType `db:"op_type" json:"op_type"`         // 操作类型 1-修改带单设置 2-跟随交易员 3-调整跟随设置 4-停止跟随交易员 5-移除跟随者
	OsType     define.OsType        `db:"os_type" json:"os_type"`         // 客户端类型（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	Device     string               `db:"device" json:"device"`           // 设备名称
	DeviceID   string               `db:"device_id" json:"device_id"`     // 设备识别码
	AppVersion string               `db:"app_version" json:"app_version"` // app版本
	IPAddr     string               `db:"ip_addr" json:"ip_addr"`         // ip地址
	Content    jsoniter.RawMessage  `db:"content" json:"content"`         // 内容
	SourceID   int64                `db:"source_id" json:"source_id"`     // 来源id (1.如果是因为调整保证金导致调整跟随设置,该id为资金记录id)
	CreateTime time.Time            `db:"create_time" json:"create_time"` // 创建时间
}
