package proto

import "time"

//notice_id      int auto_increment
//primary key,
//notice_type    int          null comment '1：资产类 2：交易类 3：API状态类',
//notice_content varchar(256) null comment '预警内容',
//email          varchar(64)  null comment '预警email',
//notice_status  int          null comment '1: 成功 0:失败',
//created_by     datetime     not null,
//hedge_account  varchar(16)  not null,
//contract_code  varchar(16)  not null comment '对冲的合约代码'
type WarnNotice struct {
	NoticeType    int       `db:"notice_type"`
	NoticeContent string    `db:"notice_content"`
	CreatedBy     time.Time `db:"created_by"`
	HedgeAccount  string    `db:"hedge_account"`
	ContractCode  string    `db:"contract_code"`
	NoticeStatus  int       `db:"notice_status"` //'1: 成功 0:失败',
}
