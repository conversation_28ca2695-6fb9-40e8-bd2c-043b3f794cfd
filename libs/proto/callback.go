package proto

type TxConfirmation struct {
	TxId           string
	Coin           string
	ConfirmedCount int
	RequiredCount  int
	MhId           int64
	Amount         string
}

// 预充币回调参数
type TryChargeCallbackParam struct {
	To          string // 到账地址
	Currency    string // 币种名称
	Amount      string // 数量
	Tx          string // 网络流水号
	CwsID       string // 交易序列号
	Date        string // 日期
	Digest      string // 签名验证
	Tag         string // 特殊币种标识, 例如eos的memo
	UnConfirmed string // 未确认数量
}

// 充币完成回调参数
type ChargeCallbackParam struct {
	To       string // 到账地址
	Currency string // 币种名称
	Amount   string // 数量
	Tx       string // 网络流水号
	CwsID    string // 交易序列号
	Date     string // 日期
	Digest   string // 签名验证
	Tag      string // 特殊币种标识, 例如eos的memo
	Internal bool   // 是否内部地址
}

// 提币回调参数
type WithdrawCallbackParam struct {
	Tx       string // 网络交易号
	CwsID    string // 网络交易号
	Date     string // 成功时间
	Digest   string // 签名验证
	Internal bool   // 是否内部地址
}
