package coinbase

import (
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"fmt"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

//https://api.pro.coinbase.com
///products/<product-id>/ticker

//https://api.pro.coinbase.com/products/BTC-USD/ticker
//{
//"trade_id": 4729088,
//"price": "333.99",
//"size": "0.193",
//"bid": "333.98",
//"ask": "333.99",
//"volume": "5957.11914015",
//"time": "2015-11-14T20:46:03.511254Z"
//}

type TickerForAPI struct {
	TradeId int64           `json:"trade_id"`
	Price   decimal.Decimal `json:"price"`
	Size    decimal.Decimal `json:"size"`
}

const (
	url  = "https://api.pro.coinbase.com/products/%v/ticker"
	name = "coinbase"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	for s, _ := range codes {
		code := strings.ReplaceAll(s, "USDT", "-USD")
		mk := getTicker(code)
		if mk != nil {
			list = append(list, *mk)
		}
	}
	return
}

func getTicker(arg string) (mk *proto.MarketTrade) {
	u := fmt.Sprintf(url, arg)
	r, err := http.Get(u)
	if err != nil {
		return
	}
	defer r.Body.Close()
	if r.StatusCode != http.StatusOK {
		log.Infof("url:%v,fail code:%v", u, r.StatusCode)
		return
	}

	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	t := new(TickerForAPI)
	err = json.Unmarshal(b, t)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
	}
	code := strings.ReplaceAll(arg, "-USD", "USDT")
	if t.Price.GreaterThan(decimal.Zero) {
		mk = &proto.MarketTrade{
			Symbol:   code,
			DealTime: time.Now().Unix(),
			Price:    t.Price,
			Volume:   t.Size,
			Ts:       time.Now(),
			Source:   "coinbase",
		}
	}
	return
}
