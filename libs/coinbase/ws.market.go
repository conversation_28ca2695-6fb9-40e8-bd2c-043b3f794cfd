/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package coinbase

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"sync"
	"time"

	"bc/libs/log"
	"github.com/gorilla/websocket"
)

const (
	_wsMarket = "wss://ws-feed.pro.coinbase.com"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to huobi fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.lastRv = time.Now()
	c.mc.lastStart = time.Now()
	log.Infof("huobi dial over r1")
	c.con = conn
	c.Receive()
	//订阅
	c.sub(c.mc.topics)
	log.Infof("huobi sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

//func (c *Client) PingLoop() {
//	go func() {
//		for range c.ticker.C {
//			c.lock.Lock()
//			c.con.WriteMessage(websocket.TextMessage, []byte("ping"))
//			c.lock.Unlock()
//		}
//	}()
//}

func (c *Client) sub(topic []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	sub := Sub{
		Type: "subscribe",
	}
	tickers := Channel{
		Name:     "ticker",
		Products: topic,
	}
	herats := Channel{
		Name:     "heartbeat",
		Products: topic,
	}
	sub.Channels = append(sub.Channels, tickers, herats)

	b, _ := json.Marshal(sub)
	log.Infof("sub data:%v", string(b))
	c.Write(b)

}

//func (c *Client) unSub(topic []string) {
//	if c.isClose {
//		return
//	}
//	var subData [][]byte
//	for _, topic := range topic {
//		d := UnSub{UnSub: topic}
//		b, err := json.Marshal(d)
//		if err != nil {
//			fmt.Errorf("marshal fail,%v", err)
//			return
//		}
//		subData = append(subData, b)
//	}
//
//	for _, v := range subData {
//		c.Write(v)
//	}
//
//}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				//c.lock.Lock()
				c.con.WriteMessage(websocket.TextMessage, data)
				//c.lock.Unlock()
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				//tick.Reset(time.Second)
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(msg.Msg))
				//log.Infof("msg:%+v,msgType；%v", string(b), msgType)
				if c.mc.msgHandler != nil {
					c.mc.msgHandler(msg.Msg)
				}
			}

		}

	}()
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string) {
	if c == nil {
		return
	}
	log.Infof("huobi sub topics:%v", topics)
	c.topics = topics
	c.client.sub(c.topics)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.client.Write(pong)
//}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.RLock()
	last := c.lastRv
	c.rvLock.RUnlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	c.client.close()
	time.Sleep(100 * time.Millisecond)
	c.client = NewClient(c)
	c.client.start()
}

//{"type":"subscriptions","channels":[{"name":"ticker","product_ids":["ETH-BTC","ETH-USD","ETH-EUR"]},{"name":"heartbeat","product_ids":["ETH-USD","ETH-EUR"]}]}
type Sub struct {
	Type     string    `json:"type"`
	Channels []Channel `json:"channels"`
}

type Channel struct {
	Name     string   `json:"name"`
	Products []string `json:"product_ids"`
}

type Ticker struct {
	Type     string          `json:"type"`
	Seq      int64           `json:"sequence"`
	Code     string          `json:"product_id"`
	Price    decimal.Decimal `json:"price"`
	Volume24 decimal.Decimal `json:"volume_24h"`
	Side     string          `json:"side"`
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}
