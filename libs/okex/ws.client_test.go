/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package okex

import (
	"bc/libs/compress"
	"bc/libs/convert"
	"bc/libs/json"
	"bc/libs/log"
	"fmt"
	"io/ioutil"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://ws.okx.com:8443/ws/v5/public", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))

	go func() {
		for {
			w.SetPingHandler(func(appData string) error {
				//te.Log(appData)
				w.WriteMessage(websocket.PongMessage, nil)
				return nil
			})
			t, b, err := w.Read<PERSON>essage()
			//d, err := zip.UnzipByte(b)
			//if err != nil {
			//	te.Log("err",err)
			//}
			//te.Log(t, string(b), err)
			d, err := compress.FlateUnCompress(b)
			if err != nil {
				log.Errorf("e:%v", err)
			}
			//fmt.Printf(string(d))
			te.Log(t, string(d), err)
		}
	}()

	go func() {
		str := `{"op": "subscribe", "args": ["swap/depth:BTC-USD-SWAP"]}`
		w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))

		//time.Sleep(1 * time.Minute)
		//t = model.SubSymbolArg{ContractCode: "eth/usdt"}
		//goto RE
	}()
	select {}

}

func TestBClient(t *testing.T) {
	log.InitLogger("log", "info", false)
	c := NewMarketWsClient(&Config{
		WsPoint: _wsMarket,
		dialer:  nil,
	}, func(data []byte) {
		//t.Logf("d:%v", convert.Bytes2Str(data))
		//unmarsha data
		//t.Logf("undata:%v",string(unData))
		p := new(OkexBasicRsp)
		err := json.Unmarshal(data, p)
		if err != nil {
			t.Logf("unmarsha fail,%v", err)
			return
		}
		if p.Action == ActionPartial {
			t.Logf("d:%v", convert.Bytes2Str(data))
		}
		//t.Logf("t data:%v",string(p.Data))

	})
	c.Subscript([]Arg{{
		Channel: "books",
		InstId:  "BTC-USDT-SWAP",
	}})
	//c.Subscript([]string{"swap/depth:BTC-USDT-SWAP","swap/funding_rate:BTC-USDT-SWAP"})
	c.Start()

	tick := time.Tick(20 * time.Second)
	for range tick {
		c.Subscript([]Arg{{
			Channel: "books",
			InstId:  "BTC-USDT-SWAP",
		}})
	}
	select {}
}
