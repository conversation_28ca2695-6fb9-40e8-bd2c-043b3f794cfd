package zb

import (
	"bc/libs/proto"
	"encoding/json"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

//http://api.zb.center/data/v1/allTicker

//{"vsysqc":{"high":"0.0884","vol":"1655036.9","last":"0.0875","low":"0.0835","buy":"0.0864","sell":"0.0894","open":"0.0876","riseRate":"-0.11"}}
type Ticker struct {
	Price decimal.Decimal `json:"last"`
}

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get("http://api.zb.center/data/v1/allTicker")
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		//log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Println(string(b))
	var tickers = make(map[string]Ticker)
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	//fmt.Println(tickers)
	if len(tickers) == 0 {
		return
	}
	for k, v := range tickers {
		code := strings.ToUpper(k)
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    v.Price,
				Side:     "",
				Ts:       time.Now(),
			}
			list = append(list, mk)
		}
	}
	return
}
