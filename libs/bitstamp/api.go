package bitstamp

import (
	"bc/libs/log"
	"bc/libs/proto"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

//https://www.bitstamp.net/api/v2/ticker/BTCUSD/

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	for s := range codes {
		code := strings.ReplaceAll(s, "USDT", "USD")
		code = strings.ToLower(code)
		mk := getTicker(code)
		if mk != nil {
			list = append(list, *mk)
		}
	}
	return
}

type Ticker struct {
	Price  decimal.Decimal `json:"last"`
	Volume decimal.Decimal `json:"volume"`
}

//{"high": "36639.23", "last": "33935.85", "timestamp": "1610522069", "bid": "33901.97", "vwap": "34284.85", "volume": "17998.65062008", "low": "32327.76", "ask": "33940.18", "open": "34035.02"}
func getTicker(arg string) (mk *proto.MarketTrade) {
	u := fmt.Sprintf("https://www.bitstamp.net/api/v2/ticker/%s/", arg)
	//fmt.Println(u)
	r, err := http.Get(u)
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		log.Infof("web；%v,http fail,code；%v", u, r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	fmt.Printf("%v", string(b))
	var ticker Ticker
	err = json.Unmarshal(b, &ticker)
	if err != nil {
		return
	}
	log.Infof("ticker:%+v", ticker)
	if ticker.Price.LessThanOrEqual(decimal.Zero) {
		return
	}
	code := strings.ReplaceAll(arg, "usd", "usdt")
	code = strings.ToUpper(code)
	mk = &proto.MarketTrade{
		Symbol:   code,
		DealTime: time.Now().Unix(),
		Price:    ticker.Price,
		Volume:   ticker.Volume,
		Ts:       time.Now(),
	}
	return
}
