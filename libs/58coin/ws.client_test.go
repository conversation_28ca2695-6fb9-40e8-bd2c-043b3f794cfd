/*
@Time : 2019-12-17 19:06
<AUTHOR> mocha
@File : client_test
*/
package fiveCoin

import (
	"bc/libs/compress"
	"bc/libs/convert"
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"io/ioutil"
	"testing"
)

func TestClient(te *testing.T) {
	w, rsp, err := websocket.DefaultDialer.Dial("wss://openws.58ex.com/v1/stream?streams=1001@depth", nil)
	if err != nil {
		fmt.Println(err)
	}
	b, err := ioutil.ReadAll(rsp.Body)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(convert.Bytes2Str(b))
	//	t := time.Tick(5 * time.Second)
	//	str := `{"op": "subscribe", "args": ["trade"]}`
	//
	//	w.WriteMessage(websocket.TextMessage, convert.Str2Bytes(str))
	//	go func() {
	//		for {
	//			select {
	//			case <-t:
	//				//s := Sub{
	//				//	Sub: "market.BTCUSDT.basis.1min.open",
	//				//	//Sub: "market.BTC-USDT.trade.detail",
	//				//	ID: "BTCUSDT",
	//				//				//}
	//				//				s := `{
	//				//  "sub": "market.BTC-USDT.detail",
	//				//  "id": "id6"
	//				// }
	//				//`
	//				s := `{
	//    "sub": "market.BTC-USDT.depth.step0",
	//    "id": "id5"
	//    }
	//
	//`
	//				//b, _ := json.Marshal(s)
	//				//str1 := `{"op": "unsubscribe", "args": ["funding"]}`
	//				w.WriteMessage(websocket.TextMessage, []byte(s))
	//
	//			}
	//		}
	//	}()
	//
	go func() {
		for {
			t, b, err := w.ReadMessage()
			//fmt.Println(string(b))
			//d, _ := zip.UnzipByte(b)
			b, _ = compress.FlateUnCompress(b)
			ping := new(Event)
			_ = json.Unmarshal(b, ping)
			if ping.Event == "ping" {
				ping.Event = "pong"
				s, _ := json.Marshal(ping)
				w.WriteMessage(websocket.TextMessage, s)
				continue
			}
			te.Log(t, convert.Bytes2Str(b), err)
			//if ping.Ping != 0 {
			//	te.Logf("ping:%+v", *ping)
			//	pong, _ := json.Marshapingl(&Pong{Pong: compress.GetUinxMillisecond()})
			//	w.WriteMessage(websocket.TextMessage, pong)
			//} else {
			//}

		}
	}()

	select {}

}

type Event struct {
	Event string `json:"event"`
	Ts    int64  `json:"ts"`
}
