package mdb

import (
	"bc/libs/define"
	"bc/libs/log"
	"github.com/jmoiron/sqlx"
	"go.uber.org/zap"
)

func Commit(tx *sqlx.Tx, err *error, reqID int64, f func(id int64, e error)) {
	if tx == nil {
		return
	}
	if err == nil {
		_ = tx.Rollback()
		return
	}

	// 判断是否有panic,如果有panic,赋值err,使事务执行回滚流程而不是提交
	if e := recover(); e != nil {
		log.Error("db.CommitTx recover",
			zap.Int64("reqID", reqID),
			zap.Any("err", e),
			zap.Stack("stackTree"))
		if *err == nil {
			*err = define.ErrMsgBusy
		}
	}

	if *err == nil {
		*err = tx.Commit()
		if *err != nil {
			log.Error("db Commit failed",
				zap.Int64("reqID", reqID),
				zap.Error(*err))
		}
	} else {
		_ = tx.Rollback()
	}

	if f != nil {
		f(reqID, *err)
	}
}

func CloseRows(rows *sqlx.Rows) {
	_ = rows.Close()
}

func CloseStmt(stmt *sqlx.Stmt) {
	_ = stmt.Close()
}
