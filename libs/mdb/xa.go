package mdb

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/jmoiron/sqlx"
)

type XaRecoverData struct {
	FormatID    uint   `db:"formatID"`     // 格式化类型
	GtridLength uint   `db:"gtrid_length"` // gtrid 部分长度
	BqualLength uint   `db:"bqual_length"` // bqual 部分长度
	Data        string `db:"data"`         // xa 事务id
}

type XaClient struct {
	gid  string
	conn *sqlx.DB
}

// GetXaClient 需要使用独立的数据库连接
func GetXaClient(gid, dsn string) (*XaClient, error) {
	db, err := sqlx.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}
	return &XaClient{gid: gid, conn: db}, err
}

// Close 释放xa事务获取的数据库连接
func (xc *XaClient) Close(ctx context.Context, rollback bool) {
	if rollback {
		xc.rollbackForErr(ctx)
	}
	_ = xc.conn.Close()
	return
}

// Start 开启一个xa事务
func (xc *XaClient) Start(ctx context.Context) error {
	_, err := xc.conn.ExecContext(ctx, fmt.Sprintf("XA START '%s';", xc.gid))
	return err
}

// QueryxContext 执行一条查询数据语句
func (xc *XaClient) QueryxContext(ctx context.Context, query string, args ...any) (*sqlx.Rows, error) {
	return xc.conn.QueryxContext(ctx, query, args...)
}

// QueryRowxContext 执行一条查询单条数据语句
func (xc *XaClient) QueryRowxContext(ctx context.Context, query string, args ...any) *sqlx.Row {
	return xc.conn.QueryRowxContext(ctx, query, args...)
}

// SelectContext 执行一条查询数据并赋值语句
func (xc *XaClient) SelectContext(ctx context.Context, dest any, query string, args ...any) error {
	return xc.conn.SelectContext(ctx, dest, query, args...)
}

// GetContext 执行一条查询单条数据并赋值语句
func (xc *XaClient) GetContext(ctx context.Context, dest any, query string, args ...any) error {
	return xc.conn.GetContext(ctx, dest, query, args...)
}

// ExecContext 执行一条sql语句
func (xc *XaClient) ExecContext(ctx context.Context, query string, args ...any) (sql.Result, error) {
	return xc.conn.ExecContext(ctx, query, args...)
}

// NamedExecContext 执行一条sql语句
func (xc *XaClient) NamedExecContext(ctx context.Context, query string, obj any) (sql.Result, error) {
	return xc.conn.NamedExecContext(ctx, query, obj)
}

// End 将xa事务标记为停止输入业务逻辑状态
func (xc *XaClient) End(ctx context.Context) error {
	_, err := xc.conn.ExecContext(ctx, fmt.Sprintf("XA END '%s';", xc.gid))
	return err
}

// Prepare 将xa事务标记为预提交状态
func (xc *XaClient) Prepare(ctx context.Context) error {
	err := xc.End(ctx)
	if err != nil {
		return err
	}
	_, err = xc.conn.ExecContext(ctx, fmt.Sprintf("XA PREPARE '%s';", xc.gid))
	return err
}

// Commit 对xa事务进行提交
func (xc *XaClient) Commit(ctx context.Context) error {
	_, err := xc.conn.ExecContext(ctx, fmt.Sprintf("XA COMMIT '%s';", xc.gid))
	return err
}

// Rollback 对xa事务进行回滚
func (xc *XaClient) Rollback(ctx context.Context) error {
	_, err := xc.conn.ExecContext(ctx, fmt.Sprintf("XA ROLLBACK '%s';", xc.gid))
	return err
}

// rollbackForErr 针对错误进行回滚
// 首先需要保证当前事务处于XA_IDLE或之后的状态
// 否则通过 'XA END' 命令将XA事务状态从XA_ACTIVE置为XA_IDLE
func (xc *XaClient) rollbackForErr(ctx context.Context) {
	_ = xc.End(ctx)
	_ = xc.Rollback(ctx)
}
