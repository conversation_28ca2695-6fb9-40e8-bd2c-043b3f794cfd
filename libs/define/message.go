package define

import (
	"bytes"
	"errors"
	"strings"
	"text/template"
)

const (
	MessageTypeOrderSuccess       = 1  //平仓成功
	MessageTypeOrderFail          = 2  //开仓失败
	MessageTypeCloseSuccess       = 3  //平仓成功
	MessageTypeCloseFail          = 4  //平仓失败
	MessageTypeBlowingOut         = 5  //爆仓
	MessageTypeStopFollow         = 6  //停止跟随
	MessageTypeProfit             = 7  //获取返佣
	MessageTypeTraderAuditSuccess = 8  //审核成功
	MessageTypeTraderAuditFail    = 9  //审核失败
	MessageTypeTraderStopValid    = 10 //身份停用
	MessageTypeTraderCancel       = 11 //身份撤销
)

const (
	MessageTitleOrderSuccess = iota
	MessageTitleOrderFail
	MessageTitleCloseSuccess
	MessageTitleCloseFail
	MessageTitleStopFollow
	MessageTitleBlowingOut
	MessageTitleTraderAuditSuccess
	MessageTitleTraderAuditFail
	MessageTitleTraderStopValid
	MessageTitleTraderCancel
	MessageTitleProfit
	MessageContentOrderSuccess
	MessageContentOrderFail
	MessageContentCloseSuccess
	MessageContentCloseFail
	MessageContentStopFollow
	MessageContentBlowingOut
	MessageContentTraderAuditSuccess
	MessageContentTraderAuditFail
	MessageContentTraderStopValid
	MessageContentTraderCancel
	MessageContentProfit

	ContentPartOrderBuy
	ContentPartOrderSell
	ContentPartCloseBuy
	ContentPartCloseSell

	ContentPartPosBuy
	ContentPartPosSell

	WarningRisk
	ForcePosition
)

var MsgLanuageMap = map[ReqLang]map[int]string{
	ReqLangCN: {
		MessageTitleOrderSuccess:         "开仓成功",
		MessageTitleOrderFail:            "开仓失败",
		MessageTitleCloseSuccess:         "平仓成功",
		MessageTitleCloseFail:            "平仓失败",
		MessageTitleStopFollow:           "停止跟随",
		MessageTitleBlowingOut:           "爆仓",
		MessageTitleTraderAuditSuccess:   "交易员审核通过",
		MessageTitleTraderAuditFail:      "交易员审核失败",
		MessageTitleTraderStopValid:      "交易员身份被停用",
		MessageTitleTraderCancel:         "交易员身份被撤销",
		MessageTitleProfit:               "获得分佣",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}}倍杠杆，开仓价{{.Price}}，成交{{.Volume}}张。",
		MessageContentOrderFail:          "抱歉，由于您跟单账户中余额/授权交易员资金不足，本次{{.Code}}合约开仓失败。",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}}倍杠杆，平仓价{{.Price}}，成交{{.Volume}}张，实现盈亏{{.Profit}}USDT。",
		MessageContentCloseFail:          "抱歉，由于您跟单账户中余额/授权交易员资金不足，本次{{.Code}}合约开仓失败。",
		MessageContentStopFollow:         "抱歉，交易员已取消您的跟随交易，之后的开仓交易您将无法跟随该交易员开仓。",
		MessageContentBlowingOut:         "您的{{.Code}}{{.Side}}{{.Lever}}倍杠杆已爆仓平多，爆仓价{{.Price}}，成交{{.Volume}}张，实现盈亏{{.Profit}}USDT。",
		MessageContentTraderAuditSuccess: "恭喜您，已成为{{.Website}}的交易员。每笔带单可获得跟单用户利润{{.Rebate}}%分佣",
		MessageContentTraderAuditFail:    "很抱歉，您提交的交易员申请审核失败。如有疑问，请联系官方客服。",
		MessageContentTraderStopValid:    "很抱歉，您的交易员身份已被停用。如有疑问，请联系官方客服。",
		MessageContentTraderCancel:       "很抱歉，您的交易员身份已被撤销。如有疑问，请联系官方客服。",
		MessageContentProfit:             "您已收到{{.Date}}的分佣，共收到分佣{{.Profit}}USDT。",
		ContentPartOrderBuy:              "开多",
		ContentPartOrderSell:             "开空",
		ContentPartCloseBuy:              "平多",
		ContentPartCloseSell:             "平空",
		ContentPartPosBuy:                "多仓",
		ContentPartPosSell:               "空仓",
		WarningRisk:                      "您的合约账户风险率已达预警线。请您控制仓位风险，自行调整保证金或者减仓。",
		ForcePosition:                    "您的合约账户风险率已达强平线，请关注账户资金变动情况，做好风险防控。",
	},
	ReqLangEN: {
		MessageTitleOrderSuccess:         "Opening Success",
		MessageTitleOrderFail:            "Opening Failed",
		MessageTitleCloseSuccess:         "Closing Success",
		MessageTitleCloseFail:            "Closing Failed",
		MessageTitleStopFollow:           "Stop Following",
		MessageTitleBlowingOut:           "Forced Full Liquidation",
		MessageTitleTraderAuditSuccess:   "Trader approved",
		MessageTitleTraderAuditFail:      "Trader audit failed",
		MessageTitleTraderStopValid:      "Trader status is disabled",
		MessageTitleTraderCancel:         "Trader status is revoked",
		MessageTitleProfit:               "Get Commission",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}}Leverage，Opening Price{{.Price}}，Opening{{.Volume}}Cont.",
		MessageContentOrderFail:          "Sorry, the {{.Code}} futures opening failed due to insufficient balance in your copy account or authorized trader's funds.",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}}Leverage, Closing Price{{.Price}}, Closing{{.Volume}}Cont, P/L{{.Profit}}USDT。",
		MessageContentCloseFail:          "Sorry, the {{.Code}} futures opening failed due to insufficient balance in your copy account or authorized trader's funds.",
		MessageContentStopFollow:         "Sorry, the trader has cancelled your copying trading, and you will not be able to follow the trader to open positions in the future.",
		MessageContentBlowingOut:         "Your{{.Code}}{{.Side}}{{.Lever}}has been closed due to forced full liquidation，price{{.Price}}, closing{{.Volume}}cont，P/L{{.Profit}}USDT。",
		MessageContentTraderAuditSuccess: "Congratulations, you have become a trader of {{.Website}}. Each order can get {{.Rebate}}% commission",
		MessageContentTraderAuditFail:    "Sorry, the trader application you submitted failed to be reviewed. If you have any questions, please contact the official customer service.",
		MessageContentTraderStopValid:    "Sorry, your trader status has been disabled. If you have any questions, please contact the official customer service.",
		MessageContentTraderCancel:       "Sorry, your trader status has been revoked. If you have any questions, please contact the official customer service.",
		MessageContentProfit:             "You have received commission of {{.Date}} , a total of {{.Profit}} USDT has been received.",
		ContentPartOrderBuy:              "Open Long",
		ContentPartOrderSell:             "Open Short",
		ContentPartCloseBuy:              "Close Long",
		ContentPartCloseSell:             "Close Short",
		ContentPartPosBuy:                "Long Positon",
		ContentPartPosSell:               "Short Position",
		WarningRisk:                      "Your contract account risk rate has reached the warning line. Please control the risk of your position, adjust your margin or lighten your position.",
		ForcePosition:                    "The risk rate of your contract account has reached a strong level. Please pay attention to the changes in account funds to prevent and control risks.",
	},
	ReqLangTC: {
		MessageTitleOrderSuccess:         "開倉成功",
		MessageTitleOrderFail:            "開倉失敗",
		MessageTitleCloseSuccess:         "平倉成功",
		MessageTitleCloseFail:            "平倉失敗",
		MessageTitleStopFollow:           "停止跟隨",
		MessageTitleBlowingOut:           "爆倉",
		MessageTitleTraderAuditSuccess:   "交易員審核通過",
		MessageTitleTraderAuditFail:      "交易員審核失敗",
		MessageTitleTraderStopValid:      "交易員身份被停用",
		MessageTitleTraderCancel:         "交易員身份被撤銷",
		MessageTitleProfit:               "獲得分傭",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}}倍杠杆，開倉價{{.Price}}，成交{{.Volume}}張。",
		MessageContentOrderFail:          "抱歉，由於您跟單帳戶中餘額/授權交易員資金不足，本次{{.Code}}合約開倉失敗。",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}}倍杠杆，平倉價{{.Price}}，成交{{.Volume}}張，實現盈虧{{.Profit}}USDT。",
		MessageContentCloseFail:          "抱歉，由於您跟單帳戶中餘額/授權交易員資金不足，本次{{.Code}}合約開倉失敗。",
		MessageContentStopFollow:         "抱歉，交易員已取消您的跟隨交易，之後的開倉交易您將無法跟隨該交易員開倉。",
		MessageContentBlowingOut:         "您的{{.Code}}{{.Side}}{{.Lever}}倍杠杆已爆倉平多，爆倉價{{.Price}}，成交{{.Volume}}張，實現盈虧{{.Profit}}USDT。",
		MessageContentTraderAuditSuccess: "恭喜您，已成為{{.Website}}的交易員。每筆帶單可獲得跟單用戶利潤{{.Rebate}}%分傭",
		MessageContentTraderAuditFail:    "很抱歉，您提交的交易員申請審核失敗。如有疑問，請聯繫官方客服。",
		MessageContentTraderStopValid:    "很抱歉，您的交易員身份已被停用。如有疑問，請聯繫官方客服。",
		MessageContentTraderCancel:       "很抱歉，您的交易員身份已被撤銷。如有疑問，請聯繫官方客服。",
		MessageContentProfit:             "您已收到{{.Date}}的分傭，共收到分傭{{.Profit}}USDT。",
		ContentPartOrderBuy:              "開多",
		ContentPartOrderSell:             "開空",
		ContentPartCloseBuy:              "平多",
		ContentPartCloseSell:             "平空",
		ContentPartPosBuy:                "多倉",
		ContentPartPosSell:               "空倉",
		WarningRisk:                      "您的合约账户风险率已达预警线。请您控制仓位风险，自行调整保证金或者减仓。",
		ForcePosition:                    "您的合约账户风险率已达强平线，请关注账户资金变动情况，做好风险防控。",
	},
	ReqLangKR: {
		MessageTitleOrderSuccess:         "성공적으로 열렸습니다.",
		MessageTitleOrderFail:            "위치를 열지 못했습니다.",
		MessageTitleCloseSuccess:         "클로즈드 포지션 성공",
		MessageTitleCloseFail:            "클로즈드 포지션 실패",
		MessageTitleStopFollow:           "팔로우 중지",
		MessageTitleBlowingOut:           "변제",
		MessageTitleTraderAuditSuccess:   "거래자 승인",
		MessageTitleTraderAuditFail:      "거래자 검토 실패",
		MessageTitleTraderStopValid:      "거래자 상태가 비활성화되었습니다.",
		MessageTitleTraderCancel:         "거래자 상태가 취소됨",
		MessageTitleProfit:               "커미션을 받다",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}} 배 레버리지,개점가 {{.Price}},{{.Volume}}개 판매",
		MessageContentOrderFail:          "죄송합니다. 귀하의 사본 계정에 승인된 거래자의 자금이 부족하여 이번에 {{.Code}} 계약 개설에 실패했습니다.",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}} 배 레버리지，종가 {{.Price}},{{.Volume}}개 판매,{{.Profit}} USDT의 손익 실현.",
		MessageContentCloseFail:          "죄송합니다. {{.Code}} 계약은 귀하의 사본 계정에 승인된 거래자의 자금이 부족하여 이번에는 포지션을 열지 못했습니다.",
		MessageContentStopFollow:         "죄송합니다. 거래자가 팔로우 거래를 취소했으며 그 이후에는 거래자를 팔로우하여 거래를 열 수 없습니다.",
		MessageContentBlowingOut:         "귀하의 {{.Code}}{{.Side}}{{.Lever}}배 레버리지가 종료되었습니다,청산가격 {{.Price}}，{{.Volume}}개 판매,{{.Profit}} USDT의 손익 실현.",
		MessageContentTraderAuditSuccess: "축하합니다. {{.Website}}로 거래자가 되셨습니다. 각 주문에 대해 복사 사용자의 이익에 대해 {{.Rebate}}% 커미션을 받을 수 있습니다. ",
		MessageContentTraderAuditFail:    "죄송합니다. 제출하신 거래자 신청서를 검토하지 못했습니다. 문의사항이 있으시면 공식 고객센터로 연락주세요.",
		MessageContentTraderStopValid:    "죄송합니다. 거래자 상태가 비활성화되었습니다. 문의 사항이 있으시면 공식 고객 서비스 센터로 문의하십시오.",
		MessageContentTraderCancel:       "죄송합니다. 귀하의 거래자 상태가 취소되었습니다. 문의 사항이 있으시면 공식 고객 서비스 센터로 문의하십시오.",
		MessageContentProfit:             "{{.Date}}로부터 커미션을 받았습니다,총 {{.Profit}} USDT를 받았습니다.",
		ContentPartOrderBuy:              "더 열어",
		ContentPartOrderSell:             "열려있는",
		ContentPartCloseBuy:              "얼룩말",
		ContentPartCloseSell:             "플랫 아웃",
		ContentPartPosBuy:                "롱 포지션",
		ContentPartPosSell:               "숏 포지션",
		WarningRisk:                      "귀하의 계약 계정 위험 비율이 경고 라인에 도달했습니다. 포지션 위험을 통제하거나 마진을 조정하거나 포지션을 직접 줄이십시오.",
		ForcePosition:                    "귀하의 계약 계정의 위험 비율이 강제 수준에 도달했습니다. 계정 자금의 변동에주의를 기울이고 위험 예방 및 통제를 잘하십시오.",
	},
	ReqLangVN: {
		MessageTitleOrderSuccess:         "Đã mở thành công",
		MessageTitleOrderFail:            "Không mở được vị trí",
		MessageTitleCloseSuccess:         "Đã đóng vị trí thành công",
		MessageTitleCloseFail:            "Đã đóng vị trí không thành công",
		MessageTitleStopFollow:           "ngừng theo dõi",
		MessageTitleBlowingOut:           "thanh toán",
		MessageTitleTraderAuditSuccess:   "Nhà giao dịch được chấp thuận",
		MessageTitleTraderAuditFail:      "Đánh giá nhà giao dịch không thành công",
		MessageTitleTraderStopValid:      "Trạng thái nhà giao dịch bị vô hiệu hóa",
		MessageTitleTraderCancel:         "Đã thu hồi trạng thái nhà giao dịch",
		MessageTitleProfit:               "nhận hoa hồng",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}}x đòn bẩy，Giá mở cửa là {{.Price}}，{{.Volume}} đã bán.",
		MessageContentOrderFail:          "Xin lỗi, nhà giao dịch được ủy quyền không có đủ tiền do số dư trong tài khoản sao chép của bạn，Lần này, việc mở hợp đồng {{.Code}} không thành công.",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}}x đòn bẩy，Giá đóng cửa là {{.Price}}，{{.Volume}} đã bán，Nhận ra lãi và lỗ của {{.Profit}} USDT。",
		MessageContentCloseFail:          "Xin lỗi, nhà giao dịch được ủy quyền không có đủ tiền do số dư trong tài khoản sao chép của bạn，Lần này, việc mở khế ước của {{.Code}} không thành công.",
		MessageContentStopFollow:         "Xin lỗi, nhà giao dịch đã hủy giao dịch theo dõi của bạn và bạn sẽ không thể theo dõi nhà giao dịch để mở giao dịch sau đó.",
		MessageContentBlowingOut:         "Đòn bẩy {{.Code}}{{.Side}}{{.Lever}} của bạn đã bị đóng，Giá thanh lý {{.Price}}，{{.Volume}} đã bán，Nhận ra lãi và lỗ của {{.Profit}} USDT.",
		MessageContentTraderAuditSuccess: "Xin chúc mừng, bạn đã trở thành một nhà giao dịch {{.Website}}.Đối với mỗi đơn đặt hàng, bạn có thể nhận được {{.Rebate}}% hoa hồng cho lợi nhuận của người sử dụng bản sao",
		MessageContentTraderAuditFail:    "Xin lỗi, đơn đăng ký thương nhân bạn đã gửi không được xem xét. Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận chăm sóc khách hàng chính thức.",
		MessageContentTraderStopValid:    "Xin lỗi, trạng thái giao dịch của bạn đã bị vô hiệu hóa. Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận chăm sóc khách hàng chính thức.",
		MessageContentTraderCancel:       "Xin lỗi, trạng thái giao dịch viên của bạn đã bị thu hồi. Nếu bạn có bất kỳ câu hỏi nào, vui lòng liên hệ với bộ phận chăm sóc khách hàng chính thức.",
		MessageContentProfit:             "Bạn đã nhận được hoa hồng của {{.Date}}，Đã nhận tổng cộng {{.Profit}} USDT tiền hoa hồng",
		ContentPartOrderBuy:              "Mở thêm",
		ContentPartOrderSell:             "mở",
		ContentPartCloseBuy:              "pinto",
		ContentPartCloseSell:             "Trải rộng",
		ContentPartPosBuy:                "vị trí lâu dài",
		ContentPartPosSell:               "vị trí ngắn",
		WarningRisk:                      "Tỷ lệ rủi ro tài khoản hợp đồng của bạn đã đạt đến mức cảnh báo. Vui lòng kiểm soát rủi ro vị thế, tự điều chỉnh mức ký quỹ hoặc giảm vị thế.",
		ForcePosition:                    "Tỷ lệ rủi ro trong tài khoản hợp đồng của bạn đã đến mức bắt buộc. Hãy chú ý đến những thay đổi trong quỹ tài khoản và làm tốt công tác phòng ngừa và kiểm soát rủi ro.",
	},
	ReqLangID: {
		MessageTitleOrderSuccess:         "Berhasil dibuka",
		MessageTitleOrderFail:            "Gagal membuka posisi",
		MessageTitleCloseSuccess:         "Posisi ditutup berhasil",
		MessageTitleCloseFail:            "Posisi tertutup gagal",
		MessageTitleStopFollow:           "berhenti mengikuti",
		MessageTitleBlowingOut:           "likuidasi",
		MessageTitleTraderAuditSuccess:   "Trader disetujui",
		MessageTitleTraderAuditFail:      "Tinjauan pedagang gagal",
		MessageTitleTraderStopValid:      "Status pedagang dinonaktifkan",
		MessageTitleTraderCancel:         "Status pedagang dicabut",
		MessageTitleProfit:               "dapatkan komisi",
		MessageContentOrderSuccess:       "{{.Code}}{{.Side}}{{.Lever}} kali leverage, harga pembukaannya adalah {{.Price}}, dan {{.Volume}} kontrak diperdagangkan.",
		MessageContentOrderFail:          "Maaf, pembukaan kontrak {{.Code}} kali ini gagal karena tidak cukupnya dana dari pedagang resmi di akun salinan Anda.",
		MessageContentCloseSuccess:       "{{.Code}}{{.Side}}{{.Lever}} kali leverage, Harga penutupan {{.Price}}, {{.Volume}} terjual, Laba dan rugi yang direalisasi sebesar {{.Profit}} USDT.",
		MessageContentCloseFail:          "Maaf, kontrak {{.Code}} gagal membuka posisi kali ini karena tidak cukup dana dari pedagang resmi di akun salinan Anda.",
		MessageContentStopFollow:         "Maaf, pedagang telah membatalkan perdagangan ikuti Anda, dan Anda tidak akan dapat mengikuti pedagang untuk membuka perdagangan setelah itu.",
		MessageContentBlowingOut:         "您的{{.Code}}{{.Side}}{{.Lever}} kali leverage telah dilikuidasi, Harga likuidasi {{.Price}}, {{.Volume}} terjual, Realisasi untung dan rugi sebesar {{.Profit}} USDT.",
		MessageContentTraderAuditSuccess: "Selamat, Anda telah menjadi pedagang {{.Website}}. Untuk setiap pesanan, Anda bisa mendapatkan {{.Rebate}}% dari keuntungan pengguna salinan",
		MessageContentTraderAuditFail:    "Maaf, aplikasi trader yang Anda kirimkan gagal ditinjau. Jika Anda memiliki pertanyaan, silakan hubungi layanan pelanggan resmi.",
		MessageContentTraderStopValid:    "Maaf, status trader Anda telah dinonaktifkan. Jika Anda memiliki pertanyaan, silakan hubungi layanan pelanggan resmi.",
		MessageContentTraderCancel:       "Maaf, status trader Anda telah dicabut. Jika Anda memiliki pertanyaan, silakan hubungi layanan pelanggan resmi.",
		MessageContentProfit:             "Anda telah menerima komisi pada {{.Date}}, Total komisi {{.Profit}} USDT telah diterima.",
		ContentPartOrderBuy:              "buka lebih banyak",
		ContentPartOrderSell:             "membuka",
		ContentPartCloseBuy:              "pinto",
		ContentPartCloseSell:             "Segera",
		ContentPartPosBuy:                "posisi panjang",
		ContentPartPosSell:               "posisi pendek",
		WarningRisk:                      "Tingkat risiko akun kontrak Anda telah mencapai garis peringatan. Silakan kendalikan risiko posisi, sesuaikan margin atau kurangi posisi sendiri.",
		ForcePosition:                    "Rasio risiko akun kontrak Anda telah mencapai tingkat yang dipaksakan. Harap perhatikan perubahan dana akun dan lakukan pekerjaan yang baik dalam pencegahan dan pengendalian risiko.",
	},
	ReqLangRU: {
		MessageTitleOrderSuccess:         "Успешно открыто",
		MessageTitleOrderFail:            "Не удалось открыть позицию",
		MessageTitleCloseSuccess:         "Закрыта позиция успешно",
		MessageTitleCloseFail:            "Не удалось закрыть позицию",
		MessageTitleStopFollow:           "прекратить следить",
		MessageTitleBlowingOut:           "ликвидация",
		MessageTitleTraderAuditSuccess:   "Трейдер одобрен",
		MessageTitleTraderAuditFail:      "Ошибка проверки трейдера",
		MessageTitleTraderStopValid:      "Статус трейдера отключен",
		MessageTitleTraderCancel:         "Статус трейдера отозван",
		MessageTitleProfit:               "получить комиссию",
		MessageContentOrderSuccess:       "{{.Code}} {{.Side}} {{.Lever}}-кратное кредитное плечо, цена открытия {{.Price}}, торгуется {{.Volume}} контрактов.",
		MessageContentOrderFail:          "К сожалению, в этот раз контракт {{.Code}} не смог открыть позицию из-за недостаточности средств авторизованного трейдера на вашем копируемом счете.",
		MessageContentCloseSuccess:       "{{.Code}} {{.Side}} {{.Lever}}-кратное кредитное плечо, цена закрытия {{.Price}}, торгуется {{.Volume}} контрактов, а прибыль и убыток составляют {{.Profit}}USDT.",
		MessageContentCloseFail:          "К сожалению, в этот раз контракт {{.Code}} не смог открыть позицию из-за недостаточности средств авторизованного трейдера на вашем копируемом счете.",
		MessageContentStopFollow:         "Извините, трейдер отменил вашу отслеживающую сделку, и после этого вы не сможете подписаться на трейдера, чтобы открывать сделки.",
		MessageContentBlowingOut:         "Ваше кредитное плечо {{.Code}} {{.Side}} {{.Lever}}x ликвидировано，Цена ликвидации составила {{.Price}}, было продано {{.Volume}} контрактов, прибыль и убыток составили {{.Profit}} USDT.",
		MessageContentTraderAuditSuccess: "Поздравляем, вы стали трейдером {{.Website}}. За каждый заказ можно получить комиссию {{.Rebate}}% от прибыли пользователя копии",
		MessageContentTraderAuditFail:    "К сожалению, отправленная вами заявка трейдера не была рассмотрена. Если у вас есть какие-либо вопросы, пожалуйста, свяжитесь с официальной службой поддержки клиентов.",
		MessageContentTraderStopValid:    "К сожалению, ваш статус трейдера деактивирован. Если у вас есть какие-либо вопросы, пожалуйста, свяжитесь с официальной службой поддержки клиентов.",
		MessageContentTraderCancel:       "К сожалению, ваш статус трейдера был отозван. Если у вас есть какие-либо вопросы, пожалуйста, свяжитесь с официальной службой поддержки клиентов.",
		MessageContentProfit:             "Вы получили субкомиссию {{.Date}} и получили в общей сложности {{.Profit}} USDT субкомиссии.",
		ContentPartOrderBuy:              "открыть больше",
		ContentPartOrderSell:             "открытым",
		ContentPartCloseBuy:              "пинто",
		ContentPartCloseSell:             "Утончаются",
		ContentPartPosBuy:                "длинная позиция",
		ContentPartPosSell:               "короткая позиция",
		WarningRisk:                      "Уровень риска вашего контрактного счета достиг линии предупреждения. Пожалуйста, контролируйте риск позиции, корректируйте маржу или уменьшайте позицию самостоятельно.",
		ForcePosition:                    "Коэффициент риска вашего контрактного счета достиг принудительного уровня. Пожалуйста, обратите внимание на изменения средств на счете и хорошо поработайте над предотвращением и контролем рисков.",
	},
	ReqLangDE: {
		MessageTitleOrderSuccess:         "Erfolgreich geöffnet",
		MessageTitleOrderFail:            "Fehler beim Öffnen der Position",
		MessageTitleCloseSuccess:         "Position erfolgreich geschlossen",
		MessageTitleCloseFail:            "Geschlossene Position fehlgeschlagen",
		MessageTitleStopFollow:           "Hör auf zu folgen",
		MessageTitleBlowingOut:           "Liquidation",
		MessageTitleTraderAuditSuccess:   "Händler genehmigt",
		MessageTitleTraderAuditFail:      "Händlerüberprüfung fehlgeschlagen",
		MessageTitleTraderStopValid:      "Händlerstatus ist deaktiviert",
		MessageTitleTraderCancel:         "Händlerstatus entzogen",
		MessageTitleProfit:               "Provision bekommen",
		MessageContentOrderSuccess:       "{{.Code}} {{.Side}} {{.Lever}}-fache Hebelwirkung, der Eröffnungspreis beträgt {{.Price}} und es wurden {{.Volume}} Kontrakte gehandelt.",
		MessageContentOrderFail:          "Leider ist die Eröffnung des {{.Code}}-Kontrakts aufgrund eines unzureichenden Guthabens/autorisierten Händlers auf Ihrem Kopierkonto diesmal fehlgeschlagen.",
		MessageContentCloseSuccess:       "{{.Code}} {{.Side}} {{.Lever}}-fache Hebelwirkung, der Schlusskurs beträgt {{.Price}}, {{.Volume}} Kontrakte werden gehandelt und der Gewinn und Verlust beträgt {{.Profit}} USDT.",
		MessageContentCloseFail:          "Leider ist die Eröffnung des {{.Code}}-Kontrakts aufgrund eines unzureichenden Guthabens/autorisierten Händlers auf Ihrem Kopierkonto diesmal fehlgeschlagen.",
		MessageContentStopFollow:         "Entschuldigung, der Trader hat Ihren Follow-Trade abgebrochen, und Sie können dem Trader danach nicht mehr folgen, um Trades zu eröffnen.",
		MessageContentBlowingOut:         "Ihr {{.Code}} {{.Side}} {{.Lever}}-facher Leverage wurde liquidiert, der Liquidationspreis beträgt {{.Price}}, {{.Volume}} Transaktion wurde gehandelt und der Gewinn und Verlust beträgt {{.Profit}}USDT.",
		MessageContentTraderAuditSuccess: "Herzlichen Glückwunsch, Sie sind ein {{.Website}}-Händler geworden. Für jede Bestellung können Sie {{.Rebate}}% des Gewinns des Kopierbenutzers erhalten",
		MessageContentTraderAuditFail:    "Entschuldigung, die von Ihnen eingereichte Händlerbewerbung konnte nicht überprüft werden. Bei Fragen wenden Sie sich bitte an den offiziellen Kundenservice.",
		MessageContentTraderStopValid:    "Entschuldigung, Ihr Händlerstatus wurde deaktiviert. Bei Fragen wenden Sie sich bitte an den offiziellen Kundenservice.",
		MessageContentTraderCancel:       "Entschuldigung, Ihr Händlerstatus wurde widerrufen. Bei Fragen wenden Sie sich bitte an den offiziellen Kundenservice.",
		MessageContentProfit:             "Sie haben die Subprovision am {{.Date}} erhalten, wobei insgesamt {{.Profit}} USDT an Subprovision erhalten wurden.",
		ContentPartOrderBuy:              "mehr öffnen",
		ContentPartOrderSell:             "offen",
		ContentPartCloseBuy:              "pinto",
		ContentPartCloseSell:             "Vollgas",
		ContentPartPosBuy:                "Long-Position",
		ContentPartPosSell:               "kurze Position",
		WarningRisk:                      "Ihre Vertragskonto-Risikoquote hat die Warnlinie erreicht. Bitte kontrollieren Sie das Positionsrisiko, passen Sie die Marge an oder reduzieren Sie die Position selbst.",
		ForcePosition:                    "Die Risikoquote Ihres Vertragskontos hat das Zwangsniveau erreicht. Bitte achten Sie auf die Veränderungen der Kontodeckung und leisten Sie gute Arbeit bei der Risikoprävention und -kontrolle.",
	},
}

const (
	TemplateVarCode    string = "Code"
	TemplateVarSide           = "Side"
	TemplateVarLever          = "Lever"
	TemplateVarPrice          = "Price"
	TemplateVarVolume         = "Volume"
	TemplateVarProfit         = "Profit"
	TemplateVarRebate         = "Rebate"
	TemplateVarErrMsg         = "ErrMsg"
	TemplateVarDate           = "Date"
	TemplateVarWebsite        = "Website"
)

var (
	ErrNotFund    = errors.New("errNotFund")
	ErrNotSupport = errors.New("errNotSupportLanguage")
)

//获取消息支持的语言
func GetMsgSupportLang() (list []ReqLang) {
	for lang, _ := range MsgLanuageMap {
		list = append(list, lang)
	}
	return
}

//获取指定语言的内容
func GetMsgLanguageWithTemplate(rl ReqLang, msgId int, templateData interface{}) (string, error) {
	c, ok := GetMsgLanguage(rl, msgId)
	if !ok {
		return c, ErrNotSupport
	}
	if templateData == nil || !strings.Contains(c, "{{") {
		return c, nil
	}
	var s bytes.Buffer
	tmp, err := template.New("tmp").Parse(c)
	if err != nil {
		return "", err
	}
	err = tmp.Execute(&s, templateData)
	if err != nil {
		return "", err
	}
	return s.String(), nil
}

func GetMsgLanguage(rl ReqLang, msgId int) (c string, ok bool) {
	lm, ok := MsgLanuageMap[rl]
	if !ok {
		return
	}
	c, ok = lm[msgId]
	return
}

func GetMsgByLanguage(rl ReqLang, msgId int) string {
	c, _ := GetMsgLanguage(rl, msgId)
	return c
}

//开仓成功-普通用户-开仓全部成交时触发
//BTC/USDT开多10倍杠杆，开仓价8900.12，成交100张。
//交易员 合约大佬
//
//平仓成功-普通用户-平仓全部成交时触发
//BTC/USDT平多10倍杠杆，平仓价8900.12，成交100张，实现盈亏100USDT.
//交易员 合约大佬
//
//开仓失败-普通用户-由于资金问题，用户没有开仓成功时触发
//抱歉，由于您跟单账户中余额/授权交易员资金不足，本次BTC/USDT合约开仓失败。
//交易员 合约大佬
//
//停止跟随-普通用户-由于交易员将用户移除时触发
//抱歉，交易员已取消您的跟随交易，之后的开仓交易您将无法跟随该交易员开仓。
//交易员 合约大佬
//
//爆仓-普通用户/交易员，爆仓时的触发
//您的BTC/USDT多仓10倍杠杆已爆仓平多，爆仓价8900.12，成交100张，实现盈亏-100USDT.
//交易员 合约大佬
//
//交易员审核通过-交易员
//恭喜您，已成为QB的交易员。每笔带单可获得跟单用户利润10%分佣
//
//交易员审核失败-交易员
//很抱歉，您提交的交易员申请审核失败。失败原因：
//
//交易员身份被停用-交易员
//很抱歉，您的交易员身份已被停用。如有疑问，请联系官方客服。
//
//交易员身份被撤销-交易员
//很抱歉，您的交易员身份已被撤销。如有疑问，请联系官方客服。
//
//获得分佣-交易员-分佣转入成功时处罚
//您已收到YYYY-MM-DD 的分佣，共收到分佣xxxxxUSDT。
