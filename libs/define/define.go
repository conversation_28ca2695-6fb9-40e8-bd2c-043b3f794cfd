package define

import (
	"strconv"
	"time"

	"github.com/shopspring/decimal"
)

const (
	TokenKey = "AOcU1sKReJtERgW2jC0sGuImiWEYHjJKzsXD4eBxXRD3Q3RcfQBdTRP3Q2DzGglAg4i5FGCmpHHqP2RyGbLRzE09fzZv1L5K3QGpK500sBWhZ2pjRw7hVDu7Oi9OR0PP"

	SecretKeyAndroid = "****************************************************************"
	SecretKeyIos     = "6d6afe0274cd5c331910f8643ecf06a662491a0cea6188a0db13cd64d0238317"
	SecretKeyWeb     = "blue4e2269ea98876cf5235e4958300f415c5121ef2ee3b46e78d2b5b64de98b"

	RequestTimeout float64 = 180000000        // 请求过期时间(秒)
	RedisPermanent         = time.Duration(0) // redis永久保存

	ActionLockExpireTime       = time.Minute * 3    // 动作锁定时间
	AuthCodeExpireTime         = time.Minute * 10   // 验证码有效时间
	SensitiveActionExpTime     = time.Minute * 30   // 敏感操作有效时间
	SafeGuardExpTime           = time.Hour * 24     // 修改密码后禁止提币时间 24小时
	WebLoginExpireDate         = time.Hour * 24 * 7 // Web端登陆过期时间 7天
	PhoneLoginExpireDate       = time.Hour * 24 * 7 // 客户端登陆过期时间 7天
	ExchangePreOrderExpireTime = time.Second * 10   // 币币兑换预下单有效期

	InviteCodeMinRemain          = 1000  // redis中最少剩余多少个可用邀请码
	InviteCodeGenIdSpan    int64 = 10    // 用于生成邀请码的id跨度
	InviteCodeAutoGenCount int64 = 10000 // 每次自动生成多少个邀请码

	WithdrawAutoTaskTime     = 60 * 3 // 提币自动审核通过 3分钟
	ProvisionsOfRiskMaxReply = 7      // 风险保证金最多获取条数
	ProvisionsOfRiskMaxDate  = 31     // 风险保证金缓存最多保存条数(31天)
	SafeUserLogMaxReply      = 30     // 用户日志最多返回条数

	DecimalExp               = -10 // float转decimal精度保留位数 10位
	FloatPrecision           = 8   // float精度位数 8位
	ReplyFloatPrecision      = 8   // 返回float精度位数 8位
	LegalOrderPrecision      = 4   // 法币订单返回精度位数 4
	RatioPrecision           = 4   // 比例精度 2位
	VerifyFaceThreshold      = 0.6 // 人脸认证匹配度
	VerifyMaxErrCounter      = 10
	DecimalShiftDigit        = 8      // 小数转整数位移位数
	UsdtAssetFloatPrecision  = 8      // usdt资产精度位数 8位
	OtherAssetFloatPrecision = 8      // 其它资产精度位数 8位
	WithdrawPrecision        = 8      // 提币精度
	UidCachePoolSize         = 100000 // redis 用户id池数量
	UidLength                = 7      // redis 用户id初始长度
	UidPrefixAdjust          = 26     // redis 用户id初始前缀增量

	LegalCoinNameCNY = "CNY"
	LegalCoinNameUSD = "USD"
	CoinNameBtc      = "BTC"
	CoinNameUsdt     = "USDT"
	CoinIdUsdt       = 36

	SystemName         = "contract" // 资金划转系统标识
	CoinAddressTagSep  = "|"        // 币种地址tag分隔符
	ThirdLegalSliceSep = ","        // 三方买币数据分隔符

	PlatForm        = 0 //默认渠道
	PlatFormDefault = 2 //默认渠道

	PingTimeoutDelay   = 999999 // 接口ping超时定义值
	PingTimeoutContent = "超时"   // 接口ping超时文案定义值
)

const (
	TimeFormatDay         = "2006-01-02"              // 日期格式化
	TimeFormatDay2        = "2006.01.02"              // 日期格式化
	TimeFormatDay3        = "20060102"                // 日期格式化
	TimeFormatDay4        = "'2006-01-02'"            // 日期格式化
	TimeFormatNormal      = "2006-01-02 15:04:05"     // 常规日期格式化
	TimeFormatMillisecond = "2006-01-02 15:04:05.999" // 带毫秒日期格式化
	TimeFormatDayKey      = "0102"                    // 用于redis key 的拼接(月日)
	TimeFormatDayKeyV2    = "0102:"                   // 用于redis key 的拼接(月日)
	TimeFormatHourKey     = "010215"                  // 用于redis key 的拼接(月日小时)
	TimeFormatSecondKey   = "150405"                  // 用于redis key 的拼接(时分秒)
)

type OsType int8

const (
	OsAll       OsType = iota // 0 所有
	OsAndroid                 // 1 android端
	OsIos                     // 2 ios端
	OsWeb                     // 3 web
	OsH5                      // 4 h5端
	OsOpenApi                 // 5 openApi
	OsAutomatic               // 6 系统自动
	OsBackend                 // 7 管理后台
)

const OsAutomaticName = "系统自动"

const (
	LoginKeySuffixWeb = "web"
	LoginKeySuffix    = "phone"
	LoginKeySuffixH5  = "h5"
)

const (
	OsStrAndroid = "1" // 1-安卓端
	OsStrIos     = "2" // 2-ios端
	OsStrWeb     = "3" // 3-web端
	OsStrH5      = "4" // 4-h5端
	OsStrOpenAPI = "5" // 5-OpenAPI
)

type ReqLang uint8

func (l ReqLang) String() string {
	return strconv.Itoa(int(l))
}

const (
	ReqLangCN         ReqLang = iota // 0-中文
	ReqLangEN                        // 1-英文
	ReqLangTC                        // 2-繁体
	ReqLangKR                        // 3-韩语
	ReqLangVN                        // 4-越南
	ReqLangID                        // 5-印尼
	ReqLangRU                        // 6-俄语
	ReqLangDE                        // 7-德语
	ReqLangNonSupport                // 不支持的语言开始
	ReqLangJP                        // 4-日语
)

const (
	ChineseCode        = "CN"
	ChineseAreaCode    = "86"
	ChineseTWAreaCode  = "886"
	ChineseHKAreaCode  = "852"
	SouthKoreaAreaCode = "82"
)

type RpcHandler func(arg *Arg) (interface{}, error)

const (
	APILogMaxLength         = 300   // 日志打印中限制最大参数打印字符数
	ApiNormalKlineCount     = 300   // 默认普通Kline接口返回数据量
	ApiKLineMaxCount        = 300   // k线单次请求最多返回点数 & tradingView版k线返回数据量
	ApiKLineMaxTotalCount   = 10080 // k线请求最多返回总点数 & tradingView版k线返回数据量
	ApiRecentTradeMaxCount  = 50    // 近期交易单次请求返回数
	ApiHistoryOrderMaxCount = 100   // 历史订单请求返回数
	SQLLimitMaxCount        = 9999  // sql中limit优化限制条数
	ApiHistoryBillMaxDays   = 30    // 历史数据请求返回最多天数
	ApiHistoryBillWeekDays  = 7     // 历史数据请求返回一周数据天数

	ApiRecentResultLimit = 3 * 24 * 60 * 60 // 近期交易返回时间限制 3天
)

const (
	KLineDbNamePrefix = "tb_kline_" //K线数据库前缀
)

var (
	DecimalOne             = decimal.New(1, 0)
	DecimalTwo             = decimal.New(2, 0)
	DecimalHundred         = decimal.New(100, 0)
	BuyMarkPriceFactor     = decimal.NewFromFloat(1.02)        // 非条件单买单价格应小于等于 标记价格*(1+0.02)
	SellMarkPriceFactor    = decimal.NewFromFloat(0.98)        // 非条件单买单价格应小于等于 标记价格*(1-0.02)
	MainMarginRate         = decimal.NewFromFloat(0.005)       // 维持保证金率 0.005
	DecimalMaxSimAsset     = decimal.New(5, 5)                 // 总权益达到50w后不可继续领取模拟资产
	DecimalSimAssetSubsidy = decimal.New(7, 3)                 // 领取模拟资产单次数量:7000
	MinMarginModifyAmount  = decimal.New(1, -8)                // 最小调整保证金操作数量 0.0001  2022-08-15改为8位小数
	TransferLimitRatio     = decimal.RequireFromString("1.25") // 交易类账户资产转出限制比例
)

const (
	CoinStateDelisted = 0 // 下架
	CoinStateNormal   = 1 // 正常
)

const (
	FundingHourFirst  = 0
	FundingHourSecond = 8
	FundingHourThree  = 16
)

// 资金记录类型枚举
const (
	AssetTypeExchange2Contract = iota + 1 // 1 币币到合约
	AssetTypeContract2Exchange            // 2 合约到币币
	AssetTypeTradeFee                     // 3 交易手续费
	AssetTypeChangeDeposit                // 4 调整保证金
	AssetTypeFundingFee                   // 5 资金费用
)

// 用户操作标记
const (
	NormalRequest       = iota // 0-常规请求
	ForgetPassword             // 1-忘记密码
	UserRegister               // 2-用户注册
	ModifyPhone                // 3-修改手机号
	NewBindPhone               // 4-新绑定手机号
	ModifyEmail                // 5-修改邮箱
	NewBindEmail               // 6-新绑定邮箱
	ModifyLoginPassword        // 7-重置登录密码
	ModifyFundPassword         // 8-重置资金密码
	ModifyAuthVerify           // 9-安全验证
	NewBindSpareEmail          // 10-绑定备用邮箱
)

const (
	MaxPageCount                     = 100  // 最大分页数据量
	MaxActivityPageCount             = 1000 // 最大分页数据量
	AccountMaxLength                 = 64   // 最大账号长度
	MaxAdeptContractCount            = 3    // 最大3个擅长合约
	MaxCommonShowFollowersCount      = 20   // 公共接口最大展示跟随者数量
	MaxProtocolCoinWithdrawAddrCount = 10   // 单协议最多设置提币地址条数
	MaxUserNicknameCharCount         = 6    // 用户昵称最大设置6个字符
	MaxThirdFaceVerifyCount          = 3    // 用户最多走三方活体认证次数限制
)

type UserOPMode uint

const (
	UserOPModeLogin UserOPMode = iota + 1 // 1-登录日志
	UserOPModeSafe                        // 2-安全活动
)

// UserOPType 0-登录 1-注册 2-找回登录密码 3-设置登录密码 4-修改登录密码 5-修改资金密码 6-修改手机号 7-修改邮箱 8-提现申请 9-设置资金密码 10-设置谷歌验证器 11-修改谷歌验证器 12-设置手机号 13-设置邮箱 14-KYC1申请 15-KYC2申请 16-人工KYC申请 17-绑定备用邮箱 18-账号注销
type UserOPType int

const (
	// 日志类型
	RecordTypeLogin          UserOPType = iota // 0-登录
	RecordTypeRegister                         // 1-注册
	RecordTypeForgetPwd                        // 2-找回登录密码
	RecordTypeSetLoginPwd                      // 3-设置登录密码
	RecordTypeChangeLoginPwd                   // 4-修改登录密码
	RecordTypeChangeFundPwd                    // 5-修改资金密码
	RecordTypeModifyPhone                      // 6-修改手机号
	RecordTypeModifyEmail                      // 7-修改邮箱
	RecordTypeWithdraw                         // 8-提现申请
	RecordTypeSetFundPwd                       // 9-设置资金密码
	RecordTypeSetTotp                          // 10-设置谷歌验证器
	RecordTypeChangeTotp                       // 11-修改谷歌验证器
	RecordTypeBindPhone                        // 12-设置手机号
	RecordTypeBindEmail                        // 13-设置邮箱
	RecordTypeKYCLevel1                        // 14-KYC1申请
	RecordTypeKYCLevel2                        // 15-KYC2申请
	RecordTypeKYCManual                        // 16-人工KYC申请
	RecordTypeBindSpareEmail                   // 17-绑定备用邮箱
	RecordTypeDeleteAccount                    // 18-账号注销
)

const (
	// 错误日志类型
	ErrorRecordTypeLogin          = iota // 0-登录
	ErrorRecordTypeRegister              // 1-注册
	ErrorRecordTypeForgetPwd             // 2-找回登录密码
	ErrorRecordTypeModifyLoginPwd        // 3-登录密码设置
	ErrorRecordTypeModifyFundPwd         // 4-资金密码设置
	ErrorRecordTypeModifyAccount         // 5-用户名设置
	ErrorRecordTypeWithdraw              // 6-提现申请
	ErrorRecordTypeModifyTotp            // 7-谷歌验证码设置
	ErrorRecordTypeOrderPlace            // 8-市价开仓
	ErrorRecordTypeOrderClose            // 9-市价平仓
	ErrorRecordTypeOrderCloseAll         // 10-一键平仓
	ErrorRecordTypeSetLimitStop          // 11-止盈止损
	ErrorRecordTypePlanPlace             // 12-计划委托
	ErrorRecordTypePlanCancel            // 13-计划单撤销
	ErrorRecordTypeLegalPlace            // 14-法币交易
	ErrorRecordTypeBindPayment           // 15-绑定支付方式
	ErrorRecordTypeAuthCode              // 16-获取验证码
	ErrorRecordTypeKYCLevel1             // 17-KYC1申请
	ErrorRecordTypeKYCLevel2             // 18-KYC2申请
	ErrorRecordTypeKYCManual             // 19-人工KYC申请
)

var SerialState = map[int]bool{1: false, 2: true, 3: false, 4: true, 5: false, 6: true, 7: false, 8: true, 9: false}

const (
	UIDBasicDefaultPrefix = 7 // 初始化默认用户id前缀
	UIDBasicDefaultSerial = 1 // 初始化默认用户id序号
)

const (
	ModifyPwdTypeLogin = iota // 0-修改登录密码
	ModifyPwdTypeFund         // 1-修改资金密码
)

// BillRecordType 资金记录类型
type WalletBillType int64

// 资金记录类型枚举
const WalletBillTypeAll WalletBillType = 0 // 0-全部
const (
	WalletBillTypeRecharge                WalletBillType = 1 << iota // 1<<0(1)-充币
	WalletBillTypeWithdraw                                           // 1<<1(2)-提现
	WalletBillTypeTransfer2Trade                                     // 1<<2(4)-划转到交易账户
	WalletBillTypeTransfer2Wallet                                    // 1<<3(8)-划转到资产账户
	WalletBillTypeInviteBrokerage                                    // 1<<4(16)-邀请佣金奖励
	WalletBillTypeAgentBrokerage                                     // 1<<5(32)-代理佣金奖励
	WalletBillTypeLegalArrive                                        // 1<<6(64)-法币订单收币
	WalletBillTypeAirdrop                                            // 1<<7(128)-空投奖励
	WalletBillTypeTransferAsset2Follow                               // 1<<8(256)-资产账户划转到跟单账户
	WalletBillTypeTransferFollow2Asset                               // 1<<9(512)-跟单账户划转到资产账户
	WalletBillTypeWithHoldingIncome                                  // 1<<10(1024)-佣金收入
	WalletBillTypeActivityWrad                                       // 1<<11(2048)-活动奖励
	WalletBillTypeLegalSellFinish                                    // 1<<12(4096)-法币订单卖出币
	WalletBillTypeAbnormalForzen                                     // 1<<13(8192)-异常资产扣除
	WalletBillTypeExchangeOut                                        // 1<<14(16384)-币币兑换(兑出)
	WalletBillTypeExchangeIn                                         // 1<<15(32768)-币币兑换(兑入)
	WalletBillTypeExchangeOutRefund                                  // 1<<16(65536)-币币兑换(兑出退还)
	WalletBillTypeTransferAsset2UsdTrade                             // 1<<17(131072)-资产账户划转到usd合约账户
	WalletBillTypeTransferUsdTrade2Asset                             // 1<<18(262144)-usd合约账户划转到资产账户
	WalletBillTypeTransferAsset2SpotTrade                            // 1<<19(524288)-资产账户划转到现货交易账户
	WalletBillTypeTransferSpotTrade2Asset                            // 1<<20(1048576)-现货交易账户划转到资产账户
)

// WalletBillTypeAny 1(1<<0)-充币 2(1<<1)-提现 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 64(1<<6)-法币订单收币 128(1<<7)-空投奖励 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励 4096(1<<12)-法币订单卖出币 8192(1<<13)-异常资产扣除 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还) 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
const WalletBillTypeAny = WalletBillTypeRecharge | WalletBillTypeWithdraw | WalletBillTypeTransfer2Trade | WalletBillTypeTransfer2Wallet | WalletBillTypeInviteBrokerage | WalletBillTypeAgentBrokerage | WalletBillTypeLegalArrive | WalletBillTypeAirdrop | WalletBillTypeTransferAsset2Follow | WalletBillTypeTransferFollow2Asset | WalletBillTypeWithHoldingIncome | WalletBillTypeActivityWrad | WalletBillTypeLegalSellFinish | WalletBillTypeAbnormalForzen | WalletBillTypeExchangeOut | WalletBillTypeExchangeIn | WalletBillTypeExchangeOutRefund | WalletBillTypeTransferAsset2UsdTrade | WalletBillTypeTransferUsdTrade2Asset | WalletBillTypeTransferAsset2SpotTrade | WalletBillTypeTransferSpotTrade2Asset

type AccountBillType uint64

// 交易账户记录类型枚举
const AccountTypeAll AccountBillType = 0 //所有
const (
	AccountTypeOpenFee              AccountBillType = 1 << iota // 1<<0(1)-交易费用
	AccountTypeFundingFee                                       // 1<<1(2)-资金费用
	AccountTypeIncome                                           // 1<<2(4)-转入(从资产账户)
	AccountTypeLoss                                             // 1<<3(8)-转出(到资产账户)
	AccountTypeProfit                                           // 1<<4(16)-平仓盈亏
	AccountTypeCloseFee                                         // 1<<5(32)-平仓手续费
	AccountTypeSimSubsidy                                       // 1<<6(64)-模拟盘补充资产
	AccountTypeMarginModifySub                                  // 1<<7(128)-减少保证金
	AccountTypeWithholdingOut                                   // 1<<8(256)-预扣佣金
	AccountTypeWithHoldingReturn                                // 1<<9(512)-佣金退款
	AccountTypeWithHoldingIncome                                // 1<<10(1024)-佣金收入
	AccountTypeTransferTrade2Follow                             // 1<<11(2048)-交易账户划转到跟单账户
	AccountTypeTransferFollow2Trade                             // 1<<12(4096)-跟单账户划转到交易账户
	AccountTypeTransferAsset2Follow                             // 1<<13(8192)-资产账户划转到跟单账户
	AccountTypeTransferFollow2Asset                             // 1<<14(16384)-跟单账户划转到资产账户
	AccountTypeForceReturn                                      // 1<<15(32768)-强平退回
	AccountTypeSimDeduct                                        // 1<<16(65536)-模拟盘减少资产
	AccountTypeBlowingFee                                       // 1<<17(131072)-爆仓结算手续费
	AccountTypeAbnormalForzen                                   // 1<<18(262144)-异常资产扣除
	AccountTypeMarginModifyAdd                                  // 1<<19(524288)-增加保证金
	AccountTypeGiftIn                                           // 1<<20(1048576)-赠金领取
	AccountTypeGiftExpire                                       // 1<<21(2097152)-赠金失效
	AccountTypeGiftRecovery                                     // 1<<22(4194304)-赠金回收
)

// AccountTypeAny 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-模拟盘补充资产 1<<7(128)-减少保证金 1<<8(256)-预扣佣金 1<<9(512)-佣金退款 1<<10(1024)-佣金收入 1<<11(2048)-交易账户划转到跟单账户 1<<12(4096)-跟单账户划转到交易账户 1<<13(8192)-资产账户划转到跟单账户 1<<14(16384)-跟单账户划转到资产账户 1<<15(32768)-强平退回 1<<16(65536)-模拟盘减少资产 1<<17(131072)-爆仓结算手续费 1<<18(262144)-异常资产扣除 1<<19(524288)-增加保证金 1<<20(1048576)-赠金领取 1<<21(2097152)-赠金失效 1<<22(4194304)-赠金回收
const AccountTypeAny = AccountTypeOpenFee | AccountTypeFundingFee | AccountTypeIncome | AccountTypeLoss | AccountTypeProfit | AccountTypeCloseFee | AccountTypeSimSubsidy | AccountTypeMarginModifySub | AccountTypeWithholdingOut | AccountTypeWithHoldingReturn | AccountTypeWithHoldingIncome | AccountTypeTransferTrade2Follow | AccountTypeTransferFollow2Trade | AccountTypeTransferAsset2Follow | AccountTypeTransferFollow2Asset | AccountTypeForceReturn | AccountTypeSimDeduct | AccountTypeBlowingFee | AccountTypeAbnormalForzen | AccountTypeMarginModifyAdd | AccountTypeGiftIn | AccountTypeGiftExpire | AccountTypeGiftRecovery

// BillStatusType 资产记录状态
type WalletBillState int

// 资金记录枚举
const (
	WalletBillStateNotReject        WalletBillState = -4 // 不包含审核驳回
	WalletBillStateNotPassed        WalletBillState = -3 // 不包含审核通过
	WalletBillStateNotArrived       WalletBillState = -2 // 不包含到账
	WalletBillStateNotPending       WalletBillState = -1 // 不包含待处理 充值-未到账 提现-申请待处理
	WalletBillStateAll              WalletBillState = 0  // 全部
	WalletBillStatePending          WalletBillState = 1  // 待处理 充值-未到账 提现-申请待处理
	WalletBillStateArrived          WalletBillState = 2  // 到账
	WalletBillStatePassed           WalletBillState = 3  // 审核通过
	WalletBillStateReject           WalletBillState = 4  // 审核驳回
	WalletBillStatePlatformReject   WalletBillState = 5  // 平台拒绝
	WalletBillStatePlatformPassed   WalletBillState = 6  // 平台通过
	WalletBillStateFirstTrialPassed WalletBillState = 7  // 初审通过
	WalletBillStateFirstTrialReject WalletBillState = 8  // 初审拒绝
)

// usd交易账户记录类型枚举
type UsdTradeAccountBillType uint64

const UsdTradeAccountBillTypeAll UsdTradeAccountBillType = 0 //所有
const (
	UsdTradeAccountBillTypeOpenFee         UsdTradeAccountBillType = 1 << iota // 1<<0(1)-交易费用
	UsdTradeAccountBillTypeFundingFee                                          // 1<<1(2)-资金费用
	UsdTradeAccountBillTypeIncome                                              // 1<<2(4)-转入(从资产账户)
	UsdTradeAccountBillTypeLoss                                                // 1<<3(8)-转出(到资产账户)
	UsdTradeAccountBillTypeProfit                                              // 1<<4(16)-平仓盈亏
	UsdTradeAccountBillTypeCloseFee                                            // 1<<5(32)-平仓手续费
	UsdTradeAccountBillTypeMarginModifyAdd                                     // 1<<6(64)-增加保证金
	UsdTradeAccountBillTypeMarginModifySub                                     // 1<<7(128)-减少保证金
	UsdTradeAccountBillTypeForceReturn                                         // 1<<8(256)-强平退回
	UsdTradeAccountBillTypeBlowingFee                                          // 1<<9(512)-爆仓结算手续费
)

// UsdTradeAccountBillTypeAny 所有usd交易账户流水记录类型 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-增加保证金 1<<7(128)-减少保证金 1<<8(256)-强平退回 1<<9(512)-爆仓结算手续费
const UsdTradeAccountBillTypeAny = UsdTradeAccountBillTypeOpenFee | UsdTradeAccountBillTypeFundingFee | UsdTradeAccountBillTypeIncome | UsdTradeAccountBillTypeLoss | UsdTradeAccountBillTypeProfit | UsdTradeAccountBillTypeCloseFee | UsdTradeAccountBillTypeMarginModifyAdd | UsdTradeAccountBillTypeMarginModifySub | UsdTradeAccountBillTypeForceReturn | UsdTradeAccountBillTypeBlowingFee

// 账户记录类型枚举
type SpotTradeAccountBillType uint64

const SpotTradeAccountBillTypeAll SpotTradeAccountBillType = 0 //所有
const (
	SpotTradeAccountBillTypeOpenFee      SpotTradeAccountBillType = 1 << iota // 1(1<<0)-交易taker手续费
	SpotTradeAccountBillTypeIncome                                            // 2(1<<2)-转入(从资产账户)
	SpotTradeAccountBillTypeLoss                                              // 4(1<<3)-转出(到资产账户)
	SpotTradeAccountBillTypeTradeOut                                          // 8(1<<4)-交易转出
	SpotTradeAccountBillTypeTradeIn                                           // 16(1<<5)-交易转入
	SpotTradeAccountBillTypeOpenFeeMaker                                      // 32(1<<6)-交易maker手续费
)

// SpotTradeAccountBillTypeAny 所有现货交易账户流水记录类型 1(1<<0)-交易taker手续费 2(1<<2)-转入(从资产账户) 4(1<<3)-转出(到资产账户) 8(1<<4)-交易转出 16(1<<5)-交易转入 32(1<<6)-交易maker手续费
const SpotTradeAccountBillTypeAny = SpotTradeAccountBillTypeOpenFee | SpotTradeAccountBillTypeIncome | SpotTradeAccountBillTypeLoss | SpotTradeAccountBillTypeTradeOut | SpotTradeAccountBillTypeTradeIn | SpotTradeAccountBillTypeOpenFeeMaker

type AssetMode uint8

const (
	AssetModeAll         AssetMode = iota // 全部账户
	AssetModeForWallet                    // 资金账户
	AssetModeForTrade                     // 交易账户
	AssetModeForFollow                    // 跟单账户
	AssetModeForUsdTrade                  // usd交易账户
)

type FollowMode uint8

const (
	FollowModeForQuantity   FollowMode = 1 << iota // 1-固定张数
	FollowModeForProportion                        // 2-固定比例
	FollowModeForNull       FollowMode = 0         // 0-没有任何跟随方式
)

const (
	OffsetOpen  = "O" //开仓
	OffsetClose = "C" //平仓
)

const (
	CloseOutTypePart = 1 //部分平仓
	CloseOutTypeFull = 2 //全平
)

const (
	CloseTypeFollowUser = 0 //用户主动平仓
	CloseTypeDealer     = 1 //跟随交易员平仓
)

const CNYPrecision = 2
const LegalPrecision = 2
const MarginPrecision = 8 //保证金数据库精度
const FundingRatePrecision = 6
const FullPrecision = 8 //数据库精度

const (
	MarginShowPrecision = 4 //保证金展示精度
	ProfitShowPrecision = 4 //收益精度
	RightsSHowPrecision = 4 //权益精度
	ValueShowPrecision  = 4 //成交价值，成交金额精度
	PriceShowPrecision  = 4
)

const (
	PlanMoneyPrecision = 4      // 条件单下单金额精度
	PlanMaxAmount      = 999999 // 条件单下单张数最大值
)

const (
	CwsUsdtName  = "usdt"
	CwsUsdt2Name = "usdt2"
)

const (
	DefaultContractLever = 10
)

var ZeroTime time.Time
var DefaultTime = time.Unix(0, 0)

const (
	IndexAvailableDataGap = 90
)

type UserVerifyState uint8

func (s UserVerifyState) Level1OrHigh() bool {
	switch s {
	case VerifyIdNumberVerified, VerifyFaceInProgress, VerifyFaceNotPass, VerifyFaceVerified, VerifyManuallyAdd:
		return true
	default:
		return false
	}
}
func (s UserVerifyState) Level2() bool { return s == VerifyFaceVerified || s == VerifyManuallyAdd }

// 认证状态
const (
	VerifyUnverified         UserVerifyState = iota // 0-未认证
	VerifyIdNumberInProgress                        // 1-实名认证审核中
	VerifyIdNumberNotPass                           // 2-实名认证审核失败
	VerifyIdNumberVerified                          // 3-实名认证审核通过
	VerifyFaceInProgress                            // 4-面部认证审核中
	VerifyFaceNotPass                               // 5-面部认证审核失败
	VerifyFaceVerified                              // 6-面部认证审核通过
	VerifyReset                                     // 7-重置
	VerifyManuallyAdd                               // 8-人工添加
)

// 认证错误码
const (
	VerifyErrCodeNone            = 0    // 无错误
	VerifyErrCodeExist           = 1001 // 证件号码已经存在
	VerifyErrCodeNotMatch        = 1002 // 证件号码与姓名不匹配
	VerifyErrCodeNotMatchFace    = 1003 // 身份信息与活体检测不匹配
	VerifyErrCodePhotoDim        = 1101 // 证件信息不清晰，证件信息不支持扫描和复印，仅支持原件照片
	VerifyErrCodeYoungAge        = 1102 // 年龄未满18周岁，认证仅支持年满18周岁的成年用户
	VerifyErrCodeOldAge          = 1103 // 年龄超过75周岁，认证仅支持75周岁以下的用户
	VerifyErrCodeNotMatchPhoto   = 1104 // 姓名与证件信息不一致
	VerifyErrCodeNotMatchCountry = 1105 // 证件信息与注册时的国家地区不一致
)

// 认证错误码对应信息
var verifyErrMsg = map[ReqLang]map[int]string{
	ReqLangCN: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "证件号码已经存在",
		VerifyErrCodeNotMatch:        "证件号码与姓名不匹配",
		VerifyErrCodeNotMatchFace:    "身份信息与活体检测不匹配",
		VerifyErrCodePhotoDim:        "证件信息不清晰，证件信息不支持扫描和复印，仅支持原件照片",
		VerifyErrCodeYoungAge:        "年龄未满18周岁，认证仅支持年满18周岁的成年用户",
		VerifyErrCodeOldAge:          "年龄超过75周岁，认证仅支持75周岁以下的用户",
		VerifyErrCodeNotMatchPhoto:   "姓名与证件信息不一致",
		VerifyErrCodeNotMatchCountry: "证件信息与注册时的国家地区不一致",
	},
	ReqLangEN: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "The id number already exists",
		VerifyErrCodeNotMatch:        "Name and certificate information is inconsistent",
		VerifyErrCodeNotMatchFace:    "Identity information does not match in vivo detection",
		VerifyErrCodePhotoDim:        "The document information is not clear, the document information does not support scanning and photocopying, only supporting the original photo",
		VerifyErrCodeYoungAge:        "Under the age of 18, the authentication only supports adult users over 18 years old",
		VerifyErrCodeOldAge:          "Over the age of 75, the certification only supports users under the age of 75",
		VerifyErrCodeNotMatchPhoto:   "Name and certificate information is inconsistent",
		VerifyErrCodeNotMatchCountry: "The document information is not consistent with the country or region at the time of registration",
	},
	ReqLangTC: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "證件號碼已經存在",
		VerifyErrCodeNotMatch:        "證件號碼與姓名不匹配",
		VerifyErrCodeNotMatchFace:    "身份信息與活體檢測不匹配",
		VerifyErrCodePhotoDim:        "證件信息不清晰,證件信息不支持掃描和複印,僅支持原件照片",
		VerifyErrCodeYoungAge:        "年齡未滿18週歲,認證僅支持年18周滿歲的成年用戶",
		VerifyErrCodeOldAge:          "年齡超過75週歲,認證僅支持75週歲以下的用戶",
		VerifyErrCodeNotMatchPhoto:   "姓名與證件信息不一致",
		VerifyErrCodeNotMatchCountry: "證件信息與註冊時的國家地區不一致",
	},
	ReqLangKR: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "ID 번호가 이미 있습니다.",
		VerifyErrCodeNotMatch:        "ID 번호가 이름과 일치하지 않습니다.",
		VerifyErrCodeNotMatchFace:    "신원 정보가 실시간 감지와 일치하지 않습니다.",
		VerifyErrCodePhotoDim:        "문서 정보가 명확하지 않고 문서 정보가 스캔 및 복사를 지원하지 않으며 원본 사진 만 지원됩니다.",
		VerifyErrCodeYoungAge:        "18 세 미만, 인증은 18 세 이상의 성인 사용자 만 지원합니다.",
		VerifyErrCodeOldAge:          "75 세 이상, 인증은 75 세 미만 사용자 만 지원합니다.",
		VerifyErrCodeNotMatchPhoto:   "이름이 ID 정보와 일치하지 않습니다.",
		VerifyErrCodeNotMatchCountry: "인증서 정보가 등록 당시의 국가 및 지역과 일치하지 않습니다.",
	},
	ReqLangVN: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "Số ID đã tồn tại",
		VerifyErrCodeNotMatch:        "Số ID không khớp với tên",
		VerifyErrCodeNotMatchFace:    "Thông tin nhận dạng không khớp với tính năng phát hiện trực tiếp",
		VerifyErrCodePhotoDim:        "Thông tin ID không rõ ràng, thông tin ID không hỗ trợ quét và sao chép, chỉ hỗ trợ ảnh gốc",
		VerifyErrCodeYoungAge:        "Dưới 18 tuổi, xác thực chỉ hỗ trợ người dùng trưởng thành trên 18 tuổi",
		VerifyErrCodeOldAge:          "Trên 75 tuổi, xác thực chỉ hỗ trợ người dùng dưới 75 tuổi",
		VerifyErrCodeNotMatchPhoto:   "Tên không khớp với thông tin tài liệu ",
		VerifyErrCodeNotMatchCountry: "Thông tin chứng chỉ không nhất quán với quốc gia và khu vực tại thời điểm đăng ký",
	},
	ReqLangID: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "Nomor ID sudah ada",
		VerifyErrCodeNotMatch:        "Nomor ID tidak sesuai dengan nama",
		VerifyErrCodeNotMatchFace:    "Informasi identitas tidak cocok dengan deteksi keaktifan",
		VerifyErrCodePhotoDim:        "Informasi ID tidak jelas, informasi ID tidak mendukung pemindaian dan penyalinan, hanya foto asli yang didukung",
		VerifyErrCodeYoungAge:        "Di bawah usia 18 tahun, otentikasi hanya mendukung pengguna dewasa di atas usia 18 tahun",
		VerifyErrCodeOldAge:          "Lebih dari 75 tahun, otentikasi hanya mendukung pengguna di bawah 75 tahun",
		VerifyErrCodeNotMatchPhoto:   "Nama tidak sesuai dengan informasi dokumen",
		VerifyErrCodeNotMatchCountry: "Informasi sertifikat tidak sesuai dengan negara dan wilayah pada saat pendaftaran",
	},
	ReqLangRU: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "Идентификационный номер уже существует",
		VerifyErrCodeNotMatch:        "Идентификационный номер не соответствует имени",
		VerifyErrCodeNotMatchFace:    "Идентификационная информация не соответствует обнаружению живучести",
		VerifyErrCodePhotoDim:        "Информация об удостоверении личности нечеткая, информация об удостоверении личности не поддерживает сканирование и копирование, поддерживается только исходная фотография",
		VerifyErrCodeYoungAge:        "До 18 лет аутентификация поддерживает только взрослых пользователей старше 18 лет.",
		VerifyErrCodeOldAge:          "старше 75 лет, аутентификация поддерживается только пользователями младше 75 лет",
		VerifyErrCodeNotMatchPhoto:   "Название не соответствует информации документа",
		VerifyErrCodeNotMatchCountry: "Информация о сертификате не соответствует стране и региону на момент регистрации.",
	},
	ReqLangDE: {
		VerifyErrCodeNone:            "",
		VerifyErrCodeExist:           "ID-Nummer existiert bereits",
		VerifyErrCodeNotMatch:        "ID-Nummer stimmt nicht mit Name überein",
		VerifyErrCodeNotMatchFace:    "Identitätsinformationen stimmen nicht mit der Lebendigkeitserkennung überein",
		VerifyErrCodePhotoDim:        "Die ID-Informationen sind nicht eindeutig, die ID-Informationen unterstützen kein Scannen und Kopieren, nur das Originalfoto wird unterstützt",
		VerifyErrCodeYoungAge:        "Unter 18 Jahren unterstützt die Authentifizierung nur erwachsene Benutzer über 18 Jahren",
		VerifyErrCodeOldAge:          "Über 75 Jahre alt, Authentifizierung unterstützt nur Benutzer unter 75 Jahren",
		VerifyErrCodeNotMatchPhoto:   "Der Name stimmt nicht mit den Dokumentinformationen überein",
		VerifyErrCodeNotMatchCountry: "Die Zertifikatsinformationen stimmen zum Zeitpunkt der Registrierung nicht mit dem Land und der Region überein",
	},
}

func GetVerifyErrMsg(code int, lang ReqLang) string {
	msgs, ok := verifyErrMsg[lang]
	if !ok {
		msgs = verifyErrMsg[ReqLangCN]
	}
	msg, ok := msgs[code]
	if ok {
		return msg
	}
	return ""
}

const (
	ShareTypeHold       = iota // 持仓分享
	ShareTypeSold              // 成交记录分享
	ShareTypeNonsupport        // 不支持的类型开始
)

const (
	PlanCloseMoreThanEqual = 1
	PlanCloseLessThanEqual = 2
)

const (
	MaxPlanOrderCount      = 10 // 最大未完成计划单张数
	MaxEntrustOrderCount   = 10 // 最大未完成委托单张数
	MaxPlanCloseOrderCount = 6  // 单合约单方最多6条计划止盈止损
)

const (
	ModifySideAdd = "add" // 增加
	ModifySideSub = "sub" // 减少
)

type ExternalSysType int8

const (
	ExternalSysTypeSenseTime ExternalSysType = iota + 1 // 1-商汤
	ExternalSysTypeFxh                                  // 2-非小号
	ExternalSysTypeGecko                                // 3-币虎
)

var externalSysName = map[ExternalSysType]string{
	ExternalSysTypeSenseTime: "商汤",
	ExternalSysTypeFxh:       "非小号",
	ExternalSysTypeGecko:     "币虎",
}

func GetExternalSysName(sysType ExternalSysType) string {
	if name, ok := externalSysName[sysType]; ok {
		return name
	}
	return "其它外部服务"
}

type SysErrorType int8

const (
	SysErrorTypeTickClose SysErrorType = iota + 1 // 1-行情断开
)

var sysErrorTypeName = map[SysErrorType]string{
	SysErrorTypeTickClose: "行情断开",
}

func GetSysErrorTypeName(sysType SysErrorType) string {
	if name, ok := sysErrorTypeName[sysType]; ok {
		return name
	}
	return "其它错误"
}

const (
	VerifyInland = iota // 0-中国大陆
	VerifyOther         // 1-其它
)

const (
	NameMaxLength   = 120 // 名或姓最长字符数
	NumberMaxLength = 40  // 证件号最长字符数
)

const (
	SettleTypeReturn = 0 //退回跟随者
	SettleTypeDealer = 1 //给交易者结算
)

var (
	FollowerBrokerageRate = decimal.NewFromFloat(0.1)
)

const (
	CacheKeyCoreServerTask                   = "bc:core:task"               // core服务待执行任务
	CoreServerTaskUpdateAllDealerFollowLimit = "updateAllDealerFollowLimit" // 更新所有交易员带单人数限制
)

const (
	MaxTimestamp = 99999999999 // 设置最大时间戳用于排序
)

const (
	RedisLockValue = "1"
)

const (
	UserWithdrawVerifyStateUnverified = iota // 0-未认证
	UserWithdrawVerifyStateInProgress        // 1-认证审核中
	UserWithdrawVerifyStateNotPass           // 2-认证审核失败
	UserWithdrawVerifyStateVerified          // 3-认证审核通过
)

const (
	TradeMarkTypeDefault = 0 //默认
	TradeMarkTypeBroken  = 1 //穿仓
)

const (
	WalletCoinConfDefaultProtocol     = "protocol.default" // 无协议定义时的默认值
	WalletCoinConfDefaultProtocolUSDT = "ERC20"            // 无协议定义时的默认值(usdt)
)

const (
	BasePriceMarket = "marketIndex" //标记价格
	BasePriceSpot   = "spotIndex"   //现货指数
	BasePriceDepth  = "depthIndex"  //铺单基准价格
)

const (
	PaymentTypeBank   = 1 //银行卡
	PaymentTypeAliPay = 2 //支付宝
	PaymentTypeWeChat = 3 //微信
)

const (
	MinChangeSafePhoneVerifyMode = 2 // 修改手机号最少需要的已有验证方式个数
)

const (
	EmptyStrSize = 0 // 空字符串长度
)

const (
	FundPasswordVerifyTypeNever  = iota // 0-从不校验
	FundPasswordVerifyTypeDaily         // 1-每24小时验证一次
	FundPasswordVerifyTypeAlways        // 2-每次都验证
)

const (
	TotpSecretSize = 16 // 验证器私钥长度
)

const (
	APIVersion = "V2" // api版本
)

const (
	FundPasswordMiscountLimit = 5 // 资金密码错误到达该次数后,退出登录,并为用户设置安全锁
)

var DecimalConvertInt64Multiple = decimal.New(1, 8) // 使用该倍数将值转换为包含8位小数的正数

const (
	SafeGuardTypeNone    = 0 // 未限制
	SafeGuardTypeSetting = 1 // 安全设置相关
	SafeGuardTypeFundPwd = 2 // 资金密码相关
)

const (
	AboutCommunityCategoryEmail     = 1 // 邮箱
	AboutCommunityCategoryCommunity = 2 // 社群
)

const (
	UserAPIStateAvailable = 0 // 用户api状态(正常)
	UserAPIStateDelete    = 1 // 用户api状态(删除)
	UserAPIStateOverdue   = 2 // 用户api状态(过期)
)

const (
	MaxUserIPv6BindCountOfAPI = 2  // api最大支持绑定ipv6个数
	MaxUserIPBindCountOfAPI   = 10 // api最大支持绑定ip个数
)

const (
	UserAPIExpireTimeForBindIP = 30 // 绑定ip的api有效期30年
	UserAPIExpireTimeForNoneIP = 90 // 没有绑定ip的api有效期90天
)

const (
	ActivityEndSet = 23 //小时
)

const (
	MaxUserAvailApiCount = 1 // 用户最多可用api个数
)

const (
	PlaceLowRisk = 0.55 //开仓最低风险率
	//PlaceLowRisk = 0.55 //开仓最低风险率
)

const (
	MaxPaymentCount = 10 // 最多添加10个支付方式
)

const (
	ForbidOpenCloseTypeNone       = 0 // 不禁止开平仓
	ForbidOpenCloseTypeOpenLimit  = 1 // 禁止开仓
	ForbidOpenCloseTypeCloseLimit = 2 // 禁止平仓
)

type Placeholder struct{}

var PlaceholderEntity = Placeholder{}

type ExchangeOrderState int

const (
	ExchangeOrderStateFinish  ExchangeOrderState = -1  // -1-已结束
	ExchangeOrderStateAll     ExchangeOrderState = 0   // 0-全部
	ExchangeOrderStatePending ExchangeOrderState = 1   // 1-等待确认
	ExchangeOrderStateCancel  ExchangeOrderState = 100 // 100-已取消
	ExchangeOrderStateDone    ExchangeOrderState = 200 // 200-已完成
)

type ExchangeHedgeState int

// 委托状态 1-默认  100-部分成交 200-全部成交 201-未成交已撤 202-部分成交已撤
const (
	ExchangeHedgeStateNew        ExchangeHedgeState = 1   // 默认
	ExchangeHedgeStatePartTrade  ExchangeHedgeState = 100 //部分成交
	ExchangeHedgeStateFullTrade  ExchangeHedgeState = 200 // 全部成交
	ExchangeHedgeStateCancel     ExchangeHedgeState = 201 // 未成交已撤
	ExchangeHedgeStatePartCancel ExchangeHedgeState = 202 // 部分成交已撤
	ExchangeHedgeStateException  ExchangeHedgeState = -1  // 异常订单
)

func (es ExchangeHedgeState) IsFinished() bool {
	return es == ExchangeHedgeStateFullTrade || es == ExchangeHedgeStateCancel || es == ExchangeHedgeStatePartCancel
}

type ExchangeHedgeSource int

const (
	ExchangeHedgeSourceBinance ExchangeHedgeSource = 1 //币安
	ExchangeHedgeSourceHuoBi   ExchangeHedgeSource = 2 //火币
)

type TransferAssetSort uint

const (
	TransferAssetSortForOut TransferAssetSort = iota // 0-转出
	TransferAssetSortForIn                           // 1-转入
)

const (
	BaseDBName = "new_basecoin"
	UsdDBName  = "tb_reverse_basecoin"
	SpotDBName = "futures_basecoin"
)

var (
	ProvisionsOfRiskMultiple = decimal.NewFromInt(9)
)
