package define

import "time"

const (
	RepeatRequestDuration = 2 * time.Minute
)

const (
	IdentifierUser        = 0 //普通用户
	IdentifierBusiness    = 1 //做市商（普通用户一样）
	IdentifierMarketRobot = 2 //做市商机器人（无账户）
)

const (
	MatchOverDefault = "freeCancel"        //策略撤销，剩余的部分撤销
	MatchOverForce   = "freeProtocolClose" //强平，剩余协议平仓
	MatchTradeOver   = "tradeAll"
)

const (
	CancelMarkUser           = 1 //用户撤销标记
	CancelMarkForce          = 2 //强平取消订单
	CancelMarkRiskController = 3 //风险控制转撤销
	CancelMarkFastClose      = 4 //用户一键平仓取消订单
	CancelMarkReverseOpen    = 5 //用户反向开仓
	CancelMarkRiskData       = 6 //系统成交风险转撤销（成交数据重复、资金不足）
	CancelMarkPositionValid  = 7 //加仓订单持仓无效
)

const (
	ActionPlace  = "place"
	ActionCancel = "cancel"
)
