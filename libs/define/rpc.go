package define

const (
	_BaseServerNamePrefix     = "base."
	BaseServerNameClosing     = _BaseServerNamePrefix + "closing"
	BaseServerNamePrice       = _BaseServerNamePrefix + "price"
	BaseServerNameCore        = "core"
	BaseServerNameLimit       = _BaseServerNamePrefix + "limit"
	BaseServerNameBucketing   = _BaseServerNamePrefix + "bucketing" //对敲铺单
	BaseServerNameKline       = _BaseServerNamePrefix + "kline"
	BaseServerNameForce       = _BaseServerNamePrefix + "force"
	BaseServerNameFollow      = _BaseServerNamePrefix + "follow"
	BaseServerNameForceFollow = _BaseServerNamePrefix + "force.follow"
	BaseServerNameTask        = _BaseServerNamePrefix + "task"
	BaseServerNameUser        = _BaseServerNamePrefix + "user"
	BaseServerNameWallet      = _BaseServerNamePrefix + "wallet"
	BaseServerNameThird       = _BaseServerNamePrefix + "third"

	_ReverseServerNamePrefix = "reverse."
	ReverseServerNameCore    = _ReverseServerNamePrefix + "core"

	_SpotServerNamePrefix = "spot."
	SpotServerNameCore    = _SpotServerNamePrefix + "core"
)

const (
	BasePath = "panda"
)

const (
	RequestIDKey    = "RequestID"
	RequestTokenKey = "Token"
	ResponseCodeKey = "ResponseCode"
)
