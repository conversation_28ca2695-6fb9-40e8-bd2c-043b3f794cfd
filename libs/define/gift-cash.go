package define

// GiftActivityType 活动类型
type GiftActivityType uint8

const (
	GiftActivityTypeFirstDeposit GiftActivityType = iota + 1 // 1-首冲活动
)

// GiftPartake 活动参与者类型
type GiftPartake uint8

const (
	GiftPartakeNormal GiftPartake = 1 << iota // 1-非代理用户
	GiftPartakeAgent                          // 2-代理用户
	GiftPartakeAll    GiftPartake = 0         // 0-全部
)

// GiftPartakeAny 活动参与者类型 0-全部 1-非代理用户 2-代理用户
const GiftPartakeAny = GiftPartakeNormal | GiftPartakeAgent

// GiftActivityState 活动参与状态
type GiftActivityState uint8

const (
	GiftActivityStateInvalid GiftActivityState = iota // 0-未启用
	GiftActivityStateValid                            // 1-启用
)

// GiftJoinState 活动参与状态
type GiftJoinState uint8

const (
	GiftJoinStateAll      GiftJoinState = iota // 0-全部
	GiftJoinStateNotStart                      // 1-未开始
	GiftJoinStateStart                         // 2-进行中
	GiftJoinStateWaitGet                       // 3-待领取
	GiftJoinStateDone                          // 4-已领取
	GiftJoinStateExceed                        // 5-已过期
)

type GiftManageSendType uint8

const (
	GiftManageSendTypeRecovery GiftManageSendType = 0 // 回收
	GiftManageSendTypeGrant    GiftManageSendType = 1 // 发放
)

type GiftManageSendCause string

const (
	GiftManageSendCauseRecoveryForActive   GiftManageSendCause = "超时未激活回收"
	GiftManageSendCauseRecoveryForTrade    GiftManageSendCause = "超时未交易回收"
	GiftManageSendCauseRecoveryForWithdraw GiftManageSendCause = "提币回收"
)
