package define

var (
	RedisCommonDb int // redis db
	RedisExtraDb  int // redis db
)

const (
	CacheDBNumber0  = iota // 数据库号0
	CacheDBNumber1         // 数据库号1
	CacheDBNumber2         // 数据库号2
	CacheDBNumber3         // 数据库号3
	CacheDBNumber4         // 数据库号4
	CacheDBNumber5         // 数据库号5
	CacheDBNumber6         // 数据库号6
	CacheDBNumber7         // 数据库号7
	CacheDBNumber8         // 数据库号8
	CacheDBNumber9         // 数据库号9
	CacheDBNumber10        // 数据库号10
	CacheDBNumber11        // 数据库号11
	CacheDBNumber12        // 数据库号12
	CacheDBNumber13        // 数据库号13
	CacheDBNumber14        // 数据库号14
	CacheDBNumber15        // 数据库号15
)

const (
	// pro
	CacheKeyProjectPrefix               = "bc:"                                                   // 项目key前缀
	CacheKeyVersion                     = CacheKeyProjectPrefix + "version:%d:%d"                 // 用于查询app升级信息
	CacheKeyCoinRate                    = CacheKeyProjectPrefix + "coins:rate"                    // 币种汇率
	CacheKeyCoinLegalRate               = CacheKeyProjectPrefix + "coins:legal:rate"              // 币种的法币汇率
	CacheKeyContractMarketPrice         = CacheKeyProjectPrefix + "price:market:price"            // 合约不同市场最新成交价
	CacheKeyContractMarketIndexPrice    = CacheKeyProjectPrefix + "price:market:index:price"      // 合约不同市场最新指数价格
	CacheKeyContractMarketMarkPrice     = CacheKeyProjectPrefix + "price:market:mark:price"       // 合约不同市场标记价格
	CacheKeyContractMarketDepth         = CacheKeyProjectPrefix + "price:market:depth"            // 合约不同市场最新成交价
	CacheKeyContractDepthBasePrice      = CacheKeyProjectPrefix + "market:depth:base:price"       // 铺单基准(三大所合约价格生成）
	CacheKeyContractCurDepthPrice       = CacheKeyProjectPrefix + "market:cur:depth:base:price"   // 上次使用的的铺单基准
	CacheKeyContractComplexPrice        = CacheKeyProjectPrefix + "market:complex:trade:price"    // 综合成交价（标记价格）
	CacheKeyContractDepthSimpleInfo     = CacheKeyProjectPrefix + "market:depth:simple:info"      // 合约铺单基础信息
	CacheKeyContractSpotPrice           = CacheKeyProjectPrefix + "price:spot:price"              // 合约不同市场最新成交价(现货）
	CacheKeyContractSpotIndexPrice      = CacheKeyProjectPrefix + "market:spot:index:price"       // 现货指数价格
	CacheKeyContractSpotIndexProtect    = CacheKeyProjectPrefix + "market:spot:index:protect"     // 现货指数价格保护缓存
	CacheKeyContractSpotIndexProtectDay = CacheKeyProjectPrefix + "market:spot:index:protect:day" // 现货指数价格保护缓存日次数
	CacheKeyTradePrice                  = CacheKeyProjectPrefix + "price:trade"                   // 最新成交价
	CacheKeyContractIndex               = CacheKeyProjectPrefix + "price:index"                   // 成交价指数
	CacheKeyTempContractIndex           = CacheKeyProjectPrefix + "price:index:temp:"             // 成交价指数(临时,供应测试）
	CacheKeyContractIndexDetail         = CacheKeyProjectPrefix + "price:index:detail"            // 价格指数详情
	CacheKeyMarketPrice                 = CacheKeyProjectPrefix + "price:market"                  // 标记价格
	CacheKeyFundingRate                 = CacheKeyProjectPrefix + "funding:rate"                  // 资金费率
	CacheKeyFundingRateLast             = CacheKeyProjectPrefix + "funding:rate:last"             // 资金费率（每次计算费用后将当前放入）
	CacheKeyDepthMidPrice               = CacheKeyProjectPrefix + "depth:mid"                     //合约深度中间价
	CacheKeyOriginDepth                 = CacheKeyProjectPrefix + "depth:0"                       // 合约原始深度
	CacheKeyDepth                       = CacheKeyProjectPrefix + "depth:"                        // 合约大于0深度
	CacheKeyTradeDepth                  = CacheKeyProjectPrefix + "depth:trade"                   // 可成交档位深度
	CacheKeyContractCurrentTrade        = CacheKeyProjectPrefix + "contract:trade:current"        // 合约最近成交（伪装成交）
	ContractSelfCurrentTrade            = CacheKeyProjectPrefix + "contract:trade:self:current"   // 合约最近成交（真实）
	ContractSelfCurrentBaitTrade        = CacheKeyProjectPrefix + "contract:bait:trade:current"   // 合约最近中鱼成交（真实）
	ContractSpotIndexDistance           = CacheKeyProjectPrefix + "contract:spot:index:distance"  // 合约现货指数全局
	CacheKeyContractList                = CacheKeyProjectPrefix + "contract:list"                 // 合约列表
	CacheKeyContractDepthConfig         = CacheKeyProjectPrefix + "contract:depth:config"         // 合约深度配置
	CacheKeyContractDepthSource         = CacheKeyProjectPrefix + "contract:depth:source"         // 合约铺单来源配置
	CacheKeyKline                       = CacheKeyProjectPrefix + "kline"                         // K线数据
	CacheKeyBasePriceKline              = CacheKeyProjectPrefix + "base:price:kline"              // K线基础价格
	CacheKeyRecentTrade                 = CacheKeyProjectPrefix + "order:recent:"                 // 近期交易
	CacheKeyApplies                     = CacheKeyProjectPrefix + "applies"                       // 涨跌幅
	CacheKeyCoinListByID                = CacheKeyProjectPrefix + "coin:list:id"                  // 币种列表 币种id为域
	CacheKeyCoinListByName              = CacheKeyProjectPrefix + "coin:list:name"                // 币种列表 币种名为域
	CacheKeyMarginCoinListByID          = CacheKeyProjectPrefix + "coin:margin:list:id"           // 保证金币种列表 币种id为域
	CacheKeyMarginCoinListByName        = CacheKeyProjectPrefix + "coin:margin:list:name"         // 保证金币种列表 币种名为域
	CacheKeyTradeCounter                = CacheKeyProjectPrefix + "trade:counter"                 // 用户下单计数
	CacheKeyMatchOrder                  = CacheKeyProjectPrefix + "match:order"                   // 撮合订单缓存(停止服务时用）
	CacheKeyRCOrder                     = CacheKeyProjectPrefix + "rc:order"                      // Rc订单缓存(停止服务时用）
	CacheKeyMatchMsgQueue               = CacheKeyProjectPrefix + "match:msg:queue"               // 撮合消息缓存

	CacheKeyInviteCodeMaxId             = CacheKeyProjectPrefix + "invite:CodeMaxIDList"        // 当前生成的邀请码列表的最大id
	CacheKeyInviteCodeList              = CacheKeyProjectPrefix + "invite:CodeList"             // 未使用邀请码列表
	CacheKeyInviteCodeGenLock           = CacheKeyProjectPrefix + "invite:CodeGenLock"          // 邀请码生成锁
	CacheKeyAuthCode                    = CacheKeyProjectPrefix + "authCode:"                   // 短信验证码
	CacheKeyUserAction                  = CacheKeyProjectPrefix + "userAction"                  // 用户操作标记(找回密码,修改手机...)
	CacheKeyLoginInfo                   = CacheKeyProjectPrefix + "login:info:"                 // 用户登录信息
	CacheKeyUserIDGenBasic              = CacheKeyProjectPrefix + "uid:basic"                   // 用户id生成要素
	CacheKeySafeGuard                   = CacheKeyProjectPrefix + "safe:guard:"                 // 用户安全保护,用于修改密码后24小时内禁止用户提币
	CacheKeyBindAccountLock             = CacheKeyProjectPrefix + "lock:account"                // 绑定账号锁
	CacheKeyDeleteAccountLock           = CacheKeyProjectPrefix + "lock:account:delete:"        // 注销账号锁
	CacheKeyAssertRecordLock            = CacheKeyProjectPrefix + "assert:record:lock"          // 用于判断记录是否存在
	CacheKeyRechargeLock                = CacheKeyProjectPrefix + "recharge:lock"               // 用于给充币回调加锁
	CacheKeyWithdrawLock                = CacheKeyProjectPrefix + "withdraw:lock"               // 用于给用户提币操作加锁
	CacheKeyWithdrawAutoTask            = CacheKeyProjectPrefix + "withdraw:auto"               // 提币自动审核任务列表
	CacheKeyLockWithdrawOperate         = CacheKeyProjectPrefix + "withdraw:operate:"           // 提币审核操作锁
	CacheKeyLockLegalAuditOperate       = CacheKeyProjectPrefix + "legal:order:operate:"        // 法币订单审核操作锁
	CacheKeyLegalPlaceLock              = CacheKeyProjectPrefix + "legal:place:lock:"           // 法币下单锁
	CacheKeySimAssetIncreaseLock        = CacheKeyProjectPrefix + "sim:asset:increase:lock"     // 用于给用户领取模拟资产操作加锁
	CacheKeySimAssetDecreaseLock        = CacheKeyProjectPrefix + "sim:asset:decrease:lock"     // 用于给用户减少模拟资产操作加锁
	CacheKeyIndexContractDealLock       = CacheKeyProjectPrefix + "index:contract:deal:lock"    //合约指数价格处理锁
	CacheKeyPlanCloseLock               = CacheKeyProjectPrefix + "plan:close:"                 //计划条件单锁
	CacheKeyPlanOpenLock                = CacheKeyProjectPrefix + "plan:open:"                  //计划条件开仓单锁
	CacheKeyBannerList                  = CacheKeyProjectPrefix + "banner:list:%d"              // 轮播图列表缓存
	CacheKeyNoticeList                  = CacheKeyProjectPrefix + "notice:list:%d"              // 公告列表
	CacheKeyNoticeDetail                = CacheKeyProjectPrefix + "notice:detail:%d"            // 公告详情
	CacheKeyImportantNoticeList         = CacheKeyProjectPrefix + "notice:impartant:list:%d"    // 重要通知列表
	CacheKeyAboutCommunity              = CacheKeyProjectPrefix + "about:community"             // 关于社群详情
	CacheKeyNewVersion                  = CacheKeyProjectPrefix + "version:new:%d:%d:%d"        // app升级信息
	CacheKeyAppVersionStore             = CacheKeyProjectPrefix + "version:store:"              // app商店下载页链接
	CacheKeyFollowConfig                = CacheKeyProjectPrefix + "follow:config"               // 跟单全局配置
	CacheKeyHotfixPatch                 = CacheKeyProjectPrefix + "version:hotfix:"             // 热升级补丁
	CacheKeyDownloadStatistic           = CacheKeyProjectPrefix + "statistic:download:%s:%d"    // app下载统计
	CacheKeyOnlineUserStatistic         = CacheKeyProjectPrefix + "statistic:onlineUser:%s:%d"  // 在线用户统计
	CacheKeyOnlineUserStatisticWithUser = CacheKeyProjectPrefix + "statistic:user:online:%s:%d" // 在线用户统计(使用{uid}-{invite_parent} 方式)

	CacheKeySyncLegalStatLock    = CacheKeyProjectPrefix + "lock:sync:legal:stat:"    // 同步前一日法币统计锁
	CacheKeyMailStatDailyLock    = CacheKeyProjectPrefix + "lock:mail:stat:daily:"    // 日报邮件任务锁
	CacheKeyMailStatHalfLock     = CacheKeyProjectPrefix + "lock:mail:stat:half:"     // 半日报邮件任务锁
	CacheKeyMailErrorCollectLock = CacheKeyProjectPrefix + "lock:mail:error:collect:" // 日志收集邮件任务锁

	CacheKeyUserOpenLock         = CacheKeyProjectPrefix + "trade:contract:open" //用户合约开仓锁
	CacheKeyUserContractSideLock = CacheKeyProjectPrefix + "trade:contract:side" //用户合约方向锁

	CacheKeyFollowUserOpenLock = CacheKeyProjectPrefix + "trade:contract:follow:open" //跟单用户合约开仓锁

	CacheKeyBuyCount  = CacheKeyProjectPrefix + "contract:buy:count"  //看涨人数
	CacheKeySellCount = CacheKeyProjectPrefix + "contract:sell:count" //看跌人数

	CacheKeyUserRiskPoint = CacheKeyProjectPrefix + "user:risk:point:add" //用户risk加点

	CacheKeyContratIndicator   = CacheKeyProjectPrefix + "contract:indicator"    //合约指标
	CacheKeyContratDragSupport = CacheKeyProjectPrefix + "contract:drag:support" //合约支撑位

	CacheKeyUserCachePosDeal   = CacheKeyProjectPrefix + "contract:position:cache.lock" //合约账户持仓缓存修改锁
	CacheKeyPosition           = CacheKeyProjectPrefix + "contract:positions:"          //合约账户用户持仓
	CacheKeyFollowPosition     = CacheKeyProjectPrefix + "follow:positions:"            //跟单账户用户持仓
	CacheKeyPositionUser       = CacheKeyProjectPrefix + "contract:position:user"       //合约账户持仓用户
	CacheKeyFollowPositionUser = CacheKeyProjectPrefix + "follow:position:user"         //跟单账户持仓用户
	CacheLockKeyIsTransfer     = CacheKeyProjectPrefix + "lock:transfer:unDone:"        // 正在进行划转操作

	CacheKeySysConfig = CacheKeyProjectPrefix + "system:config" //系统配置项

	CacheKeyShareImages = CacheKeyProjectPrefix + "share:image" // 分享图片资源
	CacheKeyShareTexts  = CacheKeyProjectPrefix + "share:text"  // 分享文案资源

	UserVerifyIdNumberRelations = CacheKeyProjectPrefix + "verify:id_number:relations" // 身份信息审核中用户id与审核任务关系表
	UserVerifyIdNumberTaskList  = CacheKeyProjectPrefix + "verify:id_number:task:list" // 身份信息审核中任务id列表
	UserVerifyIdNumberTasks     = CacheKeyProjectPrefix + "verify:id_number:task:"     // 身份信息审核任务
	UserVerifyFaceTaskList      = CacheKeyProjectPrefix + "verify:face:task:list"      // 面部信息审核任务id列表
	UserVerifyFaceTasks         = CacheKeyProjectPrefix + "verify:face:task:"          // 面部信息审核任务
	UserVerifyFaceH5TaskList    = CacheKeyProjectPrefix + "verify:faceH5:task:list"    // 面部信息审核任务id列表(h5)
	UserVerifyFaceH5Tasks       = CacheKeyProjectPrefix + "verify:faceH5:task:"        // 面部信息审核任务(h5)
	UserVerifyFaceThreshold     = CacheKeyProjectPrefix + "verify:face:threshold"      // 面部信息审核任务匹配值
	UserVerifyWithdrawTasks     = CacheKeyProjectPrefix + "verify:withdraw:task:"      // 提币身份审核任务
	UserVerifyWithdrawH5Tasks   = CacheKeyProjectPrefix + "verify:withdrawH5:task:"    // 提币身份审核任务(h5)
	UserVerifyWithdrawKeyList   = CacheKeyProjectPrefix + "verify:withdraw:key:list"   // 提币身份审核任务key列表
	UserVerifyFaceKeyList       = CacheKeyProjectPrefix + "verify:face:key:list"       // 活体审核任务key列表

	CacheKeyActivityTraderList      = CacheKeyProjectPrefix + "activity:trader:list"       //交易活动列表
	CacheKeyActivityTraderChildList = CacheKeyProjectPrefix + "activity:trader:child:list" //子活动数据

	CacheKeyUidSequence          = CacheKeyProjectPrefix + "uid:sequence:"          // 用户id序号
	CacheKeyUidPool              = CacheKeyProjectPrefix + "uid:pool:"              // 用户id池
	CacheKeyMaxUserAvailAPICount = CacheKeyProjectPrefix + "server:config:api:max"  // 用户最大可用api数量
	CacheKeyServerConfigCommon   = CacheKeyProjectPrefix + "server:config:common"   // 服务器配置信息
	CacheKeyServerConfigHosts    = CacheKeyProjectPrefix + "server:config:hosts"    // 服务器域名列表
	CacheKeyServerConfigHostsV2  = CacheKeyProjectPrefix + "server:config:hosts:v2" // 服务器域名列表
	CacheKeyUserApiInfos         = CacheKeyProjectPrefix + "openapi:user:api:list"  //openapi缓存列表
	CacheKeyRobotMarketPosition  = CacheKeyProjectPrefix + "robot:position:"        // 做市商持仓数缓存key

	CacheKeyContractFastClose = CacheKeyProjectPrefix + "close:all:contract" //合约一键平仓锁
	CacheKeyAppStatusVersion  = CacheKeyProjectPrefix + "app:status:version" // app审核版本控制

	// 活动相关
	CacheKeyGiftCashLockDevice         = CacheKeyProjectPrefix + "gift:cash:lock:device:"         // 赠金领取设备锁
	CacheKeyGiftCashActivity           = CacheKeyProjectPrefix + "gift:cash:activity"             // 赠金活动列表
	CacheKeyGiftCashReceiveExpireTask  = CacheKeyProjectPrefix + "gift:cash:task:expire:receive"  // 赠金未领取超时任务
	CacheKeyGiftCashActiveExpireTask   = CacheKeyProjectPrefix + "gift:cash:task:expire:active"   // 赠金未激活超时任务
	CacheKeyGiftCashRecoveryExpireTask = CacheKeyProjectPrefix + "gift:cash:task:expire:recovery" // 赠金回收超时任务
	CacheKeyOrderCache                 = CacheKeyProjectPrefix + "order:cache:"                   // 订单缓存
)

const (
	CacheKeyContractKey           = CacheKeyProjectPrefix + "user:contract:account"        //用户交易账户
	CacheKeyFullRiskRate          = CacheKeyProjectPrefix + "user:full:risk:rate"          //用户账户风险度
	CacheKeyContractRiskRate      = CacheKeyProjectPrefix + "user:full:contract:risk:rate" //用户单合约风险率
	CacheKeyFullAccountForcePrice = CacheKeyProjectPrefix + "user:full:position:force"     //全仓账户合约强平价

	CacheKeyFullRiskRateSend = CacheKeyProjectPrefix + "user:risk:rate:send"
)

const (
	DealerFollowPosition = CacheKeyProjectPrefix + "follow:trader:list"
	DealerClosePosList   = CacheKeyProjectPrefix + "trader:follow:close:pos"
)

const (
	CacheHedgeWarnEmailKey  = CacheKeyProjectPrefix + "warn:hedge:emails"  //报警邮件配置列表
	CacheMarketWarnEmailKey = CacheKeyProjectPrefix + "warn:market:emails" //行情报警邮件列表
	CacheMaketWarnTypeKey   = CacheKeyProjectPrefix + "warn:market:type"
)

const (
	CacheHedgeKeyConfig                        = CacheKeyProjectPrefix + "hedge:account:config"    // 对冲账户缓存
	CacheHedgeKeyOkexContracts                 = CacheKeyProjectPrefix + "hedge:okex:contracts"    //okex 合约信息
	CacheHedgeKeyHedgeContractPos              = CacheKeyProjectPrefix + "hedge:account:position"  //okex 合约持仓
	CacheHedgeKeyDealingHedge                  = CacheKeyProjectPrefix + "hedge:dealing:lock"      //处理对冲锁
	CacheHedgeKeyAccountContractLever          = CacheKeyProjectPrefix + "hedge:account:contract:" //设置Api合约杠杠
	CacheHedgeKeyContractLastFailId            = CacheKeyProjectPrefix + "hedge:contract:fail:id:" // 合约账户最后错误id
	CacheHedgeWarn                             = CacheKeyProjectPrefix + "hedge:warn:last"
	CacheMarketLastWarn                        = CacheKeyProjectPrefix + "market:warn:last"
	CacheHedgeEfficiency                       = CacheKeyProjectPrefix + "hedge:efficiency"          // 对冲效率记录
	CacheHedgingEfficiencyAbnormalCountery     = CacheKeyProjectPrefix + "hedge:efficiency:counter:" // 对冲效率异常计数
	CacheHedgingEfficiencyAbnormalDisableState = CacheKeyProjectPrefix + "hedge:efficiency:disable"  // 对冲效率异常检测关闭状态
	CacheHedgingLastQueryAssets                = CacheKeyProjectPrefix + "hedge:last:assets:"        // 记录最后查询资产数据的时间戳
)

const (
	CacheHegeBackAddAPILock = CacheKeyProjectPrefix + "back:api:add:update"
)

const (
	CacheLastNews = CacheKeyProjectPrefix + "news:last"
)

const (
	CacheUpdateNicknameLock   = CacheKeyProjectPrefix + "lock:nickname"
	CacheUpdateNicknameENLock = CacheKeyProjectPrefix + "lock:nicknameEN"
)

const (
	CacheLeaderListOrderByNormal          = CacheKeyProjectPrefix + "leader:list:normal:"
	CacheLeaderListOrderByProfit          = CacheKeyProjectPrefix + "leader:list:profit:amount:"
	CacheLeaderListOrderByProfitRate      = CacheKeyProjectPrefix + "leader:list:profit:rate:"
	CacheLeaderListOrderByWin             = CacheKeyProjectPrefix + "leader:list:win:"
	CacheLeaderListOrderByTrades          = CacheKeyProjectPrefix + "leader:list:trades:"
	CacheLeaderListOrderByTotalProfitRate = CacheKeyProjectPrefix + "leader:list:total:profit:rate:"
	CacheLeaderDetailData                 = CacheKeyProjectPrefix + "leader:detail"
	CacheLeaderFollowDetailData           = CacheKeyProjectPrefix + "leader:follow:detail"
	CacheFollowSetLock                    = CacheKeyProjectPrefix + "lock:follow:set"
	CacheFollowExitLock                   = CacheKeyProjectPrefix + "lock:follow:exit"
)

const (
	CacheFullForceKey   = CacheKeyProjectPrefix + "force:lock:"      //全仓强平锁
	CacheForceQueue     = CacheKeyProjectPrefix + "force:queue"      //全平队列
	CacheForceQueueLock = CacheKeyProjectPrefix + "force:queue:lock" //全平队列处理锁

	CacheFastCloseQueue = CacheKeyProjectPrefix + "fast:close:queue" //一键全平队列

	CacheForceKey     = CacheKeyProjectPrefix + "force:position:"      //普通强平锁仓
	CacheWareForceKey = CacheKeyProjectPrefix + "force:position:ware:" //普通强平锁仓

	CacheReturnMatch = CacheKeyProjectPrefix + "match:order:return"

	CacheRequest = CacheKeyProjectPrefix + "request"

	//跟单全仓强平锁
	CacheFollowAccountFullForceKey = CacheKeyProjectPrefix + "follow:full:force:lock:" //全仓强平锁
)

const (
	CacheCloseTask       = CacheKeyProjectPrefix + "close:task"
	CacheCloseTaskBackUp = CacheKeyProjectPrefix + "close:task:backup"
)

// 触发重复锁
const (
	CacheTriggerCoreFullForceClose = CacheKeyProjectPrefix + "core:full:force:trigger"
	CacheTriggerCoreWareForceClose = CacheKeyProjectPrefix + "core:ware:force:trigger"
	CacheTriggerCorePlanClose      = CacheKeyProjectPrefix + "trigger:core:plan:close"
	CacheTriggerCoreStop           = CacheKeyProjectPrefix + "trigger:core:stop"
	CacheTriggerPlanOpen           = CacheKeyProjectPrefix + "trigger:plan:open"
	CacheTriggerFollowForceClose   = CacheKeyProjectPrefix + "follow:force:trigger"
	CacheTriggerFollowPlanClose    = CacheKeyProjectPrefix + "trigger:follow:plan:close"
)

const (
	CacheKeyContractSideTradeCount = CacheKeyProjectPrefix + "contract:side:largeTrade:count" //合约当方向大额成交数
	CacheKeyContractTradeAmount    = CacheKeyProjectPrefix + "contract:trade:amount"          //合约同时交易量
	CacheKeyContractWarningLock    = CacheKeyProjectPrefix + "contract:warning:lock"
	CacheKeyContractNetPosCount    = CacheKeyProjectPrefix + "contract:net:position:count"  //合约净持仓统计
	CacheKeyContractBuyPosCount    = CacheKeyProjectPrefix + "contract:buy:position:count"  //合约买持仓统计
	CacheKeyContractSellPosCount   = CacheKeyProjectPrefix + "contract:sell:position:count" //合约卖持仓统计
	CacheKeyContractDepthBuyValid  = CacheKeyProjectPrefix + "contract:depth:buy:count"     //合约深度有效期买统计
	CacheKeyContractDepthSellValid = CacheKeyProjectPrefix + "contract:depth:sell:count"    //合约深度有效期买统计
)

const (
	CacheContractBucketAmountFactor = CacheKeyProjectPrefix + "contract:bucket:factor"     // 合约对敲数量系数
	CacheContractValid24Volume      = CacheKeyProjectPrefix + "contract:market:valid"      // 合约2小时有效的24小时成交量
	CacheContractValidMinuteVolume  = CacheKeyProjectPrefix + "contract:market:valid:1m"   // 合约分钟成交量
	CacheContractMarket24           = CacheKeyProjectPrefix + "contract:market:day:volume" // 合约2小时有效的24小时成交量
	CacheContractHourRandFactor     = CacheKeyProjectPrefix + "contract:hour:factor"       //合约小时随机系数
)

const (
	CacheStatusKeyNotice  = CacheKeyProjectPrefix + "status:notice:" // 公告缓存状态,防止空数据时频繁访问数据库
	CacheStatusKeyBanner  = CacheKeyProjectPrefix + "status:banner:" // 轮播图缓存状态,防止空数据时频繁访问数据库
	CacheStatusKeyVersion = CacheKeyProjectPrefix + "status:version" // app版本信息缓存状态,防止空数据时频繁访问数据库
)

const (
	CacheKeyWalletCoinDictV2 = CacheKeyProjectPrefix + "wallet:coin:dict:v2" // 只存储币种名称对应的大钱包名称对应关系,1对多 hash {coinName:{protocol:walletName...}...}
	CacheKeyWalletCoinConfig = CacheKeyProjectPrefix + "wallet:coin:config"  // 存储大钱包名称对应的配置信息,1对1 hash {walletName: {info}...}
)

const (
	CacheKeyContractGivingCount      = CacheKeyProjectPrefix + "contract:bucket:giving:count" //合约对敲放量计数器
	CacheKeyContractLastGiving       = CacheKeyProjectPrefix + "contract:bucket:last:giving"  //合约上次对敲上限
	CacheKeyContractHourDayRandIndex = CacheKeyProjectPrefix + "contract:day:hourRandIndex"   //合约小时拆分系数缓存打散序列
)

const (
	CacheKeyMarket24History    = CacheKeyProjectPrefix + "contract:market:24h:history"
	CacheKeyLastBucketDuration = CacheKeyProjectPrefix + "contract:bucket:duration:current"
)

const (
	CacheKeyDepthSpotIndexBaseMinuteHistory = CacheKeyProjectPrefix + "spot:index:base:depth"   // 铺单现货基础指数历史
	CacheKeySpotIndexBaseMinuteHistory      = CacheKeyProjectPrefix + "spot:index:base:1m"      // 现货基础指数分钟历史
	CacheKeyContractDynamicBaseDiff         = CacheKeyProjectPrefix + "contract:move:base:diff" //合约移动基差值
)

const (
	CacheLockLegalWithdraw = CacheKeyProjectPrefix + "lock:legal:withdraw:" // 法币入金禁止提币锁
)

const (
	CacheKeySysTradeConf     = CacheKeyProjectPrefix + "system:traders:key" // 全局系统交易配置
	CacheKeySysUserQuotaConf = CacheKeyProjectPrefix + "system:quota:conf"  // 全局系统用户限额配置
)

const (
	CacheKeyWithdrawDailyUsedAmount = CacheKeyProjectPrefix + "withdraw:daily:used:%s:0102" // 保存用户当日提币已用额度
	CacheKeyWithdrawWhiteList       = CacheKeyProjectPrefix + "withdraw:white:list"         // 提币白名单用户,不进行提币次数限制
)

const (
	CacheKeySafeUserNewTotpSecret    = CacheKeyProjectPrefix + "safe:secret:totp:new:"
	CacheKeySafeTradeVerifyState     = CacheKeyProjectPrefix + "safe:trade:verify:state:"
	CacheKeySafeFundPasswordMiscount = CacheKeyProjectPrefix + "safe:password:fund:miscount:"
	CacheKeySafeMarkVerifyFund       = CacheKeyProjectPrefix + "safe:mark:verify:fund:"
	CacheKeySafeMarkVerifyCode       = CacheKeyProjectPrefix + "safe:mark:verify:code:"
)

const (
	CacheTaskKeyWithdrawVerifyList = CacheKeyProjectPrefix + "task:withdraw:list"
)

const (
	CacheLockUserAPIModify = CacheKeyProjectPrefix + "lock:user:api:modify:"
)

const (
	DepthPriceSourceException       = CacheKeyProjectPrefix + "depth:price:source:exc"
	DepthPriceSourceSwitchException = CacheKeyProjectPrefix + "depth:switch:exc:"
	DepthPriceAvgWarn               = CacheKeyProjectPrefix + "depth:price:avg:warn"
)

const (
	CacheKeyPriceCount     = CacheKeyProjectPrefix + "price:count"
	CacheKeyAlarmRecord    = CacheKeyProjectPrefix + "alarm"
	CacheKeyIndexException = CacheKeyProjectPrefix + "index:exception"
)

const CacheKeyWarnExpire = CacheKeyProjectPrefix + "warn:expire"
const CacheKeyAlarmDuplicate = CacheKeyProjectPrefix + "duplicate:alarm"

// 服务缓存key
const (
	CacheServerMatchSupport = CacheKeyProjectPrefix + "server"
)

const (
	CacheKeySyncPositionDuplicate          = CacheKeyProjectPrefix + "full:position:sync"
	CacheKeyUserContractAccountSync        = CacheKeyProjectPrefix + "user:contract:position:sync"
	CacheKeyUserContractAccountSyncForRisk = CacheKeyProjectPrefix + "user:contract:risk:position:sync"
	CacheKeyUserFollowAccountSync          = CacheKeyProjectPrefix + "user:follow:position:sync"
)

const CacheMarketWarnReceive = CacheKeyProjectPrefix + "warn:receiver" //报警邮件缓存

const (
	CacheKeyUserPlaceLimit = CacheKeyProjectPrefix + "user:contract:netPos:limit"
)

const (
	CacheResponseKeyActivityMyWard = CacheKeyProjectPrefix + "cache:activity:myWard:"
)

const (
	CacheMsgClosing = CacheKeyProjectPrefix + "cache.match.msg:closing"
	CacheKeyUserAc  = CacheKeyProjectPrefix + "cache.user.contract:ac"
)

const (
	CacheOrderMsgSeqKey          = CacheKeyProjectPrefix + "order:deal:seq"             //订单消息生产序列
	CacheOrderMsgDealLock        = CacheKeyProjectPrefix + "order:closing:deal:lock"    //清算订单处理锁
	CacheCurOrderSeqID           = CacheKeyProjectPrefix + "order:closing:deal:cur:seq" //上次处理的订单订单消息序列
	CacheCurOrderDealing         = CacheKeyProjectPrefix + "order:closing:dealing"      //待处理订单消息
	CacheOrderWaitFinish         = CacheKeyProjectPrefix + "order:closing:waitFinish"   //待处理订单待完成消息
	CacheOrderWaitFinishDealLock = CacheKeyProjectPrefix + "order:closing:lock:waitFinish"
)

const (
	CacheForceFollowHandler = CacheKeyProjectPrefix + "force:handler"
)

const (
	CacheKeyExchangePreOrder       = CacheKeyProjectPrefix + "exchange:preorder:"
	CacheKeyExchangeUserUsed       = CacheKeyProjectPrefix + "exchange:user:used:%s:%d"
	CacheLockExchangePreOrder      = CacheKeyProjectPrefix + "lock:exchange:preorder:"
	CacheLockExchangeUserUsed      = CacheKeyProjectPrefix + "lock:exchange:user:used:%d:%s"
	CacheKeyExchangeOrderTask      = CacheKeyProjectPrefix + "task:exchange:order"
	CacheKeyExchangeHedgeOrderTask = CacheKeyProjectPrefix + "task:exchange:hedge:order"

	CacheKeyFinanceExchangeBalanceAlarm = CacheKeyProjectPrefix + "finance:exchange:balance:alarm"
	CacheKeyExchangeHedgeSource         = CacheKeyProjectPrefix + "task:exchange:hedge:source"
	CacheKeyExchangeHedgeConfig         = CacheKeyProjectPrefix + "task:exchange:hedge:config" //兑换对冲配置
	CacheKeyExchangePriceLimit          = CacheKeyProjectPrefix + "exchange:price:limit"       //对冲源价格限制
	CacheKeyExchangeAvgPrice            = CacheKeyProjectPrefix + "exchange:hedge:price:avg"   //对冲源单位均价
)

const (
	CacheKeyThirdLegalAllRates  = CacheKeyProjectPrefix + "third:legal:rates:"   // 三方买币汇率
	CacheKeyThirdLegalAllLimits = CacheKeyProjectPrefix + "third:legal:limits:"  // 三方买币限制
	CacheKeyThirdMerchants      = CacheKeyProjectPrefix + "third:merchants:list" //三方商家
)

const (
	QueueNameForceDealing = CacheKeyProjectPrefix + "queue:force:dealing"
)

const (
	CacheKeyUserWsOnLine = CacheKeyProjectPrefix + "ws:user"
)

const (
	CacheKeyPlatformProvisionsOfRiskHistory    = CacheKeyProjectPrefix + "collect:riskProvisions:history:%d:%s" // 风险准备金数据
	CacheKeyPlatformProvisionsOfRiskAdjustConf = CacheKeyProjectPrefix + "conf:riskProvisions:adjustConf"       // 风险准备金调整量
)
