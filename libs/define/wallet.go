package define

type AssetType uint64

func (at AssetType) Valid() bool {
	switch at {
	case AssetTypeForWallet, AssetTypeForUsdtTrade, AssetTypeForUsdtFollow, AssetTypeForUsdTrade, AssetTypeForSpotTrade:
		return true
	}
	return false
}

const (
	AssetTypeForWallet     AssetType = 1 << iota // 1<<0-资金账户
	AssetTypeForUsdtTrade                        // 1<<1-交易账户
	AssetTypeForUsdtFollow                       // 1<<2-跟单账户
	AssetTypeForUsdTrade                         // 1<<3-usd交易账户
	AssetTypeForSpotTrade                        // 1<<4-现货账户
)
const AssetTypeAll = AssetTypeForWallet | AssetTypeForUsdtTrade | AssetTypeForUsdtFollow | AssetTypeForUsdTrade | AssetTypeForSpotTrade // 全部账户
