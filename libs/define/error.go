package define

import (
	"fmt"
)

const (
	ErrBackCodeBusy         = 2001 //系统繁忙
	ErrBackCodeParam        = 2002 //参数错误
	ErrBackCodeAPIAvailable = 2003 //添加api当前不可用
	ErrBackCodeAPIExist     = 2004 //api已存在
	ErrBackCodeOverPosition = 2005 //当前持仓超过阈值
)

const (
	ErrBackMsgBusy         = "系统繁忙"       //系统繁忙
	ErrBackMsgParam        = "参数错误"       //参数错误
	ErrBackMsgAPIAvailable = "当前API当前不可用" //添加api当前不可用
	ErrBackMsgAPIExist     = "API已存在"     //api已存在
	ErrBackMsgOverPosition = "当前持仓超过阈值"   //当前持仓超过阈值
)

// 定义API错误码
const (
	ErrCodeNone = 0 // 正确

	// 公共错误
	ErrCodeBusy                   = 1001 // 系统繁忙
	ErrCodeActionInvalid          = 1002 // 无效请求
	ErrCodeServerNotAvailable     = 1003 // 服务暂时不可用
	ErrCodeParam                  = 1004 // 请求参数错误
	ErrCodeRequestTimeException   = 1005 // 请求时间异常
	ErrCodeSignature              = 1006 // 签名验证失败
	ErrCodeForceUpdate            = 1007 // 需要强制升级
	ErrCodeAuthCode               = 1008 // 验证码错误
	ErrCodeAuthCodeInvalid        = 1009 // 无效的验证码
	ErrCodeImgAuthCode            = 1010 // 图形验证码错误
	ErrCodeInvalidInviteCode      = 1011 // 无效的邀请码
	ErrCodeActionTimeout          = 1012 // 操作超时
	ErrCodeCWSParam               = 1013 // cws返回参数错误,建议驳回申请
	ErrCodeResubmit               = 1014 // 重复提交
	ErrCodeStatus                 = 1015 // 状态错误
	ErrCodeFrequent               = 1016 // 请求频繁
	ErrCodeFileTooLarge           = 1017 // 文件过大
	ErrCodeNonsupportFileType     = 1018 // 不支持的文件类型
	ErrCodeTextLengthLimit        = 1019 // 字符长度超限
	ErrCodeLimitCountry           = 1020 // 暂不对当前地区提供服务
	ErrCodeFeatureDisabled        = 1021 // 暂不提供该服务
	ErrCodeAPIFrequent            = 1022 // 请求频次过多，请稍后再试
	ErrCodeConfirmationIsRequired = 1023 // 需要进行确认

	// 用户相关
	ErrCodeShouldLogin                   = 1101 // 未登录
	ErrCodeUserNotExist                  = 1102 // 用户不存在
	ErrCodeAccountIsExist                = 1103 // 账号已存在
	ErrCodeAccountFormat                 = 1104 // 账号格式错误
	ErrCodeTooLongAccount                = 1105 // 账号过长
	ErrCodeLoginElseWhere                = 1106 // 用户在其它地方登陆
	ErrCodeFundPwdNotSet                 = 1107 // 资金密码未设置
	ErrCodeLoginPwdNotSet                = 1108 // 登录密码未设置
	ErrCodeLoginPwdIncorrect             = 1109 // 登录密码不正确
	ErrCodeLoginPwdSameAsFundPwd         = 1110 // 登录密码不能与资金密码相同
	ErrCodeFundPwdSameAsLoginPwd         = 1111 // 资金密码不能与登录密码相同
	ErrCodeLoginNotAllowed               = 1112 // 禁止登录
	ErrCodeTradeNotAllowed               = 1113 // 当前用户被禁止交易
	ErrCodeWithdrawNotAllowed            = 1114 // 当前用户被禁止提币
	ErrCodeHasAccountLock                = 1115 // 其他用户正在进行对当前账号进行注册或绑定操作,请稍后再试
	ErrCodeNeedVerify                    = 1116 // 用户未认证
	ErrCodeIdNumberVerified              = 1117 // 证件信息已经审核通过
	ErrCodeIdNumberExist                 = 1118 // 证件号已被占用
	ErrCodeVerifyIdNumber                = 1119 // 无效的身份证号
	ErrCodeVerifyValidity                = 1120 // 姓名与身份证号不匹配
	ErrCodeNeedVerifyIdentity            = 1121 // 需要先通过身份信息审核
	ErrCodeFaceVerified                  = 1122 // 面部认证已通过
	ErrCodeExisted                       = 1123 // 已存在
	ErrCodeTradeIDNotMatch               = 1124 // 当前身份无法执行此操作
	ErrCodeDealerFollowLimit             = 1125 // 当前交易员跟随人数已满
	ErrCodeDealerCanNotFollow            = 1126 // 交易员不可进行跟单绑定
	ErrCodeNoFollow                      = 1127 // 当前未跟随
	ErrCodeNonsupportModifyEmail         = 1128 // 不支持修改邮箱 // 机翻
	ErrCodeModifySafeModeCount           = 1129 // 当前操作需要有两种以上安全验证 // 机翻
	ErrCodeSafePhoneCodeInvalid          = 1130 // 无效的手机验证码 // 机翻
	ErrCodeSafeEmailCodeInvalid          = 1131 // 无效的邮箱验证码 // 机翻
	ErrCodeSafeTotpCodeInvalid           = 1132 // 无效的验证器验证码 // 机翻
	ErrCodeSafePhoneCodeExpire           = 1133 // 手机验证码已过期,请重新获取 // 机翻
	ErrCodeSafeEmailCodeExpire           = 1134 // 邮箱验证码已过期,请重新获取 // 机翻
	ErrCodeSafeNeedVerify                = 1135 // 需要进行两步验证 // 机翻
	ErrCodeSafeNeedFundPasswordVerify    = 1136 // 需要进行资金密码校验 // 机翻
	ErrCodeSafeFundPassword              = 1137 // 资金密码错误 // 机翻
	ErrCodeSameOldPassword               = 1138 // 与旧密码相同 // 机翻
	ErrCodePaymentsOverLimit             = 1139 // 收款方式达上限 //机翻
	ErrCodeApiCreateOverLimit            = 1140 // API创建数达到上限 //机翻
	ErrCodeApiBindIPOverLimit            = 1141 // 绑定IP数超出上限 //机翻
	ErrCodeApiBindIPv6OverLimit          = 1142 // 最多仅支持绑定两个IPv6地址 //机翻
	ErrCodeApiCreateNoPermission         = 1143 // 当前用户暂无创建和修改API权限 //机翻
	ErrCodeApiIPAddress                  = 1144 // IP地址不正确 //机翻
	ErrCodeApiOverdue                    = 1145 // 当前api已过期 //机翻
	ErrCodeWithdrawAddrCountLimit        = 1146 // 提币地址已达到最大数量，添加失败。 //机翻
	ErrCodeWithdrawAddressExisted        = 1147 // 当前地址已存在
	ErrCodeSafeSpareEmailCodeInvalid     = 1148 // 无效的备用邮箱验证码 // 机翻
	ErrCodeSafeSpareEmailCodeExpire      = 1149 // 备用邮箱验证码已过期,请重新获取 // 机翻
	ErrCodeNonsupportAccountType         = 1150 // 不支持的账户模式 // 机翻
	ErrCodeNonsupportPositionSplitMode   = 1151 // 您当前持有仓位或未成交的委托，不允许更改分仓模式。 // 机翻
	ErrCodeDeleteAccountAtRiskStateLimit = 1152 // 账户存在风控状态，请联系客服
	ErrCodeDeleteAccountHasUndoneOrder   = 1153 // 账户存在持仓，委托及跟随交易员，请确认以上内容全部取消后再试
	ErrCodeAccountIsDeleteState          = 1154 // 此账户已注销，如需再次注册请联系官方客服
	ErrCodeNonsupportPhoneNumber         = 1155 // 暂不支持手机号

	// 资产相关
	ErrCodeFewTransfer                      = 1201 // 小于最小单笔转入限制
	ErrCodeManyTransfer                     = 1202 // 超过最大单笔转出限制
	ErrCodeCoinNotSupportTransfer           = 1203 // 币种暂不支持划转
	ErrCodeBalanceInsufficient              = 1204 // 资产不足
	ErrCodeInvalidAddress                   = 1205 // 无效的提币地址
	ErrCodeWithdrawHasApply                 = 1206 // 当前有审核中的提币申请
	ErrCodeWithdrawHasPassed                = 1207 // 当日已经提币过
	ErrCodeDepositInsufficient              = 1208 // 保证金不足
	ErrCodeNotSupportWithdraw               = 1209 // 币种暂不支持提币
	ErrCodeWithdrawLessMinLimit             = 1210 // 提币数量小于最小提币量
	ErrCodeWithdrawGreaterMaxLimit          = 1211 // 提币数量大于最大提币量
	ErrCodeTransferInsufficient             = 1212 // 剩余可划转数量不足
	ErrCodeSafeGuardForSetting              = 1213 // 此账号已修改了安全设置。为了您的账号安全，24小时内禁止提币，与法币卖出
	ErrCodeReceivingLimitReached            = 1214 // 模拟资产领取达到限额
	ErrCodeNeedWithdrawVerifyPassed         = 1215 // 需要先完成提币身份认证
	ErrCodeLegalWithdrawSafeGuard           = 1216 // 近期有法币入金,24小时内不支持提币
	ErrCodeSafeGuardForFundPwd              = 1217 // 资金密码连续输入5次错误，并在24小时内禁止提币与法币卖出。
	ErrCodeWithdrawalRestrictionsMail       = 1218 // 提币限制: 请完成邮箱绑定
	ErrCodeInsufficientFollowBalance        = 1219 // 您的跟单可用或授权资金不满足交易员要求的最低账户资金，请转入资金后再进行跟单。
	ErrCodeDealerLimitIsChanged             = 1220 // 交易员修改了配置
	ErrCodeUnverifyWithdrawOver             = 1221 // 未认证用户提币限额超出
	ErrCodeNonsupportTransferAsset          = 1222 // 暂不支持当前所选账户之间的划转
	ErrCodeHasGiftCashNonsupportTransferOut = 1223 // 有冻结赠金的时候,不支持转出
	ErrCodeHasGiftCashDeviceLimit           = 1224 // 领取失败，该设备已领取过赠金。
	ErrCodeAssetIsLiquidise                 = 1225 // 当前资产清算中，稍后再试

	// 交易相关
	ErrCodeContractNotExist                      = 1301 // 合约不存在
	ErrCodeContractNotTrade                      = 1302 // 合约暂不可交易
	ErrCodeNonSupportOptimal                     = 1303 // 暂不支持最优价下单
	ErrCodeNonSupportPrice                       = 1304 // 限价单价格不支持
	ErrCodePositionInsufficient                  = 1305 // 剩余持仓数不足
	ErrCodeTooManyOrderVolume                    = 1306 // 超出剩余可下单张数
	ErrCodeTradeFrequent                         = 1307 // 委托请求频繁 每秒最多49单
	ErrCodeNonSupportChangeDeposit               = 1308 // 全仓模式不支持调整调整保证金
	ErrCodeBelowMinOpenVolume                    = 1309 // 低于最小下单量
	ErrCodeOverMaxOpenVolume                     = 1310 // 大于最大下单量
	ErrCodePositionInvalid                       = 1311 // 当前持仓无效或已平仓
	ErrCodeLeverInvalid                          = 1312 // 禁止切换，存在持仓，与持仓杠杆不一致
	ErrCodeOverPlaceMoney                        = 1313 // 大于可下单金额
	ErrCodePlanOrderLimit                        = 1314 // 达到未触发计划单数量限制
	ErrCodeLimitLEPrice                          = 1315 // 止盈价格需要大于开仓均价(做多)
	ErrCodeStopGEPrice                           = 1316 // 止损价格需要小于开仓均价(做多)
	ErrCodeLimitGEPrice                          = 1317 // 止盈价格需要小于开仓均价(做空)
	ErrCodeStopLEPrice                           = 1318 // 止损价格需要大于开仓均价(做空)
	ErrCodePlanLimitLEPrice                      = 1319 // 止盈价格需要大于触发价格(做多)
	ErrCodePlanStopGEPrice                       = 1320 // 止损价格需要小于触发价格(做多)
	ErrCodePlanLimitGEPrice                      = 1321 // 止盈价格需要小于触发价格(做空)
	ErrCodePlanStopLEPrice                       = 1322 // 止损价格需要大于触发价格(做空)
	ErrCodeLimitEIndex                           = 1323 // 止盈价格不可以等于合约指数
	ErrCodeStopEIndex                            = 1324 // 止损价格不可以等于合约指数
	ErrCodePlanCloseCountLimit                   = 1325 // 止盈止损已经超出6条 请删除后再设置
	ErrCodeMarginModifyBalanceInsufficient       = 1326 // 可用余额不足增加保证金失败
	ErrCodeMarginModifyMarginInsufficient        = 1327 // 保证金不足无法减少
	ErrCodeAccountTypeHasFullPosition            = 1328 // 您当前有全仓模式的持仓请先平仓后再进行切换
	ErrCodeAccountTypeHasFullPlan                = 1329 // 您当前有全仓模式的计划单请先撤销后再进行切换
	ErrCodeAccountTypeHasPartPosition            = 1330 // 您当前有逐仓模式的持仓请先平仓后再进行切换
	ErrCodeAccountTypeHasPartPlan                = 1331 // 您当前有逐仓模式的计划单请先撤销后在进行切换
	ErrCodeLeverHasBuyPartOrder                  = 1332 // 您当前合约多单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
	ErrCodeLeverHasSellPartOrder                 = 1333 // 您当前合约空单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
	ErrCodeNoAccountTypePlace                    = 1334 // 无法以当前模式下单
	ErrCodeContractMarketStop                    = 1335 // 行情中断，下单失败，请稍后再试
	ErrCodeNonsupportSwitchAccountType           = 1336 // 暂不支持切换到指定账户模式
	ErrCodeOverUnitTrade                         = 1337 // 超出合约单位时间交易限制
	ErrCodePartialFailed                         = 1338 // 部分平仓失败 机翻
	ErrCodeNotEnoughVolumeTrade                  = 1339 // 行情失败，没有足够的吃单 下单失败，暂无对手盘可成交。 机翻
	ErrCodeCloseOverPlace                        = 1340 //平仓超过警戒线 机翻
	ErrCodeRiskLess                              = 1341 //低于风险率，禁止开平仓
	ErrCodeOverPositionCount                     = 1342 //超出最大持仓条数
	ErrCodeFollowNotSupport                      = 1343 //合约不支持带单
	ErrCodePlanEntrustOverContractLimit          = 1344 //条件单价格无效,超过合约限价
	ErrCodePlanEntrustLessContractLimit          = 1345 //条件单价格无效,超过合约限价
	ErrCodePlanEntrustLess                       = 1346 //超出方向限价
	ErrCodePlanEntrustMore                       = 1347 //低于方向限价
	ErrCodePositionState                         = 1348 // 此仓位状态已经更新，请查看仓位后再进行设置
	ErrCodeOverMaxSideEntrust                    = 1349 //超出合约单边最大挂单量
	ErrCodeDepthInvalid                          = 1350 //没有足够的深度成交
	ErrCodePriceOverLimit                        = 1351 //当前委托价格超过允许范围
	ErrCodePriceInvalid                          = 1352 //当前委托价格无效
	ErrCodeEntrustCloseVolumeInvalid             = 1353 // 当前限价委托存在本仓位的平仓委托，请撤销后再使用极速平仓
	ErrCodePriceMarketOver                       = 1354 //超出行情保护阈值，取消委托
	ErrCodeInMaintenance                         = 1360 //系统维护
	ErrCodeNetPosException                       = 1361 //净持仓偏向异常
	ErrCodeForceClosing                          = 1362 //强平结算中
	ErrCodeClosing                               = 1363 //合约结算中
	ErrCodeOrderIdUserd                          = 1364 //订单号已存在
	ErrCodeUnfinishedEntrustOrderTooMany         = 1365 //当前未完成委托数过多
	ErrCodeOrderState                            = 1366 // 当前订单状态已更新,请刷新后重试
	ErrCodePlanLimitLEEntrustPrice               = 1367 // 止盈价格需要大于执行价格(做多)
	ErrCodePlanStopGEEntrustPrice                = 1368 // 止损价格需要小于执行价格(做多)
	ErrCodePlanLimitGEEntrustPrice               = 1369 // 止盈价格需要小于执行价格(做空)
	ErrCodePlanStopLEEntrustPrice                = 1370 // 止损价格需要大于执行价格(做空)
	ErrCodeBlowingRisk                           = 1371 // 您本次开仓有强平风险，已触发风险调控。
	ErrCodeNonsupportMarginCoin                  = 1372 // 不支持的保证金币种
	ErrCodeBonusNonsupportHedgeMode              = 1373 // 赠金暂不支持双向开仓，请平仓后再试
	ErrCodePositionNumsLimit                     = 1374 // 分仓合约单方向持仓数量限制
	ErrCodeNonsupportPositionSplitModeForVersion = 1375 // 当前版本无法在当前用户开仓模式下单，请升级版本。 // 机翻

	// 法币相关
	ErrCodeNoPlatformSell             = 1401 // 暂无商户接单
	ErrCodeNoPlatformSellTime         = 1402 // 不在商户服务时间
	ErrCodeLegalLessThanAmountLimit   = 1403 // 下单总金额小于系统限制
	ErrCodeLegalMoreThanAmountLimit   = 1404 // 下单总金额大于系统限制
	ErrCodeLegalHasUndoneOrder        = 1405 // 有未完成订单
	ErrCodeLegalPriceRateInvalid      = 1406 // 法币报价无效
	ErrCodeDigitalLessThanAmountLimit = 1407 // 下单币种数量小于系统限制
	ErrCodeDigitalMoreThanAmountLimit = 1408 // 下单币种数量大于系统限制
	ErrCodeFiatLessThanAmountLimit    = 1409 // 下单法币数量小于系统限制
	ErrCodeFiatMoreThanAmountLimit    = 1410 // 下单法币数量大于系统限制

	//活动相关
	ErrCodeActivityExpire = 1501 //活动已过期
	ErrCodeActivityAdopt  = 1502 //审核通过不允许上传

	// 兑币相关
	ErrCodeNonsupportExchange       = 1601 // 不支持的兑换
	ErrCodeExchangePreOrderOverdue  = 1602 // 当前价格已过期,请重新获取
	ErrCodeExchangePriceVolatility  = 1603 // 当前价格波动较大，本次下单价格已失效
	ErrCodeExchangeLessMinVolume    = 1604 // 单笔最少%s%s
	ErrCodeExchangeGreaterMaxVolume = 1605 // 单笔最大%s%s
	ErrCodeExchangePrecisionLimit   = 1606 // 输入精度限制
	ErrCodeExchangeDailyLimit       = 1607 // 超出每日限额,剩余额度: %s%s

	// 赠金相关
	ErrCodeGiftCashActivityExceed = 1701 // 活动已经过期
	ErrCodeGiftCashReceiveExceed  = 1702 // 已超过领取期限
)

const (
	NormalErrMsg = "系统繁忙,请稍后再试"
)

var (
	ErrMsgNone                                  *ReplyError = nil
	ErrMsgBusy                                              = newReplyError(ErrCodeBusy)                                  // 系统繁忙
	ErrMsgActionInvalid                                     = newReplyError(ErrCodeActionInvalid)                         // 无效请求
	ErrMsgServerNotAvailable                                = newReplyError(ErrCodeServerNotAvailable)                    // 服务暂时不可用
	ErrMsgParam                                             = newReplyError(ErrCodeParam)                                 // 请求参数错误
	ErrMsgRequestTimeException                              = newReplyError(ErrCodeRequestTimeException)                  // 请求时间异常
	ErrMsgSignature                                         = newReplyError(ErrCodeSignature)                             // 签名验证失败
	ErrMsgForceUpdate                                       = newReplyError(ErrCodeForceUpdate)                           // 需要强制升级
	ErrMsgAuthCode                                          = newReplyError(ErrCodeAuthCode)                              // 验证码错误
	ErrMsgAuthCodeInvalid                                   = newReplyError(ErrCodeAuthCodeInvalid)                       // 无效的验证码
	ErrMsgImgAuthCode                                       = newReplyError(ErrCodeImgAuthCode)                           // 图形验证码错误
	ErrMsgInvalidInviteCode                                 = newReplyError(ErrCodeInvalidInviteCode)                     // 无效的邀请码
	ErrMsgActionTimeout                                     = newReplyError(ErrCodeActionTimeout)                         // 操作超时
	ErrMsgCWSParam                                          = newReplyError(ErrCodeCWSParam)                              // cws返回参数错误,建议驳回申请
	ErrMsgResubmit                                          = newReplyError(ErrCodeResubmit)                              // 重复提交
	ErrMsgStatus                                            = newReplyError(ErrCodeStatus)                                // 状态错误
	ErrMsgFrequent                                          = newReplyError(ErrCodeFrequent)                              // 请求频繁
	ErrMsgFileTooLarge                                      = newReplyError(ErrCodeFileTooLarge)                          // 文件过大
	ErrMsgNonsupportFileType                                = newReplyError(ErrCodeNonsupportFileType)                    // 不支持的文件类型
	ErrMsgTextLengthLimit                                   = newReplyError(ErrCodeTextLengthLimit)                       // 字符长度超限
	ErrMsgLimitCountry                                      = newReplyError(ErrCodeLimitCountry)                          // 暂不对当前地区提供服务
	ErrMsgFeatureDisabled                                   = newReplyError(ErrCodeFeatureDisabled)                       // 暂不提供该服务
	ErrMsgAPIFrequent                                       = newReplyError(ErrCodeAPIFrequent)                           // 请求频次过多，请稍后再试
	ErrMsgConfirmationIsRequired                            = newReplyError(ErrCodeConfirmationIsRequired)                // 需要进行确认
	ErrMsgShouldLogin                                       = newReplyError(ErrCodeShouldLogin)                           // 未登录
	ErrMsgUserNotExist                                      = newReplyError(ErrCodeUserNotExist)                          // 用户不存在
	ErrMsgAccountIsExist                                    = newReplyError(ErrCodeAccountIsExist)                        // 账号已存在
	ErrMsgAccountFormat                                     = newReplyError(ErrCodeAccountFormat)                         // 账号格式错误
	ErrMsgTooLongAccount                                    = newReplyError(ErrCodeTooLongAccount)                        // 账号过长
	ErrMsgLoginElseWhere                                    = newReplyError(ErrCodeLoginElseWhere)                        // 用户在其它地方登陆
	ErrMsgFundPwdNotSet                                     = newReplyError(ErrCodeFundPwdNotSet)                         // 资金密码未设置
	ErrMsgLoginPwdNotSet                                    = newReplyError(ErrCodeLoginPwdNotSet)                        // 登录密码未设置
	ErrMsgLoginPwdIncorrect                                 = newReplyError(ErrCodeLoginPwdIncorrect)                     // 登录密码不正确
	ErrMsgLoginPwdSameAsFundPwd                             = newReplyError(ErrCodeLoginPwdSameAsFundPwd)                 // 登录密码不能与资金密码相同
	ErrMsgFundPwdSameAsLoginPwd                             = newReplyError(ErrCodeFundPwdSameAsLoginPwd)                 // 资金密码不能与登录密码相同
	ErrMsgLoginNotAllowed                                   = newReplyError(ErrCodeLoginNotAllowed)                       // 禁止登录
	ErrMsgTradeNotAllowed                                   = newReplyError(ErrCodeTradeNotAllowed)                       // 当前用户被禁止交易
	ErrMsgWithdrawNotAllowed                                = newReplyError(ErrCodeWithdrawNotAllowed)                    // 当前用户被禁止提币
	ErrMsgHasAccountLock                                    = newReplyError(ErrCodeHasAccountLock)                        // 其他用户正在进行对当前账号进行注册或绑定操作,请稍后再试
	ErrMsgNeedVerify                                        = newReplyError(ErrCodeNeedVerify)                            // 用户未认证
	ErrMsgIdNumberVerified                                  = newReplyError(ErrCodeIdNumberVerified)                      // 证件信息已经审核通过
	ErrMsgIdNumberExist                                     = newReplyError(ErrCodeIdNumberExist)                         // 证件号已被占用
	ErrMsgVerifyIdNumber                                    = newReplyError(ErrCodeVerifyIdNumber)                        // 无效的身份证号
	ErrMsgVerifyValidity                                    = newReplyError(ErrCodeVerifyValidity)                        // 姓名与身份证号不匹配
	ErrMsgNeedVerifyIdentity                                = newReplyError(ErrCodeNeedVerifyIdentity)                    // 需要先通过身份信息审核
	ErrMsgFaceVerified                                      = newReplyError(ErrCodeFaceVerified)                          // 面部认证已通过
	ErrMsgExisted                                           = newReplyError(ErrCodeExisted)                               // 已存在
	ErrMsgTradeIDNotMatch                                   = newReplyError(ErrCodeTradeIDNotMatch)                       // 当前身份无法执行此操作
	ErrMsgDealerFollowLimit                                 = newReplyError(ErrCodeDealerFollowLimit)                     // 当前交易员跟随人数已满
	ErrMsgDealerCanNotFollow                                = newReplyError(ErrCodeDealerCanNotFollow)                    // 交易员不可进行跟单绑定
	ErrMsgNoFollow                                          = newReplyError(ErrCodeNoFollow)                              // 当前未跟随
	ErrMsgNonsupportModifyEmail                             = newReplyError(ErrCodeNonsupportModifyEmail)                 // 不支持修改邮箱 // 机翻
	ErrMsgModifySafeModeCount                               = newReplyError(ErrCodeModifySafeModeCount)                   // 当前操作需要有两种以上安全验证 // 机翻
	ErrMsgSafePhoneCodeInvalid                              = newReplyError(ErrCodeSafePhoneCodeInvalid)                  // 无效的手机验证码 // 机翻
	ErrMsgSafeEmailCodeInvalid                              = newReplyError(ErrCodeSafeEmailCodeInvalid)                  // 无效的邮箱验证码 // 机翻
	ErrMsgSafeTotpCodeInvalid                               = newReplyError(ErrCodeSafeTotpCodeInvalid)                   // 无效的验证器验证码 // 机翻
	ErrMsgSafePhoneCodeExpire                               = newReplyError(ErrCodeSafePhoneCodeExpire)                   // 手机验证码已过期,请重新获取 // 机翻
	ErrMsgSafeEmailCodeExpire                               = newReplyError(ErrCodeSafeEmailCodeExpire)                   // 邮箱验证码已过期,请重新获取 // 机翻
	ErrMsgSafeNeedVerify                                    = newReplyError(ErrCodeSafeNeedVerify)                        // 需要进行两步验证 // 机翻
	ErrMsgSafeNeedFundPasswordVerify                        = newReplyError(ErrCodeSafeNeedFundPasswordVerify)            // 需要进行资金密码校验 // 机翻
	ErrMsgSafeFundPassword                                  = newReplyError(ErrCodeSafeFundPassword)                      // 资金密码错误 // 机翻
	ErrMsgSameOldPassword                                   = newReplyError(ErrCodeSameOldPassword)                       // 与旧密码相同 // 机翻
	ErrMsgPaymentsOverLimit                                 = newReplyError(ErrCodePaymentsOverLimit)                     // 收款方式达上限 //机翻
	ErrMsgApiCreateOverLimit                                = newReplyError(ErrCodeApiCreateOverLimit)                    // API创建数达到上限 //机翻
	ErrMsgApiBindIPOverLimit                                = newReplyError(ErrCodeApiBindIPOverLimit)                    // 绑定IP数超出上限 //机翻
	ErrMsgApiBindIPv6OverLimit                              = newReplyError(ErrCodeApiBindIPv6OverLimit)                  // 最多仅支持绑定两个IPv6地址 //机翻
	ErrMsgApiCreateNoPermission                             = newReplyError(ErrCodeApiCreateNoPermission)                 // 当前用户暂无创建和修改API权限 //机翻
	ErrMsgApiIPAddress                                      = newReplyError(ErrCodeApiIPAddress)                          // IP地址不正确 //机翻
	ErrMsgApiOverdue                                        = newReplyError(ErrCodeApiOverdue)                            // 当前api已过期 //机翻
	ErrMsgWithdrawAddrCountLimit                            = newReplyError(ErrCodeWithdrawAddrCountLimit)                // 提币地址已达到最大数量，添加失败。 //机翻
	ErrMsgWithdrawAddressExisted                            = newReplyError(ErrCodeWithdrawAddressExisted)                // 当前地址已存在
	ErrMsgSafeSpareEmailCodeInvalid                         = newReplyError(ErrCodeSafeSpareEmailCodeInvalid)             // 无效的备用邮箱验证码 // 机翻
	ErrMsgSafeSpareEmailCodeExpire                          = newReplyError(ErrCodeSafeSpareEmailCodeExpire)              // 备用邮箱验证码已过期,请重新获取 // 机翻
	ErrMsgNonsupportAccountType                             = newReplyError(ErrCodeNonsupportAccountType)                 // 不支持的账户模式 // 机翻
	ErrMsgNonsupportPositionSplitMode                       = newReplyError(ErrCodeNonsupportPositionSplitMode)           // 您当前持有仓位或未成交的委托，不允许更改分仓模式。 // 机翻
	ErrMsgDeleteAccountAtRiskStateLimit                     = newReplyError(ErrCodeDeleteAccountAtRiskStateLimit)         // 账户存在风控状态，请联系客服
	ErrMsgDeleteAccountHasUndoneOrder                       = newReplyError(ErrCodeDeleteAccountHasUndoneOrder)           // 账户存在持仓，委托及跟随交易员，请确认以上内容全部取消后再试
	ErrMsgAccountIsDeleteState                              = newReplyError(ErrCodeAccountIsDeleteState)                  // 此账户已注销，如需再次注册请联系官方客服
	ErrMsgNonsupportPhoneNumber                             = newReplyError(ErrCodeNonsupportPhoneNumber)                 // 暂不支持手机号
	ErrMsgFewTransfer                                       = newReplyError(ErrCodeFewTransfer)                           // 小于最小单笔转入限制
	ErrMsgManyTransfer                                      = newReplyError(ErrCodeManyTransfer)                          // 超过最大单笔转出限制
	ErrMsgCoinNotSupportTransfer                            = newReplyError(ErrCodeCoinNotSupportTransfer)                // 币种暂不支持划转
	ErrMsgBalanceInsufficient                               = newReplyError(ErrCodeBalanceInsufficient)                   // 资产不足
	ErrMsgInvalidAddress                                    = newReplyError(ErrCodeInvalidAddress)                        // 无效的提币地址
	ErrMsgWithdrawHasApply                                  = newReplyError(ErrCodeWithdrawHasApply)                      // 当前有审核中的提币申请
	ErrMsgWithdrawHasPassed                                 = newReplyError(ErrCodeWithdrawHasPassed)                     // 当日已经提币过
	ErrMsgDepositInsufficient                               = newReplyError(ErrCodeDepositInsufficient)                   // 保证金不足
	ErrMsgNotSupportWithdraw                                = newReplyError(ErrCodeNotSupportWithdraw)                    // 币种暂不支持提币
	ErrMsgWithdrawLessMinLimit                              = newReplyError(ErrCodeWithdrawLessMinLimit)                  // 提币数量小于最小提币量
	ErrMsgWithdrawGreaterMaxLimit                           = newReplyError(ErrCodeWithdrawGreaterMaxLimit)               // 提币数量大于最大提币量
	ErrMsgTransferInsufficient                              = newReplyError(ErrCodeTransferInsufficient)                  // 剩余可划转数量不足
	ErrMsgSafeGuardForSetting                               = newReplyError(ErrCodeSafeGuardForSetting)                   // 此账号已修改了安全设置。为了您的账号安全，24小时内禁止提币，与法币卖出
	ErrMsgReceivingLimitReached                             = newReplyError(ErrCodeReceivingLimitReached)                 // 模拟资产领取达到限额
	ErrMsgNeedWithdrawVerifyPassed                          = newReplyError(ErrCodeNeedWithdrawVerifyPassed)              // 需要先完成提币身份认证
	ErrMsgLegalWithdrawSafeGuard                            = newReplyError(ErrCodeLegalWithdrawSafeGuard)                // 近期有法币入金,24小时内不支持提币
	ErrMsgSafeGuardForFundPwd                               = newReplyError(ErrCodeSafeGuardForFundPwd)                   // 资金密码连续输入5次错误，并在24小时内禁止提币与法币卖出。
	ErrMsgWithdrawalRestrictionsMail                        = newReplyError(ErrCodeWithdrawalRestrictionsMail)            // 提币限制: 请完成邮箱绑定
	ErrMsgInsufficientFollowBalance                         = newReplyError(ErrCodeInsufficientFollowBalance)             // 您的跟单可用或授权资金不满足交易员要求的最低账户资金，请转入资金后再进行跟单。
	ErrMsgDealerLimitIsChanged                              = newReplyError(ErrCodeDealerLimitIsChanged)                  // 交易员修改了配置
	ErrMsgUnverifyWithdrawOver                              = newReplyError(ErrCodeUnverifyWithdrawOver)                  // 未认证用户提币限额超出
	ErrMsgNonsupportTransferAsset                           = newReplyError(ErrCodeNonsupportTransferAsset)               // 暂不支持当前所选账户之间的划转
	ErrMsgHasGiftCashNonsupportTransferOut                  = newReplyError(ErrCodeHasGiftCashNonsupportTransferOut)      // 有冻结赠金的时候,不支持转出
	ErrMsgHasGiftCashDeviceLimit                            = newReplyError(ErrCodeHasGiftCashDeviceLimit)                // 领取失败，该设备已领取过赠金。
	ErrMsgAssetIsLiquidise                                  = newReplyError(ErrCodeAssetIsLiquidise)                      // 当前资产清算中，稍后再试
	ErrMsgContractNotExist                                  = newReplyError(ErrCodeContractNotExist)                      // 合约不存在
	ErrMsgContractNotTrade                                  = newReplyError(ErrCodeContractNotTrade)                      // 合约暂不可交易
	ErrMsgNonSupportOptimal                                 = newReplyError(ErrCodeNonSupportOptimal)                     // 暂不支持最优价下单
	ErrMsgNonSupportPrice                                   = newReplyError(ErrCodeNonSupportPrice)                       // 限价单价格不支持
	ErrMsgPositionInsufficient                              = newReplyError(ErrCodePositionInsufficient)                  // 剩余持仓数不足
	ErrMsgTooManyOrderVolume                                = newReplyError(ErrCodeTooManyOrderVolume)                    // 超出剩余可下单张数
	ErrMsgTradeFrequent                                     = newReplyError(ErrCodeTradeFrequent)                         // 委托请求频繁 每秒最多49单
	ErrMsgNonSupportChangeDeposit                           = newReplyError(ErrCodeNonSupportChangeDeposit)               // 全仓模式不支持调整调整保证金
	ErrMsgBelowMinOpenVolume                                = newReplyError(ErrCodeBelowMinOpenVolume)                    // 低于最小下单量
	ErrMsgOverMaxOpenVolume                                 = newReplyError(ErrCodeOverMaxOpenVolume)                     // 大于最大下单量
	ErrMsgPositionInvalid                                   = newReplyError(ErrCodePositionInvalid)                       // 当前持仓无效或已平仓
	ErrMsgLeverInvalid                                      = newReplyError(ErrCodeLeverInvalid)                          // 禁止切换，存在持仓，与持仓杠杆不一致
	ErrMsgOverPlaceMoney                                    = newReplyError(ErrCodeOverPlaceMoney)                        // 大于可下单金额
	ErrMsgPlanOrderLimit                                    = newReplyError(ErrCodePlanOrderLimit)                        // 达到未触发计划单数量限制
	ErrMsgLimitLEPrice                                      = newReplyError(ErrCodeLimitLEPrice)                          // 止盈价格需要大于开仓均价(做多)
	ErrMsgStopGEPrice                                       = newReplyError(ErrCodeStopGEPrice)                           // 止损价格需要小于开仓均价(做多)
	ErrMsgLimitGEPrice                                      = newReplyError(ErrCodeLimitGEPrice)                          // 止盈价格需要小于开仓均价(做空)
	ErrMsgStopLEPrice                                       = newReplyError(ErrCodeStopLEPrice)                           // 止损价格需要大于开仓均价(做空)
	ErrMsgPlanLimitLEPrice                                  = newReplyError(ErrCodePlanLimitLEPrice)                      // 止盈价格需要大于触发价格(做多)
	ErrMsgPlanStopGEPrice                                   = newReplyError(ErrCodePlanStopGEPrice)                       // 止损价格需要小于触发价格(做多)
	ErrMsgPlanLimitGEPrice                                  = newReplyError(ErrCodePlanLimitGEPrice)                      // 止盈价格需要小于触发价格(做空)
	ErrMsgPlanStopLEPrice                                   = newReplyError(ErrCodePlanStopLEPrice)                       // 止损价格需要大于触发价格(做空)
	ErrMsgLimitEIndex                                       = newReplyError(ErrCodeLimitEIndex)                           // 止盈价格不可以等于合约指数
	ErrMsgStopEIndex                                        = newReplyError(ErrCodeStopEIndex)                            // 止损价格不可以等于合约指数
	ErrMsgPlanCloseCountLimit                               = newReplyError(ErrCodePlanCloseCountLimit)                   // 止盈止损已经超出6条 请删除后再设置
	ErrMsgMarginModifyBalanceInsufficient                   = newReplyError(ErrCodeMarginModifyBalanceInsufficient)       // 可用余额不足增加保证金失败
	ErrMsgMarginModifyMarginInsufficient                    = newReplyError(ErrCodeMarginModifyMarginInsufficient)        // 保证金不足无法减少
	ErrMsgAccountTypeHasFullPosition                        = newReplyError(ErrCodeAccountTypeHasFullPosition)            // 您当前有全仓模式的持仓请先平仓后再进行切换
	ErrMsgAccountTypeHasFullPlan                            = newReplyError(ErrCodeAccountTypeHasFullPlan)                // 您当前有全仓模式的计划单请先撤销后再进行切换
	ErrMsgAccountTypeHasPartPosition                        = newReplyError(ErrCodeAccountTypeHasPartPosition)            // 您当前有逐仓模式的持仓请先平仓后再进行切换
	ErrMsgAccountTypeHasPartPlan                            = newReplyError(ErrCodeAccountTypeHasPartPlan)                // 您当前有逐仓模式的计划单请先撤销后在进行切换
	ErrMsgLeverHasBuyPartOrder                              = newReplyError(ErrCodeLeverHasBuyPartOrder)                  // 您当前合约多单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
	ErrMsgLeverHasSellPartOrder                             = newReplyError(ErrCodeLeverHasSellPartOrder)                 // 您当前合约空单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
	ErrMsgNoAccountTypePlace                                = newReplyError(ErrCodeNoAccountTypePlace)                    // 无法以当前模式下单
	ErrMsgContractMarketStop                                = newReplyError(ErrCodeContractMarketStop)                    // 行情中断，下单失败，请稍后再试
	ErrMsgNonsupportSwitchAccountType                       = newReplyError(ErrCodeNonsupportSwitchAccountType)           // 暂不支持切换到指定账户模式
	ErrMsgOverUnitTrade                                     = newReplyError(ErrCodeOverUnitTrade)                         // 超出合约单位时间交易限制
	ErrMsgPartialFailed                                     = newReplyError(ErrCodePartialFailed)                         // 部分平仓失败 机翻
	ErrMsgNotEnoughVolumeTrade                              = newReplyError(ErrCodeNotEnoughVolumeTrade)                  // 行情失败，没有足够的吃单 下单失败，暂无对手盘可成交。 机翻
	ErrMsgCloseOverPlace                                    = newReplyError(ErrCodeCloseOverPlace)                        // 平仓超过警戒线 机翻
	ErrMsgRiskLess                                          = newReplyError(ErrCodeRiskLess)                              // 低于风险率，禁止开平仓
	ErrMsgOverPositionCount                                 = newReplyError(ErrCodeOverPositionCount)                     // 超出最大持仓条数
	ErrMsgFollowNotSupport                                  = newReplyError(ErrCodeFollowNotSupport)                      // 合约不支持带单
	ErrMsgPlanEntrustOverContractLimit                      = newReplyError(ErrCodePlanEntrustOverContractLimit)          // 条件单价格无效,超过合约限价
	ErrMsgPlanEntrustLessContractLimit                      = newReplyError(ErrCodePlanEntrustLessContractLimit)          // 条件单价格无效,超过合约限价
	ErrMsgPlanEntrustLess                                   = newReplyError(ErrCodePlanEntrustLess)                       // 超出方向限价
	ErrMsgPlanEntrustMore                                   = newReplyError(ErrCodePlanEntrustMore)                       // 低于方向限价
	ErrMsgPositionState                                     = newReplyError(ErrCodePositionState)                         // 此仓位状态已经更新，请查看仓位后再进行设置
	ErrMsgOverMaxSideEntrust                                = newReplyError(ErrCodeOverMaxSideEntrust)                    // 超出合约单边最大挂单量
	ErrMsgDepthInvalid                                      = newReplyError(ErrCodeDepthInvalid)                          // 没有足够的深度成交
	ErrMsgPriceOverLimit                                    = newReplyError(ErrCodePriceOverLimit)                        // 当前委托价格超过允许范围
	ErrMsgPriceInvalid                                      = newReplyError(ErrCodePriceInvalid)                          // 当前委托价格无效
	ErrMsgEntrustCloseVolumeInvalid                         = newReplyError(ErrCodeEntrustCloseVolumeInvalid)             // 当前限价委托存在本仓位的平仓委托，请撤销后再使用极速平仓
	ErrMsgPriceMarketOver                                   = newReplyError(ErrCodePriceMarketOver)                       // 超出行情保护阈值，取消委托
	ErrMsgInMaintenance                                     = newReplyError(ErrCodeInMaintenance)                         // 系统维护
	ErrMsgNetPosException                                   = newReplyError(ErrCodeNetPosException)                       // 净持仓偏向异常
	ErrMsgForceClosing                                      = newReplyError(ErrCodeForceClosing)                          // 强平结算中
	ErrMsgClosing                                           = newReplyError(ErrCodeClosing)                               // 合约结算中
	ErrMsgOrderIdUserd                                      = newReplyError(ErrCodeOrderIdUserd)                          // 订单号已存在
	ErrMsgUnfinishedEntrustOrderTooMany                     = newReplyError(ErrCodeUnfinishedEntrustOrderTooMany)         // 当前未完成委托数过多
	ErrMsgOrderState                                        = newReplyError(ErrCodeOrderState)                            // 当前订单状态已更新,请刷新后重试
	ErrMsgPlanLimitLEEntrustPrice                           = newReplyError(ErrCodePlanLimitLEEntrustPrice)               // 止盈价格需要大于执行价格(做多)
	ErrMsgPlanStopGEEntrustPrice                            = newReplyError(ErrCodePlanStopGEEntrustPrice)                // 止损价格需要小于执行价格(做多)
	ErrMsgPlanLimitGEEntrustPrice                           = newReplyError(ErrCodePlanLimitGEEntrustPrice)               // 止盈价格需要小于执行价格(做空)
	ErrMsgPlanStopLEEntrustPrice                            = newReplyError(ErrCodePlanStopLEEntrustPrice)                // 止损价格需要大于执行价格(做空)
	ErrMsgBlowingRisk                                       = newReplyError(ErrCodeBlowingRisk)                           // 您本次开仓有强平风险，已触发风险调控。
	ErrMsgNonsupportMarginCoin                              = newReplyError(ErrCodeNonsupportMarginCoin)                  // 不支持的保证金币种
	ErrMsgBonusNonsupportHedgeMode                          = newReplyError(ErrCodeBonusNonsupportHedgeMode)              // 赠金暂不支持双向开仓，请平仓后再试
	ErrMsgPositionNumsLimit                                 = newReplyError(ErrCodePositionNumsLimit)                     // 分仓合约单方向持仓数量限制
	ErrMsgNonsupportPositionSplitModeForVersion             = newReplyError(ErrCodeNonsupportPositionSplitModeForVersion) // 当前版本无法在当前用户开仓模式下单，请升级版本。 // 机翻
	ErrMsgNoPlatformSell                                    = newReplyError(ErrCodeNoPlatformSell)                        // 暂无商户接单
	ErrMsgNoPlatformSellTime                                = newReplyError(ErrCodeNoPlatformSellTime)                    // 不在商户服务时间
	ErrMsgLegalLessThanAmountLimit                          = newReplyError(ErrCodeLegalLessThanAmountLimit)              // 下单总金额小于系统限制
	ErrMsgLegalMoreThanAmountLimit                          = newReplyError(ErrCodeLegalMoreThanAmountLimit)              // 下单总金额大于系统限制
	ErrMsgLegalHasUndoneOrder                               = newReplyError(ErrCodeLegalHasUndoneOrder)                   // 有未完成订单
	ErrMsgLegalPriceRateInvalid                             = newReplyError(ErrCodeLegalPriceRateInvalid)                 // 法币报价无效
	ErrMsgDigitalLessThanAmountLimit                        = newReplyError(ErrCodeDigitalLessThanAmountLimit)            // 下单币种数量小于系统限制
	ErrMsgDigitalMoreThanAmountLimit                        = newReplyError(ErrCodeDigitalMoreThanAmountLimit)            // 下单币种数量大于系统限制
	ErrMsgFiatLessThanAmountLimit                           = newReplyError(ErrCodeFiatLessThanAmountLimit)               // 下单法币数量小于系统限制
	ErrMsgFiatMoreThanAmountLimit                           = newReplyError(ErrCodeFiatMoreThanAmountLimit)               // 下单法币数量大于系统限制
	ErrMsgActivityExpire                                    = newReplyError(ErrCodeActivityExpire)                        // 活动已过期
	ErrMsgActivityAdopt                                     = newReplyError(ErrCodeActivityAdopt)                         // 审核通过不允许上传
	ErrMsgNonsupportExchange                                = newReplyError(ErrCodeNonsupportExchange)                    // 不支持的兑换
	ErrMsgExchangePreOrderOverdue                           = newReplyError(ErrCodeExchangePreOrderOverdue)               // 当前价格已过期,请重新获取
	ErrMsgExchangePriceVolatility                           = newReplyError(ErrCodeExchangePriceVolatility)               // 当前价格波动较大，本次下单价格已失效
	ErrMsgExchangeLessMinVolume                             = newReplyError(ErrCodeExchangeLessMinVolume)                 // 单笔最少%s%s
	ErrMsgExchangeGreaterMaxVolume                          = newReplyError(ErrCodeExchangeGreaterMaxVolume)              // 单笔最大%s%s
	ErrMsgExchangePrecisionLimit                            = newReplyError(ErrCodeExchangePrecisionLimit)                // 输入精度限制
	ErrMsgExchangeDailyLimit                                = newReplyError(ErrCodeExchangeDailyLimit)                    // 超出每日限额,剩余额度: %s%s
	ErrMsgGiftCashActivityExceed                            = newReplyError(ErrCodeGiftCashActivityExceed)                // 活动已经过期
	ErrMsgGiftCashReceiveExceed                             = newReplyError(ErrCodeGiftCashReceiveExceed)                 // 已超过领取期限
)

var ErrMap = map[int]*ReplyError{
	ErrCodeBusy:                                  ErrMsgBusy,
	ErrCodeActionInvalid:                         ErrMsgActionInvalid,
	ErrCodeServerNotAvailable:                    ErrMsgServerNotAvailable,
	ErrCodeParam:                                 ErrMsgParam,
	ErrCodeRequestTimeException:                  ErrMsgRequestTimeException,
	ErrCodeSignature:                             ErrMsgSignature,
	ErrCodeForceUpdate:                           ErrMsgForceUpdate,
	ErrCodeAuthCode:                              ErrMsgAuthCode,
	ErrCodeAuthCodeInvalid:                       ErrMsgAuthCodeInvalid,
	ErrCodeImgAuthCode:                           ErrMsgImgAuthCode,
	ErrCodeInvalidInviteCode:                     ErrMsgInvalidInviteCode,
	ErrCodeActionTimeout:                         ErrMsgActionTimeout,
	ErrCodeCWSParam:                              ErrMsgCWSParam,
	ErrCodeResubmit:                              ErrMsgResubmit,
	ErrCodeStatus:                                ErrMsgStatus,
	ErrCodeFrequent:                              ErrMsgFrequent,
	ErrCodeFileTooLarge:                          ErrMsgFileTooLarge,
	ErrCodeNonsupportFileType:                    ErrMsgNonsupportFileType,
	ErrCodeTextLengthLimit:                       ErrMsgTextLengthLimit,
	ErrCodeLimitCountry:                          ErrMsgLimitCountry,
	ErrCodeFeatureDisabled:                       ErrMsgFeatureDisabled,
	ErrCodeAPIFrequent:                           ErrMsgAPIFrequent,
	ErrCodeConfirmationIsRequired:                ErrMsgConfirmationIsRequired,
	ErrCodeShouldLogin:                           ErrMsgShouldLogin,
	ErrCodeUserNotExist:                          ErrMsgUserNotExist,
	ErrCodeAccountIsExist:                        ErrMsgAccountIsExist,
	ErrCodeAccountFormat:                         ErrMsgAccountFormat,
	ErrCodeTooLongAccount:                        ErrMsgTooLongAccount,
	ErrCodeLoginElseWhere:                        ErrMsgLoginElseWhere,
	ErrCodeFundPwdNotSet:                         ErrMsgFundPwdNotSet,
	ErrCodeLoginPwdNotSet:                        ErrMsgLoginPwdNotSet,
	ErrCodeLoginPwdIncorrect:                     ErrMsgLoginPwdIncorrect,
	ErrCodeLoginPwdSameAsFundPwd:                 ErrMsgLoginPwdSameAsFundPwd,
	ErrCodeFundPwdSameAsLoginPwd:                 ErrMsgFundPwdSameAsLoginPwd,
	ErrCodeLoginNotAllowed:                       ErrMsgLoginNotAllowed,
	ErrCodeTradeNotAllowed:                       ErrMsgTradeNotAllowed,
	ErrCodeWithdrawNotAllowed:                    ErrMsgWithdrawNotAllowed,
	ErrCodeHasAccountLock:                        ErrMsgHasAccountLock,
	ErrCodeNeedVerify:                            ErrMsgNeedVerify,
	ErrCodeIdNumberVerified:                      ErrMsgIdNumberVerified,
	ErrCodeIdNumberExist:                         ErrMsgIdNumberExist,
	ErrCodeVerifyIdNumber:                        ErrMsgVerifyIdNumber,
	ErrCodeVerifyValidity:                        ErrMsgVerifyValidity,
	ErrCodeNeedVerifyIdentity:                    ErrMsgNeedVerifyIdentity,
	ErrCodeFaceVerified:                          ErrMsgFaceVerified,
	ErrCodeExisted:                               ErrMsgExisted,
	ErrCodeTradeIDNotMatch:                       ErrMsgTradeIDNotMatch,
	ErrCodeDealerFollowLimit:                     ErrMsgDealerFollowLimit,
	ErrCodeDealerCanNotFollow:                    ErrMsgDealerCanNotFollow,
	ErrCodeNoFollow:                              ErrMsgNoFollow,
	ErrCodeNonsupportModifyEmail:                 ErrMsgNonsupportModifyEmail,
	ErrCodeModifySafeModeCount:                   ErrMsgModifySafeModeCount,
	ErrCodeSafePhoneCodeInvalid:                  ErrMsgSafePhoneCodeInvalid,
	ErrCodeSafeEmailCodeInvalid:                  ErrMsgSafeEmailCodeInvalid,
	ErrCodeSafeTotpCodeInvalid:                   ErrMsgSafeTotpCodeInvalid,
	ErrCodeSafePhoneCodeExpire:                   ErrMsgSafePhoneCodeExpire,
	ErrCodeSafeEmailCodeExpire:                   ErrMsgSafeEmailCodeExpire,
	ErrCodeSafeNeedVerify:                        ErrMsgSafeNeedVerify,
	ErrCodeSafeNeedFundPasswordVerify:            ErrMsgSafeNeedFundPasswordVerify,
	ErrCodeSafeFundPassword:                      ErrMsgSafeFundPassword,
	ErrCodeSameOldPassword:                       ErrMsgSameOldPassword,
	ErrCodePaymentsOverLimit:                     ErrMsgPaymentsOverLimit,
	ErrCodeApiCreateOverLimit:                    ErrMsgApiCreateOverLimit,
	ErrCodeApiBindIPOverLimit:                    ErrMsgApiBindIPOverLimit,
	ErrCodeApiBindIPv6OverLimit:                  ErrMsgApiBindIPv6OverLimit,
	ErrCodeApiCreateNoPermission:                 ErrMsgApiCreateNoPermission,
	ErrCodeApiIPAddress:                          ErrMsgApiIPAddress,
	ErrCodeApiOverdue:                            ErrMsgApiOverdue,
	ErrCodeWithdrawAddrCountLimit:                ErrMsgWithdrawAddrCountLimit,
	ErrCodeWithdrawAddressExisted:                ErrMsgWithdrawAddressExisted,
	ErrCodeSafeSpareEmailCodeInvalid:             ErrMsgSafeSpareEmailCodeInvalid,
	ErrCodeSafeSpareEmailCodeExpire:              ErrMsgSafeSpareEmailCodeExpire,
	ErrCodeNonsupportAccountType:                 ErrMsgNonsupportAccountType,
	ErrCodeNonsupportPositionSplitMode:           ErrMsgNonsupportPositionSplitMode,
	ErrCodeDeleteAccountAtRiskStateLimit:         ErrMsgDeleteAccountAtRiskStateLimit,
	ErrCodeDeleteAccountHasUndoneOrder:           ErrMsgDeleteAccountHasUndoneOrder,
	ErrCodeAccountIsDeleteState:                  ErrMsgAccountIsDeleteState,
	ErrCodeNonsupportPhoneNumber:                 ErrMsgNonsupportPhoneNumber,
	ErrCodeFewTransfer:                           ErrMsgFewTransfer,
	ErrCodeManyTransfer:                          ErrMsgManyTransfer,
	ErrCodeCoinNotSupportTransfer:                ErrMsgCoinNotSupportTransfer,
	ErrCodeBalanceInsufficient:                   ErrMsgBalanceInsufficient,
	ErrCodeInvalidAddress:                        ErrMsgInvalidAddress,
	ErrCodeWithdrawHasApply:                      ErrMsgWithdrawHasApply,
	ErrCodeWithdrawHasPassed:                     ErrMsgWithdrawHasPassed,
	ErrCodeDepositInsufficient:                   ErrMsgDepositInsufficient,
	ErrCodeNotSupportWithdraw:                    ErrMsgNotSupportWithdraw,
	ErrCodeWithdrawLessMinLimit:                  ErrMsgWithdrawLessMinLimit,
	ErrCodeWithdrawGreaterMaxLimit:               ErrMsgWithdrawGreaterMaxLimit,
	ErrCodeTransferInsufficient:                  ErrMsgTransferInsufficient,
	ErrCodeSafeGuardForSetting:                   ErrMsgSafeGuardForSetting,
	ErrCodeReceivingLimitReached:                 ErrMsgReceivingLimitReached,
	ErrCodeNeedWithdrawVerifyPassed:              ErrMsgNeedWithdrawVerifyPassed,
	ErrCodeLegalWithdrawSafeGuard:                ErrMsgLegalWithdrawSafeGuard,
	ErrCodeSafeGuardForFundPwd:                   ErrMsgSafeGuardForFundPwd,
	ErrCodeWithdrawalRestrictionsMail:            ErrMsgWithdrawalRestrictionsMail,
	ErrCodeInsufficientFollowBalance:             ErrMsgInsufficientFollowBalance,
	ErrCodeDealerLimitIsChanged:                  ErrMsgDealerLimitIsChanged,
	ErrCodeUnverifyWithdrawOver:                  ErrMsgUnverifyWithdrawOver,
	ErrCodeNonsupportTransferAsset:               ErrMsgNonsupportTransferAsset,
	ErrCodeHasGiftCashNonsupportTransferOut:      ErrMsgHasGiftCashNonsupportTransferOut,
	ErrCodeHasGiftCashDeviceLimit:                ErrMsgHasGiftCashDeviceLimit,
	ErrCodeAssetIsLiquidise:                      ErrMsgAssetIsLiquidise,
	ErrCodeContractNotExist:                      ErrMsgContractNotExist,
	ErrCodeContractNotTrade:                      ErrMsgContractNotTrade,
	ErrCodeNonSupportOptimal:                     ErrMsgNonSupportOptimal,
	ErrCodeNonSupportPrice:                       ErrMsgNonSupportPrice,
	ErrCodePositionInsufficient:                  ErrMsgPositionInsufficient,
	ErrCodeTooManyOrderVolume:                    ErrMsgTooManyOrderVolume,
	ErrCodeTradeFrequent:                         ErrMsgTradeFrequent,
	ErrCodeNonSupportChangeDeposit:               ErrMsgNonSupportChangeDeposit,
	ErrCodeBelowMinOpenVolume:                    ErrMsgBelowMinOpenVolume,
	ErrCodeOverMaxOpenVolume:                     ErrMsgOverMaxOpenVolume,
	ErrCodePositionInvalid:                       ErrMsgPositionInvalid,
	ErrCodeLeverInvalid:                          ErrMsgLeverInvalid,
	ErrCodeOverPlaceMoney:                        ErrMsgOverPlaceMoney,
	ErrCodePlanOrderLimit:                        ErrMsgPlanOrderLimit,
	ErrCodeLimitLEPrice:                          ErrMsgLimitLEPrice,
	ErrCodeStopGEPrice:                           ErrMsgStopGEPrice,
	ErrCodeLimitGEPrice:                          ErrMsgLimitGEPrice,
	ErrCodeStopLEPrice:                           ErrMsgStopLEPrice,
	ErrCodePlanLimitLEPrice:                      ErrMsgPlanLimitLEPrice,
	ErrCodePlanStopGEPrice:                       ErrMsgPlanStopGEPrice,
	ErrCodePlanLimitGEPrice:                      ErrMsgPlanLimitGEPrice,
	ErrCodePlanStopLEPrice:                       ErrMsgPlanStopLEPrice,
	ErrCodeLimitEIndex:                           ErrMsgLimitEIndex,
	ErrCodeStopEIndex:                            ErrMsgStopEIndex,
	ErrCodePlanCloseCountLimit:                   ErrMsgPlanCloseCountLimit,
	ErrCodeMarginModifyBalanceInsufficient:       ErrMsgMarginModifyBalanceInsufficient,
	ErrCodeMarginModifyMarginInsufficient:        ErrMsgMarginModifyMarginInsufficient,
	ErrCodeAccountTypeHasFullPosition:            ErrMsgAccountTypeHasFullPosition,
	ErrCodeAccountTypeHasFullPlan:                ErrMsgAccountTypeHasFullPlan,
	ErrCodeAccountTypeHasPartPosition:            ErrMsgAccountTypeHasPartPosition,
	ErrCodeAccountTypeHasPartPlan:                ErrMsgAccountTypeHasPartPlan,
	ErrCodeLeverHasBuyPartOrder:                  ErrMsgLeverHasBuyPartOrder,
	ErrCodeLeverHasSellPartOrder:                 ErrMsgLeverHasSellPartOrder,
	ErrCodeNoAccountTypePlace:                    ErrMsgNoAccountTypePlace,
	ErrCodeContractMarketStop:                    ErrMsgContractMarketStop,
	ErrCodeNonsupportSwitchAccountType:           ErrMsgNonsupportSwitchAccountType,
	ErrCodeOverUnitTrade:                         ErrMsgOverUnitTrade,
	ErrCodePartialFailed:                         ErrMsgPartialFailed,
	ErrCodeNotEnoughVolumeTrade:                  ErrMsgNotEnoughVolumeTrade,
	ErrCodeCloseOverPlace:                        ErrMsgCloseOverPlace,
	ErrCodeRiskLess:                              ErrMsgRiskLess,
	ErrCodeOverPositionCount:                     ErrMsgOverPositionCount,
	ErrCodeFollowNotSupport:                      ErrMsgFollowNotSupport,
	ErrCodePlanEntrustOverContractLimit:          ErrMsgPlanEntrustOverContractLimit,
	ErrCodePlanEntrustLessContractLimit:          ErrMsgPlanEntrustLessContractLimit,
	ErrCodePlanEntrustLess:                       ErrMsgPlanEntrustLess,
	ErrCodePlanEntrustMore:                       ErrMsgPlanEntrustMore,
	ErrCodePositionState:                         ErrMsgPositionState,
	ErrCodeOverMaxSideEntrust:                    ErrMsgOverMaxSideEntrust,
	ErrCodeDepthInvalid:                          ErrMsgDepthInvalid,
	ErrCodePriceOverLimit:                        ErrMsgPriceOverLimit,
	ErrCodePriceInvalid:                          ErrMsgPriceInvalid,
	ErrCodeEntrustCloseVolumeInvalid:             ErrMsgEntrustCloseVolumeInvalid,
	ErrCodePriceMarketOver:                       ErrMsgPriceMarketOver,
	ErrCodeInMaintenance:                         ErrMsgInMaintenance,
	ErrCodeNetPosException:                       ErrMsgNetPosException,
	ErrCodeForceClosing:                          ErrMsgForceClosing,
	ErrCodeClosing:                               ErrMsgClosing,
	ErrCodeOrderIdUserd:                          ErrMsgOrderIdUserd,
	ErrCodeUnfinishedEntrustOrderTooMany:         ErrMsgUnfinishedEntrustOrderTooMany,
	ErrCodeOrderState:                            ErrMsgOrderState,
	ErrCodePlanLimitLEEntrustPrice:               ErrMsgPlanLimitLEEntrustPrice,
	ErrCodePlanStopGEEntrustPrice:                ErrMsgPlanStopGEEntrustPrice,
	ErrCodePlanLimitGEEntrustPrice:               ErrMsgPlanLimitGEEntrustPrice,
	ErrCodePlanStopLEEntrustPrice:                ErrMsgPlanStopLEEntrustPrice,
	ErrCodeBlowingRisk:                           ErrMsgBlowingRisk,
	ErrCodeNonsupportMarginCoin:                  ErrMsgNonsupportMarginCoin,
	ErrCodeBonusNonsupportHedgeMode:              ErrMsgBonusNonsupportHedgeMode,
	ErrCodePositionNumsLimit:                     ErrMsgPositionNumsLimit,
	ErrCodeNonsupportPositionSplitModeForVersion: ErrMsgNonsupportPositionSplitModeForVersion,
	ErrCodeNoPlatformSell:                        ErrMsgNoPlatformSell,
	ErrCodeNoPlatformSellTime:                    ErrMsgNoPlatformSellTime,
	ErrCodeLegalLessThanAmountLimit:              ErrMsgLegalLessThanAmountLimit,
	ErrCodeLegalMoreThanAmountLimit:              ErrMsgLegalMoreThanAmountLimit,
	ErrCodeLegalHasUndoneOrder:                   ErrMsgLegalHasUndoneOrder,
	ErrCodeLegalPriceRateInvalid:                 ErrMsgLegalPriceRateInvalid,
	ErrCodeDigitalLessThanAmountLimit:            ErrMsgDigitalLessThanAmountLimit,
	ErrCodeDigitalMoreThanAmountLimit:            ErrMsgDigitalMoreThanAmountLimit,
	ErrCodeFiatLessThanAmountLimit:               ErrMsgFiatLessThanAmountLimit,
	ErrCodeFiatMoreThanAmountLimit:               ErrMsgFiatMoreThanAmountLimit,
	ErrCodeActivityExpire:                        ErrMsgActivityExpire,
	ErrCodeActivityAdopt:                         ErrMsgActivityAdopt,
	ErrCodeNonsupportExchange:                    ErrMsgNonsupportExchange,
	ErrCodeExchangePreOrderOverdue:               ErrMsgExchangePreOrderOverdue,
	ErrCodeExchangePriceVolatility:               ErrMsgExchangePriceVolatility,
	ErrCodeExchangeLessMinVolume:                 ErrMsgExchangeLessMinVolume,
	ErrCodeExchangeGreaterMaxVolume:              ErrMsgExchangeGreaterMaxVolume,
	ErrCodeExchangePrecisionLimit:                ErrMsgExchangePrecisionLimit,
	ErrCodeExchangeDailyLimit:                    ErrMsgExchangeDailyLimit,
	ErrCodeGiftCashActivityExceed:                ErrMsgGiftCashActivityExceed,
	ErrCodeGiftCashReceiveExceed:                 ErrMsgGiftCashReceiveExceed,
}

var errMsgMap = map[ReqLang]map[int]string{
	ReqLangCN: {
		ErrCodeBusy:                                  NormalErrMsg,
		ErrCodeActionInvalid:                         "无效请求",
		ErrCodeServerNotAvailable:                    "服务暂时不可用",
		ErrCodeParam:                                 "请求参数错误",
		ErrCodeRequestTimeException:                  "设备时间异常，请修改后重试",
		ErrCodeSignature:                             "签名验证失败",
		ErrCodeForceUpdate:                           "需要升级",
		ErrCodeAuthCode:                              "验证码错误",
		ErrCodeAuthCodeInvalid:                       "验证码无效,请重新获取",
		ErrCodeImgAuthCode:                           "图形验证码错误",
		ErrCodeInvalidInviteCode:                     "无效的邀请码",
		ErrCodeActionTimeout:                         "操作超时",
		ErrCodeCWSParam:                              "cws错误",
		ErrCodeResubmit:                              "请勿重复提交",
		ErrCodeStatus:                                "状态错误",
		ErrCodeFrequent:                              "请求频繁",
		ErrCodeFileTooLarge:                          "文件过大",
		ErrCodeNonsupportFileType:                    "不支持的文件类型",
		ErrCodeTextLengthLimit:                       "字符长度超限",
		ErrCodeLimitCountry:                          "暂不对当前地区提供服务",
		ErrCodeFeatureDisabled:                       "暂不提供该服务",
		ErrCodeAPIFrequent:                           "请求频次过多，请稍后再试",
		ErrCodeConfirmationIsRequired:                "需要进行确认",
		ErrCodeShouldLogin:                           "请先登录",
		ErrCodeUserNotExist:                          "用户不存在",
		ErrCodeAccountIsExist:                        "账号已存在",
		ErrCodeAccountFormat:                         "账号格式错误",
		ErrCodeTooLongAccount:                        "账号长度过长",
		ErrCodeLoginElseWhere:                        "账号在其它地方登录",
		ErrCodeFundPwdNotSet:                         "资金密码未设置",
		ErrCodeLoginPwdNotSet:                        "登录密码未设置",
		ErrCodeLoginPwdIncorrect:                     "登录密码不正确",
		ErrCodeLoginPwdSameAsFundPwd:                 "登录密码不能与资金密码相同",
		ErrCodeFundPwdSameAsLoginPwd:                 "资金密码不能与登录密码相同",
		ErrCodeLoginNotAllowed:                       "禁止登录",
		ErrCodeTradeNotAllowed:                       "当前用户被禁止交易",
		ErrCodeWithdrawNotAllowed:                    "当前用户被禁止提币",
		ErrCodeHasAccountLock:                        "其他用户正在进行对当前账号进行注册或绑定操作,请稍后再试",
		ErrCodeNeedVerify:                            "用户未认证",
		ErrCodeIdNumberVerified:                      "证件信息已经审核通过,请进行面部认证",
		ErrCodeIdNumberExist:                         "证件号已被占用",
		ErrCodeVerifyIdNumber:                        "无效的身份证号",
		ErrCodeVerifyValidity:                        "姓名与身份证号不匹配",
		ErrCodeNeedVerifyIdentity:                    "需要先通过身份信息审核",
		ErrCodeFaceVerified:                          "面部认证已通过",
		ErrCodeExisted:                               "已存在",
		ErrCodeTradeIDNotMatch:                       "当前身份无法执行此操作",
		ErrCodeDealerFollowLimit:                     "当前交易员跟随人数已满",
		ErrCodeDealerCanNotFollow:                    "交易员不可进行跟单绑定",
		ErrCodeNoFollow:                              "当前未跟随",
		ErrCodeNonsupportModifyEmail:                 "不支持修改邮箱",
		ErrCodeModifySafeModeCount:                   "当前操作需要有两种以上安全验证",
		ErrCodeSafePhoneCodeInvalid:                  "无效的手机验证码",
		ErrCodeSafeEmailCodeInvalid:                  "无效的邮箱验证码",
		ErrCodeSafeTotpCodeInvalid:                   "无效的验证器验证码",
		ErrCodeSafePhoneCodeExpire:                   "手机验证码已过期,请重新获取",
		ErrCodeSafeEmailCodeExpire:                   "邮箱验证码已过期,请重新获取",
		ErrCodeSafeNeedVerify:                        "需要进行两步验证",
		ErrCodeSafeNeedFundPasswordVerify:            "需要进行资金密码校验",
		ErrCodeSafeFundPassword:                      "资金密码错误",
		ErrCodeSameOldPassword:                       "与旧密码相同",
		ErrCodeFewTransfer:                           "小于最小单笔划转限制",
		ErrCodeManyTransfer:                          "超过最大单笔划转限制",
		ErrCodeCoinNotSupportTransfer:                "币种暂不支持划转",
		ErrCodeBalanceInsufficient:                   "可用余额不足",
		ErrCodeInvalidAddress:                        "无效的地址",
		ErrCodeWithdrawHasApply:                      "当日有正在审核中的提币申请",
		ErrCodeWithdrawHasPassed:                     "当日提币成功次数已达上限",
		ErrCodeDepositInsufficient:                   "保证金不足",
		ErrCodeNotSupportWithdraw:                    "当前币种不支持提币",
		ErrCodeWithdrawLessMinLimit:                  "小于最小提币限额",
		ErrCodeWithdrawGreaterMaxLimit:               "大于最大提币限额",
		ErrCodeTransferInsufficient:                  "剩余可划转数量不足",
		ErrCodeSafeGuardForSetting:                   "此账号已修改了安全设置。为了您的账号安全，24小时内禁止提币",
		ErrCodeReceivingLimitReached:                 "模拟账户总权益大于50W不可再领取",
		ErrCodeNeedWithdrawVerifyPassed:              "需要先完成提币身份认证",
		ErrCodeLegalWithdrawSafeGuard:                "为了您的账户安全。法币交易后，24小时内暂不可提币。",
		ErrCodeSafeGuardForFundPwd:                   "资金密码连续输入5次错误，并在24小时内禁止提币。",
		ErrCodeWithdrawalRestrictionsMail:            "提币限制: 请完成邮箱绑定",
		ErrCodeInsufficientFollowBalance:             "您的跟单可用或授权资金不满足交易员要求的最低账户资金，请转入资金后再进行跟单。",
		ErrCodeDealerLimitIsChanged:                  "交易员修改了配置",
		ErrCodeUnverifyWithdrawOver:                  "您本日提币金额已达到限额，需要身份认证才可继续提币。",
		ErrCodeNonsupportTransferAsset:               "暂不支持当前所选账户之间的划转",
		ErrCodeHasGiftCashNonsupportTransferOut:      "您当前有使用赠金的持仓或委托，暂时不可划转",
		ErrCodeHasGiftCashDeviceLimit:                "领取失败，该设备已领取过赠金。",
		ErrCodeAssetIsLiquidise:                      "当前资产清算中，稍后再试",
		ErrCodeContractNotExist:                      "合约不存在",
		ErrCodeContractNotTrade:                      "合约暂不可交易",
		ErrCodeNonSupportOptimal:                     "暂不支持最优价下单",
		ErrCodeNonSupportPrice:                       "不支持的价格",
		ErrCodePositionInsufficient:                  "剩余持仓数不足",
		ErrCodeTooManyOrderVolume:                    "超过最大持仓量，触发风险调控。请调整下单量",
		ErrCodeTradeFrequent:                         "委托请求频繁",
		ErrCodeNonSupportChangeDeposit:               "全仓模式不支持调整调整保证金",
		ErrCodeBelowMinOpenVolume:                    "低于单笔最小开仓量",
		ErrCodeOverMaxOpenVolume:                     "高于单笔最大开仓量",
		ErrCodePositionInvalid:                       "当前持仓无效或已平仓",
		ErrCodeLeverInvalid:                          "与当前持仓或计划单杠杆倍数不同",
		ErrCodeOverPlaceMoney:                        "大于可下单金额",
		ErrCodePlanOrderLimit:                        "您合约未触发的计划单已达到10条",
		ErrCodeLimitLEPrice:                          "止盈价格需要大于开仓均价",
		ErrCodeStopGEPrice:                           "止损价格需要小于开仓均价",
		ErrCodeLimitGEPrice:                          "止盈价格需要小于开仓均价",
		ErrCodeStopLEPrice:                           "止损价格需要大于开仓均价",
		ErrCodePlanLimitLEPrice:                      "止盈价格需要大于触发价格",
		ErrCodePlanStopGEPrice:                       "止损价格需要小于触发价格",
		ErrCodePlanLimitGEPrice:                      "止盈价格需要小于触发价格",
		ErrCodePlanStopLEPrice:                       "止损价格需要大于触发价格",
		ErrCodeLimitEIndex:                           "止盈价格不可以等于合约指数",
		ErrCodeStopEIndex:                            "止损价格不可以等于合约指数",
		ErrCodePlanCloseCountLimit:                   "止盈止损已经超出6条 请删除后再设置",
		ErrCodeMarginModifyBalanceInsufficient:       "可用余额不足增加保证金失败",
		ErrCodeMarginModifyMarginInsufficient:        "保证金不足无法减少",
		ErrCodeAccountTypeHasFullPosition:            "您当前有全仓模式的持仓请先平仓后再进行切换",
		ErrCodeAccountTypeHasFullPlan:                "您当前有全仓模式的计划单请先撤销后再进行切换",
		ErrCodeAccountTypeHasPartPosition:            "您当前有逐仓模式的持仓请先平仓后再进行切换",
		ErrCodeAccountTypeHasPartPlan:                "您当前有逐仓模式的计划单请先撤销后在进行切换",
		ErrCodeLeverHasBuyPartOrder:                  "您当前合约多单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换",
		ErrCodeLeverHasSellPartOrder:                 "您当前合约空单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换",
		ErrCodeNoAccountTypePlace:                    "无法以当前账户模式下单",
		ErrCodeContractMarketStop:                    "下单失败，请稍后再试",
		ErrCodeNonsupportSwitchAccountType:           "暂不支持切换到指定账户模式",
		ErrCodeOverUnitTrade:                         "下单失败，请稍后再试",
		ErrCodePartialFailed:                         "部分平仓失败,请稍后再试",
		ErrCodeNotEnoughVolumeTrade:                  "下单失败，暂无对手盘可成交",
		ErrCodeCloseOverPlace:                        "超过单笔最大下单量，触发风险调控。请调整下单量",
		ErrCodeNoPlatformSell:                        "暂无商户接单",
		ErrCodeNoPlatformSellTime:                    "不在商户服务时间",
		ErrCodeLegalLessThanAmountLimit:              "单笔最小限额为 %s",
		ErrCodeLegalMoreThanAmountLimit:              "单笔最大限额为 %s",
		ErrCodeLegalHasUndoneOrder:                   "有未完成订单",
		ErrCodeLegalPriceRateInvalid:                 "抱歉，该报价已失效，请重新下单",
		ErrCodeDigitalLessThanAmountLimit:            "下单币种数量应大于 %s%s",
		ErrCodeDigitalMoreThanAmountLimit:            "下单币种数量应小于 %s%s",
		ErrCodeFiatLessThanAmountLimit:               "下单法币数量应大于 %s%s",
		ErrCodeFiatMoreThanAmountLimit:               "下单法币数量应小于 %s%s",
		ErrCodeActivityExpire:                        "活动已过期",
		ErrCodeActivityAdopt:                         "奖励已发放",
		ErrCodePaymentsOverLimit:                     "您的收款方式已达到上限，暂不可添加新的收款方式",
		ErrCodeApiCreateOverLimit:                    "API创建数达到上限",
		ErrCodeApiBindIPOverLimit:                    "绑定IP数超出上限",
		ErrCodeApiBindIPv6OverLimit:                  "最多仅支持绑定两个IPv6地址",
		ErrCodeApiCreateNoPermission:                 "当前用户暂无创建和修改API权限",
		ErrCodeApiIPAddress:                          "IP地址不正确",
		ErrCodeApiOverdue:                            "当前API已失效",
		ErrCodeWithdrawAddrCountLimit:                "提币地址已达到最大数量，添加失败。",
		ErrCodeWithdrawAddressExisted:                "当前地址已存在",
		ErrCodeSafeSpareEmailCodeInvalid:             "无效的备用邮箱验证码",
		ErrCodeSafeSpareEmailCodeExpire:              "备用邮箱验证码已过期,请重新获取",
		ErrCodeNonsupportAccountType:                 "不支持的账户模式",
		ErrCodeNonsupportPositionSplitMode:           "您当前持有仓位或未成交的委托，不允许更改分仓模式。",
		ErrCodeDeleteAccountAtRiskStateLimit:         "账户存在风控状态，请联系客服",
		ErrCodeDeleteAccountHasUndoneOrder:           "账户存在持仓，委托及跟随交易员，请确认以上内容全部取消后再试",
		ErrCodeAccountIsDeleteState:                  "此账户已注销，如需再次注册请联系官方客服",
		ErrCodeNonsupportPhoneNumber:                 "暂不支持手机号",
		ErrCodeRiskLess:                              "当前风险率过低,触发风险调控。暂不可执行此操作",
		ErrCodeOverPositionCount:                     "超出最大持仓数",
		ErrCodeFollowNotSupport:                      "当前合约暂不支持带单",
		ErrCodePlanEntrustOverContractLimit:          "委托失败，执行价格超过当前限价:%v",
		ErrCodePlanEntrustLessContractLimit:          "委托失败，执行价格低于当前限价：%v",
		ErrCodePlanEntrustMore:                       "执行价格请≥%v，否则您的单可能不会成交",
		ErrCodePlanEntrustLess:                       "执行价格请<=%v，否则您的订单可能不会成交",
		ErrCodeOverMaxSideEntrust:                    "超出合约单边最大挂单量",
		ErrCodeDepthInvalid:                          "当前深度不足",
		ErrCodePriceOverLimit:                        "当前委托价格超过允许范围",
		ErrCodePriceInvalid:                          "当前委托价格无效",
		ErrCodeEntrustCloseVolumeInvalid:             "当前限价委托存在本仓位的平仓委托，请撤销后再使用极速平仓",
		ErrCodePriceMarketOver:                       "超过价差保护阈值,触发风险调控",
		ErrCodePositionState:                         "此仓位状态已经更新，请查看仓位后再进行设置",
		ErrCodeInMaintenance:                         "该合约正在维护中，请稍后再试",
		ErrCodeNetPosException:                       "委托失败",
		ErrCodeForceClosing:                          "当前正进行强平清算，请稍后再试",
		ErrCodeClosing:                               "当前合约正进行清算，请稍后再试",
		ErrCodeOrderIdUserd:                          "订单号已存在",
		ErrCodeUnfinishedEntrustOrderTooMany:         "当前未完成委托数过多",
		ErrCodeOrderState:                            "当前订单状态已更新,请刷新后重试",
		ErrCodePlanLimitLEEntrustPrice:               "止盈价格需要大于执行价格",
		ErrCodePlanStopGEEntrustPrice:                "止损价格需要小于执行价格",
		ErrCodePlanLimitGEEntrustPrice:               "止盈价格需要小于执行价格",
		ErrCodePlanStopLEEntrustPrice:                "止损价格需要大于执行价格",
		ErrCodeBlowingRisk:                           "您本次开仓有强平风险，已触发风险调控。",
		ErrCodeNonsupportMarginCoin:                  "不支持的保证金币种",
		ErrCodeBonusNonsupportHedgeMode:              "赠金暂不支持双向开仓，请平仓后再试。",
		ErrCodeNonsupportExchange:                    "不支持的兑换",
		ErrCodeExchangePreOrderOverdue:               "当前价格已过期,请重新获取",
		ErrCodeExchangePriceVolatility:               "当前价格波动较大，本次下单价格已失效",
		ErrCodeExchangeLessMinVolume:                 "单笔最少%s%s",
		ErrCodeExchangeGreaterMaxVolume:              "单笔最大%s%s",
		ErrCodeExchangePrecisionLimit:                "输入精度限制",
		ErrCodeExchangeDailyLimit:                    "超出每日限额,剩余额度: %s%s",
		ErrCodeGiftCashActivityExceed:                "活动已经过期",
		ErrCodeGiftCashReceiveExceed:                 "已超过领取期限",
		ErrCodePositionNumsLimit:                     "分仓模式每个合约单边最多5个持仓，您可以选择持有的仓位进行加仓操作",
		ErrCodeNonsupportPositionSplitModeForVersion: "当前版本无法在当前用户开仓模式下单，请升级版本。",
	},
	ReqLangEN: {
		ErrCodeBusy:                                  "The system is busy",
		ErrCodeActionInvalid:                         "Invalid request",
		ErrCodeServerNotAvailable:                    "Service Temporarily Unavailable",
		ErrCodeParam:                                 "Request parameter error",
		ErrCodeRequestTimeException:                  "Abnormal device time, please modify and try again", // 机翻
		ErrCodeSignature:                             "Sign verification failed",
		ErrCodeForceUpdate:                           "Upgrade required",
		ErrCodeAuthCode:                              "The verification code was incorrect",
		ErrCodeAuthCodeInvalid:                       "The verification code is invalid, please get it again",
		ErrCodeImgAuthCode:                           "The verification code was incorrect",
		ErrCodeInvalidInviteCode:                     "Invalid invitation code",
		ErrCodeActionTimeout:                         "Operation timed-out",
		ErrCodeCWSParam:                              "cws error",
		ErrCodeResubmit:                              "Please click only once",
		ErrCodeStatus:                                "Status error",
		ErrCodeFrequent:                              "Frequent requests",
		ErrCodeFileTooLarge:                          "File is too large",
		ErrCodeNonsupportFileType:                    "Unsupported file type",
		ErrCodeTextLengthLimit:                       "Character length exceeded",
		ErrCodeLimitCountry:                          "Services are not available for the current region at this time",
		ErrCodeFeatureDisabled:                       "This service is not currently available",
		ErrCodeAPIFrequent:                           "Too many requests, please try again later",
		ErrCodeConfirmationIsRequired:                "Confirmation required",
		ErrCodeShouldLogin:                           "Please sign in first",
		ErrCodeUserNotExist:                          "User does not exist",
		ErrCodeAccountIsExist:                        "Account already exists",
		ErrCodeAccountFormat:                         "Account format error",
		ErrCodeTooLongAccount:                        "Account length is too long",
		ErrCodeLoginElseWhere:                        "Account login in other places",
		ErrCodeFundPwdNotSet:                         "Password Not Set",
		ErrCodeLoginPwdNotSet:                        "Login password is not set",
		ErrCodeLoginPwdIncorrect:                     "Incorrect login password",
		ErrCodeLoginPwdSameAsFundPwd:                 "The login password cannot be the same as the fund password",
		ErrCodeFundPwdSameAsLoginPwd:                 "The fund password cannot be the same as the login password",
		ErrCodeLoginNotAllowed:                       "Forbidden login",
		ErrCodeTradeNotAllowed:                       "The current user is prohibited transaction",
		ErrCodeWithdrawNotAllowed:                    "The current user is prohibited from withdrawing",
		ErrCodeHasAccountLock:                        "Other users are registering or binding the current account. Please try again later",
		ErrCodeNeedVerify:                            "User is not authenticated",
		ErrCodeIdNumberVerified:                      "The credential information has been reviewed, please perform facial authentication",
		ErrCodeIdNumberExist:                         "ID number has been occupied",
		ErrCodeVerifyIdNumber:                        "Invalid ID number",
		ErrCodeVerifyValidity:                        "Name does not match ID number",
		ErrCodeNeedVerifyIdentity:                    "Need to pass the identity information review first",
		ErrCodeFaceVerified:                          "Facial authentication has passed",
		ErrCodeExisted:                               "Existed",
		ErrCodeTradeIDNotMatch:                       "The current identity cannot perform this operation",
		ErrCodeDealerFollowLimit:                     "The current followers of trader is full",
		ErrCodeDealerCanNotFollow:                    "Traders cannot perform copy binding",
		ErrCodeNoFollow:                              "Not following",
		ErrCodeNonsupportModifyEmail:                 "Does not support modifying email",
		ErrCodeModifySafeModeCount:                   "The current operation requires more than two security verifications",
		ErrCodeSafePhoneCodeInvalid:                  "Invalid mobile verification code",
		ErrCodeSafeEmailCodeInvalid:                  "Invalid mailbox verification code",
		ErrCodeSafeTotpCodeInvalid:                   "Invalid verifier captcha",
		ErrCodeSafePhoneCodeExpire:                   "The mobile phone verification code has expired, please get it again",
		ErrCodeSafeEmailCodeExpire:                   "Email verification code has expired, please get it again",
		ErrCodeSafeNeedVerify:                        "Two-step verification is required",
		ErrCodeSafeNeedFundPasswordVerify:            "Fund password verification is required",
		ErrCodeSafeFundPassword:                      "The fund password is incorrect",
		ErrCodeSameOldPassword:                       "Same as old password",
		ErrCodeFewTransfer:                           "Less than the minimum single transfer limit",
		ErrCodeManyTransfer:                          "Exceed the maximum single transfer limit",
		ErrCodeCoinNotSupportTransfer:                "Currency does not support transfer",
		ErrCodeBalanceInsufficient:                   "Insufficient available balance",
		ErrCodeInvalidAddress:                        "Invalid address",
		ErrCodeWithdrawHasApply:                      "There are withdrawal applications under processing on the day",
		ErrCodeWithdrawHasPassed:                     "The number of successful withdrawals on the day has reached the upper limit",
		ErrCodeDepositInsufficient:                   "Insufficient margin",
		ErrCodeNotSupportWithdraw:                    "The current currency does not support withdrawal",
		ErrCodeWithdrawLessMinLimit:                  "Less than the minimum withdrawal limit",
		ErrCodeWithdrawGreaterMaxLimit:               "More than the maximum withdrawal limit",
		ErrCodeTransferInsufficient:                  "The Insufficient amount of remaining transferable",
		ErrCodeSafeGuardForSetting:                   "Security settings have been modified for this account. For the sake of your account security, it is forbidden to withdraw money within 24 hours.",
		ErrCodeReceivingLimitReached:                 "The total equity of the demo account more than 500K USDT and cannot be claimed",
		ErrCodeNeedWithdrawVerifyPassed:              "Need to complete the identity verification for withdrawal",
		ErrCodeLegalWithdrawSafeGuard:                "For the security of your account. After the legal currency transaction, it can not be withdrawn within 24 hours.",
		ErrCodeSafeGuardForFundPwd:                   "The fund password is entered incorrectly for 5 consecutive times, and withdrawal is prohibited within 24 hours.",                                    // 机翻
		ErrCodeWithdrawalRestrictionsMail:            "Withdrawal restrictions: Please complete the mailbox binding",                                                                                       // 机翻
		ErrCodeInsufficientFollowBalance:             "Your copy is available or the authorized funds do not meet the minimum account funds required by the trader, please transfer funds before copying.", // 机翻
		ErrCodeDealerLimitIsChanged:                  "Trader modified the configuration",                                                                                                                  // 机翻
		ErrCodeUnverifyWithdrawOver:                  "You have reached the limit for the amount of coins you have withdrawn this day, and you need authentication to continue to withdraw coins.",
		ErrCodeNonsupportTransferAsset:               "Transfers between currently selected accounts are not currently supported",
		ErrCodeHasGiftCashNonsupportTransferOut:      "There are positions or orders that use the bonus, you cannot be transferred assets now",
		ErrCodeHasGiftCashDeviceLimit:                "Claim failed, the device has already claimed the bonus.",
		ErrCodeAssetIsLiquidise:                      "The current asset is being liquidated, try again later",
		ErrCodeContractNotExist:                      "Contract does not exist",
		ErrCodeContractNotTrade:                      "Temporarily non-tradable",
		ErrCodeNonSupportOptimal:                     "Does not support the best price order",
		ErrCodeNonSupportPrice:                       "Unsupported price",
		ErrCodePositionInsufficient:                  "Insufficient remaining positions",
		ErrCodeTooManyOrderVolume:                    "Exceeding the maximum position, triggering risk control. Please adjust the order quantity",
		ErrCodeTradeFrequent:                         "Frequent delegation requests",
		ErrCodeNonSupportChangeDeposit:               "The cross position does not support adjustment margin",
		ErrCodeBelowMinOpenVolume:                    "BLess than the single minimum opening amount",
		ErrCodeOverMaxOpenVolume:                     "Higher than the single largest open amount",
		ErrCodePositionInvalid:                       "Current position is invalid or closed",
		ErrCodeLeverInvalid:                          "Leverage cannot be changed when have positions or planned orders",
		ErrCodeOverPlaceMoney:                        "More than the orderable amount",
		ErrCodePlanOrderLimit:                        "10 contract has not triggered",
		ErrCodeLimitLEPrice:                          "The take profit price needs to be higher than the average opening price",
		ErrCodeStopGEPrice:                           "The stop loss price needs to be lower than the average opening price",
		ErrCodeLimitGEPrice:                          "The take profit price needs to be lower than the average opening price",
		ErrCodeStopLEPrice:                           "The stop loss price needs to be higher than the average opening price",
		ErrCodePlanLimitLEPrice:                      "The take profit price needs to be greater than the trigger price",
		ErrCodePlanStopGEPrice:                       "The stop loss price needs to be lower than the trigger price",
		ErrCodePlanLimitGEPrice:                      "The take profit price needs to be lower than the trigger price",
		ErrCodePlanStopLEPrice:                       "The stop loss price needs to be higher than the trigger price",
		ErrCodeLimitEIndex:                           "The take profit price cannot be equal to the contract index",
		ErrCodeStopEIndex:                            "The stop loss price cannot be equal to the contract index",
		ErrCodePlanCloseCountLimit:                   "Stop-profit and stop-loss have exceeded 6 items, please delete and set again",
		ErrCodeMarginModifyBalanceInsufficient:       "Insufficient available balance,failed to increase margin",
		ErrCodeMarginModifyMarginInsufficient:        "Insufficient margin",
		ErrCodeAccountTypeHasFullPosition:            "You currently have a position in the full position mode, please close the position before switching",
		ErrCodeAccountTypeHasFullPlan:                "You currently have a plan in full warehouse mode, please cancel it before switching",
		ErrCodeAccountTypeHasPartPosition:            "You currently have a position in the position by position mode, please close the position before switching",
		ErrCodeAccountTypeHasPartPlan:                "You currently have a warehouse-by-warehouse plan order, please cancel it before switching",
		ErrCodeLeverHasBuyPartOrder:                  "Your current contract has multiple orders or has not triggered the plan order, please close or cancel the position before performing the leverage switch",
		ErrCodeLeverHasSellPartOrder:                 "Your current contract empty order has a position or the plan order is not triggered, please close or cancel the position before performing the leverage switch",
		ErrCodeNoAccountTypePlace:                    "Cannot place an order with the current account mode",
		ErrCodeContractMarketStop:                    "Order failed, please try again later",
		ErrCodeNonsupportSwitchAccountType:           "Not support switching to designated account mode",
		ErrCodeOverUnitTrade:                         "Order failed, please try again later",
		ErrCodePartialFailed:                         "Partial closing failed, please try again later",
		ErrCodeNotEnoughVolumeTrade:                  "The order is unsuccessful, and there is no counterparty to be traded",
		ErrCodeCloseOverPlace:                        "Exceeding the single maximum order quantity triggers risk control. Please adjust the order quantity",
		ErrCodeNoPlatformSell:                        "No merchant orders",
		ErrCodeNoPlatformSellTime:                    "Not in business hours",
		ErrCodeLegalLessThanAmountLimit:              "The minimum limit for a single transaction is %s",
		ErrCodeLegalMoreThanAmountLimit:              "The maximum limit for a single transaction is %s",
		ErrCodeLegalHasUndoneOrder:                   "You have position or open order now, please close it",
		ErrCodeLegalPriceRateInvalid:                 "Sorry, the quotation has failure, please order again",
		ErrCodeDigitalLessThanAmountLimit:            "The quantity of the order currency should be greater than %s%s",
		ErrCodeDigitalMoreThanAmountLimit:            "The quantity of the order currency should be less than %s%s",
		ErrCodeFiatLessThanAmountLimit:               "The amount of fiat currency to place an order should be greater than %s%s ",
		ErrCodeFiatMoreThanAmountLimit:               "The amount of fiat currency to place an order should be less than %s%s",
		ErrCodeActivityExpire:                        "Activity expired",
		ErrCodeActivityAdopt:                         "Award issued",
		ErrCodePaymentsOverLimit:                     "Your payment method has reached the upper limit, you cannot add a new payment method temporarily",
		ErrCodeApiCreateOverLimit:                    "The number of API creations reaches the upper limit",
		ErrCodeApiBindIPOverLimit:                    "The number of bound IPs exceeds the upper limit",
		ErrCodeApiBindIPv6OverLimit:                  "Only supports binding up to two IPv6 addresses",
		ErrCodeApiCreateNoPermission:                 "The current user has no permission to create and modify API",
		ErrCodeApiIPAddress:                          "IP address is incorrect",
		ErrCodeApiOverdue:                            "The current API is invalid",
		ErrCodeWithdrawAddrCountLimit:                "The withdrawal address has reached the maximum number, and the addition failed.",
		ErrCodeWithdrawAddressExisted:                "Current address already exists",
		ErrCodeSafeSpareEmailCodeInvalid:             "Invalid alternate mailbox verification code",
		ErrCodeSafeSpareEmailCodeExpire:              "The alternate mailbox verification code has expired. Please get it again",
		ErrCodeNonsupportAccountType:                 "Unsupported account mode",
		ErrCodeNonsupportPositionSplitMode:           "You currently hold positions or unfilled orders, and you are not allowed to change the position split mode.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "The account in risk control status, please contact customer service",
		ErrCodeDeleteAccountHasUndoneOrder:           "There are positions or orders or following traders, please confirm that all the above contents are cancelled and try again",
		ErrCodeAccountIsDeleteState:                  "This account has been delete, if you need to register again, please contact the official customer service",
		ErrCodeNonsupportPhoneNumber:                 "Mobile phone number not supported",
		ErrCodeRiskLess:                              "The current risk rate is too low, triggering risk control. Cannot perform this operation temporarily",
		ErrCodeOverPositionCount:                     "Exceed the maximum number of positions",
		ErrCodeFollowNotSupport:                      "The current contract does not support taking orders",
		ErrCodePlanEntrustOverContractLimit:          "The order failed, the execution price exceeds the current limit price: %v",
		ErrCodePlanEntrustLessContractLimit:          "The order failed, the execution price is lower than the current limit price: %v",
		ErrCodePlanEntrustMore:                       "Please execute price ≥%v, otherwise your order may not be executed",
		ErrCodePlanEntrustLess:                       "Please execute price <=%v, otherwise your order may not be executed",
		ErrCodePositionState:                         "The status of this position has been updated, please check the position before setting",
		ErrCodeOverMaxSideEntrust:                    "Exceeds the maximum amount of pending orders on one side of the contract",
		ErrCodeDepthInvalid:                          "Current depth is insufficient",
		ErrCodePriceOverLimit:                        "The current commission price exceeds the allowable range",
		ErrCodePriceInvalid:                          "The current order price is invalid",
		ErrCodeEntrustCloseVolumeInvalid:             "The current limit order has a closing order for this position. Please cancel it before using the quick closing order",
		ErrCodePriceMarketOver:                       "Exceeding the spread protection threshold, triggering risk control",
		ErrCodeInMaintenance:                         "The contract is under maintenance, please try again later",
		ErrCodeNetPosException:                       "Delegation failed",
		ErrCodeForceClosing:                          "Liquidation is currently in progress, please try again later",
		ErrCodeClosing:                               "The current contract is being liquidated, please try again later",
		ErrCodeOrderIdUserd:                          "Order number already exists",
		ErrCodeUnfinishedEntrustOrderTooMany:         "Too many outstanding orders",
		ErrCodeOrderState:                            "The current order status has been updated, please refresh and try again",
		ErrCodePlanLimitLEEntrustPrice:               "The take profit price needs to be greater than the strike price",
		ErrCodePlanStopGEEntrustPrice:                "The stop loss price needs to be less than the strike price",
		ErrCodePlanLimitGEEntrustPrice:               "The take profit price needs to be less than the strike price",
		ErrCodePlanStopLEEntrustPrice:                "The stop loss price needs to be greater than the strike price",
		ErrCodeBlowingRisk:                           "There is a liquidation risk when opening a position, which has triggered risk control.",
		ErrCodeNonsupportMarginCoin:                  "Unsupported margin currency",
		ErrCodeBonusNonsupportHedgeMode:              "Bonus doesn’t support Hedge Mode, please close position and try again.",
		ErrCodeNonsupportExchange:                    "Unsupported exchange",
		ErrCodeExchangePreOrderOverdue:               "The price has expired. Please re-acquire.",
		ErrCodeExchangePriceVolatility:               "The price fluctuates greatly, and the price of this order has expired.",
		ErrCodeExchangeLessMinVolume:                 "Minimum for a single transaction %s%s",
		ErrCodeExchangeGreaterMaxVolume:              "Maximum for a single transaction %s%s",
		ErrCodeExchangePrecisionLimit:                "Enter accuracy limit",
		ErrCodeExchangeDailyLimit:                    "The daily limit is exceeded. remaining limit:%s%s",
		ErrCodeGiftCashActivityExceed:                "Event has expired",
		ErrCodeGiftCashReceiveExceed:                 "Collection period has expired",
		ErrCodePositionNumsLimit:                     "Split position mode Each contract can hold up to 5 positions on one side. You can choose the positions you hold to add positions.",
		ErrCodeNonsupportPositionSplitModeForVersion: "The current version cannot place an order in the current user open mode, please upgrade the version.",
	},
	ReqLangTC: {
		ErrCodeBusy:                                  "系統繁忙,請稍後再試",
		ErrCodeActionInvalid:                         "無效請求",
		ErrCodeServerNotAvailable:                    "服務暫時不可用",
		ErrCodeParam:                                 "請求參數錯誤",
		ErrCodeRequestTimeException:                  "設備時間异常，請修改後重試",
		ErrCodeSignature:                             "簽名驗證失敗",
		ErrCodeForceUpdate:                           "需要升級",
		ErrCodeAuthCode:                              "驗證碼錯誤",
		ErrCodeAuthCodeInvalid:                       "驗證碼無效,請重新獲取",
		ErrCodeImgAuthCode:                           "圖形驗證碼錯誤",
		ErrCodeInvalidInviteCode:                     "無效的邀請碼",
		ErrCodeActionTimeout:                         "操作超時",
		ErrCodeCWSParam:                              "cws錯誤",
		ErrCodeResubmit:                              "請勿重複提交",
		ErrCodeStatus:                                "狀態錯誤",
		ErrCodeFrequent:                              "請求頻繁",
		ErrCodeFileTooLarge:                          "文件過大",
		ErrCodeNonsupportFileType:                    "不支持的文件類型",
		ErrCodeTextLengthLimit:                       "字符長度超限",
		ErrCodeLimitCountry:                          "暫不對當前地區提供服務",
		ErrCodeFeatureDisabled:                       "暫不提供該服務",
		ErrCodeAPIFrequent:                           "請求頻次過多，請稍後再試",
		ErrCodeConfirmationIsRequired:                "需要進行確認",
		ErrCodeShouldLogin:                           "請先登錄",
		ErrCodeUserNotExist:                          "用戶不存在",
		ErrCodeAccountIsExist:                        "帳號已存在",
		ErrCodeAccountFormat:                         "帳號格式錯誤",
		ErrCodeTooLongAccount:                        "帳號長度過長",
		ErrCodeLoginElseWhere:                        "帳號在其他地方登錄",
		ErrCodeFundPwdNotSet:                         "資金密碼未設置",
		ErrCodeLoginPwdNotSet:                        "登錄密碼未設置",
		ErrCodeLoginPwdIncorrect:                     "登錄密碼不正確",
		ErrCodeLoginPwdSameAsFundPwd:                 "登錄密碼不能與資金密碼相同",
		ErrCodeFundPwdSameAsLoginPwd:                 "資金密碼不能與登錄密碼相同",
		ErrCodeLoginNotAllowed:                       "禁止登錄",
		ErrCodeTradeNotAllowed:                       "當前用戶被禁止交易",
		ErrCodeWithdrawNotAllowed:                    "當前用戶被禁止提幣",
		ErrCodeHasAccountLock:                        "其他用戶正在進行對當前帳號進行註冊或綁定操作,請稍後再試",
		ErrCodeNeedVerify:                            "用戶未認證",
		ErrCodeIdNumberVerified:                      "證件資訊已經審核通過,請進行面部認證",
		ErrCodeIdNumberExist:                         "證件號已被佔用",
		ErrCodeVerifyIdNumber:                        "无效的身份证号",
		ErrCodeVerifyValidity:                        "姓名與證件號不匹配",
		ErrCodeNeedVerifyIdentity:                    "需要先通過身份資訊審核",
		ErrCodeFaceVerified:                          "面部認證已通過",
		ErrCodeExisted:                               "已存在",
		ErrCodeTradeIDNotMatch:                       "當前身份無法執行此操作",
		ErrCodeDealerFollowLimit:                     "當前交易員跟隨人數已滿",
		ErrCodeDealerCanNotFollow:                    "交易員不可進行跟單綁定",
		ErrCodeNoFollow:                              "當前未跟隨",
		ErrCodeNonsupportModifyEmail:                 "不支持修改郵箱",
		ErrCodeModifySafeModeCount:                   "當前操作需要有兩種以上安全驗證",
		ErrCodeSafePhoneCodeInvalid:                  "無效的手機驗證碼",
		ErrCodeSafeEmailCodeInvalid:                  "無效的郵箱驗證碼",
		ErrCodeSafeTotpCodeInvalid:                   "無效的驗證器驗證碼",
		ErrCodeSafePhoneCodeExpire:                   "手機驗證碼已過期，請重新獲取",
		ErrCodeSafeEmailCodeExpire:                   "郵箱驗證碼已過期，請重新獲取",
		ErrCodeSafeNeedVerify:                        "需要進行兩步驗證",
		ErrCodeSafeNeedFundPasswordVerify:            "需要進行資金密碼校驗",
		ErrCodeSafeFundPassword:                      "資金密碼錯誤",
		ErrCodeSameOldPassword:                       "與舊密碼相同",
		ErrCodeFewTransfer:                           "小於最小單筆劃轉限制",
		ErrCodeManyTransfer:                          "超過最大單筆劃轉限制",
		ErrCodeCoinNotSupportTransfer:                "幣種暫不支持劃轉",
		ErrCodeBalanceInsufficient:                   "可用餘額不足",
		ErrCodeInvalidAddress:                        "無效的地址",
		ErrCodeWithdrawHasApply:                      "當日有正在審核中的提幣申請",
		ErrCodeWithdrawHasPassed:                     "當日提幣成功次數已達上限",
		ErrCodeDepositInsufficient:                   "保證金不足",
		ErrCodeNotSupportWithdraw:                    "當前幣種不支持提幣",
		ErrCodeWithdrawLessMinLimit:                  "小於最小提幣限額",
		ErrCodeWithdrawGreaterMaxLimit:               "大於最大提幣限額",
		ErrCodeTransferInsufficient:                  "剩餘可劃轉數量不足",
		ErrCodeSafeGuardForSetting:                   "此帳號已修改了安全設置。為了您的帳號安全，24小時內禁止提幣",
		ErrCodeReceivingLimitReached:                 "模擬賬戶總權益大於50W不可再領取",
		ErrCodeNeedWithdrawVerifyPassed:              "需要先完成提幣身份認證",
		ErrCodeLegalWithdrawSafeGuard:                "為了您的帳戶安全。法幣交易後，24小時內暫不可提幣。",
		ErrCodeSafeGuardForFundPwd:                   "資金密碼連續輸入5次錯誤，並在24小時內禁止提幣。",
		ErrCodeWithdrawalRestrictionsMail:            "提幣限制: 請完成郵箱綁定",
		ErrCodeInsufficientFollowBalance:             "您的跟單可用或授權資金不滿足交易員要求的最低賬戶資金，請轉入資金後再進行跟單。",
		ErrCodeDealerLimitIsChanged:                  "交易員修改了配置",
		ErrCodeUnverifyWithdrawOver:                  "您本日提幣金額已達到限額，需要身份認證才可繼續提幣。",
		ErrCodeNonsupportTransferAsset:               "暫不支持當前所選賬戶之間的劃轉",
		ErrCodeHasGiftCashNonsupportTransferOut:      "您當前有使用贈金的持倉或委託，暫時不可劃轉",
		ErrCodeHasGiftCashDeviceLimit:                "領取失敗，該設備已領取過贈金。",
		ErrCodeAssetIsLiquidise:                      "當前資產清算中，稍後再試",
		ErrCodeContractNotExist:                      "合約不存在",
		ErrCodeContractNotTrade:                      "合約暫不可交易",
		ErrCodeNonSupportOptimal:                     "暫不支持最優價下單",
		ErrCodeNonSupportPrice:                       "不支持的價格",
		ErrCodePositionInsufficient:                  "剩餘持倉數不足",
		ErrCodeTooManyOrderVolume:                    "超過最大持倉量，觸發風險調控。請調整下單量",
		ErrCodeTradeFrequent:                         "委託請求頻繁",
		ErrCodeNonSupportChangeDeposit:               "全倉模式不支持調整調整保證金",
		ErrCodeBelowMinOpenVolume:                    "低於單筆最小開倉量",
		ErrCodeOverMaxOpenVolume:                     "高於單筆最大開倉量",
		ErrCodePositionInvalid:                       "當前持倉無效或已平倉",
		ErrCodeLeverInvalid:                          "與當前持倉或計畫單杠杆倍數不同",
		ErrCodeOverPlaceMoney:                        "大於可下單金額",
		ErrCodePlanOrderLimit:                        "您合約未觸發的計劃單已達到10條",
		ErrCodeLimitLEPrice:                          "止盈價格需要大于開倉均價",
		ErrCodeStopGEPrice:                           "止損價格需要小于開倉均價",
		ErrCodeLimitGEPrice:                          "止盈價格需要小于開倉均價",
		ErrCodeStopLEPrice:                           "止損價格需要大于開倉均價",
		ErrCodePlanLimitLEPrice:                      "止盈價格需要大于觸發價格",
		ErrCodePlanStopGEPrice:                       "止損價格需要小于觸發價格",
		ErrCodePlanLimitGEPrice:                      "止盈價格需要小于觸發價格",
		ErrCodePlanStopLEPrice:                       "止損價格需要大于觸發價格",
		ErrCodeLimitEIndex:                           "止盈價格不可以等于合約指數",
		ErrCodeStopEIndex:                            "止損價格不可以等于合約指數",
		ErrCodePlanCloseCountLimit:                   "止盈止損已經超出6條 請刪除後再設置",
		ErrCodeMarginModifyBalanceInsufficient:       "可用餘額不足增加保證金失敗",
		ErrCodeMarginModifyMarginInsufficient:        "保證金不足無法減少",
		ErrCodeAccountTypeHasFullPosition:            "您當前有全倉模式的持倉請先平倉後再進行切換",
		ErrCodeAccountTypeHasFullPlan:                "您當前有全倉模式的計劃單請先撤銷後再進行切換",
		ErrCodeAccountTypeHasPartPosition:            "您當前有逐倉模式的持倉請先平倉後再進行切換",
		ErrCodeAccountTypeHasPartPlan:                "您當前有逐倉模式的計劃單請先撤銷後在進行切換",
		ErrCodeLeverHasBuyPartOrder:                  "您當前合約多單有持倉或未觸發計劃單請先平倉或撤銷後再進行槓桿切換",
		ErrCodeLeverHasSellPartOrder:                 "您當前合約空單有持倉或未觸發計劃單請先平倉或撤銷後再進行槓桿切換",
		ErrCodeNoAccountTypePlace:                    "無法以當前賬戶模式下單",
		ErrCodeContractMarketStop:                    "下單失敗，請稍後再試",
		ErrCodeNonsupportSwitchAccountType:           "暫不支持切換到指定帳戶模式",
		ErrCodeOverUnitTrade:                         "下單失敗，請稍後再試",
		ErrCodePartialFailed:                         "部分平仓失败,请稍后再试",
		ErrCodeNotEnoughVolumeTrade:                  "下單失敗，暫無對手盤可成交",
		ErrCodeCloseOverPlace:                        "超過單筆最大下單量，觸發風險調控。請調整下單量",
		ErrCodeNoPlatformSell:                        "暫無商戶接單",
		ErrCodeNoPlatformSellTime:                    "不在商戶服務時間",
		ErrCodeLegalLessThanAmountLimit:              "單筆最小限額為 %s",
		ErrCodeLegalMoreThanAmountLimit:              "單筆最大限額為 %s",
		ErrCodeLegalHasUndoneOrder:                   "有未完成訂單",
		ErrCodeLegalPriceRateInvalid:                 "抱歉，該報價已失效，請重新下單",
		ErrCodeDigitalLessThanAmountLimit:            "下單幣種數量應大於 %s%s",
		ErrCodeDigitalMoreThanAmountLimit:            "下單幣種數量應小於 %s%s",
		ErrCodeFiatLessThanAmountLimit:               "下單法幣數量應大於 %s%s",
		ErrCodeFiatMoreThanAmountLimit:               "下單法幣數量應小於 %s%s",
		ErrCodeActivityExpire:                        "活動已過期",
		ErrCodeActivityAdopt:                         "獎勵已發放",
		ErrCodePaymentsOverLimit:                     "您的收款方式已達到上限，暫不可添加新的收款方式",
		ErrCodeApiCreateOverLimit:                    "API創建數達到上限",
		ErrCodeApiBindIPOverLimit:                    "綁定IP數超出上限",
		ErrCodeApiBindIPv6OverLimit:                  "最多僅支持綁定兩個IPv6地址",
		ErrCodeApiCreateNoPermission:                 "當前用戶暫無創建和修改API權限",
		ErrCodeApiIPAddress:                          "IP地址不正確",
		ErrCodeApiOverdue:                            "當前API已失效",
		ErrCodeWithdrawAddrCountLimit:                "提幣地址已達到最大數量，添加失敗。",
		ErrCodeWithdrawAddressExisted:                "當前地址已存在",
		ErrCodeSafeSpareEmailCodeInvalid:             "無效的備用郵箱驗證碼",
		ErrCodeSafeSpareEmailCodeExpire:              "備用郵箱驗證碼已過期，請重新獲取",
		ErrCodeNonsupportAccountType:                 "不支持的帳戶模式",
		ErrCodeNonsupportPositionSplitMode:           "您當前持有倉位或未成交的委託，不允許更改分倉模式。",
		ErrCodeDeleteAccountAtRiskStateLimit:         "賬戶存在風控狀態，請聯繫客服",
		ErrCodeDeleteAccountHasUndoneOrder:           "賬戶存在持倉，委託及跟隨交易員，請確認以上內容全部取消後再試",
		ErrCodeAccountIsDeleteState:                  "此賬戶已註銷，如需再次註冊請聯繫官方客服",
		ErrCodeNonsupportPhoneNumber:                 "暫不支持手機號",
		ErrCodeRiskLess:                              "當前風險率過低,觸發風險調控。暫不可執行此操作",
		ErrCodeOverPositionCount:                     "超出最大持倉數",
		ErrCodeFollowNotSupport:                      "當前合約暫不支持帶單",
		ErrCodePlanEntrustOverContractLimit:          "委託失敗，執行價格超過當前限價:%v",
		ErrCodePlanEntrustLessContractLimit:          "委託失敗，執行價格低於當前限價：%v",
		ErrCodePlanEntrustMore:                       "執行價格請≥%v，否則您的單可能不會成交",
		ErrCodePlanEntrustLess:                       "執行價格請<=%v，否則您的訂單可能不會成交",
		ErrCodeOverMaxSideEntrust:                    "超出合約單邊最大掛單量",
		ErrCodeDepthInvalid:                          "當前深度不足",
		ErrCodePriceOverLimit:                        "當前委託價格超過允許範圍",
		ErrCodePriceInvalid:                          "當前委託價格無效",
		ErrCodeEntrustCloseVolumeInvalid:             "當前限價委託存在本倉位的平倉委託，請撤銷後再使用極速平倉",
		ErrCodePriceMarketOver:                       "超過價差保護閾值,觸發風險調控",
		ErrCodePositionState:                         "此倉位狀態已經更新，請查看倉位後再進行設置",
		ErrCodeInMaintenance:                         "該合約正在維護中，請稍後再試",
		ErrCodeNetPosException:                       "委託失敗",
		ErrCodeForceClosing:                          "當前正進行強平清算，請稍後再試",
		ErrCodeClosing:                               "當前合約正進行清算，請稍後再試",
		ErrCodeOrderIdUserd:                          "訂單號已存在",
		ErrCodeUnfinishedEntrustOrderTooMany:         "當前未完成委託數過多",
		ErrCodeOrderState:                            "當前訂單狀態已更新，請重繪後重試",
		ErrCodePlanLimitLEEntrustPrice:               "止盈價格需要大於執行價格",
		ErrCodePlanStopGEEntrustPrice:                "止損價格需要小於執行價格",
		ErrCodePlanLimitGEEntrustPrice:               "止盈價格需要小於執行價格",
		ErrCodePlanStopLEEntrustPrice:                "止損價格需要大於執行價格",
		ErrCodeBlowingRisk:                           "您本次開倉有強平風險，已觸發風險調控。",
		ErrCodeNonsupportMarginCoin:                  "不支持的保證金幣種",
		ErrCodeBonusNonsupportHedgeMode:              "贈金暫不支持雙向開倉，請平倉後再試。",
		ErrCodeNonsupportExchange:                    "不支持的兌換",
		ErrCodeExchangePreOrderOverdue:               "當前價格已過期，請重新獲取",
		ErrCodeExchangePriceVolatility:               "當前價格波動較大，本次下單價格已失效",
		ErrCodeExchangeLessMinVolume:                 "單筆最少%s%s",
		ErrCodeExchangeGreaterMaxVolume:              "單筆最大%s%s",
		ErrCodeExchangePrecisionLimit:                "輸入精度限制",
		ErrCodeExchangeDailyLimit:                    "超出每日限額，剩餘額度: %s%s",
		ErrCodeGiftCashActivityExceed:                "活動已經過期",
		ErrCodeGiftCashReceiveExceed:                 "已超過領取期限",
		ErrCodePositionNumsLimit:                     "分倉模式每個合約單邊最多5個持倉，您可以選擇持有的倉位進行加倉操作",
		ErrCodeNonsupportPositionSplitModeForVersion: "當前版本無法在當前用戶開倉模式下單，請升級版本。",
	},
	ReqLangKR: {
		ErrCodeBusy:                                  "시스템이 바쁘니 잠시 후에 다시 시도해 주십시오.",
		ErrCodeActionInvalid:                         "유효하지 않은 요청",
		ErrCodeServerNotAvailable:                    "서비스가 일시적으로 사용 불가능합니다",
		ErrCodeParam:                                 "요청 인자 오류",
		ErrCodeRequestTimeException:                  "설비 시간 이상, 수정 후 다시 시도 하 세 요", // 机翻
		ErrCodeSignature:                             "서명 검증 실패",
		ErrCodeForceUpdate:                           "업그레이드 필요",
		ErrCodeAuthCode:                              "인증 번호가 틀렸다",
		ErrCodeAuthCodeInvalid:                       "인증번호가 유효하지 않습니다, 다시 발급받으시기 바랍니다",
		ErrCodeImgAuthCode:                           "그래픽 인증 번호 오류",
		ErrCodeInvalidInviteCode:                     "잘못된 초대장 코드",
		ErrCodeActionTimeout:                         "작업 시간 초과",
		ErrCodeCWSParam:                              "cws 오류",
		ErrCodeResubmit:                              "중복 제출하지 마시오",
		ErrCodeStatus:                                "상태 오류",
		ErrCodeFrequent:                              "요청이 잦다",
		ErrCodeFileTooLarge:                          "파일이 너무 큼",
		ErrCodeNonsupportFileType:                    "지원하지 않는 파일 형식",
		ErrCodeTextLengthLimit:                       "문자열 길이 제한 초과",
		ErrCodeLimitCountry:                          "현재 지역에는 서비스가 제공되지 않습니다",
		ErrCodeFeatureDisabled:                       "이 서비스는 현재 사용할 수 없습니다",
		ErrCodeAPIFrequent:                           "요청이 너무 많습니다. 나중에 다시 시도해 주세요.",
		ErrCodeConfirmationIsRequired:                "확인 필요",
		ErrCodeShouldLogin:                           "먼저 로그인 하세요",
		ErrCodeUserNotExist:                          "사용자가 존재하지 않습니다",
		ErrCodeAccountIsExist:                        "계정이 존재함",
		ErrCodeAccountFormat:                         "계정 형식이 잘못되었습니다",
		ErrCodeTooLongAccount:                        "계좌번호 길이가 너무 길어서",
		ErrCodeLoginElseWhere:                        "아이디는 다른데 로그인하기",
		ErrCodeFundPwdNotSet:                         "자금 비밀번호가 설정되지 않았습니다",
		ErrCodeLoginPwdNotSet:                        "로그인 비밀번호가 설정되지 않았습니다",
		ErrCodeLoginPwdIncorrect:                     "로그인 비밀번호가 틀렸습니다",
		ErrCodeLoginPwdSameAsFundPwd:                 "로그인 비밀번호는 자금 비밀번호와 바꿀 수 없습니다.같다",
		ErrCodeFundPwdSameAsLoginPwd:                 "자금 비밀번호는 로그인 비밀번호와 바꿀 수 없습니다.같다",
		ErrCodeLoginNotAllowed:                       "로그인 금지",
		ErrCodeTradeNotAllowed:                       "현재 사용자의 거래 금지",
		ErrCodeWithdrawNotAllowed:                    "현재 사용자는 화폐인상이 금지되어 있다",
		ErrCodeHasAccountLock:                        "다른 사용자가 현재 진행 중아이디는 등록 또는 연동 조작을 하시면 됩니다.나중에 다시 해볼게요",
		ErrCodeNeedVerify:                            "사용자 인증 안됨",
		ErrCodeIdNumberVerified:                      "신분증 정보는 이미 심사를 통과하였다.인면 인식해주세요",
		ErrCodeIdNumberExist:                         "신분증는 이미 점용되었다",
		ErrCodeVerifyIdNumber:                        "유효하지 않은 주민등록번호",
		ErrCodeVerifyValidity:                        "이름과 주민번호가 일치하지 않음",
		ErrCodeNeedVerifyIdentity:                    "먼저 신원 정보 검열을 통과해야 합니다.",
		ErrCodeFaceVerified:                          "인면 인식 통과",
		ErrCodeExisted:                               "이미 존재하다",
		ErrCodeTradeIDNotMatch:                       "현재 위치에서 이 작업을 수행할 수 없습니다.",
		ErrCodeDealerFollowLimit:                     "현재 거래원의 수행원이 이미 꽉 찼다.",
		ErrCodeDealerCanNotFollow:                    "거래원은하청결정을할수없습니다.",
		ErrCodeNoFollow:                              "현재 미수종",
		ErrCodeNonsupportModifyEmail:                 "이메일 수정은 지원되지 않습니다",
		ErrCodeModifySafeModeCount:                   "현재 작업은 두 가지 이상이 필요합니다안전 검증",
		ErrCodeSafePhoneCodeInvalid:                  "잘못된 핸드폰 인증번호",
		ErrCodeSafeEmailCodeInvalid:                  "잘못된 이메일 인증번호",
		ErrCodeSafeTotpCodeInvalid:                   "잘못된 인증기 인증 번호",
		ErrCodeSafePhoneCodeExpire:                   "핸드폰 인증 번호가 이미 기한이 지났으니, 무거우세요.새로 가져오기",
		ErrCodeSafeEmailCodeExpire:                   "이메일 인증 번호가 이미 유효기간이 지났습니다.새로 가져오기",
		ErrCodeSafeNeedVerify:                        "2차 검증 필요",
		ErrCodeSafeNeedFundPasswordVerify:            "자금 비밀번호 검사를 진행해야 한다",
		ErrCodeSafeFundPassword:                      "자금 비밀번호 오류",
		ErrCodeSameOldPassword:                       "이전 비밀번호와 동일",
		ErrCodeFewTransfer:                           "최소 단일 회전 제한보다 작음",
		ErrCodeManyTransfer:                          "최대 단일 회전 제한 초과",
		ErrCodeCoinNotSupportTransfer:                "화폐의 종류는 잠시 이체하는 것을 지지하지 않는다.",
		ErrCodeBalanceInsufficient:                   "가용잔고부족",
		ErrCodeInvalidAddress:                        "잘못된 주소",
		ErrCodeWithdrawHasApply:                      "당일 심사 중인 화폐가 발행되었다.신청하다.",
		ErrCodeWithdrawHasPassed:                     "당일 화폐 인상에 성공한 횟수는 이미 상위에 이르렀다.제한하다",
		ErrCodeDepositInsufficient:                   "보증금부족",
		ErrCodeNotSupportWithdraw:                    "현재 화폐의 종류는 화폐인상을 지지하지 않는다.",
		ErrCodeWithdrawLessMinLimit:                  "최소통화인상한도액보다 작음",
		ErrCodeWithdrawGreaterMaxLimit:               "최대통화인상 한도보다 크다",
		ErrCodeTransferInsufficient:                  "잔여회전가능수량부족",
		ErrCodeSafeGuardForSetting:                   "이 계정의 보안 설정이 수정되었습니다.당신의 계좌 안전을 위해서 24시간 내에 인출을 금지합니다",
		ErrCodeReceivingLimitReached:                 "아날로그 계정 총이익이 50보다 큽니다W재수령불가",
		ErrCodeNeedWithdrawVerifyPassed:              "통화스와프 인증 먼저 끝내야",
		ErrCodeLegalWithdrawSafeGuard:                "귀하의 계좌 안전을 위해서입니다.법정 화폐 거래 후, 24시간 내에 잠시 언급할 수 없다.화폐",
		ErrCodeSafeGuardForFundPwd:                   "잘못된 자금 비밀번호를 5회 연속 입력하고 24시간 동안 인출을 금지한다.",                                    // 机翻
		ErrCodeWithdrawalRestrictionsMail:            "출금제한 : 우편함 바인딩을 완료해주세요.",                                                      // 机翻
		ErrCodeInsufficientFollowBalance:             "귀하의 사본을 사용할 수 있거나 승인된 자금이 거래자가 요구하는 최소 계정 자금을 충족하지 못합니다. 복사하기 전에 자금을 이체하십시오.", // 机翻
		ErrCodeDealerLimitIsChanged:                  "거래자가 구성을 수정했습니다.",                                                             // 机翻
		ErrCodeUnverifyWithdrawOver:                  "오늘 인출한 금액이 한도에 도달했으며, 코인을 계속 인출하려면 신원 확인이 필요합니다.",
		ErrCodeNonsupportTransferAsset:               "현재 선택한 계정 간의 이체는 현재 지원되지 않습니다.",
		ErrCodeHasGiftCashNonsupportTransferOut:      "보너스 잔액을 사용하고 있는 오더 있어서 지금 출금할 수 없습니다.",
		ErrCodeHasGiftCashDeviceLimit:                "청구 실패, 장치가 이미 보너스를 청구했습니다.",
		ErrCodeAssetIsLiquidise:                      "현재 자산이 청산 중입니다. 나중에 다시 시도하세요.",
		ErrCodeContractNotExist:                      "계약이 존재하지 않는다",
		ErrCodeContractNotTrade:                      "계약은 잠시 거래할 수 없다.",
		ErrCodeNonSupportOptimal:                     "최적 가격 계산은 당분간 지원하지 않는다.",
		ErrCodeNonSupportPrice:                       "지지하지 않는 가격",
		ErrCodePositionInsufficient:                  "잔존부족",
		ErrCodeTooManyOrderVolume:                    "최대 위치를 초과하여 위험 관리가 시작됩니다. 주문 수량을 조정하십시오",
		ErrCodeTradeFrequent:                         "청탁이 잦다",
		ErrCodeNonSupportChangeDeposit:               "전구간 모드에서 조정 지원 안 함보증금",
		ErrCodeBelowMinOpenVolume:                    "최소최소창고량보다",
		ErrCodeOverMaxOpenVolume:                     "최대최대창고량보다많다",
		ErrCodePositionInvalid:                       "현재 지분이 유효하지 않거나 균등화됨",
		ErrCodeLeverInvalid:                          "현재 보유 또는 계획표와 레버리지배수가 다르다",
		ErrCodeOverPlaceMoney:                        "발주가능금액보다 큽니다",
		ErrCodePlanOrderLimit:                        "계약에 의해 실행되지 않은 계획 주문이 10 개 있습니다.",
		ErrCodeLimitLEPrice:                          "지영가격이 재고량보다 커야 한다.값",
		ErrCodeStopGEPrice:                           "손실 방지 가격은 창고 개설균보다 작아야 한다.값",
		ErrCodeLimitGEPrice:                          "지영가격이 재고량보다 작아야 한다.값",
		ErrCodeStopLEPrice:                           "손실제한가격은 재고량보다 커야 한다.값",
		ErrCodePlanLimitLEPrice:                      "지영가격이 촉발가보다 커야 한다격",
		ErrCodePlanStopGEPrice:                       "손실 방지 값은 촉발 값보다 작아야 합니다.격",
		ErrCodePlanLimitGEPrice:                      "지영가격이 촉발가보다 작아야 한다격",
		ErrCodePlanStopLEPrice:                       "손실 방지 값은 촉발 값보다 커야 합니다.격",
		ErrCodeLimitEIndex:                           "지영 정지 가격은 계약과 같을 수 없다.지수",
		ErrCodeStopEIndex:                            "지손가격은 계약과 같을 수 없다.지수",
		ErrCodePlanCloseCountLimit:                   "손절 6건 초과 삭제 후 다시 설정해 주세요",
		ErrCodeMarginModifyBalanceInsufficient:       "가용잔액부족증대보증금실패하다.",
		ErrCodeMarginModifyMarginInsufficient:        "보증금 부족을 줄일 수 없다.",
		ErrCodeAccountTypeHasFullPosition:            "현재 풀 옵션 모드를 가지고 있습니다창고를 비우고 전환하십시오",
		ErrCodeAccountTypeHasFullPlan:                "현재 토털 모드 계획을 가지고 있습니다먼저 취소하고 다시 전환하십시오",
		ErrCodeAccountTypeHasPartPosition:            "현재 매입형 모드의 소유자가 있습니다창고를 비우고 전환하십시오",
		ErrCodeAccountTypeHasPartPlan:                "현재 캐시백 모델의 계획이 있습니다.먼저 취소하고 전환하십시오",
		ErrCodeLeverHasBuyPartOrder:                  "당신의 현재 계약서는 대부분 보유하고 있거나촉발되지 않은 계획서는 먼저 보통으로 보관하거나 취소하십시오.지렛대 전환 다시 시작하기",
		ErrCodeLeverHasSellPartOrder:                 "당신의 현재 계약서는 공매도이거나촉발되지 않은 계획서는 먼저 보통으로 보관하거나 취소하십시오.지렛대 전환 다시 시작하기",
		ErrCodeNoAccountTypePlace:                    "현재 계정으로 부팅할 수 없음",
		ErrCodeContractMarketStop:                    "주문이 실패했습니다. 잠시 후에 다시 시도하십시오",
		ErrCodeNonsupportSwitchAccountType:           "지정한 계정으로 전환하는 것을 지원하지 않습니다.패턴",
		ErrCodeOverUnitTrade:                         "주문이 실패했습니다. 잠시 후에 다시 시도하십시오",
		ErrCodePartialFailed:                         "부분적으로 창고를 평정하는데 실패했으니, 잠시 후에 다시 기다려 주십시오.시험하다",
		ErrCodeNotEnoughVolumeTrade:                  "주문이 실패했으며 거래 할 거래 상대방이 없습니다",
		ErrCodeCloseOverPlace:                        "한 펜의 최대 하단량을 초과하면 위험조정이 일어나므로 하단량을 조정하십시오.",
		ErrCodeNoPlatformSell:                        "인수인계가 없다",
		ErrCodeNoPlatformSellTime:                    "부재자 서비스 시간",
		ErrCodeLegalLessThanAmountLimit:              "최소 펜 한도는 %s입니다.",
		ErrCodeLegalMoreThanAmountLimit:              "단일 최대 한도는 %s",
		ErrCodeLegalHasUndoneOrder:                   "포지션 또는 오픈 오더가 있습니다, 닫으십시오",
		ErrCodeLegalPriceRateInvalid:                 "미안하지만 이 제시가격은 이미 효력을 상실하였습니다.다시 주문하다",
		ErrCodeDigitalLessThanAmountLimit:            "주문 통화의 수량은 %s%s 보다 커야 합니다.",
		ErrCodeDigitalMoreThanAmountLimit:            "주문 통화 수량은 %s%s 미만이어야 합니다.",
		ErrCodeFiatLessThanAmountLimit:               "주문할 법정 통화 금액은 %s%s 보다 커야 합니다.",
		ErrCodeFiatMoreThanAmountLimit:               "주문할 법정 통화 금액은 %s%s 미만이어야 합니다.",
		ErrCodeActivityExpire:                        "이벤트가 만료되었습니다",
		ErrCodeActivityAdopt:                         "상여가 이미 지급되었다",
		ErrCodePaymentsOverLimit:                     "당신의 수납 방식은 이미 상한에 도달했습니다.새로운 수납방식을 추가할 수 없습니다.",
		ErrCodeApiCreateOverLimit:                    "API 생성 수가 상한에 도달했습니다.",
		ErrCodeApiBindIPOverLimit:                    "바인딩 된 IP 수가 상한을 초과합니다.",
		ErrCodeApiBindIPv6OverLimit:                  "최대 2 개의 IPv6 주소 바인딩 만 지원합니다.",
		ErrCodeApiCreateNoPermission:                 "현재 사용자는 API를 만들고 수정할 권한이 없습니다.",
		ErrCodeApiIPAddress:                          "IP 주소가 올바르지 않습니다.",
		ErrCodeApiOverdue:                            "현재 API가 잘못되었습니다.",
		ErrCodeWithdrawAddrCountLimit:                "출금 주소가 최대 수에 도달하여 추가에 실패했습니다.",
		ErrCodeWithdrawAddressExisted:                "현재 주소가 이미 존재합니다",
		ErrCodeSafeSpareEmailCodeInvalid:             "잘못된 예비 메 일 인증 코드",
		ErrCodeSafeSpareEmailCodeExpire:              "예비 메 일 인증 코드 가 만 료 되 었 습 니 다. 다시 가 져 오 십시오.",
		ErrCodeNonsupportAccountType:                 "지원 되 지 않 는 계 정 모드",
		ErrCodeNonsupportPositionSplitMode:           "귀하는 현재 포지션 또는 미완료 주문을 보유하고 있으며 포지션 분할 모드를 변경할 수 없습니다.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "위험 관리 상태에 있는 계정은 고객 서비스에 문의하십시오.",
		ErrCodeDeleteAccountHasUndoneOrder:           "포지션, 오더, 팔로우 트레이더가 있습니다. 포지션이나 주문이 없는지 확인하고 다시 시도하세요.",
		ErrCodeAccountIsDeleteState:                  "삭제된 계정입니다, 재가입이 필요하시면 공식 고객센터로 연락주세요",
		ErrCodeNonsupportPhoneNumber:                 "지원되지 않는 휴대폰 번호",
		ErrCodeRiskLess:                              "현재 위험률이 너무 낮아 위험 제어를 트리거합니다. 이 작업을 일시적으로 수행할 수 없습니다.",
		ErrCodeOverPositionCount:                     "최대 위치 수 초과",
		ErrCodeFollowNotSupport:                      "현재 계약은 주문 접수를 지원하지 않습니다.",
		ErrCodePlanEntrustOverContractLimit:          "주문이 실패하고 실행 가격이 현재 제한 가격을 초과합니다:%v",
		ErrCodePlanEntrustLessContractLimit:          "주문이 실패했습니다. 실행 가격이 현재 제한 가격보다 낮습니다：%v",
		ErrCodePlanEntrustMore:                       "가격 ≥ %v를 실행하십시오. 그렇지 않으면 주문이 실행되지 않을 수 있습니다.",
		ErrCodePlanEntrustLess:                       "가격 <= %v을 실행하십시오. 그렇지 않으면 주문이 실행되지 않을 수 있습니다.",
		ErrCodeOverMaxSideEntrust:                    "계약의 한 쪽에서 대기중인 주문의 최대 금액을 초과합니다.",
		ErrCodeDepthInvalid:                          "현재 깊이가 충분하지 않습니다.",
		ErrCodePriceOverLimit:                        "현재 커미션 가격이 허용 범위를 초과합니다.",
		ErrCodePriceInvalid:                          "현재 주문 가격이 잘못되었습니다.",
		ErrCodeEntrustCloseVolumeInvalid:             "현재 가격 제한 위탁 은 본 창고 의 평 창 의뢰 가 존재 하 니, 취소 후 극 속 평 창 고 를 사용 하 십시오.",
		ErrCodePriceMarketOver:                       "확산 방지 임계 값을 초과하여 위험 제어 트리거.",
		ErrCodePositionState:                         "이 위치의 상태가 업데이트되었습니다. 설정하기 전에 위치를 확인하십시오.",
		ErrCodeInMaintenance:                         "계약이 유지 보수 중입니다. 나중에 다시 시도하십시오.",
		ErrCodeNetPosException:                       "위임 실패",
		ErrCodeForceClosing:                          "청산이 현재 진행 중입니다. 나중에 다시 시도하십시오.",
		ErrCodeClosing:                               "현재 계약이 청산 중입니다. 나중에 다시 시도해 주세요.",
		ErrCodeOrderIdUserd:                          "주문 번호가 이미 있습니다.",
		ErrCodeUnfinishedEntrustOrderTooMany:         "미결제 주문이 너무 많습니다.",
		ErrCodeOrderState:                            "현재 주문 상태가 업데이트되었으므로 새로 고치고 다시 시도하십시오",
		ErrCodePlanLimitLEEntrustPrice:               "이익실현 가격은 행사 가격보다 커야 합니다.",
		ErrCodePlanStopGEEntrustPrice:                "손절매 가격은 행사 가격보다 낮아야 합니다.",
		ErrCodePlanLimitGEEntrustPrice:               "이익실현 가격은 행사 가격보다 낮아야 합니다.",
		ErrCodePlanStopLEEntrustPrice:                "손절매 가격은 행사 가격보다 커야 합니다.",
		ErrCodeBlowingRisk:                           "이번에 포지션을 열 때 청산 위험이 있으며 위험 통제가 트리거되었습니다.",
		ErrCodeNonsupportMarginCoin:                  "지원되지 않는 마진 통화 ",
		ErrCodeBonusNonsupportHedgeMode:              "보너스는 헤지 모드를 지원하지 않습니다. 포지션을 닫고 다시 시도하십시오.\n",
		ErrCodeNonsupportExchange:                    "지원되지 않는 교환입니다",
		ErrCodeExchangePreOrderOverdue:               "현재 가격은 이미 기한이 지났으니 다시 받으십시오",
		ErrCodeExchangePriceVolatility:               "현재 가격 파동이 비교적 커서 이번 주문 가격은 더 이상 유효하지 않습니다",
		ErrCodeExchangeLessMinVolume:                 "단건 최소 %s%s",
		ErrCodeExchangeGreaterMaxVolume:              "단건 최대 %s%s",
		ErrCodeExchangePrecisionLimit:                "정밀도 제한을 입력합니다",
		ErrCodeExchangeDailyLimit:                    "일일 한도액 초과, 잔여 한도액:%s%s",
		ErrCodeGiftCashActivityExceed:                "이벤트가 만료되었습니다",
		ErrCodeGiftCashReceiveExceed:                 "수집 기간이 만료되었습니다",
		ErrCodePositionNumsLimit:                     "분할 포지션 모드 각 계약은 한 쪽에서 최대 5개의 포지션을 보유할 수 있습니다. 보유하고 있는 포지션을 선택하여 포지션을 추가할 수 있습니다.",
		ErrCodeNonsupportPositionSplitModeForVersion: "현재 버전은 현재 사용자 열기 모드에서 주문할 수 없습니다. 버전을 업그레이드하십시오.",
	},
	ReqLangVN: {
		ErrCodeBusy:                                  "Hệ thống đang bận, vui lòng thử lại sau",
		ErrCodeActionInvalid:                         "yêu cầu không hợp lệ",
		ErrCodeServerNotAvailable:                    "Dịch vụ tạm thời không có",
		ErrCodeParam:                                 "tham số yêu cầu sai",
		ErrCodeRequestTimeException:                  "Thời gian của thiết bị không bình thường, vui lòng sửa đổi và thử lại",
		ErrCodeSignature:                             "Xác minh chữ ký không thành công",
		ErrCodeForceUpdate:                           "cần nâng cấp",
		ErrCodeAuthCode:                              "Lỗi mã xác minh",
		ErrCodeAuthCodeInvalid:                       "Mã xác minh không hợp lệ, vui lòng lấy lại",
		ErrCodeImgAuthCode:                           "Lỗi mã xác minh đồ họa",
		ErrCodeInvalidInviteCode:                     "Mã lời mời không hợp lệ",
		ErrCodeActionTimeout:                         "Chiến dịch kết thúc",
		ErrCodeCWSParam:                              "lỗi cws",
		ErrCodeResubmit:                              "Không gửi lại",
		ErrCodeStatus:                                "Lỗi trạng thái",
		ErrCodeFrequent:                              "yêu cầu thường xuyên",
		ErrCodeFileTooLarge:                          "tệp quá lớn",
		ErrCodeNonsupportFileType:                    "loại tập tin không được hỗ trợ",
		ErrCodeTextLengthLimit:                       "Đã vượt quá độ dài ký tự",
		ErrCodeLimitCountry:                          "Hiện không phục vụ khu vực hiện tại",
		ErrCodeFeatureDisabled:                       "Dịch vụ này hiện không khả dụng",
		ErrCodeAPIFrequent:                           "Quá nhiều yêu cầu. Vui lòng thử lại sau",
		ErrCodeConfirmationIsRequired:                "Yêu cầu xác nhận",
		ErrCodeShouldLogin:                           "vui lòng đăng nhập trước",
		ErrCodeUserNotExist:                          "người dùng không tồn tại",
		ErrCodeAccountIsExist:                        "tài khoản đã tồn tại",
		ErrCodeAccountFormat:                         "Lỗi định dạng tài khoản",
		ErrCodeTooLongAccount:                        "Độ dài tài khoản quá dài",
		ErrCodeLoginElseWhere:                        "Tài khoản đã đăng nhập ở nơi khác",
		ErrCodeFundPwdNotSet:                         "Mật khẩu quỹ chưa được đặt",
		ErrCodeLoginPwdNotSet:                        "Mật khẩu đăng nhập chưa được đặt",
		ErrCodeLoginPwdIncorrect:                     "Mật khẩu đăng nhập không chính xác",
		ErrCodeLoginPwdSameAsFundPwd:                 "Mật khẩu đăng nhập không được giống với mật khẩu quỹ",
		ErrCodeFundPwdSameAsLoginPwd:                 "Mật khẩu quỹ không được giống với mật khẩu đăng nhập",
		ErrCodeLoginNotAllowed:                       "Tắt đăng nhập",
		ErrCodeTradeNotAllowed:                       "Người dùng hiện tại bị cấm giao dịch",
		ErrCodeWithdrawNotAllowed:                    "Người dùng hiện tại bị cấm rút tiền",
		ErrCodeHasAccountLock:                        "Những người dùng khác đang đăng ký hoặc ràng buộc tài khoản hiện tại, vui lòng thử lại sau",
		ErrCodeNeedVerify:                            "Người dùng không được xác thực",
		ErrCodeIdNumberVerified:                      "Thông tin ID đã được xem xét và phê duyệt, vui lòng thực hiện xác thực khuôn mặt",
		ErrCodeIdNumberExist:                         "Số ID đã được sử dụng",
		ErrCodeVerifyIdNumber:                        "Số ID không hợp lệ",
		ErrCodeVerifyValidity:                        "Tên không khớp với số ID",
		ErrCodeNeedVerifyIdentity:                    "Cần phải vượt qua xác minh danh tính trước tiên",
		ErrCodeFaceVerified:                          "Xác thực khuôn mặt đã được thông qua",
		ErrCodeExisted:                               "tồn tại",
		ErrCodeTradeIDNotMatch:                       "Danh tính hiện tại không thể thực hiện thao tác này",
		ErrCodeDealerFollowLimit:                     "Số lượng nhà giao dịch hiện tại cần theo dõi đã đầy",
		ErrCodeDealerCanNotFollow:                    "Nhà giao dịch không thể thực hiện ràng buộc bản sao",
		ErrCodeNoFollow:                              "Hiện không theo dõi",
		ErrCodeNonsupportModifyEmail:                 "Sửa đổi địa chỉ email không được hỗ trợ",
		ErrCodeModifySafeModeCount:                   "Hoạt động hiện tại yêu cầu nhiều hơn hai xác minh bảo mật",
		ErrCodeSafePhoneCodeInvalid:                  "Mã xác minh điện thoại không hợp lệ",
		ErrCodeSafeEmailCodeInvalid:                  "Mã xác minh email không hợp lệ",
		ErrCodeSafeTotpCodeInvalid:                   "Hình ảnh xác thực trình xác thực không hợp lệ",
		ErrCodeSafePhoneCodeExpire:                   "Mã xác minh điện thoại di động đã hết hạn, vui lòng lấy lại",
		ErrCodeSafeEmailCodeExpire:                   "Mã xác minh email đã hết hạn, vui lòng lấy lại",
		ErrCodeSafeNeedVerify:                        "Yêu cầu xác minh 2 bước",
		ErrCodeSafeNeedFundPasswordVerify:            "Xác minh mật khẩu quỹ là bắt buộc",
		ErrCodeSafeFundPassword:                      "mật khẩu quỹ sai",
		ErrCodeSameOldPassword:                       "giống như mật khẩu cũ",
		ErrCodeFewTransfer:                           "Ít hơn giới hạn hành trình đơn tối thiểu",
		ErrCodeManyTransfer:                          "Vượt quá giới hạn truyền một lần tối đa",
		ErrCodeCoinNotSupportTransfer:                "Tiền tệ hiện không hỗ trợ chuyển khoản",
		ErrCodeBalanceInsufficient:                   "Không đủ số dư khả dụng",
		ErrCodeInvalidAddress:                        "Địa chỉ không hợp lệ",
		ErrCodeWithdrawHasApply:                      "Có đơn rút tiền đang được xem xét trong ngày",
		ErrCodeWithdrawHasPassed:                     "Số lần rút tiền thành công trong ngày đã đạt đến giới hạn trên",
		ErrCodeDepositInsufficient:                   "Ký quỹ không đủ",
		ErrCodeNotSupportWithdraw:                    "Đồng tiền hiện tại không hỗ trợ rút tiền",
		ErrCodeWithdrawLessMinLimit:                  "Dưới giới hạn rút tiền tối thiểu",
		ErrCodeWithdrawGreaterMaxLimit:               "Nhiều hơn giới hạn rút tiền tối đa",
		ErrCodeTransferInsufficient:                  "Không đủ số tiền còn lại có thể chuyển nhượng",
		ErrCodeSafeGuardForSetting:                   "Cài đặt bảo mật đã được sửa đổi cho tài khoản này. Vì sự an toàn của tài khoản của bạn, việc rút tiền bị cấm trong vòng 24 giờ",
		ErrCodeReceivingLimitReached:                 "Tổng vốn chủ sở hữu của tài khoản demo lớn hơn 50W và không thể xác nhận quyền sở hữu được nữa",
		ErrCodeNeedWithdrawVerifyPassed:              "Cần phải hoàn thành xác minh danh tính rút tiền trước tiên",
		ErrCodeLegalWithdrawSafeGuard:                "Để bảo mật tài khoản của bạn. Sau giao dịch tiền tệ fiat, tiền tệ không thể được rút trong vòng 24 giờ.。",
		ErrCodeSafeGuardForFundPwd:                   "Mật khẩu quỹ bị nhập sai 5 lần liên tiếp và việc rút tiền sẽ bị cấm trong vòng 24 giờ.",
		ErrCodeWithdrawalRestrictionsMail:            "Hạn chế rút tiền: Vui lòng hoàn thành ràng buộc hộp thư",
		ErrCodeInsufficientFollowBalance:             "Các khoản tiền có sẵn hoặc được ủy quyền trong bản sao của bạn không đáp ứng số tiền tài khoản tối thiểu mà người giao dịch yêu cầu, vui lòng chuyển tiền trước khi sao chép.",
		ErrCodeDealerLimitIsChanged:                  "Nhà giao dịch đã sửa đổi cấu hình",
		ErrCodeUnverifyWithdrawOver:                  "Số tiền rút của bạn đã đạt đến giới hạn hôm nay và cần phải xác minh danh tính trước khi bạn có thể tiếp tục rút.",
		ErrCodeNonsupportTransferAsset:               "Chuyển giữa các tài khoản hiện được chọn hiện không được hỗ trợ",
		ErrCodeHasGiftCashNonsupportTransferOut:      "Có các vị trí hoặc lệnh sử dụng tiền thưởng, bạn không thể chuyển tài sản ngay bây giờ",
		ErrCodeHasGiftCashDeviceLimit:                "Xác nhận không thành công, thiết bị đã xác nhận phần thưởng.",
		ErrCodeAssetIsLiquidise:                      "Tài sản hiện tại đang được thanh lý, hãy thử lại sau",
		ErrCodeContractNotExist:                      "hợp đồng không tồn tại",
		ErrCodeContractNotTrade:                      "Hợp đồng tạm thời không có sẵn",
		ErrCodeNonSupportOptimal:                     "Đơn đặt hàng giá tốt nhất hiện không được hỗ trợ",
		ErrCodeNonSupportPrice:                       "giá không được hỗ trợ",
		ErrCodePositionInsufficient:                  "Không đủ vị trí còn lại",
		ErrCodeTooManyOrderVolume:                    "Vượt quá vị thế mở tối đa sẽ kích hoạt kiểm soát rủi ro. Vui lòng điều chỉnh số lượng đặt hàng",
		ErrCodeTradeFrequent:                         "Yêu cầu ủy quyền là thường xuyên",
		ErrCodeNonSupportChangeDeposit:               "Chế độ lề chéo không hỗ trợ điều chỉnh điều chỉnh lề",
		ErrCodeBelowMinOpenVolume:                    "Dưới vị thế mở tối thiểu duy nhất",
		ErrCodeOverMaxOpenVolume:                     "cao hơn vị trí mở tối đa",
		ErrCodePositionInvalid:                       "Vị trí hiện tại không hợp lệ hoặc đã bị đóng",
		ErrCodeLeverInvalid:                          "Khác với bội số đòn bẩy của vị thế hoặc kế hoạch hiện tại",
		ErrCodeOverPlaceMoney:                        "nhiều hơn số tiền đặt hàng",
		ErrCodePlanOrderLimit:                        "Hợp đồng của bạn chưa kích hoạt 10 đơn đặt hàng theo kế hoạch",
		ErrCodeLimitLEPrice:                          "Giá chốt lời cần lớn hơn giá mở cửa trung bình",
		ErrCodeStopGEPrice:                           "Giá cắt lỗ cần phải nhỏ hơn giá mở cửa trung bình",
		ErrCodeLimitGEPrice:                          "Giá chốt lời cần phải nhỏ hơn giá mở cửa trung bình",
		ErrCodeStopLEPrice:                           "Giá cắt lỗ cần lớn hơn giá mở cửa trung bình",
		ErrCodePlanLimitLEPrice:                      "Giá chốt lời cần lớn hơn giá kích hoạt",
		ErrCodePlanStopGEPrice:                       "Giá cắt lỗ cần phải nhỏ hơn giá kích hoạt",
		ErrCodePlanLimitGEPrice:                      "Giá chốt lời cần phải nhỏ hơn giá kích hoạt",
		ErrCodePlanStopLEPrice:                       "Giá cắt lỗ cần lớn hơn giá kích hoạt",
		ErrCodeLimitEIndex:                           "Giá chốt lời không được bằng chỉ số hợp đồng",
		ErrCodeStopEIndex:                            "Giá cắt lỗ không được bằng chỉ số hợp đồng",
		ErrCodePlanCloseCountLimit:                   "Chốt lời và cắt lỗ đã vượt quá 6, vui lòng xóa và sau đó đặt",
		ErrCodeMarginModifyBalanceInsufficient:       "Không đủ số dư khả dụng không thể tăng ký quỹ",
		ErrCodeMarginModifyMarginInsufficient:        "Không thể giảm ký quỹ đủ",
		ErrCodeAccountTypeHasFullPosition:            "Bạn hiện có một vị trí ở chế độ vị trí chéo, vui lòng đóng vị trí trước rồi chuyển nó",
		ErrCodeAccountTypeHasFullPlan:                "Bạn hiện đang có một kế hoạch ở chế độ nhập kho, vui lòng hủy kế hoạch đó trước rồi chuyển sang",
		ErrCodeAccountTypeHasPartPosition:            "Bạn hiện đang có một vị trí bị cô lập, vui lòng đóng vị trí trước khi chuyển đổi.",
		ErrCodeAccountTypeHasPartPlan:                "Bạn hiện có một đơn đặt hàng đã lên kế hoạch ở chế độ vị trí cô lập, vui lòng hủy đơn đặt hàng đó trước rồi chuyển nó",
		ErrCodeLeverHasBuyPartOrder:                  "Nếu bạn có một vị thế mua trong hợp đồng hiện tại hoặc lệnh đã lên kế hoạch chưa được kích hoạt, vui lòng đóng vị thế hoặc hủy nó trước khi chuyển đổi đòn bẩy.",
		ErrCodeLeverHasSellPartOrder:                 "Nếu bạn có một vị thế mở trong hợp đồng hiện tại hoặc lệnh dự kiến chưa được kích hoạt, vui lòng đóng hoặc hủy vị trí trước khi chuyển đổi đòn bẩy.",
		ErrCodeNoAccountTypePlace:                    "Không thể đặt hàng ở chế độ tài khoản hiện tại",
		ErrCodeContractMarketStop:                    "Đặt hàng không thành công, vui lòng thử lại sau",
		ErrCodeNonsupportSwitchAccountType:           "Chuyển sang chế độ tài khoản được chỉ định hiện không được hỗ trợ",
		ErrCodeOverUnitTrade:                         "Đặt hàng không thành công, vui lòng thử lại sau",
		ErrCodePartialFailed:                         "Không thể đóng một số vị trí, vui lòng thử lại sau",
		ErrCodeNotEnoughVolumeTrade:                  "Không thể đặt hàng, hiện không có đối tác nào để giao dịch",
		ErrCodeCloseOverPlace:                        "Vượt quá số lượng đơn đặt hàng tối đa sẽ kích hoạt kiểm soát rủi ro. Vui lòng điều chỉnh số lượng đặt hàng",
		ErrCodeNoPlatformSell:                        "Không có người bán nào chấp nhận đơn đặt hàng",
		ErrCodeNoPlatformSellTime:                    "Ngoài giờ làm việc",
		ErrCodeLegalLessThanAmountLimit:              "Giới hạn tối thiểu cho một giao dịch là %s",
		ErrCodeLegalMoreThanAmountLimit:              "Giới hạn tối đa cho một giao dịch là %s",
		ErrCodeLegalHasUndoneOrder:                   "Bạn có vị trí hoặc lệnh mở ngay bây giờ, vui lòng đóng nó",
		ErrCodeLegalPriceRateInvalid:                 "Xin lỗi, ưu đãi đã hết hạn, vui lòng đặt hàng mới",
		ErrCodeDigitalLessThanAmountLimit:            "Số lượng đơn vị tiền tệ phải lớn hơn %s%s",
		ErrCodeDigitalMoreThanAmountLimit:            "Số lượng đơn vị tiền tệ phải nhỏ hơn %s%s",
		ErrCodeFiatLessThanAmountLimit:               "Số lượng tiền tệ fiat để đặt hàng phải lớn hơn %s%s",
		ErrCodeFiatMoreThanAmountLimit:               "Số lượng tiền tệ fiat để đặt hàng phải nhỏ hơn %s%s",
		ErrCodeActivityExpire:                        "Sự kiện đã hết hạn",
		ErrCodeActivityAdopt:                         "Phần thưởng đã được phát hành",
		ErrCodePaymentsOverLimit:                     "Phương thức thanh toán của bạn đã đạt đến giới hạn và không thể thêm phương thức thanh toán mới vào lúc này",
		ErrCodeApiCreateOverLimit:                    "Số lượng tác phẩm API đã đạt đến giới hạn trên",
		ErrCodeApiBindIPOverLimit:                    "Số lượng IP bị ràng buộc vượt quá giới hạn trên",
		ErrCodeApiBindIPv6OverLimit:                  "Chỉ hỗ trợ liên kết tối đa hai địa chỉ IPv6",
		ErrCodeApiCreateNoPermission:                 "Người dùng hiện tại không có quyền tạo và sửa đổi các API",
		ErrCodeApiIPAddress:                          "Địa chỉ IP không chính xác",
		ErrCodeApiOverdue:                            "API hiện tại đã hết hạn",
		ErrCodeWithdrawAddrCountLimit:                "Địa chỉ rút tiền đã đạt đến số lượng tối đa và không thêm được.",
		ErrCodeWithdrawAddressExisted:                "Địa chỉ hiện tại đã tồn tại",
		ErrCodeSafeSpareEmailCodeInvalid:             "Mã xác minh email dự phòng không hợp lệ",
		ErrCodeSafeSpareEmailCodeExpire:              "Mã xác minh email thay thế đã hết hạn, vui lòng lấy lại",
		ErrCodeNonsupportAccountType:                 "Chế độ tài khoản không được hỗ trợ",
		ErrCodeNonsupportPositionSplitMode:           "Bạn hiện đang nắm giữ các vị thế hoặc các lệnh chưa được thực hiện và bạn không được phép thay đổi chế độ phân chia vị thế.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "Tài khoản ở trạng thái kiểm soát rủi ro, vui lòng liên hệ với bộ phận chăm sóc khách hàng",
		ErrCodeDeleteAccountHasUndoneOrder:           "Có vị trí hoặc đơn đặt hàng hoặc người giao dịch sau, vui lòng xác nhận rằng không có vị trí hoặc đơn đặt hàng và thử lại",
		ErrCodeAccountIsDeleteState:                  "Tài khoản này đã bị xóa, nếu bạn cần đăng ký lại, vui lòng liên hệ với bộ phận chăm sóc khách hàng chính thức",
		ErrCodeNonsupportPhoneNumber:                 "Số điện thoại di động không được hỗ trợ",
		ErrCodeRiskLess:                              "Tỷ lệ rủi ro hiện tại quá thấp, kích hoạt khả năng kiểm soát rủi ro. Tác vụ này hiện không khả dụng",
		ErrCodeOverPositionCount:                     "Đã vượt quá số vị trí tối đa",
		ErrCodeFollowNotSupport:                      "Hợp đồng hiện tại không hỗ trợ giao hàng",
		ErrCodePlanEntrustOverContractLimit:          "Lệnh không thành công, giá thực hiện vượt quá giá giới hạn hiện tại: %v",
		ErrCodePlanEntrustLessContractLimit:          "Lệnh không thành công, giá thực hiện thấp hơn giá giới hạn hiện tại: %v",
		ErrCodePlanEntrustMore:                       "Giá thực hiện vui lòng ≥ %v, nếu không đơn đặt hàng của bạn có thể không được thực hiện",
		ErrCodePlanEntrustLess:                       "Giá thực hiện vui lòng <=%v, nếu không đơn hàng của bạn có thể không được thực hiện",
		ErrCodeOverMaxSideEntrust:                    "Vượt quá số lượng đơn đặt hàng đang chờ xử lý tối đa ở một bên của hợp đồng",
		ErrCodeDepthInvalid:                          "Không đủ độ sâu hiện tại",
		ErrCodePriceOverLimit:                        "Giá đặt hàng hiện tại vượt quá phạm vi cho phép",
		ErrCodePriceInvalid:                          "Giá đặt hàng hiện tại không hợp lệ",
		ErrCodeEntrustCloseVolumeInvalid:             "Lệnh giới hạn hiện tại có lệnh đóng cho vị trí này, vui lòng hủy lệnh đó và sau đó sử dụng lệnh đóng nhanh",
		ErrCodePriceMarketOver:                       "Vượt quá ngưỡng bảo vệ chênh lệch giá, kích hoạt kiểm soát rủi ro",
		ErrCodePositionState:                         "Trạng thái của vị trí này đã được cập nhật, vui lòng kiểm tra vị trí trước khi đặt",
		ErrCodeInMaintenance:                         "Hợp đồng này đang được bảo trì, vui lòng thử lại sau",
		ErrCodeNetPosException:                       "Ủy quyền không thành công",
		ErrCodeForceClosing:                          "Hiện đang thanh lý, vui lòng thử lại sau",
		ErrCodeClosing:                               "Hợp đồng hiện tại đang được thanh lý, vui lòng thử lại sau",
		ErrCodeOrderIdUserd:                          "Số đơn hàng đã tồn tại",
		ErrCodeUnfinishedEntrustOrderTooMany:         "Quá nhiều đơn đặt hàng chưa hoàn thành",
		ErrCodeOrderState:                            "Trạng thái đơn hàng hiện tại đã được cập nhật, vui lòng làm mới và thử lại",
		ErrCodePlanLimitLEEntrustPrice:               "Giá chốt lời cần lớn hơn giá thực hiện",
		ErrCodePlanStopGEEntrustPrice:                "Giá cắt lỗ cần phải nhỏ hơn giá thực hiện",
		ErrCodePlanLimitGEEntrustPrice:               "Giá chốt lời cần phải nhỏ hơn giá thực hiện",
		ErrCodePlanStopLEEntrustPrice:                "Giá cắt lỗ cần lớn hơn giá thực hiện",
		ErrCodeBlowingRisk:                           "Bạn có nguy cơ buộc phải thanh lý khi bạn mở một vị thế lần này và việc kiểm soát rủi ro đã được kích hoạt.",
		ErrCodeNonsupportMarginCoin:                  "Đơn vị tiền tệ ký quỹ không được hỗ trợ",
		ErrCodeBonusNonsupportHedgeMode:              "Phần thưởng không hỗ trợ Chế độ hàng rào, vui lòng đóng vị trí và thử lại.",
		ErrCodeNonsupportExchange:                    "Đổi thưởng không được hỗ trợ",
		ErrCodeExchangePreOrderOverdue:               "Giá hiện tại đã hết, vui lòng lấy lại",
		ErrCodeExchangePriceVolatility:               "Giá hiện tại dao động rất lớn, giá của đơn hàng này đã bị vô hiệu",
		ErrCodeExchangeLessMinVolume:                 "Tối thiểu %s%s cho một giao dịch",
		ErrCodeExchangeGreaterMaxVolume:              "Một giao dịch lên đến %s%s",
		ErrCodeExchangePrecisionLimit:                "Giới hạn độ chính xác đầu vào",
		ErrCodeExchangeDailyLimit:                    "Vượt quá giới hạn hàng ngày, giới hạn còn lại: %s%s",
		ErrCodeGiftCashActivityExceed:                "Sự kiện đã hết hạn",
		ErrCodeGiftCashReceiveExceed:                 "Thời hạn thu tiền đã hết",
		ErrCodePositionNumsLimit:                     "Chế độ phân chia vị trí Mỗi hợp đồng có thể giữ tối đa 5 vị trí trên một mặt Bạn có thể chọn các vị trí mà bạn nắm giữ để thêm vị trí.",
		ErrCodeNonsupportPositionSplitModeForVersion: "Phiên bản hiện tại không thể đặt hàng ở chế độ mở người dùng hiện tại, vui lòng nâng cấp phiên bản.",
	},
	ReqLangID: {
		ErrCodeBusy:                                  "Sistem sedang sibuk, silakan coba lagi nanti",
		ErrCodeActionInvalid:                         "permintaan tidak valid",
		ErrCodeServerNotAvailable:                    "Layanan tidak tersedia untuk sementara",
		ErrCodeParam:                                 "parameter permintaan yang salah",
		ErrCodeRequestTimeException:                  "Waktu perangkat tidak normal, harap ubah dan coba lagi",
		ErrCodeSignature:                             "Verifikasi tanda tangan gagal",
		ErrCodeForceUpdate:                           "perlu ditingkatkan",
		ErrCodeAuthCode:                              "Kesalahan kode verifikasi",
		ErrCodeAuthCodeInvalid:                       "Kode verifikasi tidak valid, harap dapatkan lagi",
		ErrCodeImgAuthCode:                           "Kesalahan kode verifikasi grafis",
		ErrCodeInvalidInviteCode:                     "Kode undangan tidak valid",
		ErrCodeActionTimeout:                         "Waktu operasi habis",
		ErrCodeCWSParam:                              "kesalahan cws",
		ErrCodeResubmit:                              "Jangan kirim ulang",
		ErrCodeStatus:                                "Kesalahan status",
		ErrCodeFrequent:                              "permintaan yang sering",
		ErrCodeFileTooLarge:                          "file terlalu besar",
		ErrCodeNonsupportFileType:                    "tipe file tidak didukung",
		ErrCodeTextLengthLimit:                       "Panjang karakter terlampaui",
		ErrCodeLimitCountry:                          "Saat ini tidak melayani wilayah saat ini",
		ErrCodeFeatureDisabled:                       "Layanan ini saat ini tidak tersedia",
		ErrCodeAPIFrequent:                           "Terlalu banyak permintaan. Mohon ulangi beberapa saat lagi",
		ErrCodeConfirmationIsRequired:                "Konfirmasi diperlukan",
		ErrCodeShouldLogin:                           "silahkan masuk terlebih dahulu",
		ErrCodeUserNotExist:                          "pengguna tidak ada",
		ErrCodeAccountIsExist:                        "akun sudah ada",
		ErrCodeAccountFormat:                         "Kesalahan format akun",
		ErrCodeTooLongAccount:                        "Panjang akun terlalu panjang",
		ErrCodeLoginElseWhere:                        "Akunnya masuk di tempat lain",
		ErrCodeFundPwdNotSet:                         "Kata sandi dana tidak disetel",
		ErrCodeLoginPwdNotSet:                        "Kata sandi masuk tidak disetel",
		ErrCodeLoginPwdIncorrect:                     "Kata sandi masuk salah",
		ErrCodeLoginPwdSameAsFundPwd:                 "Password login tidak boleh sama dengan password dana",
		ErrCodeFundPwdSameAsLoginPwd:                 "Password dana tidak boleh sama dengan password login",
		ErrCodeLoginNotAllowed:                       "Nonaktifkan login",
		ErrCodeTradeNotAllowed:                       "Pengguna saat ini dilarang berdagang",
		ErrCodeWithdrawNotAllowed:                    "Pengguna saat ini dilarang menarik koin",
		ErrCodeHasAccountLock:                        "Pengguna lain mendaftar atau mengikat akun saat ini, silakan coba lagi nanti",
		ErrCodeNeedVerify:                            "Pengguna tidak diautentikasi",
		ErrCodeIdNumberVerified:                      "Informasi ID telah ditinjau dan disetujui, silakan lakukan otentikasi wajah",
		ErrCodeIdNumberExist:                         "Nomor ID sudah terisi",
		ErrCodeVerifyIdNumber:                        "Nomor ID tidak valid",
		ErrCodeVerifyValidity:                        "Nama tidak sesuai dengan nomor ID",
		ErrCodeNeedVerifyIdentity:                    "Harus lulus verifikasi identitas terlebih dahulu",
		ErrCodeFaceVerified:                          "Otentikasi wajah lulus",
		ErrCodeExisted:                               "ada",
		ErrCodeTradeIDNotMatch:                       "Identitas saat ini tidak dapat melakukan operasi ini",
		ErrCodeDealerFollowLimit:                     "Jumlah trader yang harus diikuti saat ini sudah penuh",
		ErrCodeDealerCanNotFollow:                    "Trader tidak dapat melakukan copy binding",
		ErrCodeNoFollow:                              "Saat ini tidak mengikuti",
		ErrCodeNonsupportModifyEmail:                 "Modifikasi alamat email tidak didukung",
		ErrCodeModifySafeModeCount:                   "Operasi saat ini membutuhkan lebih dari dua verifikasi keamanan",
		ErrCodeSafePhoneCodeInvalid:                  "Kode verifikasi telepon tidak valid",
		ErrCodeSafeEmailCodeInvalid:                  "Kode verifikasi email tidak valid",
		ErrCodeSafeTotpCodeInvalid:                   "captcha validator tidak valid",
		ErrCodeSafePhoneCodeExpire:                   "Kode verifikasi ponsel telah kedaluwarsa, silakan dapatkan lagi",
		ErrCodeSafeEmailCodeExpire:                   "Kode verifikasi email telah kedaluwarsa, harap dapatkan lagi",
		ErrCodeSafeNeedVerify:                        "Verifikasi 2 langkah diperlukan",
		ErrCodeSafeNeedFundPasswordVerify:            "Verifikasi kata sandi dana diperlukan",
		ErrCodeSafeFundPassword:                      "kata sandi dana salah",
		ErrCodeSameOldPassword:                       "sama dengan kata sandi lama",
		ErrCodeFewTransfer:                           "Kurang dari batas minimal satu pukulan",
		ErrCodeManyTransfer:                          "Melebihi batas transfer satu langkah maksimum",
		ErrCodeCoinNotSupportTransfer:                "Mata uang saat ini tidak mendukung transfer",
		ErrCodeBalanceInsufficient:                   "Saldo yang tersedia tidak mencukupi",
		ErrCodeInvalidAddress:                        "alamat tidak valid",
		ErrCodeWithdrawHasApply:                      "Ada aplikasi penarikan yang sedang ditinjau pada hari itu",
		ErrCodeWithdrawHasPassed:                     "Jumlah penarikan yang berhasil pada hari itu telah mencapai batas atas",
		ErrCodeDepositInsufficient:                   "Margin tidak mencukupi",
		ErrCodeNotSupportWithdraw:                    "Mata uang saat ini tidak mendukung penarikan",
		ErrCodeWithdrawLessMinLimit:                  "Kurang dari batas penarikan minimum",
		ErrCodeWithdrawGreaterMaxLimit:               "Lebih dari batas penarikan maksimum",
		ErrCodeTransferInsufficient:                  "Sisa jumlah yang dapat ditransfer tidak mencukupi",
		ErrCodeSafeGuardForSetting:                   "Pengaturan keamanan telah diubah untuk akun ini. Demi keamanan akun Anda, penarikan dilarang dalam waktu 24 jam",
		ErrCodeReceivingLimitReached:                 "Total ekuitas akun demo lebih besar dari 50 W dan tidak dapat diklaim lagi",
		ErrCodeNeedWithdrawVerifyPassed:              "Perlu menyelesaikan verifikasi identitas penarikan terlebih dahulu",
		ErrCodeLegalWithdrawSafeGuard:                "Untuk keamanan akun Anda. Setelah transaksi mata uang fiat, mata uang tidak dapat ditarik dalam waktu 24 jam.",
		ErrCodeSafeGuardForFundPwd:                   "Password dana yang dimasukkan salah 5 kali berturut-turut, dan penarikan akan dilarang dalam waktu 24 jam.",
		ErrCodeWithdrawalRestrictionsMail:            "Batasan penarikan: Harap lengkapi penjilidan kotak surat",
		ErrCodeInsufficientFollowBalance:             "Dana yang tersedia atau resmi dari salinan Anda tidak memenuhi dana akun minimum yang diminta oleh pedagang, harap transfer dana sebelum menyalin.",
		ErrCodeDealerLimitIsChanged:                  "Trader memodifikasi konfigurasi",
		ErrCodeUnverifyWithdrawOver:                  "Jumlah penarikan Anda telah mencapai batas hari ini, dan verifikasi identitas diperlukan sebelum Anda dapat melanjutkan penarikan.",
		ErrCodeNonsupportTransferAsset:               "Transfer antar akun yang dipilih saat ini tidak didukung",
		ErrCodeHasGiftCashNonsupportTransferOut:      "Ada posisi atau pesanan yang menggunakan bonus, Anda tidak dapat mentransfer aset sekarang",
		ErrCodeHasGiftCashDeviceLimit:                "Klaim gagal, perangkat telah mengklaim bonus.",
		ErrCodeAssetIsLiquidise:                      "Aset saat ini sedang dilikuidasi, coba lagi nanti",
		ErrCodeContractNotExist:                      "kontrak tidak ada",
		ErrCodeContractNotTrade:                      "Kontrak untuk sementara tidak tersedia",
		ErrCodeNonSupportOptimal:                     "Urutan harga terbaik saat ini tidak didukung",
		ErrCodeNonSupportPrice:                       "harga tidak didukung",
		ErrCodePositionInsufficient:                  "Posisi yang tersisa tidak mencukupi",
		ErrCodeTooManyOrderVolume:                    "Melebihi posisi terbuka maksimum memicu pengendalian risiko. Harap sesuaikan jumlah pesanan",
		ErrCodeTradeFrequent:                         "Permintaan delegasi sering terjadi",
		ErrCodeNonSupportChangeDeposit:               "Mode lintas margin tidak mendukung penyesuaian penyesuaian margin",
		ErrCodeBelowMinOpenVolume:                    "Di bawah satu posisi terbuka minimum",
		ErrCodeOverMaxOpenVolume:                     "lebih tinggi dari posisi terbuka maksimum",
		ErrCodePositionInvalid:                       "Posisi saat ini tidak valid atau ditutup",
		ErrCodeLeverInvalid:                          "Berbeda dari kelipatan leverage dari posisi atau rencana saat ini",
		ErrCodeOverPlaceMoney:                        "lebih dari jumlah pesanan",
		ErrCodePlanOrderLimit:                        "Kontrak Anda belum memicu 10 pesanan yang direncanakan",
		ErrCodeLimitLEPrice:                          "Harga take profit harus lebih besar dari harga pembukaan rata-rata",
		ErrCodeStopGEPrice:                           "Harga stop loss harus kurang dari harga pembukaan rata-rata",
		ErrCodeLimitGEPrice:                          "Harga take profit harus kurang dari harga pembukaan rata-rata",
		ErrCodeStopLEPrice:                           "Harga stop loss harus lebih besar dari harga pembukaan rata-rata",
		ErrCodePlanLimitLEPrice:                      "Harga take profit harus lebih besar dari harga trigger",
		ErrCodePlanStopGEPrice:                       "Harga stop loss harus kurang dari harga pemicu",
		ErrCodePlanLimitGEPrice:                      "Harga take profit harus kurang dari harga pemicu",
		ErrCodePlanStopLEPrice:                       "Harga stop loss harus lebih besar dari harga pemicu",
		ErrCodeLimitEIndex:                           "Harga take profit tidak boleh sama dengan indeks kontrak",
		ErrCodeStopEIndex:                            "Harga stop loss tidak boleh sama dengan indeks kontrak",
		ErrCodePlanCloseCountLimit:                   "Take profit dan stop loss sudah melebihi 6, silahkan hapus lalu set",
		ErrCodeMarginModifyBalanceInsufficient:       "Saldo yang tersedia tidak mencukupi gagal meningkatkan margin",
		ErrCodeMarginModifyMarginInsufficient:        "Margin yang tidak mencukupi tidak dapat dikurangi",
		ErrCodeAccountTypeHasFullPosition:            "Saat ini Anda memiliki posisi dalam mode posisi silang, harap tutup posisi terlebih dahulu lalu alihkan",
		ErrCodeAccountTypeHasFullPlan:                "Saat ini Anda memiliki paket dalam mode lintas pergudangan, harap batalkan terlebih dahulu, lalu alihkan",
		ErrCodeAccountTypeHasPartPosition:            "Saat ini Anda memiliki posisi terisolasi, harap tutup posisi sebelum beralih.",
		ErrCodeAccountTypeHasPartPlan:                "Saat ini Anda memiliki pesanan yang direncanakan dalam mode posisi terisolasi, harap batalkan terlebih dahulu lalu alihkan",
		ErrCodeLeverHasBuyPartOrder:                  "Jika Anda memiliki posisi long dalam kontrak saat ini atau pesanan yang direncanakan belum dipicu, harap tutup posisi atau batalkan sebelum beralih leverage.",
		ErrCodeLeverHasSellPartOrder:                 "Jika Anda memiliki posisi terbuka dalam kontrak saat ini atau pesanan yang direncanakan belum dipicu, harap tutup atau batalkan posisi sebelum beralih leverage.",
		ErrCodeNoAccountTypePlace:                    "Tidak dapat melakukan pemesanan dalam mode akun saat ini",
		ErrCodeContractMarketStop:                    "Pesanan gagal, silakan coba lagi nanti",
		ErrCodeNonsupportSwitchAccountType:           "Beralih ke mode akun yang ditunjuk saat ini tidak didukung",
		ErrCodeOverUnitTrade:                         "Pesanan gagal, silakan coba lagi nanti",
		ErrCodePartialFailed:                         "Beberapa posisi gagal ditutup, silakan coba lagi nanti",
		ErrCodeNotEnoughVolumeTrade:                  "Gagal melakukan pemesanan, saat ini tidak ada rekanan untuk diperdagangkan",
		ErrCodeCloseOverPlace:                        "Melebihi jumlah pesanan tunggal maksimum akan memicu pengendalian risiko. Harap sesuaikan jumlah pesanan",
		ErrCodeNoPlatformSell:                        "Tidak ada pedagang yang menerima pesanan",
		ErrCodeNoPlatformSellTime:                    "Di luar jam kerja",
		ErrCodeLegalLessThanAmountLimit:              "Batas minimum untuk satu transaksi adalah %s",
		ErrCodeLegalMoreThanAmountLimit:              "Batas maksimum untuk satu transaksi adalah %s",
		ErrCodeLegalHasUndoneOrder:                   "Ada posisi atau open order, silahkan close",
		ErrCodeLegalPriceRateInvalid:                 "Maaf, penawaran telah kedaluwarsa, silakan lakukan pemesanan baru",
		ErrCodeDigitalLessThanAmountLimit:            "Kuantitas mata uang pesanan harus lebih besar dari %s%s",
		ErrCodeDigitalMoreThanAmountLimit:            "Jumlah mata uang pesanan harus kurang dari %s%s",
		ErrCodeFiatLessThanAmountLimit:               "Jumlah mata uang fiat untuk melakukan pemesanan harus lebih besar dari %s%s",
		ErrCodeFiatMoreThanAmountLimit:               "Jumlah mata uang fiat untuk melakukan pemesanan harus kurang dari %s%s",
		ErrCodeActivityExpire:                        "Acara telah kedaluwarsa",
		ErrCodeActivityAdopt:                         "Hadiah telah dikeluarkan",
		ErrCodePaymentsOverLimit:                     "Metode pembayaran Anda telah mencapai batas, dan metode pembayaran baru tidak dapat ditambahkan saat ini",
		ErrCodeApiCreateOverLimit:                    "Jumlah kreasi API telah mencapai batas atas",
		ErrCodeApiBindIPOverLimit:                    "Jumlah IP terikat melebihi batas atas",
		ErrCodeApiBindIPv6OverLimit:                  "Hanya mendukung pengikatan hingga dua alamat IPv6",
		ErrCodeApiCreateNoPermission:                 "Pengguna saat ini tidak memiliki izin untuk membuat dan memodifikasi API",
		ErrCodeApiIPAddress:                          "Alamat IP salah",
		ErrCodeApiOverdue:                            "API saat ini telah kedaluwarsa",
		ErrCodeWithdrawAddrCountLimit:                "Alamat penarikan telah mencapai jumlah maksimum dan gagal ditambahkan.",
		ErrCodeWithdrawAddressExisted:                "Alamat saat ini sudah ada",
		ErrCodeSafeSpareEmailCodeInvalid:             "Kode verifikasi email cadangan tidak valid",
		ErrCodeSafeSpareEmailCodeExpire:              "Kode verifikasi email alternatif telah kedaluwarsa, harap dapatkan lagi",
		ErrCodeNonsupportAccountType:                 "Mode Akun Tidak Didukung",
		ErrCodeNonsupportPositionSplitMode:           "Saat ini Anda memegang posisi atau order yang belum terisi, dan Anda tidak diperbolehkan untuk mengubah mode pembagian posisi.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "Akun dalam status pengendalian risiko, silakan hubungi layanan pelanggan",
		ErrCodeDeleteAccountHasUndoneOrder:           "Ada posisi atau order atau mengikuti trader,  harap konfirmasi bahwa tidak ada posisi atau pesanan dan coba lagi",
		ErrCodeAccountIsDeleteState:                  "Akun ini telah dihapus, jika Anda perlu mendaftar lagi, silakan hubungi layanan pelanggan resmi",
		ErrCodeNonsupportPhoneNumber:                 "Nomor ponsel tidak didukung",
		ErrCodeRiskLess:                              "Tingkat risiko saat ini terlalu rendah, memicu pengendalian risiko. Tindakan ini saat ini tidak tersedia",
		ErrCodeOverPositionCount:                     "Melebihi jumlah posisi maksimum",
		ErrCodeFollowNotSupport:                      "Kontrak saat ini tidak mendukung pengiriman pesanan",
		ErrCodePlanEntrustOverContractLimit:          "Pesanan gagal, harga eksekusi melebihi harga batas saat ini: %v",
		ErrCodePlanEntrustLessContractLimit:          "Pesanan gagal, harga eksekusi lebih rendah dari harga batas saat ini: %v",
		ErrCodePlanEntrustMore:                       "Harga eksekusi harus %v, jika tidak, pesanan Anda mungkin tidak akan dieksekusi",
		ErrCodePlanEntrustLess:                       "Harga eksekusi harap <= %v, jika tidak, pesanan Anda mungkin tidak terisi",
		ErrCodeOverMaxSideEntrust:                    "Melebihi jumlah maksimum pesanan tertunda di satu sisi kontrak",
		ErrCodeDepthInvalid:                          "Kedalaman arus tidak mencukupi",
		ErrCodePriceOverLimit:                        "Harga pesanan saat ini melebihi kisaran yang diizinkan",
		ErrCodePriceInvalid:                          "Harga pesanan saat ini tidak valid",
		ErrCodeEntrustCloseVolumeInvalid:             "Limit order saat ini memiliki closing order untuk posisi ini, mohon batalkan dan gunakan fast closing order",
		ErrCodePriceMarketOver:                       "Melebihi ambang batas perlindungan perbedaan harga, memicu pengendalian risiko",
		ErrCodePositionState:                         "Status posisi ini telah diperbarui, harap periksa posisi sebelum pengaturan",
		ErrCodeInMaintenance:                         "Kontrak ini sedang dalam pemeliharaan, silakan coba lagi nanti",
		ErrCodeNetPosException:                       "Delegasi gagal",
		ErrCodeForceClosing:                          "Likuidasi sedang berlangsung, silakan coba lagi nanti",
		ErrCodeClosing:                               "Kontrak saat ini sedang dalam likuidasi, silakan coba lagi nanti",
		ErrCodeOrderIdUserd:                          "Nomor pesanan sudah ada",
		ErrCodeUnfinishedEntrustOrderTooMany:         "Terlalu banyak pesanan yang belum selesai",
		ErrCodeOrderState:                            "Status pesanan saat ini telah diperbarui, harap segarkan dan coba lagi",
		ErrCodePlanLimitLEEntrustPrice:               "Harga take profit harus lebih besar dari harga strike",
		ErrCodePlanStopGEEntrustPrice:                "Harga stop loss harus kurang dari harga strike",
		ErrCodePlanLimitGEEntrustPrice:               "Harga take profit harus kurang dari harga strike",
		ErrCodePlanStopLEEntrustPrice:                "Harga stop loss harus lebih besar dari harga strike",
		ErrCodeBlowingRisk:                           "Anda memiliki risiko likuidasi paksa saat Anda membuka posisi kali ini, dan kontrol risiko telah dipicu.",
		ErrCodeNonsupportMarginCoin:                  "Mata Uang Margin yang Tidak Didukung",
		ErrCodeBonusNonsupportHedgeMode:              "Bonus tidak mendukung Mode Hedge, silakan tutup posisi dan coba lagi.",
		ErrCodeNonsupportExchange:                    "Penukaran tidak didukung",
		ErrCodeExchangePreOrderOverdue:               "Harga saat ini telah kedaluwarsa, silakan dapatkan lagi",
		ErrCodeExchangePriceVolatility:               "Harga saat ini sangat berfluktuasi, harga pesanan ini telah dibatalkan",
		ErrCodeExchangeLessMinVolume:                 "Minimal %s %s untuk satu transaksi",
		ErrCodeExchangeGreaterMaxVolume:              "Maksimum %s %s untuk satu transaksi",
		ErrCodeExchangePrecisionLimit:                "Batas presisi masukan",
		ErrCodeExchangeDailyLimit:                    "Melebihi batas harian, batas yang tersisa: %s%s",
		ErrCodeGiftCashActivityExceed:                "Acara telah kedaluwarsa",
		ErrCodeGiftCashReceiveExceed:                 "Periode pengumpulan telah kedaluwarsa",
		ErrCodePositionNumsLimit:                     "Mode posisi split Setiap kontrak dapat menampung hingga 5 posisi di satu sisi. Anda dapat memilih posisi yang Anda pegang untuk menambah posisi.",
		ErrCodeNonsupportPositionSplitModeForVersion: "Versi saat ini tidak dapat memesan dalam mode terbuka pengguna saat ini, harap tingkatkan versinya.",
	},
	ReqLangRU: {
		ErrCodeBusy:                                  "Система занята",
		ErrCodeActionInvalid:                         "неверный запрос",
		ErrCodeServerNotAvailable:                    "Сервис временно недоступен",
		ErrCodeParam:                                 "неверный параметр запроса",
		ErrCodeRequestTimeException:                  "Время устройства не соответствует норме, измените и повторите попытку.",
		ErrCodeSignature:                             "Ошибка проверки подписи",
		ErrCodeForceUpdate:                           "нужно обновить",
		ErrCodeAuthCode:                              "Ошибка кода подтверждения",
		ErrCodeAuthCodeInvalid:                       "Код подтверждения недействителен, получите его еще раз",
		ErrCodeImgAuthCode:                           "Ошибка графического кода подтверждения",
		ErrCodeInvalidInviteCode:                     "Неверный код приглашения",
		ErrCodeActionTimeout:                         "Время ожидания операции истекло",
		ErrCodeCWSParam:                              "ошибка cws",
		ErrCodeResubmit:                              "Не отправлять повторно",
		ErrCodeStatus:                                "Ошибка статуса",
		ErrCodeFrequent:                              "частые запросы",
		ErrCodeFileTooLarge:                          "файл слишком большой",
		ErrCodeNonsupportFileType:                    "неподдерживаемый тип файла",
		ErrCodeTextLengthLimit:                       "Превышена длина символа",
		ErrCodeLimitCountry:                          "В настоящее время не обслуживает текущий регион",
		ErrCodeFeatureDisabled:                       "Эта услуга в настоящее время недоступна",
		ErrCodeAPIFrequent:                           "Пожалуйста, попробуйте позже, Слишком много запросов",
		ErrCodeConfirmationIsRequired:                "Требуется подтверждение",
		ErrCodeShouldLogin:                           "Пожалуйста, войдите сначала",
		ErrCodeUserNotExist:                          "Пользователь не существует",
		ErrCodeAccountIsExist:                        "аккаунт уже существует",
		ErrCodeAccountFormat:                         "Ошибка формата учетной записи",
		ErrCodeTooLongAccount:                        "Длина учетной записи слишком велика",
		ErrCodeLoginElseWhere:                        "Аккаунт зарегистрирован в другом месте",
		ErrCodeFundPwdNotSet:                         "Пароль фонда не установлен",
		ErrCodeLoginPwdNotSet:                        "Пароль для входа не установлен",
		ErrCodeLoginPwdIncorrect:                     "Неверный пароль для входа",
		ErrCodeLoginPwdSameAsFundPwd:                 "Пароль для входа не может совпадать с паролем фонда",
		ErrCodeFundPwdSameAsLoginPwd:                 "Пароль фонда не может совпадать с паролем для входа",
		ErrCodeLoginNotAllowed:                       "Отключить вход",
		ErrCodeTradeNotAllowed:                       "Текущему пользователю запрещено торговать",
		ErrCodeWithdrawNotAllowed:                    "Текущему пользователю запрещено снимать монеты",
		ErrCodeHasAccountLock:                        "Другие пользователи регистрируют или привязывают текущую учетную запись, повторите попытку позже.",
		ErrCodeNeedVerify:                            "Пользователь не авторизован",
		ErrCodeIdNumberVerified:                      "Идентификационная информация была проверена и утверждена, пожалуйста, выполните аутентификацию по лицу.",
		ErrCodeIdNumberExist:                         "Идентификационный номер уже занят",
		ErrCodeVerifyIdNumber:                        "Недействительный идентификационный номер",
		ErrCodeVerifyValidity:                        "Имя не соответствует идентификационному номеру",
		ErrCodeNeedVerifyIdentity:                    "Сначала необходимо пройти проверку личности",
		ErrCodeFaceVerified:                          "Аутентификация по лицу пройдена",
		ErrCodeExisted:                               "существовал",
		ErrCodeTradeIDNotMatch:                       "Текущее удостоверение не может выполнить эту операцию",
		ErrCodeDealerFollowLimit:                     "Текущее количество трейдеров, за которыми нужно следить, заполнено",
		ErrCodeDealerCanNotFollow:                    "Трейдеры не могут выполнять привязку копирования",
		ErrCodeNoFollow:                              "В настоящее время не подписан",
		ErrCodeNonsupportModifyEmail:                 "Изменение адреса электронной почты не поддерживается",
		ErrCodeModifySafeModeCount:                   "Текущая операция требует более двух проверок безопасности.",
		ErrCodeSafePhoneCodeInvalid:                  "Неверный код подтверждения телефона",
		ErrCodeSafeEmailCodeInvalid:                  "Неверный код подтверждения электронной почты",
		ErrCodeSafeTotpCodeInvalid:                   "Неверная капча валидатора",
		ErrCodeSafePhoneCodeExpire:                   "Срок действия кода подтверждения мобильного телефона истек, пожалуйста, получите его еще раз",
		ErrCodeSafeEmailCodeExpire:                   "Срок действия кода подтверждения электронной почты истек, пожалуйста, получите его еще раз",
		ErrCodeSafeNeedVerify:                        "Требуется двухэтапная аутентификация",
		ErrCodeSafeNeedFundPasswordVerify:            "Требуется подтверждение пароля фонда",
		ErrCodeSafeFundPassword:                      "неверный пароль фонда",
		ErrCodeSameOldPassword:                       "такой же, как старый пароль",
		ErrCodeFewTransfer:                           "Меньше, чем минимальный предел одиночного хода",
		ErrCodeManyTransfer:                          "Превышает максимальный предел передачи одиночного удара",
		ErrCodeCoinNotSupportTransfer:                "Валюта в настоящее время не поддерживает перевод",
		ErrCodeBalanceInsufficient:                   "Недостаточно доступного баланса",
		ErrCodeInvalidAddress:                        "неверный адрес",
		ErrCodeWithdrawHasApply:                      "Заявки на вывод находятся на рассмотрении в день",
		ErrCodeWithdrawHasPassed:                     "Количество успешных выводов за день достигло верхнего предела",
		ErrCodeDepositInsufficient:                   "Недостаточная маржа",
		ErrCodeNotSupportWithdraw:                    "Текущая валюта не поддерживает вывод",
		ErrCodeWithdrawLessMinLimit:                  "Меньше минимального лимита вывода",
		ErrCodeWithdrawGreaterMaxLimit:               "Больше максимального лимита вывода",
		ErrCodeTransferInsufficient:                  "Недостаточная оставшаяся переводимая сумма",
		ErrCodeSafeGuardForSetting:                   "Настройки безопасности для этой учетной записи были изменены. В целях безопасности вашего аккаунта снятие средств запрещено в течение 24 часов.",
		ErrCodeReceivingLimitReached:                 "Общий капитал демо-счета превышает 50 Вт и больше не может быть востребован.",
		ErrCodeNeedWithdrawVerifyPassed:              "Сначала необходимо завершить проверку личности при снятии средств.",
		ErrCodeLegalWithdrawSafeGuard:                "Для безопасности вашего аккаунта. После операции с фиатной валютой валюта не может быть снята в течение 24 часов.",
		ErrCodeSafeGuardForFundPwd:                   "Пароль фонда введен неверно 5 раз подряд, и вывод средств будет запрещен в течение 24 часов.",
		ErrCodeWithdrawalRestrictionsMail:            "Ограничения на вывод: Пожалуйста, завершите привязку почтового ящика",
		ErrCodeInsufficientFollowBalance:             "Доступные или разрешенные средства вашей копии не соответствуют минимальным средствам на счете, требуемым трейдерами, пожалуйста, переведите средства перед копированием.",
		ErrCodeDealerLimitIsChanged:                  "Трейдер изменил конфигурацию",
		ErrCodeUnverifyWithdrawOver:                  "Сумма вашего вывода сегодня достигла предела, и вам необходимо подтвердить личность, прежде чем вы сможете продолжить вывод средств.",
		ErrCodeNonsupportTransferAsset:               "Переводы между выбранными учетными записями в настоящее время не поддерживаются",
		ErrCodeHasGiftCashNonsupportTransferOut:      "В настоящее время у вас есть позиции или ордера, использующие бонус, который нельзя временно перевести",
		ErrCodeHasGiftCashDeviceLimit:                "Претензия не удалась, устройство уже забрало бонус.",
		ErrCodeAssetIsLiquidise:                      "Текущий актив ликвидируется, повторите попытку позже",
		ErrCodeContractNotExist:                      "договора не существует",
		ErrCodeContractNotTrade:                      "Контракт временно недоступен",
		ErrCodeNonSupportOptimal:                     "Ордер по лучшей цене в настоящее время не поддерживается",
		ErrCodeNonSupportPrice:                       "неподдерживаемая цена",
		ErrCodePositionInsufficient:                  "Недостаточно оставшихся позиций",
		ErrCodeTooManyOrderVolume:                    "Превышение максимальной открытой позиции приводит к срабатыванию контроля риска. Пожалуйста, скорректируйте количество заказа",
		ErrCodeTradeFrequent:                         "Частые запросы делегирования",
		ErrCodeNonSupportChangeDeposit:               "Режим кросс-маржи не поддерживает регулировку поля.",
		ErrCodeBelowMinOpenVolume:                    "Ниже единственной минимальной открытой позиции",
		ErrCodeOverMaxOpenVolume:                     "выше максимальной открытой позиции",
		ErrCodePositionInvalid:                       "Текущая позиция недействительна или закрыта",
		ErrCodeLeverInvalid:                          "Отличается от кредитного плеча, кратного текущей позиции или плану",
		ErrCodeOverPlaceMoney:                        "больше суммы заказа",
		ErrCodePlanOrderLimit:                        "Ваш контракт не вызвал 10 запланированных заказов",
		ErrCodeLimitLEPrice:                          "Цена тейк-профита должна быть выше средней цены открытия.",
		ErrCodeStopGEPrice:                           "Цена стоп-лосса должна быть меньше средней цены открытия.",
		ErrCodeLimitGEPrice:                          "Цена тейк-профита должна быть меньше средней цены открытия.",
		ErrCodeStopLEPrice:                           "Цена стоп-лосса должна быть выше средней цены открытия.",
		ErrCodePlanLimitLEPrice:                      "Цена тейк-профита должна быть выше цены срабатывания.",
		ErrCodePlanStopGEPrice:                       "Цена стоп-лосса должна быть меньше цены срабатывания.",
		ErrCodePlanLimitGEPrice:                      "Цена тейк-профита должна быть меньше цены срабатывания.",
		ErrCodePlanStopLEPrice:                       "Цена стоп-лосса должна быть выше цены срабатывания.",
		ErrCodeLimitEIndex:                           "Цена тейк-профита не может быть равна индексу контракта",
		ErrCodeStopEIndex:                            "Цена стоп-лосса не может быть равна индексу контракта",
		ErrCodePlanCloseCountLimit:                   "Тейк-профит и стоп-лосс превысили 6, пожалуйста, удалите, а затем установите",
		ErrCodeMarginModifyBalanceInsufficient:       "Недостаточно доступного баланса не удалось увеличить маржу",
		ErrCodeMarginModifyMarginInsufficient:        "Недостаточная маржа не может быть уменьшена",
		ErrCodeAccountTypeHasFullPosition:            "В настоящее время у вас есть позиция в режиме кросс-позиции, пожалуйста, сначала закройте позицию, а затем переключите ее",
		ErrCodeAccountTypeHasFullPlan:                "В настоящее время у вас есть план в режиме нескольких складов. Сначала отмените его, а затем переключите.",
		ErrCodeAccountTypeHasPartPosition:            "В настоящее время у вас есть изолированная позиция, пожалуйста, закройте позицию перед переключением.",
		ErrCodeAccountTypeHasPartPlan:                "В настоящее время у вас есть запланированный ордер в режиме изолированной позиции, пожалуйста, сначала отмените его, а затем переключите.",
		ErrCodeLeverHasBuyPartOrder:                  "Если у вас длинная позиция по текущему контракту или запланированный ордер не сработал, пожалуйста, закройте позицию или отмените ее перед переключением кредитного плеча.",
		ErrCodeLeverHasSellPartOrder:                 "Если у вас есть открытая позиция по текущему контракту или запланированный ордер не сработал, пожалуйста, закройте или отмените позицию перед переключением кредитного плеча.",
		ErrCodeNoAccountTypePlace:                    "Невозможно разместить заказ в режиме текущей учетной записи",
		ErrCodeContractMarketStop:                    "Заказ не выполнен, повторите попытку позже",
		ErrCodeNonsupportSwitchAccountType:           "Переключение в режим назначенной учетной записи в настоящее время не поддерживается.",
		ErrCodeOverUnitTrade:                         "Заказ не выполнен, повторите попытку позже",
		ErrCodePartialFailed:                         "Не удалось закрыть некоторые позиции. Повторите попытку позже.",
		ErrCodeNotEnoughVolumeTrade:                  "Не удалось разместить ордер, в настоящее время нет контрагента для торговли",
		ErrCodeCloseOverPlace:                        "Превышение максимальной суммы одного ордера приведет к срабатыванию контроля рисков. Пожалуйста, скорректируйте количество заказа",
		ErrCodeNoPlatformSell:                        "Нет продавцов, принимающих заказы",
		ErrCodeNoPlatformSellTime:                    "Нерабочее время",
		ErrCodeLegalLessThanAmountLimit:              "Минимальный лимит на одну транзакцию составляет %s",
		ErrCodeLegalMoreThanAmountLimit:              "Максимальный лимит на одну транзакцию составляет %s",
		ErrCodeLegalHasUndoneOrder:                   "Есть позиция или открытый ордер, пожалуйста, закройте его",
		ErrCodeLegalPriceRateInvalid:                 "Извините, срок действия предложения истек, пожалуйста, разместите новый заказ",
		ErrCodeDigitalLessThanAmountLimit:            "Количество валюты заказа должно быть больше %s %s.",
		ErrCodeDigitalMoreThanAmountLimit:            "Количество валюты ордера должно быть меньше %s %s.",
		ErrCodeFiatLessThanAmountLimit:               "Сумма фиатной валюты для размещения ордера должна быть больше %s %s.",
		ErrCodeFiatMoreThanAmountLimit:               "Сумма фиатной валюты для размещения ордера должна быть менее %s %s.",
		ErrCodeActivityExpire:                        "Мероприятие истекло",
		ErrCodeActivityAdopt:                         "Награды были выданы",
		ErrCodePaymentsOverLimit:                     "Ваш способ оплаты достиг предела, и в настоящее время невозможно добавить новый способ оплаты.",
		ErrCodeApiCreateOverLimit:                    "Количество созданий API достигло верхнего предела",
		ErrCodeApiBindIPOverLimit:                    "Количество связанных IP-адресов превышает верхний предел",
		ErrCodeApiBindIPv6OverLimit:                  "Поддерживает только привязку до двух адресов IPv6",
		ErrCodeApiCreateNoPermission:                 "У текущего пользователя нет разрешения на создание и изменение API.",
		ErrCodeApiIPAddress:                          "Неверный IP-адрес",
		ErrCodeApiOverdue:                            "Срок действия текущего API истек",
		ErrCodeWithdrawAddrCountLimit:                "Адрес вывода средств достиг максимального числа и не может быть добавлен.",
		ErrCodeWithdrawAddressExisted:                "Текущий адрес уже существует",
		ErrCodeSafeSpareEmailCodeInvalid:             "Неверный резервный код подтверждения электронной почты",
		ErrCodeSafeSpareEmailCodeExpire:              "Срок действия альтернативного кода подтверждения электронной почты истек, пожалуйста, получите его еще раз",
		ErrCodeNonsupportAccountType:                 "Неподдерживаемый режим учетной записи",
		ErrCodeNonsupportPositionSplitMode:           "В настоящее время у вас есть позиции или невыполненные ордера, и вам не разрешено изменять режим разделения позиций.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "Аккаунт в статусе контроля рисков, пожалуйста, свяжитесь со службой поддержки",
		ErrCodeDeleteAccountHasUndoneOrder:           "Есть позиции или ордера или следующие трейдеры, пожалуйста, подтвердите, что все вышеуказанное содержимое отменено, и повторите попытку.",
		ErrCodeAccountIsDeleteState:                  "Эта учетная запись была удалена, если вам нужно зарегистрироваться снова, обратитесь в официальную службу поддержки клиентов.",
		ErrCodeNonsupportPhoneNumber:                 "Номер мобильного телефона не поддерживается",
		ErrCodeRiskLess:                              "Текущий уровень риска слишком низок, что приводит к срабатыванию контроля над риском. Это действие в настоящее время недоступно",
		ErrCodeOverPositionCount:                     "Превышено максимальное количество позиций",
		ErrCodeFollowNotSupport:                      "Текущий контракт не поддерживает доставку заказа",
		ErrCodePlanEntrustOverContractLimit:          "Ордер не выполнен, цена исполнения превышает текущую лимитную цену: %v",
		ErrCodePlanEntrustLessContractLimit:          "Ордер не выполнен, цена исполнения ниже текущей лимитной цены: %v",
		ErrCodePlanEntrustMore:                       "Цена исполнения должна быть ≥%v, иначе ваш ордер может не исполниться",
		ErrCodePlanEntrustLess:                       "Цена исполнения <=%v, иначе ваш ордер может быть не выполнен",
		ErrCodeOverMaxSideEntrust:                    "Превышает максимальное количество отложенных ордеров на одной стороне контракта",
		ErrCodeDepthInvalid:                          "Недостаточная текущая глубина",
		ErrCodePriceOverLimit:                        "Текущая цена ордера превышает допустимый диапазон",
		ErrCodePriceInvalid:                          "Текущая цена заказа недействительна",
		ErrCodeEntrustCloseVolumeInvalid:             "Текущий лимитный ордер имеет ордер на закрытие этой позиции, пожалуйста, отмените его, а затем используйте ордер на быстрое закрытие.",
		ErrCodePriceMarketOver:                       "Превышение порога защиты от ценовой разницы, срабатывание контроля риска",
		ErrCodePositionState:                         "Статус этой позиции был обновлен, пожалуйста, проверьте позицию перед установкой",
		ErrCodeInMaintenance:                         "Этот контракт находится на техническом обслуживании, повторите попытку позже.",
		ErrCodeNetPosException:                       "Ошибка делегата",
		ErrCodeForceClosing:                          "В настоящее время выполняется ликвидация, повторите попытку позже.",
		ErrCodeClosing:                               "Текущий договор ликвидируется, повторите попытку позже.",
		ErrCodeOrderIdUserd:                          "Номер заказа уже существует",
		ErrCodeUnfinishedEntrustOrderTooMany:         "Слишком много невыполненных заказов",
		ErrCodeOrderState:                            "Текущий статус заказа обновлен, обновите и повторите попытку.",
		ErrCodePlanLimitLEEntrustPrice:               "Цена тейк-профита должна быть выше цены исполнения.",
		ErrCodePlanStopGEEntrustPrice:                "Цена стоп-лосса должна быть меньше цены исполнения.",
		ErrCodePlanLimitGEEntrustPrice:               "Цена тейк-профита должна быть меньше цены исполнения.",
		ErrCodePlanStopLEEntrustPrice:                "Цена стоп-лосса должна быть больше цены исполнения.",
		ErrCodeBlowingRisk:                           "У вас есть риск принудительной ликвидации, когда вы открываете позицию на этот раз, и сработал контроль риска.",
		ErrCodeNonsupportMarginCoin:                  "Неподдерживаемая валюта маржи",
		ErrCodeBonusNonsupportHedgeMode:              "В настоящее время бонус не поддерживает двустороннее открытие, пожалуйста, закройте позицию и повторите попытку.",
		ErrCodeNonsupportExchange:                    "Неподдерживаемое погашение",
		ErrCodeExchangePreOrderOverdue:               "Текущая цена истекла, пожалуйста, получите ее снова",
		ErrCodeExchangePriceVolatility:               "Текущая цена сильно колеблется, цена этого ордера была признана недействительной",
		ErrCodeExchangeLessMinVolume:                 "Минимум %s %s за одну транзакцию",
		ErrCodeExchangeGreaterMaxVolume:              "Разовая транзакция до %s %s",
		ErrCodeExchangePrecisionLimit:                "Предел точности ввода",
		ErrCodeExchangeDailyLimit:                    "Превышен дневной лимит, оставшийся лимит: %s %s",
		ErrCodeGiftCashActivityExceed:                "Мероприятие истекло",
		ErrCodeGiftCashReceiveExceed:                 "Срок сбора истек",
		ErrCodePositionNumsLimit:                     "Режим разделения позиций Каждый контракт может содержать до 5 позиций с одной стороны.Вы можете выбрать позиции, которые вы удерживаете, чтобы добавить позиции.",
		ErrCodeNonsupportPositionSplitModeForVersion: "Текущая версия не может разместить заказ в режиме открытия текущего пользователя, обновите версию.",
	},
	ReqLangDE: {
		ErrCodeBusy:                                  "Das System ist ausgelastet, bitte versuchen Sie es später erneut",
		ErrCodeActionInvalid:                         "ungültige Anfrage",
		ErrCodeServerNotAvailable:                    "Der Dienst ist vorübergehend nicht verfügbar",
		ErrCodeParam:                                 "falscher Anforderungsparameter",
		ErrCodeRequestTimeException:                  "Die Gerätezeit ist nicht normal, bitte ändern Sie sie und versuchen Sie es erneut",
		ErrCodeSignature:                             "Signaturprüfung gescheitert",
		ErrCodeForceUpdate:                           "müssen upgraden",
		ErrCodeAuthCode:                              "Bestätigungscode-Fehler",
		ErrCodeAuthCodeInvalid:                       "Der Bestätigungscode ist ungültig, bitte fordern Sie ihn erneut an",
		ErrCodeImgAuthCode:                           "Fehler beim grafischen Bestätigungscode",
		ErrCodeInvalidInviteCode:                     "Ungültiger Einladungscode",
		ErrCodeActionTimeout:                         "Zeit abgelaufen für Vorgang",
		ErrCodeCWSParam:                              "cws-Fehler",
		ErrCodeResubmit:                              "Nicht erneut einreichen",
		ErrCodeStatus:                                "Statusfehler",
		ErrCodeFrequent:                              "häufige Anfragen",
		ErrCodeFileTooLarge:                          "Datei zu groß",
		ErrCodeNonsupportFileType:                    "nicht unterstütztes Dateiformat",
		ErrCodeTextLengthLimit:                       "Zeichenlänge überschritten",
		ErrCodeLimitCountry:                          "Derzeit wird die aktuelle Region nicht bedient",
		ErrCodeFeatureDisabled:                       "Dieser Dienst ist derzeit nicht verfügbar",
		ErrCodeAPIFrequent:                           "Zu viele Anfragen. Bitte versuchen Sie es später noch einmal",
		ErrCodeConfirmationIsRequired:                "Bestätigung erforderlich",
		ErrCodeShouldLogin:                           "Bitte loggen Sie sich zuerst ein",
		ErrCodeUserNotExist:                          "Benutzer existiert nicht",
		ErrCodeAccountIsExist:                        "Konto existiert bereits",
		ErrCodeAccountFormat:                         "Kontoformatfehler",
		ErrCodeTooLongAccount:                        "Kontolänge ist zu lang",
		ErrCodeLoginElseWhere:                        "Das Konto ist woanders angemeldet",
		ErrCodeFundPwdNotSet:                         "Etatkennwort ist nicht festgelegt",
		ErrCodeLoginPwdNotSet:                        "Login-Passwort nicht gesetzt",
		ErrCodeLoginPwdIncorrect:                     "Falsches Login-Passwort",
		ErrCodeLoginPwdSameAsFundPwd:                 "Das Login-Passwort darf nicht mit dem Kassenpasswort übereinstimmen",
		ErrCodeFundPwdSameAsLoginPwd:                 "Das Fondspasswort darf nicht mit dem Login-Passwort identisch sein",
		ErrCodeLoginNotAllowed:                       "Anmeldung deaktivieren",
		ErrCodeTradeNotAllowed:                       "Der aktuelle Benutzer ist vom Handel ausgeschlossen",
		ErrCodeWithdrawNotAllowed:                    "Dem aktuellen Benutzer ist das Abheben von Coins untersagt",
		ErrCodeHasAccountLock:                        "Andere Benutzer registrieren oder binden das aktuelle Konto, bitte versuchen Sie es später erneut",
		ErrCodeNeedVerify:                            "Benutzer nicht authentifiziert",
		ErrCodeIdNumberVerified:                      "Die ID-Informationen wurden überprüft und genehmigt, bitte führen Sie eine Gesichtsauthentifizierung durch",
		ErrCodeIdNumberExist:                         "Ausweisnummer ist bereits vergeben",
		ErrCodeVerifyIdNumber:                        "Ungültige ID-Nummer",
		ErrCodeVerifyValidity:                        "Name stimmt nicht mit ID-Nummer überein",
		ErrCodeNeedVerifyIdentity:                    "Muss zuerst die Identitätsprüfung bestehen",
		ErrCodeFaceVerified:                          "Gesichtsauthentifizierung bestanden",
		ErrCodeExisted:                               "existierte",
		ErrCodeTradeIDNotMatch:                       "Die aktuelle Identität kann diesen Vorgang nicht ausführen",
		ErrCodeDealerFollowLimit:                     "Die aktuelle Anzahl der zu verfolgenden Händler ist voll",
		ErrCodeDealerCanNotFollow:                    "Händler können keine Kopierbindung durchführen",
		ErrCodeNoFollow:                              "Folgt derzeit nicht",
		ErrCodeNonsupportModifyEmail:                 "Die Änderung der E-Mail-Adresse wird nicht unterstützt",
		ErrCodeModifySafeModeCount:                   "Der aktuelle Vorgang erfordert mehr als zwei Sicherheitsüberprüfungen",
		ErrCodeSafePhoneCodeInvalid:                  "Ungültiger telefonischer Bestätigungscode",
		ErrCodeSafeEmailCodeInvalid:                  "Ungültiger E-Mail-Bestätigungscode",
		ErrCodeSafeTotpCodeInvalid:                   "Ungültiges Validierungs-Captcha",
		ErrCodeSafePhoneCodeExpire:                   "Der Handy-Bestätigungscode ist abgelaufen, bitte fordern Sie ihn erneut an",
		ErrCodeSafeEmailCodeExpire:                   "Der E-Mail-Bestätigungscode ist abgelaufen, bitte fordern Sie ihn erneut an",
		ErrCodeSafeNeedVerify:                        "Bestätigung in zwei Schritten erforderlich",
		ErrCodeSafeNeedFundPasswordVerify:            "Die Überprüfung des Fondskennworts ist erforderlich",
		ErrCodeSafeFundPassword:                      "falsches Fondspasswort",
		ErrCodeSameOldPassword:                       "wie altes Passwort",
		ErrCodeFewTransfer:                           "Weniger als die minimale Einzelhubgrenze",
		ErrCodeManyTransfer:                          "Überschreitet die maximale Einzelhub-Übertragungsgrenze",
		ErrCodeCoinNotSupportTransfer:                "Die Währung unterstützt derzeit keine Übertragung",
		ErrCodeBalanceInsufficient:                   "Unzureichendes verfügbares Guthaben",
		ErrCodeInvalidAddress:                        "ungültige Adresse",
		ErrCodeWithdrawHasApply:                      "An diesem Tag werden Auszahlungsanträge geprüft",
		ErrCodeWithdrawHasPassed:                     "Die Anzahl erfolgreicher Abhebungen an diesem Tag hat die Obergrenze erreicht",
		ErrCodeDepositInsufficient:                   "Unzureichende Marge",
		ErrCodeNotSupportWithdraw:                    "Die aktuelle Währung unterstützt keine Auszahlung",
		ErrCodeWithdrawLessMinLimit:                  "Weniger als das Mindestauszahlungslimit",
		ErrCodeWithdrawGreaterMaxLimit:               "Mehr als das maximale Auszahlungslimit",
		ErrCodeTransferInsufficient:                  "Unzureichender verbleibender Überweisungsbetrag",
		ErrCodeSafeGuardForSetting:                   "Die Sicherheitseinstellungen für dieses Konto wurden geändert. Zur Sicherheit Ihres Kontos sind Auszahlungen innerhalb von 24 Stunden verboten",
		ErrCodeReceivingLimitReached:                 "Das Gesamtguthaben des Demokontos ist größer als 50 W und kann nicht mehr beansprucht werden",
		ErrCodeNeedWithdrawVerifyPassed:              "Sie müssen zuerst die Überprüfung der Auszahlungsidentität abschließen",
		ErrCodeLegalWithdrawSafeGuard:                "Für Ihre Kontosicherheit. Nach der Fiat-Währungstransaktion kann die Währung nicht innerhalb von 24 Stunden abgehoben werden.",
		ErrCodeSafeGuardForFundPwd:                   "Das Fondspasswort wird 5 Mal hintereinander falsch eingegeben und die Auszahlung wird innerhalb von 24 Stunden gesperrt.",
		ErrCodeWithdrawalRestrictionsMail:            "Widerrufsbeschränkungen: Bitte füllen Sie das Postfach verbindlich aus",
		ErrCodeInsufficientFollowBalance:             "Das verfügbare oder autorisierte Guthaben Ihrer Kopie entspricht nicht dem von Händlern geforderten Mindestkontoguthaben. Bitte überweisen Sie das Guthaben vor dem Kopieren.",
		ErrCodeDealerLimitIsChanged:                  "Der Händler hat die Konfiguration geändert",
		ErrCodeUnverifyWithdrawOver:                  "Ihr Auszahlungsbetrag hat heute das Limit erreicht, und eine Identitätsprüfung ist erforderlich, bevor Sie mit der Auszahlung fortfahren können.",
		ErrCodeNonsupportTransferAsset:               "Überweisungen zwischen derzeit ausgewählten Konten werden derzeit nicht unterstützt",
		ErrCodeHasGiftCashNonsupportTransferOut:      "Sie haben derzeit Positionen oder Aufträge, die den Bonus nutzen, der vorübergehend nicht übertragen werden kann",
		ErrCodeHasGiftCashDeviceLimit:                "Anspruch fehlgeschlagen, das Gerät hat den Bonus bereits beansprucht.",
		ErrCodeAssetIsLiquidise:                      "Der aktuelle Vermögenswert wird liquidiert, versuchen Sie es später erneut",
		ErrCodeContractNotExist:                      "Vertrag besteht nicht",
		ErrCodeContractNotTrade:                      "Der Vertrag ist vorübergehend nicht verfügbar",
		ErrCodeNonSupportOptimal:                     "Die Bestellung zum besten Preis wird derzeit nicht unterstützt",
		ErrCodeNonSupportPrice:                       "nicht unterstützter Preis",
		ErrCodePositionInsufficient:                  "Unzureichende verbleibende Positionen",
		ErrCodeTooManyOrderVolume:                    "Das Überschreiten der maximal offenen Position löst Risikokontrolle aus. Bitte passen Sie die Bestellmenge an",
		ErrCodeTradeFrequent:                         "Delegiertenanfragen sind häufig",
		ErrCodeNonSupportChangeDeposit:               "Der Cross-Rand-Modus unterstützt keine Anpassung der Randanpassung",
		ErrCodeBelowMinOpenVolume:                    "Unterhalb der einzelnen minimalen offenen Position",
		ErrCodeOverMaxOpenVolume:                     "höher als die maximale Öffnungsposition",
		ErrCodePositionInvalid:                       "Die aktuelle Position ist ungültig oder geschlossen",
		ErrCodeLeverInvalid:                          "Anders als das Vielfache der Hebelwirkung der aktuellen Position oder des aktuellen Plans",
		ErrCodeOverPlaceMoney:                        "mehr als die Bestellmenge",
		ErrCodePlanOrderLimit:                        "Ihr Vertrag hat keine 10 Auftragsvorschläge ausgelöst",
		ErrCodeLimitLEPrice:                          "Der Take-Profit-Preis muss größer sein als der durchschnittliche Eröffnungspreis",
		ErrCodeStopGEPrice:                           "Der Stop-Loss-Preis muss unter dem durchschnittlichen Eröffnungspreis liegen",
		ErrCodeLimitGEPrice:                          "Der Take-Profit-Preis muss unter dem durchschnittlichen Eröffnungspreis liegen",
		ErrCodeStopLEPrice:                           "Der Stop-Loss-Preis muss größer sein als der durchschnittliche Eröffnungspreis",
		ErrCodePlanLimitLEPrice:                      "Der Take-Profit-Preis muss größer sein als der Auslösepreis",
		ErrCodePlanStopGEPrice:                       "Der Stop-Loss-Preis muss unter dem Auslösepreis liegen",
		ErrCodePlanLimitGEPrice:                      "Der Take-Profit-Preis muss unter dem Auslösepreis liegen",
		ErrCodePlanStopLEPrice:                       "Der Stop-Loss-Preis muss größer sein als der Auslösepreis",
		ErrCodeLimitEIndex:                           "Der Take-Profit-Preis kann nicht gleich dem Kontraktindex sein",
		ErrCodeStopEIndex:                            "Der Stop-Loss-Preis kann nicht gleich dem Kontraktindex sein",
		ErrCodePlanCloseCountLimit:                   "Take Profit und Stop Loss haben 6 überschritten, bitte löschen und dann setzen",
		ErrCodeMarginModifyBalanceInsufficient:       "Unzureichender verfügbarer Saldo konnte die Marge nicht erhöhen",
		ErrCodeMarginModifyMarginInsufficient:        "Eine unzureichende Marge kann nicht reduziert werden",
		ErrCodeAccountTypeHasFullPosition:            "Sie haben derzeit eine Position im Cross-Position-Modus, bitte schließen Sie die Position zuerst und wechseln Sie sie dann",
		ErrCodeAccountTypeHasFullPlan:                "Sie haben derzeit einen Plan im Cross-Warehousing-Modus, bitte kündigen Sie ihn zuerst und wechseln Sie ihn dann",
		ErrCodeAccountTypeHasPartPosition:            "Sie haben derzeit eine isolierte Position, bitte schließen Sie die Position, bevor Sie wechseln.",
		ErrCodeAccountTypeHasPartPlan:                "Sie haben aktuell einen Planauftrag im isolierten Positionsmodus, bitte stornieren Sie diesen zuerst und schalten Sie ihn dann um",
		ErrCodeLeverHasBuyPartOrder:                  "Wenn Sie eine Long-Position im aktuellen Kontrakt haben oder die geplante Order nicht ausgelöst wurde, schließen Sie bitte die Position oder stornieren Sie sie, bevor Sie den Hebel wechseln.",
		ErrCodeLeverHasSellPartOrder:                 "Wenn Sie eine offene Position im aktuellen Kontrakt haben oder der geplante Auftrag nicht ausgelöst wurde, schließen oder stornieren Sie die Position bitte, bevor Sie den Hebel wechseln.",
		ErrCodeNoAccountTypePlace:                    "Bestellung im Kontokorrentmodus nicht möglich",
		ErrCodeContractMarketStop:                    "Bestellung fehlgeschlagen, bitte versuchen Sie es später erneut",
		ErrCodeNonsupportSwitchAccountType:           "Das Wechseln in den designierten Kontomodus wird derzeit nicht unterstützt",
		ErrCodeOverUnitTrade:                         "Bestellung fehlgeschlagen, bitte versuchen Sie es später erneut",
		ErrCodePartialFailed:                         "Einige Positionen konnten nicht geschlossen werden, bitte versuchen Sie es später erneut",
		ErrCodeNotEnoughVolumeTrade:                  "Auftragserteilung fehlgeschlagen, derzeit gibt es keine Gegenpartei zum Handeln",
		ErrCodeCloseOverPlace:                        "Das Überschreiten des maximalen Einzelauftragsbetrags löst eine Risikokontrolle aus. Bitte passen Sie die Bestellmenge an",
		ErrCodeNoPlatformSell:                        "Keine Händler, die Bestellungen annehmen",
		ErrCodeNoPlatformSellTime:                    "Außerhalb der Geschäftszeiten",
		ErrCodeLegalLessThanAmountLimit:              "Das Mindestlimit für eine einzelne Transaktion beträgt %s",
		ErrCodeLegalMoreThanAmountLimit:              "Die Höchstgrenze für eine einzelne Transaktion beträgt %s",
		ErrCodeLegalHasUndoneOrder:                   "Es gibt eine Position oder einen offenen Auftrag, bitte schließen Sie ihn",
		ErrCodeLegalPriceRateInvalid:                 "Entschuldigung, das Angebot ist abgelaufen, bitte geben Sie eine neue Bestellung auf",
		ErrCodeDigitalLessThanAmountLimit:            "Die Menge der Auftragswährung sollte größer als %s %s sein",
		ErrCodeDigitalMoreThanAmountLimit:            "Die Menge der Auftragswährung sollte weniger als %s %s betragen",
		ErrCodeFiatLessThanAmountLimit:               "Der Betrag der Fiat-Währung, um eine Bestellung aufzugeben, sollte größer als %s %s sein",
		ErrCodeFiatMoreThanAmountLimit:               "Der Betrag der Fiat-Währung, um eine Bestellung aufzugeben, sollte weniger als %s %s betragen",
		ErrCodeActivityExpire:                        "Veranstaltung ist abgelaufen",
		ErrCodeActivityAdopt:                         "Belohnungen wurden ausgegeben",
		ErrCodePaymentsOverLimit:                     "Ihre Zahlungsmethode hat das Limit erreicht und es kann derzeit keine neue Zahlungsmethode hinzugefügt werden",
		ErrCodeApiCreateOverLimit:                    "Die Anzahl der API-Erstellungen hat die Obergrenze erreicht",
		ErrCodeApiBindIPOverLimit:                    "Die Anzahl der gebundenen IPs überschreitet die Obergrenze",
		ErrCodeApiBindIPv6OverLimit:                  "Unterstützt nur die Bindung von bis zu zwei IPv6-Adressen",
		ErrCodeApiCreateNoPermission:                 "Der aktuelle Benutzer hat keine Berechtigung zum Erstellen und Ändern von APIs",
		ErrCodeApiIPAddress:                          "Falsche IP-Adresse",
		ErrCodeApiOverdue:                            "Die aktuelle API ist abgelaufen",
		ErrCodeWithdrawAddrCountLimit:                "Die Auszahlungsadresse hat die maximale Anzahl erreicht und konnte nicht hinzugefügt werden.",
		ErrCodeWithdrawAddressExisted:                "Die aktuelle Adresse existiert bereits",
		ErrCodeSafeSpareEmailCodeInvalid:             "Ungültiger Backup-E-Mail-Bestätigungscode",
		ErrCodeSafeSpareEmailCodeExpire:              "Der alternative E-Mail-Bestätigungscode ist abgelaufen, bitte fordern Sie ihn erneut an",
		ErrCodeNonsupportAccountType:                 "Nicht unterstützter Kontomodus",
		ErrCodeNonsupportPositionSplitMode:           "Sie halten derzeit Positionen oder offene Aufträge und dürfen den Positionsaufteilungsmodus nicht ändern.",
		ErrCodeDeleteAccountAtRiskStateLimit:         "Das Konto im Risikokontrollstatus, wenden Sie sich bitte an den Kundendienst",
		ErrCodeDeleteAccountHasUndoneOrder:           "Es gibt Positionen oder Aufträge. Bitte bestätigen Sie, dass keine Positionen oder Aufträge vorhanden sind, und versuchen Sie es erneut",
		ErrCodeAccountIsDeleteState:                  "Dieses Konto wurde gelöscht. Wenn Sie sich erneut registrieren müssen, wenden Sie sich bitte an den offiziellen Kundendienst",
		ErrCodeNonsupportPhoneNumber:                 "Mobiltelefonnummer wird nicht unterstützt",
		ErrCodeRiskLess:                              "Die aktuelle Risikorate ist zu niedrig und löst eine Risikokontrolle aus. Diese Aktion ist derzeit nicht verfügbar",
		ErrCodeOverPositionCount:                     "Maximale Anzahl von Positionen überschritten",
		ErrCodeFollowNotSupport:                      "Der aktuelle Vertrag unterstützt keine Auftragslieferung",
		ErrCodePlanEntrustOverContractLimit:          "Der Auftrag ist fehlgeschlagen, der Ausführungspreis übersteigt den aktuellen Limitpreis: %v",
		ErrCodePlanEntrustLessContractLimit:          "Der Auftrag ist fehlgeschlagen, der Ausführungspreis ist niedriger als der aktuelle Limitpreis: %v",
		ErrCodePlanEntrustMore:                       "Der Ausführungspreis sollte >=%v sein, sonst wird Ihre Order möglicherweise nicht ausgeführt",
		ErrCodePlanEntrustLess:                       "Bitte führen Sie den Preis <=%v aus, sonst wird Ihre Order möglicherweise nicht ausgeführt",
		ErrCodeOverMaxSideEntrust:                    "Überschreitet die maximale Anzahl an ausstehenden Bestellungen auf einer Seite des Vertrags",
		ErrCodeDepthInvalid:                          "Unzureichende Stromtiefe",
		ErrCodePriceOverLimit:                        "Der aktuelle Auftragspreis überschreitet den zulässigen Bereich",
		ErrCodePriceInvalid:                          "Der aktuelle Bestellpreis ist ungültig",
		ErrCodeEntrustCloseVolumeInvalid:             "Die aktuelle Limit-Order hat eine Schließungsorder für diese Position, bitte stornieren Sie sie und verwenden Sie dann die Schnellschließungsorder",
		ErrCodePriceMarketOver:                       "Überschreiten der Preisdifferenz-Schutzschwelle, Auslösen der Risikokontrolle",
		ErrCodePositionState:                         "Der Status dieser Position wurde aktualisiert, bitte überprüfen Sie die Position vor der Einstellung",
		ErrCodeInMaintenance:                         "Dieser Vertrag wird gewartet, bitte versuchen Sie es später erneut",
		ErrCodeNetPosException:                       "Delegierter fehlgeschlagen",
		ErrCodeForceClosing:                          "Die Liquidation ist derzeit im Gange, bitte versuchen Sie es später erneut",
		ErrCodeClosing:                               "Der aktuelle Vertrag befindet sich in Liquidation, bitte versuchen Sie es später erneut",
		ErrCodeOrderIdUserd:                          "Bestellnummer existiert bereits",
		ErrCodeUnfinishedEntrustOrderTooMany:         "Zu viele unerledigte Bestellungen",
		ErrCodeOrderState:                            "Der aktuelle Bestellstatus wurde aktualisiert, bitte aktualisieren Sie und versuchen Sie es erneut",
		ErrCodePlanLimitLEEntrustPrice:               "Der Take-Profit-Preis muss höher sein als der Ausübungspreis",
		ErrCodePlanStopGEEntrustPrice:                "Der Stop-Loss-Preis muss unter dem Ausübungspreis liegen",
		ErrCodePlanLimitGEEntrustPrice:               "Der Take-Profit-Preis muss unter dem Ausübungspreis liegen",
		ErrCodePlanStopLEEntrustPrice:                "Der Stop-Loss-Preis muss größer sein als der Ausübungspreis",
		ErrCodeBlowingRisk:                           "Wenn Sie diesmal eine Position eröffnen, besteht das Risiko einer erzwungenen Liquidation, und die Risikokontrolle wurde ausgelöst.",
		ErrCodeNonsupportMarginCoin:                  "Nicht unterstützte Margin-Währung",
		ErrCodeBonusNonsupportHedgeMode:              "Der Bonus unterstützt derzeit keine Zwei-Wege-Eröffnung, bitte schließen Sie die Position und versuchen Sie es erneut.",
		ErrCodeNonsupportExchange:                    "Nicht unterstützte Einlösung",
		ErrCodeExchangePreOrderOverdue:               "Der aktuelle Preis ist abgelaufen, bitte fordern Sie ihn erneut an",
		ErrCodeExchangePriceVolatility:               "Der aktuelle Preis schwankt stark, der Preis dieser Bestellung wurde ungültig",
		ErrCodeExchangeLessMinVolume:                 "Mindestens %s %s für eine einzelne Transaktion",
		ErrCodeExchangeGreaterMaxVolume:              "Einzeltransaktion bis zu %s %s",
		ErrCodeExchangePrecisionLimit:                "Eingabegenauigkeitsgrenze",
		ErrCodeExchangeDailyLimit:                    "Überschreiten Sie das Tageslimit, das verbleibende Limit: %s %s",
		ErrCodeGiftCashActivityExceed:                "Veranstaltung ist abgelaufen",
		ErrCodeGiftCashReceiveExceed:                 "Die Sammelfrist ist abgelaufen",
		ErrCodePositionNumsLimit:                     "Split-Positionsmodus Jeder Kontrakt kann bis zu 5 Positionen auf einer Seite halten.Sie können die Positionen, die Sie halten, auswählen, um Positionen hinzuzufügen.",
		ErrCodeNonsupportPositionSplitModeForVersion: "Die aktuelle Version kann keine Bestellung im aktuellen Benutzeröffnungsmodus aufgeben, bitte aktualisieren Sie die Version.",
	},
}

// 注意 如果想使用这个方法请 添加对应的ErrMap
func NewReplyErrorByCode(Code int) *ReplyError {
	return ErrMap[Code]
}

// ReplyError 带错误码的错误
type ReplyError struct {
	Code     int
	Msg      string
	isFormat bool
	ErrData  []interface{}
}

func (e *ReplyError) Error() string {
	if e == nil {
		return ""
	}
	return e.LangErrMsg(ReqLangCN)
}

func (e *ReplyError) ErrMsg(lang ReqLang) string {
	if e == nil {
		return ""
	}
	errs, ok := errMsgMap[lang]
	if !ok {
		errs = errMsgMap[ReqLangCN]
	}
	msg, ok := errs[e.Code]
	if !ok {
		if len(e.Msg) == 0 {
			msg = NormalErrMsg
		} else {
			msg = e.Msg
		}
	}
	return msg
}

func (e *ReplyError) LangErrMsg(lang ReqLang) string {
	if e == nil {
		return ""
	}
	msg := e.ErrMsg(lang)
	if !e.isFormat {
		return msg
	}
	e.Msg = msg
	msg = fmt.Sprintf(e.Msg, e.ErrData...)
	return msg
}

// newReplyError 返回ReplyError
func newReplyError(code int) *ReplyError {
	return &ReplyError{Code: code}
}

// NewReplyErrorWithMsg 返回ReplyError
func NewReplyErrorWithMsg(code int, msg string) error {
	return &ReplyError{Code: code, Msg: msg}
}

// NewReplyErrorWithFormatData 错误携带数据
func NewReplyErrorWithFormatData(code int, data ...any) error {
	return &ReplyError{Code: code, isFormat: true, ErrData: data}
}
