/*
@Time : 2019-12-26 16:20
<AUTHOR> mocha
@File : config.ws
*/
package define

//room名称配置
const (
	RoomMarket  = "market.%s" //默认一个交易对一个房间
	RoomDepth   = "market.depth.%s.%s"
	RoomTicker  = "market.ticker.%s"
	RoomApplies = "market.applies.%s"
	RoomSymbols = "contracts.market" //所有交易对行情及价格推送
)

//notify消息类型
const (
	NotifyDepth                        = "market.depth"
	NotifyTicker                       = "market.ticker"               //最新成交
	NotifyIndexPrice                   = "market.price.index"          //标记价格价格
	NotifySpotIndexPrice               = "market.price.index.spot"     //现货指数价格
	NotifySymbols                      = "contracts.market"            //所有合约价格及深度
	NotifyExchangeRate                 = "exchange.rate"               // 所有支持币种的兑换价格汇率推送
	NotifyContractInfo                 = "contract.applies"            //合约明细
	NotifyMessage                      = "message"                     //消息
	NotifyAccountComplex               = "user.account.complex"        //用户账户综合信息(风险率）
	NotifyContractForcePrice           = "user.contract.force"         //用户全仓合约强平价
	NotifyContractPositionUpdate       = "user.position.update"        //用户持仓修改
	NotifyContractFollowPositionUpdate = "user.follow.position.update" //用户跟单持仓修改
	NotifyPosDynamic                   = "user.position.dynamic"       //用户持仓动态数据
	NotifyEntrustOrderUpdate           = "user.entrust.order.update"   //用户委托订单更新
)

//订阅及通知动作
const (
	WsActionSub    = "sub"
	WsActionUnSub  = "unSub"
	WsActionAuth   = "auth"
	WsActionNotify = "notify"
)

//自动订阅房间
var AutoSubRooms = []string{RoomMarket}

const (
	RoomOpRegister   = 0
	RoomOpUnRegister = 1
)

const (
	ActionAdd    = "add"
	ActionUpdate = "update"
	ActionRemove = "remove"
)
