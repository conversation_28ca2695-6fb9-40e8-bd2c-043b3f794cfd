package bittrex

import (
	"bc/libs/json"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

const (
	_name = "bittrex"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get("https://api.bittrex.com/v3/markets/tickers")
	if err != nil {
		return
	}
	defer r.Body.Close()

	if r.StatusCode != http.StatusOK {
		//log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Println(string(b))
	var tickers []Tick
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	//fmt.Println(tickers)
	if len(tickers) == 0 {
		return
	}
	for _, v := range tickers {
		code := strings.Replace(v.Code, "-", "", -1)
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    v.Price,
				Side:     "",
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

//{"symbol":"4ART-BTC","lastTradeRate":"0.00000042","bidRate":"0.00000038","askRate":"0.00000041"}
type Tick struct {
	Code  string          `json:"symbol"`
	Price decimal.Decimal `json:"lastTradeRate"`
}
