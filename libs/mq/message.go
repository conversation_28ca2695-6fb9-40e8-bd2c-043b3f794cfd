/*
@Time : 2019-12-30 19:44
<AUTHOR> mocha
@File : message
*/
package mq

import (
	"bc/libs/snowflake"
	"encoding/json"
)

type MsgHandleFunc func(message MessagePack)

var DefaultHandleMap = make(map[string]MsgHandleFunc, 0)

type Extra struct {
	ID    int64 `json:"id"`
	SeqId int64 `json:"seq_id"`
}

type MessagePack struct {
	MsgId int64  `json:"msg_id"`
	Topic string `json:"topic"`
	Code  string `json:"code,omitempty" `
	Extra
	Data       json.RawMessage `json:"data,omitempty"`
	DealId     int64           `json:"deal_id,omitempty"`
	Identifier int             `json:"identifier,omitempty"`
	UserId     int64           `json:"user_id,omitempty"`

	WaitTs int64 `json:"wait_ts"`
}

var Ids *snowflake.IdWorker

func init() {
	Ids, _ = snowflake.NewIdWorker(1000)
}

func SetIdWorker(workerID int64) {
	Ids, _ = snowflake.NewIdWorker(workerID)
}

func GetNext() int64 {
R:
	id, err := Ids.NextId()
	if err != nil {
		goto R
	}
	return id
}

func (mp MessagePack) Marsha() []byte {
	if mp.MsgId == 0 {
		mp.MsgId = GetNext()
	}
	b, _ := json.Marshal(mp)
	return b
}

func (mp MessagePack) Marshal() ([]byte, int64) {
	mp.MsgId = GetNext()
	b, _ := json.Marshal(mp)
	return b, mp.MsgId
}

func (mp *MessagePack) SetExtra(id, seqId int64) {
	mp.Extra.ID = id
	mp.Extra.SeqId = seqId
}
