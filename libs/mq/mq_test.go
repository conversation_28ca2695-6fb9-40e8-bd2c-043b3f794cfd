/*
@Time : 2019-12-30 18:59
<AUTHOR> mocha
@File : mq_test
*/
package mq

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"bc/libs/convert"
	"bc/libs/log"
)

var file = "data.log"
var topic = "quickly.test2"

func TestPub1(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.Ping()
	for i := 0; i < 100; i++ {
		err := m.Publish(topic, "direct", "key_1", convert.Str2Bytes("msg_test_a_"+strconv.Itoa(i)), true)
		if err != nil {
			fmt.Println(err)
		}
		time.Sleep(2 * time.Second)

	}
	select {}
}

func TestPub(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.<PERSON>()
	for i := 0; i < 100; i++ {
		err := m.Publish(topic, "direct", "key_1", convert.Str2Bytes("msg_test_b_"+strconv.Itoa(i)), true)
		if err != nil {
			fmt.Println(err)
		}
		time.Sleep(2 * time.Second)

	}
	select {}
}
func TestSub(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	err := m.Consumer(topic, "direct", "t3", "key_1")
	m.IsForce = true
	m.Ping()
	if err != nil {
		log.Errorf("connection consumer fail,%v", err)
	}
	s := time.Tick(5 * time.Second)
	go func() {
		for range s {
			//m.ShutDown()
		}
	}()
	select {}
}

func TestSub5(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.Ping()
	err := m.Consumer(topic, "direct", "t4", "key_1")
	if err != nil {
		fmt.Println(err)
	}
	select {}
}

func TestSub6(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.Ping()
	err := m.Consumer(topic, "direct", "t6", "key_1")
	if err != nil {
		fmt.Println(err)
	}
	select {}
}

//exchange:ex.quickly,exchangeType:direct,queueName:tquickly,key:quickly

func TestSubQuickly(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.Ping()
	err := m.Consumer("ex.quickly", "direct", "tquickly", "quickly")
	if err != nil {
		fmt.Println(err)
	}
	select {}
}

func TestSub2(t *testing.T) {
	// 初始化日志
	log.InitLogger(file, "debug", false)

	m := NewMessageQueue("amqp://user:password@localhost:5672")
	m.Ping()
	err := m.Consumer(topic, "direct", "t2", "key_1")
	if err != nil {
		fmt.Println(err)
	}
	select {}
}
