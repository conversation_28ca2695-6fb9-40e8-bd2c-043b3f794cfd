package okex

import (
	"testing"
	"time"
)

func TestRESTClient_GetBalance(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testtest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	asset, err := cli.GetBalance()
	if err != nil {
		t.Fatal(err.Error())
	}
	t.Logf("%+v", asset)
}

func TestRESTClient_GetAccountConfig(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testtest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	cfg, err := cli.GetAccountConfig()
	if err != nil {
		t.Fatal(err.Error())
	}
	t.Logf("%+v", cfg)
}

func TestRESTClient_SetPositionMode(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testtest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	err := cli.SetPositionMode(PositionModeLongShort)
	if err != nil {
		t.Fatal(err.Error())
	}
}

func TestRESTClient_SetLeverage(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testtest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	err := cli.SetLeverage("BTC-USDT-SWAP", "125", MarginModeCross)
	if err != nil {
		t.Fatal(err.Error())
	}
}

func TestRESTClient_GetPositions(t *testing.T) {
	InitLogger()
	SetGlobal("https://www.ouyi.icu", time.Second*5, true)
	cli := NewRESTClient(Config{
		ApiKey:     "0b7dab4e-1343-4a47-8466-03478fc1301b",
		PassPhrase: "testiest",
		SecretKey:  "F2D0F917ADEB15528A9A5C06D607C62B",
	})
	pos, err := cli.GetPositions("BTC-USDT-SWAP")
	if err != nil {
		t.Fatal(err.Error())
	}
	t.Logf("%+v", pos)
}
