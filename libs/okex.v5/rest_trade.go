/*
	资产相关接口,需要进行api认证
*/

package okex

import (
	"bc/libs/log"
	"go.uber.org/zap"
	"net/http"
)

// Place 下单
func (rc *RESTClient) Place(instId, clOrdId, sz, px string, oSide OrderSide, posSide PositionSide) (*PlaceResult, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}
	if !oSide.Check() || !posSide.Check() {
		return nil, ParamNotSupport
	}

	param := map[string]interface{}{
		"instId":  instId,
		"tdMode":  MarginModeCross,
		"ccy":     "USDT",
		"clOrdId": clOrdId,
		"side":    oSide,
		"posSide": posSide,
		"ordType": OrderTypeIoc,
		"sz":      sz,
		"px":      px,
	}
	log.Info("dealHedgeForOkex Place begin",
		zap.Any("params", param))
	var list []PlaceResult
	result, err := rc.do(http.MethodPost, RESTAPIPlaceOrder, param, &list)
	if err != nil {
		return nil, err
	}
	log.Info("dealHedgeForOkex Place back",
		zap.Any("result", result))
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}

// GetOrder 获取订单信息
func (rc *RESTClient) GetOrder(instId, ordId string) (*Order, error) {
	if rc == nil {
		return nil, ClientUninitialized
	}

	param := map[string]interface{}{
		"instId": instId,
		"ordId":  ordId,
	}

	var list []Order
	result, err := rc.do(http.MethodGet, RESTAPIGetOrder, param, &list)
	if err != nil {
		return nil, err
	}
	if result.Code != RESTResultCodeSuccess {
		return nil, &result.RESTResultStatus
	}
	if len(list) == 0 {
		return nil, nil
	}

	return &list[0], nil
}
