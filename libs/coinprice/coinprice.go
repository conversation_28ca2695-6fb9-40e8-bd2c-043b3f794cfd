package coinprice

import (
	"bc/libs/conf"
	"crypto/tls"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"bc/libs/commonsrv"
	"bc/libs/convert"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/msg"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

const (
	_coinGeckoBaseRate    = "/simple/price"
	_coinGeckoCoinList    = "/coins/list"
	_coinGeckoCoinsMarket = "/coins/markets"
)

var client = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
	},
}

func GetCoinRate(coins map[string]bool) []proto.CoinRate {
	var errMsg string
	runID := database.NextID()

	// 从非小号获取数据
	//fxh, err := GetCoinRateByFXH(runID, coins)
	//if err != nil {
	//	log.Error("[获取币价]非小号出现错误", zap.Int64("runID", runID), zap.Error(err))
	//	errMsg += "\n非小号获取币种数据出错:\n" + err.Error()
	//}

	// 从币虎获取汇率
	bh := make(map[string]proto.CoinRate)
	bhRate, err := GetUSD2CNYByCoinGecko(runID)
	if err != nil {
		log.Error("[获取币价]币虎出现错误", zap.Int64("runID", runID), zap.Error(err))
		errMsg += "\n币虎获取美元汇率数据出错:\n" + err.Error()
	}
	if bhRate.GreaterThan(decimal.Zero) {
		// 转换成币虎对应币种id列表
		ids, err := ConvertBHCoinIDs(runID, coins)
		if err != nil {
			log.Error("[获取币价]币虎出现错误", zap.Int64("runID", runID), zap.Error(err))
			errMsg += "\n转换成币虎对应币种id列表出错:\n" + err.Error()
		}
		if len(ids) > 0 {
			// 获取币虎币种数据
			bh, err = GetCoinRateByCoinGecko(runID, ids, bhRate)
			if err != nil {
				log.Error("[获取币价]币虎出现错误", zap.Int64("runID", runID), zap.Error(err))
				errMsg += "\n币虎获取币种数据出错:\n" + err.Error()
			}
		}
	}

	//if len(fxh) == 0 && len(bh) == 0 {
	if len(bh) == 0 {
		// 两个网站都获取失败
		ip, _ := utils.GetExtranetIp()
		msg.SendCautionEmail("定时获取币价失败", "本机IP: "+ip+"\n报错内容: "+errMsg+"\n请及时处理!")
		return nil
	}

	cny := proto.CoinRate{Symbol: define.LegalCoinNameCNY, PriceCNY: 1}
	usd := proto.CoinRate{Symbol: define.LegalCoinNameUSD, PriceUSD: 1}

	one := decimal.New(1, 0)
	//count := decimal.New(2, 0)
	rates := make([]proto.CoinRate, 0)
	for key := range coins {
		var rate proto.CoinRate
		//_, fxhExist := fxh[key]
		_, bhExist := bh[key]
		//if fxhExist && bhExist {
		//	// 取两个网站的平均值
		//	rate.Symbol = strings.ToUpper(key)
		//	rate.PriceCNY, _ = decimal.NewFromFloat(fxh[key].PriceCNY).Add(decimal.NewFromFloat(bh[key].PriceCNY)).Div(count).Truncate(2).Float64()
		//	rate.PriceUSD, _ = decimal.NewFromFloat(fxh[key].PriceUSD).Add(decimal.NewFromFloat(bh[key].PriceUSD)).Div(count).Truncate(6).Float64()
		//	rate.PriceBTC, _ = decimal.NewFromFloat(fxh[key].PriceBTC).Add(decimal.NewFromFloat(bh[key].PriceBTC)).Div(count).Truncate(define.FloatPrecision).Float64()
		//	rate.PriceUSDT, _ = decimal.NewFromFloat(fxh[key].PriceUSDT).Add(decimal.NewFromFloat(bh[key].PriceUSDT)).Div(count).Truncate(define.FloatPrecision).Float64()
		//	if key == define.CoinNameBtc {
		//		if rate.PriceCNY != 0 {
		//			cny.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
		//		}
		//		if rate.PriceUSD != 0 {
		//			usd.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
		//		}
		//	} else if key == define.CoinNameUsdt {
		//		if rate.PriceCNY != 0 {
		//			cny.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
		//		}
		//		if rate.PriceUSD != 0 {
		//			usd.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
		//		}
		//	}
		//
		//	log.Info("[获取币价]得到两个网站数据平均值", zap.Int64("runID", runID), zap.Any("rate", rate))
		//	rates = append(rates, rate)
		//	continue
		//}
		//if fxhExist {
		//	rate = fxh[key]
		//	rate.Symbol = strings.ToUpper(rate.Symbol)
		//	if key == define.CoinNameBtc {
		//		if rate.PriceCNY != 0 {
		//			cny.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
		//		}
		//		if rate.PriceUSD != 0 {
		//			usd.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
		//		}
		//	} else if key == define.CoinNameUsdt {
		//		if rate.PriceCNY != 0 {
		//			cny.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
		//		}
		//		if rate.PriceUSD != 0 {
		//			usd.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
		//		}
		//	}
		//
		//	log.Info("[获取币价]得到非小号网站数据", zap.Int64("runID", runID), zap.Any("rate", rate))
		//	rates = append(rates, rate)
		//	continue
		//}
		if bhExist {
			rate = bh[key]
			rate.Symbol = strings.ToUpper(rate.Symbol)
			if key == define.CoinNameBtc {
				if rate.PriceCNY != 0 {
					cny.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
				}
				if rate.PriceUSD != 0 {
					usd.PriceBTC, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
				}
			} else if key == define.CoinNameUsdt {
				if rate.PriceCNY != 0 {
					cny.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceCNY)).Truncate(define.FloatPrecision).Float64()
				}
				if rate.PriceUSD != 0 {
					usd.PriceUSDT, _ = one.Div(decimal.NewFromFloat(rate.PriceUSD)).Truncate(define.FloatPrecision).Float64()
				}
			}

			log.Info("[获取币价]得到币虎网站数据", zap.Int64("runID", runID), zap.Any("rate", rate))
			rates = append(rates, rate)
		}
	}

	if cny.PriceUSDT != 0 && usd.PriceUSDT != 0 {
		cny.PriceUSD, _ = decimal.NewFromFloat(cny.PriceUSDT).Div(decimal.NewFromFloat(usd.PriceUSDT)).Truncate(define.FloatPrecision).Float64()
		usd.PriceCNY, _ = decimal.NewFromFloat(usd.PriceUSDT).Div(decimal.NewFromFloat(cny.PriceUSDT)).Truncate(define.FloatPrecision).Float64()
	} else if cny.PriceBTC != 0 && usd.PriceBTC != 0 {
		cny.PriceUSD, _ = decimal.NewFromFloat(cny.PriceBTC).Div(decimal.NewFromFloat(usd.PriceBTC)).Truncate(define.FloatPrecision).Float64()
		usd.PriceCNY, _ = decimal.NewFromFloat(usd.PriceBTC).Div(decimal.NewFromFloat(cny.PriceBTC)).Truncate(define.FloatPrecision).Float64()
	}
	log.Info("[获取币价]", zap.Int64("runID", runID), zap.Any("rate", cny))
	log.Info("[获取币价]", zap.Int64("runID", runID), zap.Any("rate", usd))
	rates = append(rates, cny, usd)

	return rates
}

// 从非小号获取币种数据
func GetCoinRateByFXH(runID int64, coins map[string]bool) (map[string]proto.CoinRate, error) {
	uri := conf.FXHOpenAPI()
	body, code, err := httpGet(uri, map[string]string{"convert": "CNY"})
	if code != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, 0, define.ExternalSysTypeFxh, code, http.StatusText(code), uri)
		log.Error("GetCoinByFXH http status abnormal", zap.Int64("runID", runID), zap.Int("resp.StatusCode", code))
		return nil, fmt.Errorf("http 返回状态码[%d]", code)
	}
	if err != nil {
		log.Error("GetCoinByFXH http error", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}
	var list []proto.CoinRate
	err = json.Unmarshal(body, &list)
	if err != nil {
		log.Error("GetCoinByFXH http json unmarshal failed", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}

	// 先取出usd到usdt的汇率
	var rate decimal.Decimal
	for i := range list {
		if list[i].Symbol == define.CoinNameUsdt {
			rate = decimal.NewFromFloat(list[i].PriceUSD)
			break
		}
	}

	result := make(map[string]proto.CoinRate)
	for i := range list {
		if _, ok := coins[list[i].Symbol]; ok {
			list[i].Symbol = strings.ToUpper(list[i].Symbol)
			if list[i].Symbol == define.CoinNameUsdt {
				list[i].PriceUSDT = 1
			} else if !rate.IsZero() {
				list[i].PriceUSDT, _ = decimal.NewFromFloat(list[i].PriceUSD).Div(rate).Truncate(define.FloatPrecision).Float64()
			}
			result[list[i].Symbol] = list[i]
			if len(coins) == len(result) {
				break
			}
		}
	}
	return result, nil
}

type _price struct {
	CNY float64 `json:"cny"`
	USD float64 `json:"usd"`
}

// 从币虎获取美元与人民币汇率
func GetUSD2CNYByCoinGecko(runID int64) (decimal.Decimal, error) {
	body, code, err := httpGet(conf.CoinGeckoOpenAPI()+_coinGeckoBaseRate, map[string]string{"ids": "tether", "vs_currencies": "cny,usd"})
	if code != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, 0, define.ExternalSysTypeGecko, code, http.StatusText(code), conf.CoinGeckoOpenAPI()+_coinGeckoBaseRate)
		log.Error("GetUSD2CNYByCoinGecko http status abnormal", zap.Int64("runID", runID), zap.Int("resp.StatusCode", code))
		return decimal.Zero, fmt.Errorf("http 返回状态码[%d]", code)
	}
	if err != nil {
		log.Error("GetUSD2CNYByCoinGecko http error", zap.Int64("runID", runID), zap.Error(err))
		return decimal.Zero, err
	}

	rate := make(map[string]_price)
	err = json.Unmarshal(body, &rate)
	if err != nil {
		log.Error("GetUSD2CNYByCoinGecko json unmarshal error", zap.Int64("runID", runID), zap.Error(err))
		return decimal.Zero, err
	}

	for _, val := range rate {
		if val.USD <= 0 {
			return decimal.Zero, fmt.Errorf("无法获取美元兑换人民币汇率, body: %s", convert.Bytes2Str(body))
		}
		return decimal.NewFromFloat(val.CNY).Div(decimal.NewFromFloat(val.USD)), nil
	}

	return decimal.Zero, nil
}

type _coin struct {
	ID     string `json:"id"`
	Symbol string `json:"symbol"`
}

// 转换为币虎对应的币种id
func ConvertBHCoinIDs(runID int64, coins map[string]bool) (string, error) {
	body, code, err := httpGet(conf.CoinGeckoOpenAPI()+_coinGeckoCoinList, nil)
	if code != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, 0, define.ExternalSysTypeGecko, code, http.StatusText(code), conf.CoinGeckoOpenAPI()+_coinGeckoCoinList)
		log.Error("ConvertBHCoinIDs http status abnormal", zap.Int64("runID", runID), zap.Int("resp.StatusCode", code))
		return "", fmt.Errorf("http 返回状态码[%d]", code)
	}
	if err != nil {
		log.Error("ConvertBHCoinIDs http error", zap.Int64("runID", runID), zap.Error(err))
		return "", err
	}

	var list []_coin
	err = json.Unmarshal(body, &list)
	if err != nil {
		log.Error("ConvertBHCoinIDs json unmarshal error", zap.Int64("runID", runID), zap.Error(err))
		return "", err
	}

	var ids []string
	for i := range list {
		if _, ok := coins[strings.ToUpper(list[i].Symbol)]; ok {
			ids = append(ids, list[i].ID)
		}
	}
	return strings.Join(ids, ","), nil
}

type _market struct {
	Symbol       string  `json:"symbol"`
	CurrentPrice float64 `json:"current_price"`
}

// 从币虎获取币种数据
func GetCoinRateByCoinGecko(runID int64, coins string, cny decimal.Decimal) (map[string]proto.CoinRate, error) {
	body, code, err := httpGet(conf.CoinGeckoOpenAPI()+_coinGeckoCoinsMarket, map[string]string{"vs_currency": "usd", "page": "1", "per_page": "200", "sparkline": "false", "ids": coins})
	if code != http.StatusOK {
		commonsrv.SaveExternalErrorRecord(nil, runID, 0, define.ExternalSysTypeGecko, code, http.StatusText(code), conf.CoinGeckoOpenAPI()+_coinGeckoCoinsMarket)
		log.Error("GetCoinRateByCoinGecko http status abnormal", zap.Int64("runID", runID), zap.Int("resp.StatusCode", code))
		return nil, fmt.Errorf("http 返回状态码[%d]", code)
	}
	if err != nil {
		log.Error("GetCoinRateByCoinGecko http error", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}

	var list []_market
	err = json.Unmarshal(body, &list)
	if err != nil {
		log.Error("GetCoinRateByCoinGecko http json unmarshal failed", zap.Int64("runID", runID), zap.Error(err))
		return nil, err
	}

	// 先取出btc和usdt相应的价格用于转换
	var btc, usdt decimal.Decimal
	for i := range list {
		if strings.ToUpper(list[i].Symbol) == define.CoinNameBtc {
			btc = decimal.NewFromFloat(list[i].CurrentPrice)
		} else if strings.ToUpper(list[i].Symbol) == define.CoinNameUsdt {
			usdt = decimal.NewFromFloat(list[i].CurrentPrice)
		}
	}

	result := make(map[string]proto.CoinRate)
	for i := range list {
		list[i].Symbol = strings.ToUpper(list[i].Symbol)
		rate := proto.CoinRate{
			Symbol:   list[i].Symbol,
			PriceUSD: list[i].CurrentPrice,
		}
		rate.PriceCNY, _ = decimal.NewFromFloat(list[i].CurrentPrice).Mul(cny).Truncate(2).Float64()
		if !btc.IsZero() {
			rate.PriceBTC, _ = decimal.NewFromFloat(list[i].CurrentPrice).Div(btc).Truncate(define.FloatPrecision).Float64()
		}
		if !usdt.IsZero() {
			rate.PriceUSDT, _ = decimal.NewFromFloat(list[i].CurrentPrice).Div(usdt).Truncate(define.FloatPrecision).Float64()
		}
		result[list[i].Symbol] = rate
	}
	return result, nil
}

func httpGet(uri string, param map[string]string) ([]byte, int, error) {
	req, err := http.NewRequest("GET", uri, nil)
	if err != nil {
		log.Error("httpGet http error", zap.Error(err))
		return nil, http.StatusOK, err
	}

	q := url.Values{}
	for key, val := range param {
		q.Add(key, val)
	}

	req.Header.Set("Accepts", "application/json")
	req.URL.RawQuery = q.Encode()

	resp, err := client.Do(req)
	if err != nil {
		log.Error("httpGet http Do failed", zap.Error(err))
		return nil, http.StatusOK, err
	}
	if resp.StatusCode != http.StatusOK {
		log.Error("httpGet http status abnormal", zap.Int("resp.StatusCode", resp.StatusCode))
		return nil, resp.StatusCode, nil
	}

	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("httpGet http read body failed", zap.Error(err))
		return nil, http.StatusOK, err
	}
	return respBody, http.StatusOK, nil
}
