package poloniex

import (
	"bc/libs/json"
	"io/ioutil"
	"net/http"
	"testing"
)

func TestApiTicker(t *testing.T) {
	s := GetTickers(map[string]struct{}{"BTCUSDT": {}})
	t.Logf("v:%+v", s)
}

func TestApispot(t *testing.T) {
	str := "https://poloniex.com/public?command=returnTicker"
	r, err := http.Get(str)
	if err != nil {
		return
	}
	defer r.Body.Close()
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	t.Logf("r:%v", string(b))
	s := make(map[string]Tick)
	e := json.Unmarshal(b, &s)
	if e != nil {
		t.Logf("t:%v", e)
		return
	}
	t.Logf("tv:%+v", s)
}
