package poloniex

//
//import (
//	"bc/libs/json"
//	"bc/libs/proto"
//	"bc/libs/utils"
//	"fmt"
//	"github.com/shopspring/decimal"
//	"io/ioutil"
//	"net/http"
//	"time"
//)
//
////https://futures-docs.poloniex.com/#general
////https://futures-docs.poloniex.com/#apply-for-connection-token
//
////https://futures-api.poloniex.com
//
////https://futures-api.poloniex.com/api/v1/ticker?symbol=BTCUSDTPERP
////{"code":"200000", "data":{"sequence":369332, "symbol":"ETHUSDTPERP", "side":"sell", "size":907, "price":"1177.0","bestBidSize":1593, "bestBidPrice":"1177.0", "bestAskPrice":"1189.7", "tradeId":"5ff80041d49eb25bfa0116bf", "bestAskSize":1279,"ts":1610088513790043330}}
//type ticks struct {
//	Code string `json:"code"`
//	Data struct {
//		Code  string          `json:"code"`
//		Side  string          `json:"side"`
//		Size  decimal.Decimal `json:"size"`
//		Price decimal.Decimal `json:"price"`
//	} `json:"data"`
//}
//
//const (
//	url       = "https://futures-api.poloniex.com"
//	apiTicker = "/api/v1/ticker?symbol="
//	name      = "poloniex"
//)
//
//func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
//	for s := range codes {
//		m := getTicker(s)
//		if m == nil {
//			continue
//		}
//		m.Source = name
//		list = append(list, *m)
//	}
//	return
//}
//
//func getTicker(code string) (mt *proto.MarketTrade) {
//	s := fmt.Sprintf("%sPERP", code)
//	u := utils.StrBuilder(url, apiTicker, s)
//	r, err := http.Get(u)
//	if err != nil {
//		return
//	}
//	if r.StatusCode != http.StatusOK {
//		return
//	}
//	defer r.Body.Close()
//	b, err := ioutil.ReadAll(r.Body)
//	if err != nil {
//		return
//	}
//	fmt.Printf("%v", string(b))
//	var ticker ticks
//	err = json.Unmarshal(b, &ticker)
//	if err != nil {
//		return
//	}
//	fmt.Println(ticker)
//	if ticker.Code == "200000" {
//		mt = &proto.MarketTrade{
//			Symbol:   code,
//			DealTime: time.Now().Unix(),
//			Price:    ticker.Data.Price,
//			Volume:   ticker.Data.Size,
//			Side:     ticker.Data.Side,
//			Ts:       time.Now(),
//		}
//		return
//	}
//	return
//}
//
//
////https://poloniex.com/public?command=returnTicker
