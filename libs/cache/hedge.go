package cache

import (
	"strconv"
	"time"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/okex.v5"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//获取okex合约
func GetOkexContract(contractCode string) (contract okex.Instrument) {
	b, err := DefaultRedis().HGet(define.CacheHedgeKeyOkexContracts, contractCode).Bytes()
	if err != nil {
		log.Error("GetOkexContract get fail", zap.String("code", contractCode), zap.Error(err))
		return
	}
	err = json.Unmarshal(b, &contract)
	if err != nil {
		log.Error("GetOkexContract json unmarshal fail", zap.String("code", contractCode), zap.Error(err))
		return
	}
	return
}

// CleanOkexContract 清理合约信息
func CleanOkexContract() {

}

// SetOkexContract 设置okex合约
func SetOkexContract(contract string, info okex.Instrument) {
	b, err := json.Marshal(info)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheHedgeKeyOkexContracts, contract, b)
}

//缓存对冲合约配置
func CacheHedgeConfig(hcs []proto.HedgeConfig) {
	for i := range hcs {
		b, err := json.Marshal(&hcs[i])
		if err != nil {
			log.Error("CacheHedgeConfig marshal fail", zap.Any("data", hcs[i]), zap.Error(err))
			continue
		}

		err = DefaultRedis().HSet(define.CacheHedgeKeyConfig, hcs[i].HedgeAccount, b).Err()
		if err != nil {
			log.Error("CacheHedgeConfig redis error", zap.Any("data", hcs[i]), zap.Error(err))
			return
		}
	}
}

//获取对冲合约配置
func GetHedgeConfig(hedgeAccount string) (config *proto.HedgeConfig) {
	b, err := DefaultRedis().HGet(define.CacheHedgeKeyConfig, hedgeAccount).Bytes()
	if err != nil {
		log.Errorf("GetHedgeConfig get fail,%v", err)
		return
	}
	c := new(proto.HedgeConfig)
	err = json.Unmarshal(b, c)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	config = c
	return
}

//设置api，合约账户持仓
func SetHedgeContractPosition(hedgeConfId int, p proto.HedgePosition) {
	b, err := json.Marshal(p)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	key := define.CacheHedgeKeyHedgeContractPos + ":" + strconv.Itoa(hedgeConfId)
	field := p.ContractAccount + ":" + string(p.Side)
	DefaultRedis().HSet(key, field, b)
}

//删除指定api下所有合约缓存
func DelHedgePosition(hedgeConfId int) {
	key := define.CacheHedgeKeyHedgeContractPos + ":" + strconv.Itoa(hedgeConfId)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("DelHedgePosition cache key fail,%v", err)
	}
}

//获取指定api下合约持仓
func GetHedgeContractPosition(hedgeConfId int, contractCode string, side string) *proto.HedgePosition {
	pos := new(proto.HedgePosition)
	key := define.CacheHedgeKeyHedgeContractPos + ":" + strconv.Itoa(hedgeConfId)
	field := contractCode + ":" + side

	b, err := DefaultRedis().HGet(key, field).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetHedgeContractPosition redis get fail,%v", err)
		}
		return nil
	}
	err = json.Unmarshal(b, pos)
	if err != nil {
		log.Errorf("GetHedgeContractPosition unmarshal fail,%v", err)
		return nil
	}
	return pos
}

func SetHedgeAccountConractLever(hedgeConfId int, contractCode string, lever int) (err error) {
	key := define.CacheHedgeKeyAccountContractLever + strconv.Itoa(hedgeConfId)

	_, err = DefaultRedis().HSet(key, contractCode, lever).Result()
	if err != nil {
		log.Errorf("setHedgeAccountConractLever redis get fail,%v", err)
		return
	}
	return
}

func ExsitHedgeAccountConractLever(hedgeConfId int, contractCode string) (exist bool) {
	key := define.CacheHedgeKeyAccountContractLever + strconv.Itoa(hedgeConfId)
	exist = DefaultRedis().HExists(key, contractCode).Val()
	return
}

func SetWarnEmailsByHedge(emails []string) (err error) {
	b, err := json.Marshal(emails)
	if err != nil {
		log.Errorf("SetWarnEmailsByHedge warn emails json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().Set(define.CacheHedgeWarnEmailKey, b, time.Minute).Err()
	return
}

func GetWarnEmailsByHedge() (emails []string, err error) {
	b, err := DefaultRedis().Get(define.CacheHedgeWarnEmailKey).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, err
		}
		log.Errorf("GetWarnEmailsByHedge redis get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &emails)
	if err != nil {
		log.Errorf("GetWarnEmails json unMarshal fail,%v", err)
		return
	}
	return
}

func SetWarnEmailsByMarket(emails []string) (err error) {
	b, err := json.Marshal(emails)
	if err != nil {
		log.Errorf("SetWarnEmailsByMarket warn emails json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().Set(define.CacheMarketWarnEmailKey, b, time.Minute).Err()
	return
}

func GetWarnEmailsByMarket() (emails []string, err error) {
	b, err := DefaultRedis().Get(define.CacheMarketWarnEmailKey).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, err
		}
		log.Errorf("GetWarnEmailsByMarket redis get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &emails)
	if err != nil {
		log.Errorf("GetWarnEmailsByMarket json unMarshal fail,%v", err)
		return
	}
	return
}

//获取账户最后错误id
func GetHedgeContractLastFailOrderId(account, contractCode string) (id string) {
	key := define.CacheHedgeKeyContractLastFailId + account
	id, err := DefaultRedis().HGet(key, contractCode).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetHedgeContractLastFailOrderId cache fail,%v", err)
		}
		return
	}
	return
}

//设置对从合约账户列表
func SetHedgeContractLastFailOrderId(account, contractCode, orderId string) error {
	key := define.CacheHedgeKeyContractLastFailId + account
	return DefaultRedis().HSet(key, contractCode, orderId).Err()
}

//获取账户最后错误id
func GetHedgeLastWarn(field string) (content string) {
	key := define.CacheHedgeWarn
	content, err := DefaultRedis().HGet(key, field).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetHedgeContractLastFailOrderId cache fail,%v", err)
		}
		return
	}
	return
}

//设置对从合约账户列表
func SetHedgeLastWarn(filed, value string) error {
	return DefaultRedis().HSet(define.CacheHedgeWarn, filed, value).Err()
}

//设置行情上一次告警
func SetMarketLastWarn(filed, value string) error {
	return DefaultRedis().HSet(define.CacheMarketLastWarn, filed, value).Err()
}

func GetMarketLastWarn(field string) (content string) {
	key := define.CacheMarketLastWarn
	content, err := DefaultRedis().HGet(key, field).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetMarketLastWarn cache fail,%v", err)
		}
		return
	}
	return
}

func DelMarketLastWarn(field string) {
	key := define.CacheMarketLastWarn
	if DefaultRedis().HExists(key, field).Val() {
		DefaultRedis().HDel(key, field)
	}
}

func DisableHedgingCaution() bool {
	state, _ := DefaultRedis().Get(define.CacheHedgingEfficiencyAbnormalDisableState).Int()
	return state == 1
}

func TakeOkexHedgingEfficiency(account string, expectVolume, actualVolume decimal.Decimal) {
	d, err := json.Marshal([]decimal.Decimal{expectVolume, actualVolume})
	if err != nil {
		log.Error("TakeOkexHedgingEfficiency marshal failed",
			zap.String("account", account),
			zap.String("expectVolume", expectVolume.String()),
			zap.String("actualVolume", actualVolume.String()),
			zap.Error(err))
		return
	}

	key := utils.StrBuilderBySep(":", define.CacheHedgeEfficiency, account, time.Now().Format("********"))
	err = DefaultRedis().LPush(key, d).Err()
	if err != nil {
		log.Error("TakeOkexHedgingEfficiency push failed",
			zap.String("key", key),
			zap.String("expectVolume", expectVolume.String()),
			zap.String("actualVolume", actualVolume.String()),
			zap.Error(err))
		return
	}
	err = DefaultRedis().Expire(key, time.Hour).Err()
	if err != nil {
		log.Error("TakeOkexHedgingEfficiency redis expire failed",
			zap.String("key", key),
			zap.String("expectVolume", expectVolume.String()),
			zap.String("actualVolume", actualVolume.String()),
			zap.Error(err))
		return
	}
}

func GetOkexHedgingEfficiency(account string, minute int) (decimal.Decimal, error) {
	if minute <= 1 {
		minute = 1
	}

	var totalExpect, totalActual decimal.Decimal

	// 获取几个时间段内的数据,键的时间为分钟级别,如果传入count为1,那么使用当前时间和向前一个时间时间段的数据
	now := time.Now()
	for i := 0; i <= minute; i++ {
		key := utils.StrBuilderBySep(":", define.CacheHedgeEfficiency, account, now.Add(-time.Minute*time.Duration(i)).Format("********"))
		list, err := DefaultRedis().LRange(key, 0, -1).Result()
		if err != nil {
			log.Error("GetOkexHedgingEfficiency redis error",
				zap.String("key", key),
				zap.Error(err))
			return decimal.Zero, err
		}

		var d []decimal.Decimal
		for _, data := range list {
			err = json.Unmarshal([]byte(data), &d)
			if err != nil {
				log.Error("GetOkexHedgingEfficiency redis error",
					zap.String("key", key),
					zap.Error(err))
				continue
			}

			// 每条数据应该有两个元素,一个期望值,一个实际值
			if len(d) != 2 {
				continue
			}
			totalExpect = totalExpect.Add(d[0])
			totalActual = totalActual.Add(d[1])
		}
	}

	if totalExpect.IsZero() {
		return define.DecimalOne, nil
	}
	return totalActual.Div(totalExpect), nil
}

func IncrHedgingEfficiencyAbnormalCounter(platform int) int64 {
	return DefaultRedis().Incr(define.CacheHedgingEfficiencyAbnormalCountery + strconv.Itoa(platform)).Val()
}

func ClearHedgingEfficiencyAbnormalCounter(platform int) {
	DefaultRedis().Del(define.CacheHedgingEfficiencyAbnormalCountery + strconv.Itoa(platform))
}

func GetLastQueryAssetAPITimestamp(account string) int64 {
	ts, _ := DefaultRedis().Get(define.CacheHedgingLastQueryAssets + account).Int64()
	return ts
}

func SetLastQueryAssetAPITimestamp(reqID, last int64, account string) {
	err := DefaultRedis().Set(define.CacheHedgingLastQueryAssets+account, last, time.Hour).Err()
	if err != nil {
		log.Error("SetLastQueryAssetAPITimestamp cache failed", zap.Int64("reqID", reqID), zap.Error(err))
	}
}
