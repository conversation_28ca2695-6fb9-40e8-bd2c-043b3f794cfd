package cache

import (
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"go.uber.org/zap"
	"strconv"
	"time"
)

// PushAlarmRecord 从左侧插入报警
func PushAlarmRecord(am *proto.Alarm) (err error) {
	date := time.Now().Format("20060102")
	key := utils.StrBuilderBySep(":", define.CacheKeyAlarmRecord, strconv.Itoa(am.Type), date)
	b, err := json.Marshal(am)
	if err != nil {
		log.Error("PushAlarmRecord fail", zap.Error(err))
		return
	}
	c := DefaultRedis().Pipeline()
	c.Expire(key, 3*24*time.Hour)
	c.LPush(key, b)
	_, err = c.Exec()
	if err != nil {
		return
	}
	return
}

// GetAlarmFirst 获取指定类型的最新数据
func GetAlarmFirst(alarmType int) (list []proto.Alarm, err error) {
	date := time.Now().Format("20060102")
	key := utils.StrBuilderBySep(":", define.CacheKeyAlarmRecord, strconv.Itoa(alarmType), date)
	result, err := DefaultRedis().LRange(key, 0, 0).Result()
	if err != nil {
		log.Error("GetAlarmFirst ", zap.Error(err))
		return
	}
	if len(result) == 0 {
		return
	}
	for _, s := range result {
		i := new(proto.Alarm)
		err = json.Unmarshal([]byte(s), i)
		if err != nil {
			log.Error("GetAlarmFirst ")
			return
		}
		list = append(list, *i)
	}
	return
}

//获取指定日期类型记录
func listAlarms(alarmType int, dateT time.Time) (list []proto.Alarm, err error) {
	date := dateT.Format("20060102")
	key := utils.StrBuilderBySep(":", define.CacheKeyAlarmRecord, strconv.Itoa(alarmType), date)
	result, err := DefaultRedis().LRange(key, 0, -1).Result()
	if err != nil {
		log.Error("GetAlarmFirst ", zap.Error(err))
		return
	}
	if len(result) == 0 {
		return
	}
	for _, s := range result {
		i := new(proto.Alarm)
		err = json.Unmarshal([]byte(s), i)
		if err != nil {
			log.Error("GetAlarmFirst ")
			return
		}
		list = append(list, *i)
	}
	return
}

// ListAlarms 获取最近一天数据
func ListAlarms(alarmType int) (list []proto.Alarm, err error) {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	dateList := []time.Time{now, yesterday}
	for _, i := range dateList {
		l, err := listAlarms(alarmType, i)
		if err != nil {
			log.Error("listAlarms fail", zap.Error(err), zap.Int("type", alarmType), zap.Time("date", i))
			continue
		}
		list = append(list, l...)
	}
	return
}

// AddIndexSpot 增加现货指数稳定性统计
func AddIndexSpot(code, source string) (result int64, err error) {
	if code == "" {
		return
	}
	defer func() {
		if err != nil {
			result = 0
		}
	}()
	date := time.Now().Format("20060102")
	key := utils.StrBuilderBySep(":", define.CacheKeyIndexException, code, date)
	c := DefaultRedis().Pipeline()
	c.Expire(key, 3*24*time.Hour)
	result, err = c.HIncrBy(key, source, 1).Result()
	_, err = c.Exec()
	if err != nil {
		return
	}
	return
}

// ListSupportIndexSource 获取指数源
func ListSupportIndexSource() []string {
	return []string{
		"huobi", "binance", "okex", "bitfinex", "bittrex", "poloniex", "coinbase", "hitBtc", "kraken", "bitstamp", "ftx",
	}
}

// ListIndexSpot 查询指定日期交易对的指数统计
func ListIndexSpot(code string, date time.Time) (result map[string]string, err error) {
	dateKey := date.Format("20060102")
	key := utils.StrBuilderBySep(":", define.CacheKeyIndexException, code, dateKey)
	return DefaultRedis().HGetAll(key).Result()
}

// ListContractIndexSpot 获取全部合约稳定性统计
func ListContractIndexSpot(date time.Time) (list map[string]map[string]string, err error) {
	cs, err := GetAllContractList()
	if err != nil {
		log.Error("ListContractIndexSpot GetAllContractList fail", zap.Error(err))
		return
	}
	list = make(map[string]map[string]string)
	for _, v := range cs {
		if v.Delisted {
			continue
		}
		r, e := ListIndexSpot(v.ContractCode, date)
		if e != nil {
			log.Error("ListIndexSpot fail", zap.Error(e))
			continue
		}
		list[v.ContractCode] = r
	}
	return
}

func SetWarnWithExpireTime(args []string) (err error) {
	s := []string{define.CacheKeyWarnExpire}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	c := DefaultRedis().Pipeline()
	c.Set(key, time.Now().Format("20060102 15:04:05"), 0)
	_, err = c.Exec()
	if err != nil {
		log.Error("SetWarnWithExpireTime", zap.Error(err), zap.String("key", key))
		return
	}
	return
}

func GetWarnWithExpireTime(args []string) (duration time.Duration, err error) {
	s := []string{define.CacheKeyWarnExpire}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	c, err := DefaultRedis().Get(key).Result()
	if err != nil {
		log.Error("GetWarnWithExpireTime", zap.Error(err), zap.String("key", key))
		return
	}
	last, err := time.ParseInLocation("20060102 15:04:05", c, time.Local)
	if err != nil {
		log.Error("GetWarnWithExpireTime", zap.Error(err), zap.String("key", key))
		return
	}
	if last.IsZero() {
		return
	}
	duration = time.Now().Sub(last)
	return
}

func DelWarnWithExpireTime(args []string) (err error) {
	s := []string{define.CacheKeyWarnExpire}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	err = DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("DelWarnWithExpireTime", zap.Error(err), zap.String("key", key))
	}
	return
}

// SetWarnMsgForDuplicateSend 设置避免重复发送报警
func SetWarnMsgForDuplicateSend(args []string) (err error) {
	s := []string{define.CacheKeyAlarmDuplicate}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	c := DefaultRedis().Pipeline()
	c.Set(key, time.Now().Format("20060102 15:04:05"), 3*24*time.Hour)
	_, err = c.Exec()
	if err != nil {
		return
	}
	return
}

func GetWarnMsgForDuplicateSend(args []string) (duration time.Duration, err error) {
	s := []string{define.CacheKeyAlarmDuplicate}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	c, err := DefaultRedis().Get(key).Result()
	if err != nil {
		return
	}
	last, err := time.Parse("20060102 15:04:05", c)
	if err != nil {
		return
	}
	duration = time.Now().Sub(last)
	return
}

func DelWarnMsgForDuplicateSend(args []string) (err error) {
	s := []string{define.CacheKeyAlarmDuplicate}
	s = append(s, args...)
	key := utils.StrBuilderBySep(":", s...)
	err = DefaultRedis().Del(key).Err()
	return
}

func IsContractNetPosExceptions(code, side string) bool {
	key := utils.StrBuilderBySep(":", define.ContractNetPosSideException, code, side)
	result, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Error("IsContractNetPosExceptions", zap.Error(err))
		return false
	}
	return result == 1
}

func SetContractNetPosExceptions(code, side string) {
	key := utils.StrBuilderBySep(":", define.ContractNetPosSideException, code, side)
	err := DefaultRedis().Set(key, side, 0).Err()
	if err != nil {
		log.Error("SetContractNetPosExceptions", zap.Error(err))
		return
	}
}

func DelContractNetPosExceptions(code, side string) {
	key := utils.StrBuilderBySep(":", define.ContractNetPosSideException, code, side)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("DelContractNetPosExceptions", zap.Error(err))
		return
	}
}
