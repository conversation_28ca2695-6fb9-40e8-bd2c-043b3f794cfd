package cache

import (
	"fmt"
	"time"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

func StagingExchangePreOrder(order *proto.ExchangePreOrder) error {
	b, err := json.Marshal(order)
	if err != nil {
		return err
	}

	return DefaultRedis().SetNX(define.CacheKeyExchangePreOrder+order.PreOrderID, b, define.ExchangePreOrderExpireTime).Err()
}

func GetExchangePreOrder(preOrderID string) (*proto.ExchangePreOrder, error) {
	b, err := DefaultRedis().Get(define.CacheKeyExchangePreOrder + preOrderID).Bytes()
	if err != nil {
		return nil, err
	}

	var po proto.ExchangePreOrder
	err = json.Unmarshal(b, &po)
	return &po, err
}

func AddExchangeOrderTask(orderID string) {
	err := DefaultRedis().RPush(define.CacheKeyExchangeOrderTask, orderID).Err()
	if err != nil {
		log.Error("AddExchangeOrderTask rpush fail", zap.String("orderID", orderID), zap.Error(err))
	}
}

func DelExchangeOrderTask(orderID string) {
	err := DefaultRedis().LRem(define.CacheKeyExchangeOrderTask, 1, orderID).Err()
	if err != nil {
		log.Error("AddExchangeOrderTask lrem fail", zap.String("orderID", orderID), zap.Error(err))
	}
}

func GetExchangeOrderTasks() ([]string, error) {
	return DefaultRedis().LRange(define.CacheKeyExchangeOrderTask, 0, -1).Result()
}

func AddExchangeHedgeOrderTask(orderID string) {
	err := DefaultRedis().RPush(define.CacheKeyExchangeHedgeOrderTask, orderID).Err()
	if err != nil {
		log.Error("AddExchangeHedgeOrderTask rpush fail", zap.String("orderID", orderID), zap.Error(err))
	}
}

func DelExchangeHedgeOrderTask(orderID string) {
	err := DefaultRedis().LRem(define.CacheKeyExchangeHedgeOrderTask, 1, orderID).Err()
	if err != nil {
		log.Error("DelExchangeHedgeOrderTask lrem fail", zap.String("orderID", orderID), zap.Error(err))
	}
}

func GetExchangeHedgeOrderTasks() ([]string, error) {
	return DefaultRedis().LRange(define.CacheKeyExchangeHedgeOrderTask, 0, -1).Result()
}

func GetUserDailyExchangeUsedVolume(userID int64, basicCoin string) (decimal.Decimal, error) {
	key := fmt.Sprintf(define.CacheKeyExchangeUserUsed, time.Now().Format(define.TimeFormatDayKey), userID)
	v, err := DefaultRedis().HGet(key, basicCoin).Result()
	if err == redis.Nil {
		err = nil
	}
	// 将取出的数小数点左移取真实值
	return nums.NewFromString(v).Shift(-define.DecimalShiftDigit), err
}

func SetUserDailyExchangeUsedVolume(date time.Time, userID int64, basicCoin string, volume decimal.Decimal) error {
	key := fmt.Sprintf(define.CacheKeyExchangeUserUsed, date.Format(define.TimeFormatDayKey), userID)
	err := DefaultRedis().HIncrBy(key, basicCoin, volume.Shift(define.DecimalShiftDigit).IntPart()).Err()
	if err != nil {
		return err
	}
	return DefaultRedis().Expire(key, time.Hour*24).Err()
}

//设置兑币对冲源
func SetExchangeHedgeSource(source define.ExchangeHedgeSource) {
	err := DefaultRedis().Set(define.CacheKeyExchangeHedgeSource, nums.Int2String(int(source)), 0*time.Second).Err()
	if err != nil {
		log.Error("SetExchangeHedgeSource set err", zap.Error(err))
		return
	}
}

func GetExchangeHedgeSource() (source define.ExchangeHedgeSource) {
	id, err := DefaultRedis().Get(define.CacheKeyExchangeHedgeSource).Int()
	if err != nil {
		if err == redis.Nil {
			return define.ExchangeHedgeSourceBinance
		}
		log.Error("SetExchangeHedgeSource set err", zap.Error(err))
		return
	}
	source = define.ExchangeHedgeSource(id)
	return
}

func ListExchangeHedgeConfig() (list []proto.ExchangeHedgeConfig, err error) {
	data, err := DefaultRedis().Get(define.CacheKeyExchangeHedgeConfig).Bytes()
	if err != nil {
		if err == redis.Nil {
			return
		}
		log.Error("ListExchangeHedgeConfig set err", zap.Error(err))
		return
	}
	err = json.Unmarshal(data, &list)
	if err != nil {
		log.Error("ListExchangeHedgeConfig Unmarshal fail", zap.Error(err))
		return
	}
	return
}

func SetExchangeHedgeConfig(list []proto.ExchangeHedgeConfig) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Error("SetExchangeHedgeConfig fail", zap.Error(err))
		return
	}
	err = DefaultRedis().Set(define.CacheKeyExchangeHedgeConfig, b, 1*time.Minute).Err()
	if err != nil {
		log.Error("SetExchangeHedgeConfig error", zap.Error(err))
		return
	}
	return
}
