// Package cache /*
package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/utils"
	"strconv"
	"time"
)

func SetMatchMsgQueue(id int64, msg []byte) {
	DefaultRedis().HSet(define.CacheKeyMatchMsgQueue, convert.Int64String(id), msg)
}

func GetMatchMsg(id int64) (msg string) {
	msg, err := DefaultRedis().HGet(define.CacheKeyMatchMsgQueue, convert.Int64String(id)).Result()
	if err != nil {
		log.Errorf("GetMatchMsg get fail,%v", err)
		return
	}
	return
}

//获取缓存中消息
func GetMatchMsgs() (list map[string]string) {
	list, err := DefaultRedis().HGetAll(define.CacheKeyMatchMsgQueue).Result()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	return
}

//删除缓存中消息
func RemoveMatchMsg(msgId int64) (success bool) {
	_, err := DefaultRedis().HDel(define.CacheKeyMatchMsgQueue, convert.Int64String(msgId)).Result()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	return true
}

func SetNcr(uid int64, ncr string) error {
	key := "ncr:" + convert.Int64String(uid) + ":" + ncr
	_, err := DefaultRedis().Set(key, 1, 2*time.Minute).Result()
	return err
}

func DelNcr(uid int64, ncr string) {
	key := "ncr:" + convert.Int64String(uid) + ":" + ncr
	DefaultRedis().Del(key)
}

func IsNcrExist(uid int64, ncr string) bool {
	key := "ncr:" + convert.Int64String(uid) + ":" + ncr
	i, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Errorf("IsNcrExist fail,%v", err)
		return false
	}
	return i == 1
}

/*** 做市商持仓统计 start ***/

func SetContractRobotPos(code string, side string, amount int) {
	key := utils.StrBuilder(define.CacheKeyRobotMarketPosition, code)
	e := DefaultRedis().HSet(key, side, strconv.Itoa(amount)).Err()
	if e != nil {
		log.Errorf("SetContractRobotPos fail,%v")
		return
	}
}

func IncrContractRobotPos(code string, side string, amount int) {
	key := utils.StrBuilder(define.CacheKeyRobotMarketPosition, code)
	e := DefaultRedis().HIncrBy(key, side, int64(amount)).Err()
	if e != nil {
		log.Errorf("IncrContractRobotPos fail,%v")
		return
	}
}

func GetContractRobotSidePos(code, side string) (amount int) {
	key := utils.StrBuilder(define.CacheKeyRobotMarketPosition, code)
	amount, err := DefaultRedis().HGet(key, side).Int()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	return
}

func GetContractRobotPos(code string) map[string]int {
	key := utils.StrBuilder(define.CacheKeyRobotMarketPosition, code)
	b := DefaultRedis().HGetAll(key).Val()
	r := make(map[string]int)
	for side, a := range b {
		r[side] = convert.String2Int(a)
	}

	return r
}

func RemoveContractRobotPos(code string) {
	key := utils.StrBuilder(define.CacheKeyRobotMarketPosition, code)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("RemoveContractRobotPos get fail,%v", err)
		return
	}
	return
}

/*** 做市商持仓统计 end ***/
