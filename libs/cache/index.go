/*
@Time : 3/6/20 2:48 下午
<AUTHOR> mocha
@File : index
*/
package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"
	"go.uber.org/zap"
	"time"
)

//设置临时合约价格指数
func SetTempContractPriceIndex(history *proto.IndexHistory) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	key := define.CacheKeyTempContractIndex + history.ContractCode
	DefaultRedis().Set(key, b, 1*time.Minute)
}

//获取临时合约价格指数
func GetTempContractPriceIndexIgnoreErr(contractCode string) (index *proto.IndexHistory) {
	temp := new(proto.IndexHistory)
	key := define.CacheKeyTempContractIndex + contractCode
	b, err := DefaultRedis().Get(key).Bytes()
	if err != nil {
		//log.Errorf("GetTempContractPriceIndexIgnoreErr get fail,%v,contractCode:%v,", err, contractCode)
		return
	}
	err = json.Unmarshal(b, temp)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	index = temp
	return
}

//设置合约成交价
func SetContractPriceIndex(history *proto.IndexHistory) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractIndex, history.ContractCode, b)
}

//获取合约价格指数
func GetContractPriceIndexIgnoreErr(contractCode string) (index *proto.IndexHistory) {
	index = GetContractPriceIndex(contractCode)
	if index == nil {
		index = new(proto.IndexHistory)
	}
	return
}

//设置合约价格和原始数据入缓存
func SetContractPriceIndexAndBuySells(d *proto.IndexPriceAndSells) {
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyContractIndexDetail, d.Index.ContractCode, b).Err()
	if err != nil {
		log.Errorf("SetContractPriceIndexAndBuySells hset fail,%v", err)
		return
	}
}

//获取合约价格及计算详情
func GetContractPriceIndexAndBuySells(contractCode string) (d *proto.IndexPriceAndSells, err error) {
	index := new(proto.IndexPriceAndSells)
	b, err := DefaultRedis().HGet(define.CacheKeyContractIndexDetail, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndexAndBuySells get fail,%v,contractCode:%v,", err, contractCode)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("GetContractPriceIndexAndBuySells json unmarshal fail,%v", err)
		return
	}
	d = index
	return
}

//获取合约成交价
func GetContractPriceIndex(contractCode string) *proto.IndexHistory {
	// 同一服务的同一时间内执行,合并查询
	index, _ := singleflight.MergeDo[*proto.IndexHistory](define.CacheKeyContractIndex+contractCode, func() (any, error) {
		index := new(proto.IndexHistory)
		b, err := DefaultRedis().HGet(define.CacheKeyContractIndex, contractCode).Bytes()
		if err != nil {
			log.Infof("GetContractPriceIndex get fail,%v,contractCode:%v,", err, contractCode)
			return index, nil
		}
		err = json.Unmarshal(b, index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return index, nil
		}
		return index, nil
	})
	return index
}

// 获取全部合约最新成交价格
func GetAllPriceIndex() map[string]proto.IndexHistory {
	// 同一服务的同一时间内执行,合并查询
	pi, _ := singleflight.MergeDo[map[string]proto.IndexHistory](define.CacheKeyContractIndex, func() (any, error) {
		mp := make(map[string]proto.IndexHistory)
		result, err := DefaultRedis().HGetAll(define.CacheKeyContractIndex).Result()
		if err != nil {
			log.Error("GetAllPriceIndex redis error", zap.Error(err))
			return mp, nil
		}

		var index proto.IndexHistory
		for key, val := range result {
			err = json.Unmarshal(convert.Str2Bytes(val), &index)
			if err != nil {
				log.Error("GetAllPriceIndex json unmarshal error", zap.Error(err))
				continue
			}
			mp[key] = index
		}
		return mp, nil
	})

	return pi
}

////获取标记价格
//func GetMarketPrice(contractCode string) (index *proto.MarkPrice) {
//	index = new(proto.MarkPrice)
//	b, err := DefaultRedis().HGet(define.CacheKeyMarketPrice, contractCode).Bytes()
//	if err != nil {
//		log.Warnf("GetMarketPrice get fail,contract:%v,%v", contractCode, err)
//		return
//	}
//	err = json.Unmarshal(b, index)
//	if err != nil {
//		log.Errorf("json unmarshal fail,%v", err)
//		return
//	}
//	return
//}

//// 获取所有标记价格
//func GetAllMarketPrice() (mp map[string]proto.MarkPrice) {
//	var price proto.MarkPrice
//	mp = make(map[string]proto.MarkPrice)
//	sli, err := DefaultRedis().HVals(define.CacheKeyMarketPrice).Result()
//	if err != nil {
//		log.Error("GetAllMarketPrice get fail", zap.Error(err))
//		return
//	}
//
//	for i := range sli {
//		err = json.Unmarshal(convert.Str2Bytes(sli[i]), price)
//		if err != nil {
//			log.Error("GetAllMarketPrice json unmarshal fail", zap.Error(err))
//			return
//		}
//		mp[price.ContractCode] = price
//	}
//	return
//}

//设置合约最新标记价格
func SetContractComplexTradePrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractComplexPrice, history.ContractCode, b)
}

//获取合约最新标记价格
func GetContractComplexTradePrice(contractCode string) *proto.ComplexPrice {
	// 同一服务的同一时间内执行,合并查询
	index, _ := singleflight.MergeDo[*proto.ComplexPrice](define.CacheKeyContractComplexPrice+contractCode, func() (any, error) {
		idx := new(proto.ComplexPrice)
		b, err := DefaultRedis().HGet(define.CacheKeyContractComplexPrice, contractCode).Bytes()
		if err != nil {
			log.Warnf("GetContractComplexTradePrice get fail,contract:%v,%v", contractCode, err)
			return idx, nil
		}
		err = json.Unmarshal(b, idx)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return idx, nil
		}
		return idx, nil
	})
	return index
}

func GetAllContractComplexPrice() map[string]proto.ComplexPrice {
	// 同一服务的同一时间内执行,合并查询
	mp, _ := singleflight.MergeDo[map[string]proto.ComplexPrice](define.CacheKeyContractComplexPrice, func() (any, error) {
		m := make(map[string]proto.ComplexPrice)
		list, err := DefaultRedis().HVals(define.CacheKeyContractComplexPrice).Result()
		if err != nil {
			log.Warnf("GetContractComplexTradePrice get fail%v", err)
			return m, nil
		}
		for _, v := range list {
			index := new(proto.ComplexPrice)
			err = json.Unmarshal([]byte(v), index)
			if err != nil {
				log.Errorf("json unmarshal fail,%v", err)
				continue
			}
			m[index.ContractCode] = *index
		}
		return m, nil
	})
	return mp
}

//设置合约现货指数价格
func SetContractSpotIndexPrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("SetContractSpotIndexPrice json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractSpotIndexPrice, history.ContractCode, b)
}

//获取合约现货指数价格
func GetContractSpotIndexPrice(contractCode string) *proto.ComplexPrice {
	// 同一服务的同一时间内执行,合并查询
	cp, _ := singleflight.MergeDo[*proto.ComplexPrice](define.CacheKeyContractSpotIndexPrice+contractCode, func() (any, error) {
		index := new(proto.ComplexPrice)
		b, err := DefaultRedis().HGet(define.CacheKeyContractSpotIndexPrice, contractCode).Bytes()
		if err != nil {
			log.Warnf("GetContractSpotIndexPrice get fail,contract:%v,%v", contractCode, err)
			return index, nil
		}
		err = json.Unmarshal(b, index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return index, nil
		}
		return index, nil
	})
	return cp
}

//获取所有合约现货指数价格
func GetAllContractSpotIndexPrice() map[string]proto.ComplexPrice {
	// 同一服务的同一时间内执行,合并查询
	cm, _ := singleflight.MergeDo[map[string]proto.ComplexPrice](define.CacheKeyContractSpotIndexPrice, func() (any, error) {
		m := make(map[string]proto.ComplexPrice)
		list, err := DefaultRedis().HVals(define.CacheKeyContractSpotIndexPrice).Result()
		if err != nil {
			log.Warnf("GetAllContractSpotIndexPrice get fail%v", err)
			return m, nil
		}
		for _, v := range list {
			index := new(proto.ComplexPrice)
			err = json.Unmarshal([]byte(v), index)
			if err != nil {
				log.Errorf("json unmarshal fail,%v", err)
				continue
			}
			m[index.ContractCode] = *index
		}
		return m, nil
	})
	return cm
}

//设置资金费率
func SetFundingRate(history *proto.CPriceRate) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyFundingRate, history.ContractCode, b)
}

//获取资金费率
func GetFundingRate(contractCode string) *proto.CPriceRate {
	// 同一服务的同一时间内执行,合并查询
	index, _ := singleflight.MergeDo[*proto.CPriceRate](define.CacheKeyFundingRate+contractCode, func() (any, error) {
		idx := new(proto.CPriceRate)
		b, err := DefaultRedis().HGet(define.CacheKeyFundingRate, contractCode).Bytes()
		if err != nil {
			log.Errorf("GetFundingRate get fail,contract:%v,%v", contractCode, err)
			return idx, nil
		}
		err = json.Unmarshal(b, idx)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return idx, nil
		}
		return idx, nil
	})
	return index
}

//获取资金费率map
func GetFundingRateMap() map[string]proto.CPriceRate {
	// 同一服务的同一时间内执行,合并查询
	index, _ := singleflight.MergeDo[map[string]proto.CPriceRate](define.CacheKeyFundingRate, func() (any, error) {
		pr := make(map[string]proto.CPriceRate)
		b, err := DefaultRedis().HGetAll(define.CacheKeyFundingRate).Result()
		if err != nil {
			log.Errorf("GetFundingRateMap get fail,%v", err)
			return pr, nil
		}
		for k, v := range b {
			crate := new(proto.CPriceRate)
			err = json.Unmarshal([]byte(v), crate)
			if err != nil {
				log.Errorf("GetFundingRateMap json unmarshal fail,%v", err)
				continue
			}
			pr[k] = *crate
		}
		return pr, nil
	})
	return index
}

//设置上次资金费率
func SetLastCalFundingRate(history *proto.CPriceRate) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyFundingRateLast, history.ContractCode, b).Err()
	if err != nil {
		log.Errorf("设置结算资金费率出错;%v", err)
	}
}

//获取上一次资金费率
func GetLastCalFundingRate(contractCode string) (index *proto.CPriceRate) {
	index = new(proto.CPriceRate)
	b, err := DefaultRedis().HGet(define.CacheKeyFundingRateLast, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetFundingRate get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取上次资金费率map
func GetLastCalFundingRateMap() (index map[string]proto.CPriceRate) {
	index = make(map[string]proto.CPriceRate)
	b, err := DefaultRedis().HGetAll(define.CacheKeyFundingRateLast).Result()
	if err != nil {
		log.Errorf("GetFundingRateMap get fail,%v", err)
		return
	}
	for k, v := range b {
		crate := new(proto.CPriceRate)
		err = json.Unmarshal([]byte(v), crate)
		if err != nil {
			log.Errorf("GetFundingRateMap json unmarshal fail,%v", err)
			continue
		}
		index[k] = *crate
	}
	return
}

//设置合约铺单基准价格（采用三大所辅助使用现货指数）
func SetContractDepthBasePrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractDepthBasePrice, history.ContractCode, b)
}

//获取合约铺单基准价格（采用三大所辅助使用现货指数)
func GetContractDepthBasePrice(contractCode string) (index *proto.ComplexPrice) {
	index = new(proto.ComplexPrice)
	b, err := DefaultRedis().HGet(define.CacheKeyContractDepthBasePrice, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractComplexTradePrice get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

// GetAllContractDepthBasePrice 获取所有合约的铺单基准价
func GetAllContractDepthBasePrice() (cmap map[string]proto.ComplexPrice) {
	cmap = make(map[string]proto.ComplexPrice)
	m, err := DefaultRedis().HGetAll(define.CacheKeyContractDepthBasePrice).Result()
	if err != nil {
		log.Warn("GetAllContractDepthBasePrice fail", zap.Error(err))
		return
	}
	for code, s := range m {
		index := new(proto.ComplexPrice)
		err = json.Unmarshal([]byte(s), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return
		}
		cmap[code] = *index
	}
	return
}

//设置合约上次基准价格
func SetContractCurDepthBasePrice(history *proto.ComplexPrice) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractCurDepthPrice, history.ContractCode, b)
}

//获取合约上次铺单基准价格
func GetContractCurDepthBasePrice(contractCode string) (index *proto.ComplexPrice) {
	index = new(proto.ComplexPrice)
	b, err := DefaultRedis().HGet(define.CacheKeyContractCurDepthPrice, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractComplexTradePrice get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取撮合成交信息
// GetContractTradeMerge 获取合约成交综合信息
func GetContractTradeMerge(code string) (d *proto.DealInfo, err error) {
	b, err := DefaultRedis().HGet(define.CacheKeyTradePrice, code).Bytes()
	if err != nil {
		log.Errorf("GetContractTradeMerge fail,%v", err)
		return
	}
	d = new(proto.DealInfo)
	err = json.Unmarshal(b, d)
	if err != nil {
		return
	}
	return
}

// SetContractTradeMerge 设置合约价格和原始数据入缓存
func SetContractTradeMerge(d *proto.DealInfo) {
	if d == nil {
		return
	}
	in, _ := GetContractTradeMerge(d.Code)
	if in != nil {
		d.LastDealPrice = in.LastDealPrice
		if !in.CreateTime.IsZero() && d.CreateTime.Before(in.CreateTime) {
			log.Info("设置合约综合成交数据，数据早于最新数据，本次不做处理", zap.Any("数据", d), zap.Any("缓存最新", in))
			return
		}
	}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyTradePrice, d.Code, b).Err()
	if err != nil {
		log.Errorf("SetContractTradeMerge hset fail,%v", err)
		return
	}
}

// GetAllTradeMerge 获取全部合约最新成交价格
func GetAllTradeMerge() map[string]proto.DealInfo {
	mp := make(map[string]proto.DealInfo)
	result, err := DefaultRedis().HGetAll(define.CacheKeyTradePrice).Result()
	if err != nil {
		log.Error("GetAllTradeMerge redis error", zap.Error(err))
		return mp
	}

	var index proto.DealInfo
	for key, val := range result {
		err = json.Unmarshal(convert.Str2Bytes(val), &index)
		if err != nil {
			log.Error("GetAllTradeMerge json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = index
	}
	return mp
}

//设置铺单简单数据
func SetContractDepthInfo(history *proto.DepthSimpleInfo) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("SetContractDepthInfo json marshal fail,contract:%v,%v", history.ContractCode, err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyContractDepthSimpleInfo, history.ContractCode, b)
}

//获取铺单简单数据
func GetContractDepthInfo(contractCode string) (index *proto.DepthSimpleInfo) {
	index = new(proto.DepthSimpleInfo)
	b, err := DefaultRedis().HGet(define.CacheKeyContractDepthSimpleInfo, contractCode).Bytes()
	if err != nil {
		log.Warnf("GetContractDepthInfo get fail,contract:%v,%v", contractCode, err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取所有合约铺单简易数据
func GetAllContractDepthInfo() (m map[string]proto.DepthSimpleInfo) {
	m = make(map[string]proto.DepthSimpleInfo)
	list, err := DefaultRedis().HVals(define.CacheKeyContractDepthSimpleInfo).Result()
	if err != nil {
		log.Warnf("GetAllContractDepthInfo get fail%v", err)
		return
	}
	for _, v := range list {
		index := new(proto.DepthSimpleInfo)
		err = json.Unmarshal([]byte(v), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			continue
		}
		m[index.ContractCode] = *index
	}
	return
}
