/*
@Time : 3/6/20 5:43 下午
<AUTHOR> mocha
@File : depth
*/
package cache

import (
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"strconv"
	"time"
)

//设置原始深度
func SetContractOriginDepth(history *proto.DepthContainer) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v,histroy:%+v", err, *history)
		return
	}
	_, err = DefaultRedis().HSet(define.CacheKeyOriginDepth, history.ContractCode, b).Result()
	if err != nil {
		log.Errorf("SetContractOriginDepth fail,%v", err)
		return
	}
}

//获取原始深度
func GetContractOriginDepth(contractCode string) (index *proto.DepthContainer) {
	index = new(proto.DepthContainer)
	b, err := DefaultRedis().HGet(define.CacheKeyOriginDepth, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取原始深度
func GetContractOriginDepthForPub(contractCode string) (index *proto.DepthContainerPub) {
	index = new(proto.DepthContainerPub)
	b, err := DefaultRedis().HGet(define.CacheKeyOriginDepth, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractOriginDepthForPub get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//设置原始深度
func SetContractDepth(history *proto.DepthContainer) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	key := define.CacheKeyDepth + nums.Int32String(history.Level)
	if history.Level == 0 {
		SetContractOriginDepth(history)
		return
	}
	DefaultRedis().HSet(key, history.ContractCode, b)
}

//获取原始深度
func GetContractTradeLevelDepth(contractCode string) (index *proto.DepthContainer) {
	index = new(proto.DepthContainer)
	b, err := DefaultRedis().HGet(define.CacheKeyOriginDepth, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractTradeLevelDepth get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//设置原始深度
func SetContractTradeLevelDepth(history *proto.DepthContainer) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("SetContractTradeLevelDepth json marshal fail,%v", err)
		return
	}
	key := define.CacheKeyDepth + nums.Int32String(history.Level)
	if history.Level == 0 {
		SetContractOriginDepth(history)
		return
	}
	DefaultRedis().HSet(key, history.ContractCode, b)
}

//获取深度
func GetContractDepth(contractCode string, level int) (index *proto.DepthContainer) {
	index = new(proto.DepthContainer)
	key := define.CacheKeyDepth + strconv.Itoa(level)
	if level == 0 {
		return GetContractOriginDepth(contractCode)
	}
	b, err := DefaultRedis().HGet(key, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}

//获取指定高度深度
func GetContractDepthByHeight(contractCode string, level int, height int) (depth *proto.DepthContainer) {
	if height == 0 {
		height = define.DepthHeight
	}
	depth = new(proto.DepthContainer)
	key := define.CacheKeyDepth + strconv.Itoa(level)
	if level == 0 {
		depth = GetContractOriginDepth(contractCode)
	} else {
		b, err := DefaultRedis().HGet(key, contractCode).Bytes()
		if err != nil {
			log.Errorf("GetContractPriceIndex get fail,%v", err)
			return
		}
		err = json.Unmarshal(b, depth)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			return
		}
	}

	if depth != nil {
		depth.Buy = depth.Buy[:getMinNum(height, len(depth.Buy))]
		depth.Sell = depth.Sell[:getMinNum(height, len(depth.Sell))]
	}
	return
}

func getMinNum(a, b int) int {
	if a > b {
		return b
	} else {
		return a
	}
}

//设置合约对敲数量系数
func SetContractBucketAmountFactor(code string, amount decimal.Decimal) {
	key := utils.StrBuilder(define.CacheContractBucketAmountFactor, ":", code)
	err := DefaultRedis().Set(key, amount.String(), 24*time.Hour).Err()
	if err != nil {
		log.Errorf("SetContractBucketAmountFactor fail,%v", err)
		return
	}
}

//获取合约对敲数量系数
func GetContractBucketAmountFactor(code string) (amount decimal.Decimal, err error) {
	var s string
	key := utils.StrBuilder(define.CacheContractBucketAmountFactor, ":", code)
	s, err = DefaultRedis().Get(key).Result()
	if err != nil {
		log.Errorf("GetContractBucketAmountFactor fail,%v", err)
		return
	}
	amount = nums.NewFromString(s)
	return
}

//设置合约不同市场24小时成交量
func SetContractMarket24Volume(c *proto.Market24Volume) (err error) {
	key := utils.StrBuilder(define.CacheContractMarket24, c.Symbol)
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractMarket24Volume marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(key, c.Source, b).Err()
	if err != nil {
		log.Errorf("SetContractMarket24Volume fail,%v", err)
	}
	return
}

// 获取合约不同市场价格
func GeContractMarket24Volume(code string) (mt map[string]proto.Market24Volume) {
	key := utils.StrBuilder(define.CacheContractMarket24, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeContractMarket24Volume redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.Market24Volume)
	for source, s := range result {
		m := new(proto.Market24Volume)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeContractMarket24Volume json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

//设置合约综合成交量，2小时失效
func SetContractValidMarket24Volume(code string, amount decimal.Decimal) {
	key := utils.StrBuilder(define.CacheContractValid24Volume, ":", code)
	err := DefaultRedis().Set(key, amount.String(), 2*time.Hour).Err()
	if err != nil {
		log.Errorf("SetContractValidMarket24Volume fail,%v", err)
		return
	}
}

//获取合约综合成交量，2小时失效
func GetContractValidMarket24Volume(code string) (amount decimal.Decimal, err error) {
	var s string
	key := utils.StrBuilder(define.CacheContractValid24Volume, ":", code)
	s, err = DefaultRedis().Get(key).Result()
	if err != nil {
		log.Errorf("GetContractMarket24Volume fail,%v", err)
		return
	}
	amount = nums.NewFromString(s)
	return
}

//设置合约综合成交量，1分钟
func SetContractValidMinuteVolume(code string, amount decimal.Decimal) {
	key := utils.StrBuilder(define.CacheContractValidMinuteVolume, ":", code)
	err := DefaultRedis().Set(key, amount.String(), time.Minute).Err()
	if err != nil {
		log.Errorf("SetContractValidMinuteVolume fail,%v", err)
		return
	}
}

//获取合约综合成交量，1分钟
func GetContractValidMinuteVolume(code string) (amount decimal.Decimal, err error) {
	var s string
	key := utils.StrBuilder(define.CacheContractValidMinuteVolume, ":", code)
	s, err = DefaultRedis().Get(key).Result()
	if err != nil {
		log.Errorf("GetContractValidMinuteVolume fail,%v", err)
		return
	}
	amount = nums.NewFromString(s)
	return
}

//设置合约小时随机数
func SetContractHourFactor(code string, hf *proto.HourFactor) {
	date := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheContractHourRandFactor, ":", date, ":", code)
	bf, err := json.Marshal(hf)
	if err != nil {
		return
	}
	err = DefaultRedis().Set(key, bf, 2*time.Hour).Err()
	if err != nil {
		log.Errorf("SetContractValidMinuteVolume fail,%v", err)
		return
	}
}

//获取合约小时随机数
func GetContractHourFactor(code string) (hf *proto.HourFactor, err error) {
	var s []byte
	date := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheContractHourRandFactor, ":", date, ":", code)
	s, err = DefaultRedis().Get(key).Bytes()
	if err != nil {
		log.Errorf("GetContractValidMinuteVolume fail,%v", err)
		return
	}
	h := new(proto.HourFactor)
	err = json.Unmarshal(s, h)
	if err != nil {
		return nil, err
	}
	hf = h
	return
}

//获取指定合约价格全局
func ListContractDistance(code string, start, end int64) (order []proto.ContractDistance) {
	key := utils.StrBuilder(define.ContractSpotIndexDistance, ":", code)
	data, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("ListContractDistance lrange fail,%v", err)
		}
		return
	}
	for _, datum := range data {
		o := new(proto.ContractDistance)
		err = json.Unmarshal([]byte(datum), o)
		if err != nil {
			log.Errorf("ListContractDistance json unmarshal fail,%v", err)
			return
		}
		order = append(order, *o)
	}
	return
}

//增加合约价格全局
func AddContractDistance(order *proto.ContractDistance) {
	if order == nil {
		log.Infof("order AddContractDistance is nil")
		return
	}
	key := utils.StrBuilder(define.ContractSpotIndexDistance, ":", order.Code)
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("AddContractDistance json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().LPush(key, b).Err()
	if err != nil {
		log.Errorf("AddContractDistance lpush fail,%v", err)
		return
	}
	err = DefaultRedis().LTrim(key, 0, 100).Err()
	if err != nil {
		log.Errorf("AddContractDistance ltrim fail,%v", err)
		return
	}
}

//设置可成交档位原始深度
func SetContractTradeDepth(history *proto.DepthTradeContainer) {
	b, err := json.Marshal(*history)
	if err != nil {
		log.Errorf("json marshal fail,%v,histroy:%+v", err, *history)
		return
	}
	_, err = DefaultRedis().HSet(define.CacheKeyTradeDepth, history.ContractCode, b).Result()
	if err != nil {
		log.Errorf("SetContractTradeDepth fail,%v", err)
		return
	}
}

//获取可成交档位原始深度
func GetContractTradeDepth(contractCode string) (index *proto.DepthTradeContainer) {
	index = new(proto.DepthTradeContainer)
	b, err := DefaultRedis().HGet(define.CacheKeyTradeDepth, contractCode).Bytes()
	if err != nil {
		log.Errorf("GetContractPriceIndex get fail,%v", err)
		return
	}
	err = json.Unmarshal(b, index)
	if err != nil {
		log.Errorf("json unmarshal fail,%v", err)
		return
	}
	return
}
