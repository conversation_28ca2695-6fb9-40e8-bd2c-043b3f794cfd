package cache

import (
	"strconv"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

func SetSysValue(list []proto.SystemValue) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		return
	}
	err = DefaultRedis().Set(define.CacheKeySysConfig, b, 0).Err()
	return
}

func ListSysValue() map[string]string {
	// 同一服务的同一时间内执行,合并查询
	sv, _ := singleflight.MergeDo[map[string]string](define.CacheKeySysConfig, func() (any, error) {
		b, err := DefaultRedis().Get(define.CacheKeySysConfig).Bytes()
		if err != nil {
			return nil, err
		}

		var list []proto.SystemValue
		err = json.Unmarshal(b, &list)
		if err != nil {
			return nil, err
		}

		m := make(map[string]string, len(list))
		for _, v := range list {
			m[v.Key] = v.Value
		}

		return m, nil
	})

	return sv
}

func GetSystemTradeConf(platformID int) *proto.SystemTradeConfig {
	// 同一服务的同一时间内执行,合并查询
	cfg, _ := singleflight.MergeDo[*proto.SystemTradeConfig](define.CacheKeySysTradeConf+strconv.Itoa(platformID), func() (any, error) {
		var tc proto.SystemTradeConfig
		b, err := DefaultRedis().HGet(define.CacheKeySysTradeConf, strconv.Itoa(platformID)).Bytes()
		if err != nil {
			if err != redis.Nil {
				log.Error("GetSystemTradeConf cache error", zap.Int("platform", platformID), zap.Error(err))
			}
			return &tc, nil
		}
		err = json.Unmarshal(b, &tc)
		if err != nil {
			log.Error("GetSystemTradeConf unmarshal failed", zap.Int("platform", platformID), zap.Error(err))
		}
		return &tc, nil
	})

	return cfg
}

func GetUserQuotaConfig(platformID int) (*proto.UserQuotaConfig, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.UserQuotaConfig](define.CacheKeySysUserQuotaConf+strconv.Itoa(platformID), func() (any, error) {
		var cfg proto.UserQuotaConfig
		res, err := DefaultRedis().HGet(define.CacheKeySysUserQuotaConf, strconv.Itoa(platformID)).Bytes()
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(res, &cfg)
		return &cfg, err
	})
}
