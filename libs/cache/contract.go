package cache

import (
	"sort"
	"strings"
	"sync"
	"time"

	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/singleflight"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

var contractSetLock sync.Mutex

func SaveContractList(list []proto.Contract, force bool) {
	var counter int
flag:
	if !contractSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer contractSetLock.Unlock()

	codeKeys := make(map[string]define.Placeholder, len(list))
	for i := range list {
		codeKeys[list[i].ContractCode] = define.PlaceholderEntity
	}

	var delCodes []string
	currCodeKeys := DefaultRedis().HKeys(define.CacheKeyContractList).Val()
	for i := range currCodeKeys {
		if _, ok := codeKeys[currCodeKeys[i]]; !ok {
			delCodes = append(delCodes, currCodeKeys[i])
		}
	}

	pp := DefaultRedis().TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveContractList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyContractList, strings.ToUpper(list[i].ContractCode), data)
	}
	if len(delCodes) > 0 {
		pp.HDel(define.CacheKeyContractList, delCodes...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveContractList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllContractList() ([]proto.Contract, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[[]proto.Contract](define.CacheKeyContractList, func() (any, error) {
		encode, err := DefaultRedis().HVals(define.CacheKeyContractList).Result()
		if err != nil {
			log.Error("GetAllContractList redis error", zap.Error(err))
			return nil, err
		}

		var contract proto.Contract
		list := make([]proto.Contract, 0, len(encode))
		for i := range encode {
			//log.Infof("print:%v",string(encode[i]))
			err = json.Unmarshal(convert.Str2Bytes(encode[i]), &contract)
			if err != nil {
				log.Error("GetAllContractList json unmarshal error", zap.Error(err))
				return nil, err
			}
			list = append(list, contract)
		}

		sort.SliceStable(list, func(i, j int) bool {
			return list[i].OrderBy > list[j].OrderBy
		})

		return list, nil
	})
}

func GetAllContractMap() (map[string]proto.Contract, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.Contract](define.CacheKeyContractList, func() (any, error) {
		encode, err := DefaultRedis().HGetAll(define.CacheKeyContractList).Result()
		if err != nil {
			log.Error("GetAllContractMap redis error", zap.Error(err))
			return nil, err
		}

		var contract proto.Contract
		mp := make(map[string]proto.Contract, len(encode))
		for key, val := range encode {
			err = json.Unmarshal(convert.Str2Bytes(val), &contract)
			if err != nil {
				log.Error("GetAllContractMap json unmarshal error", zap.String("field", key), zap.Error(err))
				return nil, err
			}
			mp[key] = contract
		}
		return mp, nil
	})
}

func GetContractInfo(contractCode string) (*proto.Contract, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.Contract](define.CacheKeyContractList+contractCode, func() (any, error) {
		encode, err := DefaultRedis().HGet(define.CacheKeyContractList, contractCode).Bytes()
		if err != nil {
			log.Error("GetContractInfo redis error", zap.Error(err))
			return nil, err
		}

		contract := new(proto.Contract)
		err = json.Unmarshal(encode, contract)
		if err != nil {
			log.Error("GetContractInfo json unmarshal error", zap.String("field", contractCode), zap.Error(err))
			return nil, err
		}
		return contract, nil
	})
}

// 从redis中取近期交易的50条记录
func GetRecentOrders(contractCode string) ([]proto.ApiRecentOrder, error) {
	// 同一服务的同一时间内执行,合并查询
	key := define.CacheKeyRecentTrade + contractCode
	return singleflight.MergeDo[[]proto.ApiRecentOrder](key, func() (any, error) {
		encode, err := DefaultRedis().LRange(key, 0, define.ApiRecentTradeMaxCount).Result()
		if err != nil {
			log.Error("GetRecentOrders redis error", zap.String("contractCode", contractCode), zap.Error(err))
			return nil, err
		}
		if len(encode) == 0 {
			return nil, redis.Nil
		}
		list := make([]proto.ApiRecentOrder, len(encode))
		for i := range encode {
			err = json.Unmarshal(convert.Str2Bytes(encode[i]), &list[i])
			if err != nil {
				log.Errorf("GetRecentOrders json unmarshal error", zap.String("contractCode", contractCode), zap.Error(err))
				return nil, err
			}
		}

		return list, nil
	})
}

// 将最新的交易记录存入redis中
func PushNewDealToRedis(deal *proto.ApiRecentOrder) {
	b, err := json.Marshal(deal)
	if err != nil {
		log.Error("PushNewDealToRedis json marshal error", zap.Any("order", deal), zap.Error(err))
		return
	}

	key := define.CacheKeyRecentTrade + deal.ContractCode
	err = DefaultRedis().LPush(key, b).Err()
	if err != nil {
		log.Error("PushNewDealToRedis push error", zap.Any("order", deal), zap.Error(err))
		return
	}

	// 只保留50条
	err = DefaultRedis().LTrim(key, 0, define.ApiRecentTradeMaxCount-1).Err()
	if err != nil {
		log.Error("addNewDealToRedis trim error", zap.Any("order", deal), zap.Error(err))
	}
}

//
////设置看涨人数
//func IncreBuyCount(contractCode string, count int64) (int64, error) {
//	return DefaultRedis().HIncrBy(define.CacheKeyBuyCount, contractCode, count).Result()
//}
//
////设置看跌人数
//func IncreSellCount(contractCode string, count int64) (int64, error) {
//	return DefaultRedis().HIncrBy(define.CacheKeySellCount, contractCode, count).Result()
//}

//获取看涨看跌人数
func GetContractBuySellCount(contractCode string) (buyCount, sellCount int64) {
	//buyCount, _ = DefaultRedis().HGet(define.CacheKeyBuyCount, contractCode).Int64()
	//sellCount, _ = DefaultRedis().HGet(define.CacheKeySellCount, contractCode).Int64()
	index := GetContractPriceIndexIgnoreErr(contractCode)
	buyCount = index.BuyCount
	sellCount = index.SellCount
	return buyCount, sellCount
}

//
//// 获取全部看涨人数
//func GetAllContractBuyCount() map[string]int64 {
//	mp := make(map[string]int64)
//	result, err := DefaultRedis().HGetAll(define.CacheKeyBuyCount).Result()
//	if err != nil {
//		log.Error("GetAllContractBuyCount redis error", zap.Error(err))
//		return mp
//	}
//	for key, val := range result {
//		mp[key], _ = strconv.ParseInt(val, 10, 64)
//	}
//	return mp
//}
//
//// 获取全部看跌人数
//func GetAllContractSellCount() map[string]int64 {
//	mp := make(map[string]int64)
//	result, err := DefaultRedis().HGetAll(define.CacheKeySellCount).Result()
//	if err != nil {
//		log.Error("GetAllContractSellCount redis error", zap.Error(err))
//		return mp
//	}
//	for key, val := range result {
//		mp[key], _ = strconv.ParseInt(val, 10, 64)
//	}
//	return mp
//}

func SetContractIndicator(c *proto.ContractIndicator) (err error) {
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractIndicator marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(define.CacheKeyContratIndicator, c.ContractCode, b).Err()
	return
}

// 获取合约指标
func GeContractIndicator(code string) *proto.ContractIndicator {
	// 同一服务的同一时间内执行,合并查询
	mp, _ := singleflight.MergeDo[*proto.ContractIndicator](define.CacheKeyContratIndicator+code, func() (any, error) {
		var ci proto.ContractIndicator
		result, err := DefaultRedis().HGet(define.CacheKeyContratIndicator, code).Bytes()
		if err != nil {
			log.Info("GeContractIndicator redis error", zap.Error(err))
			return &ci, nil
		}
		err = json.Unmarshal(result, &ci)
		if err != nil {
			log.Errorf("GeContractIndicator json unmarshal fail,code:%v,%v", code, err)
			return &ci, nil
		}
		return &ci, nil
	})
	return mp
}

// 获取合约指标
func GetAllContractIndicator() (mp map[string]proto.ContractIndicator) {
	mp = make(map[string]proto.ContractIndicator)
	result, err := DefaultRedis().HGetAll(define.CacheKeyContratIndicator).Result()
	if err != nil {
		log.Error("GetAllContractSellCount redis error", zap.Error(err))
		return mp
	}
	for key, val := range result {
		indicator := proto.ContractIndicator{}
		err := json.Unmarshal([]byte(val), &indicator)
		if err != nil {
			log.Errorf("GetAllContractIndicator json unmarshal fail,%v", err)
			return
		}
		mp[key] = indicator
	}
	return mp
}

func SetContractDragSupport(c *proto.ContractSupport) (err error) {
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractIndicator marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(define.CacheKeyContratDragSupport, c.ContractCode, b).Err()
	return
}

// 获取合约指标
func GeContractDragSupport(code string) (mp *proto.ContractSupport) {
	result, err := DefaultRedis().HGet(define.CacheKeyContratDragSupport, code).Bytes()
	if err != nil {
		log.Info("GeContractIndicator redis error", zap.Error(err))
		return mp
	}
	indicator := proto.ContractSupport{}
	err = json.Unmarshal(result, &indicator)
	if err != nil {
		log.Errorf("GeContractIndicator json unmarshal fail,code:%v,%v", code, err)
		return
	}
	mp = &indicator
	return
}

func GetAllContractDragSupport() (mp map[string]proto.ContractSupport) {
	mp = make(map[string]proto.ContractSupport)
	result, err := DefaultRedis().HGetAll(define.CacheKeyContratDragSupport).Result()
	if err != nil {
		log.Error("GetAllContractSellCount redis error", zap.Error(err))
		return mp
	}
	for key, val := range result {
		indicator := proto.ContractSupport{}
		err := json.Unmarshal([]byte(val), &indicator)
		if err != nil {
			log.Errorf("GetAllContractIndicator json unmarshal fail,%v", err)
			return
		}
		mp[key] = indicator
	}
	return mp
}

//删除合约配置
func DelContractDepthConfig() (err error) {
	e := DefaultRedis().Del(define.CacheKeyContractDepthConfig).Err()
	if e != nil {
		log.Errorf("DelContractDepthConfig fail,%v", err)
		return
	}
	return
}

//设置合约深度配置
func SetContractDepthConfig(list []proto.ContractDepth) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Errorf("SetContractDepth fail,%v", err)
		return
	}
	log.Info("设置深度配置到缓存", zap.ByteString("string", b))
	err = DefaultRedis().Set(define.CacheKeyContractDepthConfig, b, 10*time.Second).Err()
	//err = DefaultRedis().Set(define.CacheKeyContractDepthConfig, b, time.Minute).Err()
	return
}

//获取合约深度配置
func GetContractDepthConfig() (list []proto.ContractDepth, err error) {
	b, err := DefaultRedis().Get(define.CacheKeyContractDepthConfig).Bytes()
	if err != nil {
		log.Errorf("GetContractDepthConfig -》Get \"bc:contract:depth:config\"  fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("GetContractDepthConfig fail,%v", err)
		return
	}
	return
}

//删除合约配置
func DelContractDepthSource() (err error) {
	e := DefaultRedis().Del(define.CacheKeyContractDepthSource).Err()
	if e != nil {
		log.Errorf("DelContractDepthSource fail,%v", err)
		return
	}
	return
}

//设置合约深度配置
func SetContractDepthSource(list []proto.ContractDepthSource) (err error) {
	b, err := json.Marshal(list)
	if err != nil {
		log.Errorf("SetContractDepthSource fail,%v", err)
		return
	}
	err = DefaultRedis().Set(define.CacheKeyContractDepthSource, b, time.Minute).Err()
	return
}

//获取合约深度配置
func GetContractDepthSource() (list []proto.ContractDepthSource, err error) {
	b, err := DefaultRedis().Get(define.CacheKeyContractDepthSource).Bytes()
	if err != nil {
		log.Errorf("GetContractDepthSource -》Get \"bc:contract:depth:source\"  fail,%v", err)
		return
	}
	err = json.Unmarshal(b, &list)
	if err != nil {
		log.Errorf("GetContractDepthSource fail,%v", err)
		return
	}
	return
}

func SetDepthPriceSwitchException(code string) (err error) {
	err = DefaultRedis().HSet(define.DepthPriceSourceException, code, convert.Int64String(time.Now().Unix())).Err()
	return
}

func GetDepthPriceSwitchException(code string) (s int64) {
	s, err := DefaultRedis().HGet(define.DepthPriceSourceException, code).Int64()
	if err != nil {
		log.Errorf("GetDepthPriceSwitchException fail,%v,code：%v", err, code)
	}
	return
}

func DelDepthPriceSwitchException(code string) (seconds int64) {
	err := DefaultRedis().HDel(define.DepthPriceSourceException, code).Err()
	if err != nil {
		log.Errorf("DelDepthPriceSwitchException DefaultRedis().HDel(define.DepthPriceSourceException, code).Err():%v", err)
		return
	}
	return
}

func SetDepthSwitchExceptionCache(code string, originSwitch int) (err error) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	err = DefaultRedis().Set(key, time.Now().Unix(), 2*time.Minute).Err()
	return
}

func GetDepthSwitchExceptionCache(code string, originSwitch int) (s int64) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	s, err := DefaultRedis().Get(key).Int64()
	if err != nil {
		log.Errorf("GetDepthSwitchExceptionCache fail,%v,code：%v", err, code)
	}
	return
}

func DelDepthSwitchExceptionCache(code string, originSwitch int) (seconds int64) {
	key := define.DepthPriceSourceSwitchException + code + nums.Int2String(originSwitch)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("DelDepthSwitchExceptionCache.Err():%v", err)
		return
	}
	return
}

func SetDepthAvgPriceSpotNumsCheck(code string) (s int64) {
	err := DefaultRedis().Set(define.DepthPriceAvgWarn+code, code, 10*time.Second).Err()
	if err != nil {
		log.Errorf("SetDepthAvgPriceSpotNumsCheck fail,%v,code：%v", err, code)
	}
	return
}

func GetDepthAvgPriceSpotNumsCheck(code string) bool {
	s, err := DefaultRedis().Exists(define.DepthPriceAvgWarn + code).Result()
	if err != nil {
		log.Errorf("GetDepthPriceSwitchException fail,%v,code：%v", err, code)
	}
	if s == 1 {
		return true
	}
	return false
}

func DelDepthAvgPriceSpotNumsCheck(code string) (seconds int64) {
	err := DefaultRedis().Del(define.DepthPriceAvgWarn + code).Err()
	if err != nil {
		log.Errorf("DelDepthAvgPriceSpotNumsCheck .Err():%v", err)
		return
	}
	return
}
