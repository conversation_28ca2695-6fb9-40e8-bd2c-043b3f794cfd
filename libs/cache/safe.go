package cache

import (
	"bc/libs/convert"
	"strconv"
	"time"

	"bc/libs/define"
	"bc/libs/log"
	"go.uber.org/zap"
)

func CheckFundVerifyState(userID int64) (bool, error) {
	state, err := DefaultRedis().Exists(define.CacheKeySafeTradeVerifyState + strconv.FormatInt(userID, 10)).Result()
	if err != nil {
		return false, err
	}
	return state != 0, nil
}

func SetFundVerifyState(reqID, userID int64) {
	err := DefaultRedis().Set(define.CacheKeySafeTradeVerifyState+strconv.FormatInt(userID, 10), define.RedisLockValue, time.Hour*24).Err()
	if err != nil {
		log.Error("SetFundVerifyState set failed", zap.Int64("reqID", reqID), zap.Error(err))
	}
}

func IncrFundPasswordMiscount(userID int64) (int64, error) {
	now := convert.Hour2Time(0)
	key := define.CacheKeySafeFundPasswordMiscount + now.Format(define.TimeFormatDayKeyV2) + strconv.FormatInt(userID, 10)
	current, err := DefaultRedis().Incr(key).Result()
	if err == nil {
		err = DefaultRedis().ExpireAt(key, now.AddDate(0, 0, 1)).Err()
	}
	return current, err
}

func CleanFundPasswordMiscount(reqID, userID int64) {
	now := convert.Hour2Time(0)
	key := define.CacheKeySafeFundPasswordMiscount + now.Format(define.TimeFormatDayKeyV2) + strconv.FormatInt(userID, 10)
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("CleanFundPasswordMiscount set failed", zap.Int64("reqID", reqID), zap.Error(err))
	}
}

func MarkSafeVerifyWithCode(userID, verifyID int64) error {
	key := define.CacheKeySafeMarkVerifyCode + strconv.FormatInt(verifyID, 10)
	return DefaultRedis().Set(key, userID, time.Hour).Err()
}

func GetSafeVerifyMarkWithCode(verifyID int64) (int64, error) {
	key := define.CacheKeySafeMarkVerifyCode + strconv.FormatInt(verifyID, 10)
	return DefaultRedis().Get(key).Int64()
}

func CleanSafeVerifyMarkWithCode(verifyID int64) {
	key := define.CacheKeySafeMarkVerifyCode + strconv.FormatInt(verifyID, 10)
	DefaultRedis().Del(key)
}

func MarkSafeVerifyWithFundPwd(userID, verifyID int64) error {
	key := define.CacheKeySafeMarkVerifyFund + strconv.FormatInt(verifyID, 10)
	return DefaultRedis().Set(key, userID, time.Hour).Err()
}

func GetSafeVerifyMarkWithFundPwd(verifyID int64) (int64, error) {
	key := define.CacheKeySafeMarkVerifyFund + strconv.FormatInt(verifyID, 10)
	return DefaultRedis().Get(key).Int64()
}

func CleanSafeVerifyMarkWithFundPwd(verifyID int64) {
	key := define.CacheKeySafeMarkVerifyFund + strconv.FormatInt(verifyID, 10)
	DefaultRedis().Del(key)
}

// 检查是否处于提币保护限制中
func CheckSafeGuard(uid int64) int {
	sgType, _ := DefaultRedis().Get(define.CacheKeySafeGuard + strconv.FormatInt(uid, 10)).Int()
	return sgType
}
