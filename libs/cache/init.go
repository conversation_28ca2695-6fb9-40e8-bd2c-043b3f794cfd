package cache

import (
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/msg"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

var conn = make(map[int]*redis.Client)

func DefaultRedis() *redis.Client {
	c, ok := conn[define.RedisCommonDb]
	if !ok {
		panic(fmt.Errorf("redis cache db[%d] is not initialization", define.RedisCommonDb))
	}
	return c
}

func DefaultRedisWithDB(db int) *redis.Client {
	c, ok := conn[db]
	if !ok {
		panic(fmt.Errorf("redis cache db[%d] is not initialization", db))
	}
	return c
}

func InitDefaultRedisConn() {
	rc := conf.DefaultRedisConf()
	for db := range rc.DBNums {
		conn[db] = initRedisDB(rc.Address, rc.Password, db, rc.PoolSize, rc.UseTLS)
	}
}

//提供供测试使用的初始化缓存方法
func InitDefaultRedisDBCon(addr, pwd string, db int, tlsEnable bool) {
	conn[db] = initRedisDB(addr, pwd, db, 20, tlsEnable)
}

var extraConn = make(map[int]*redis.Client)

func ExtraRedis() *redis.Client {
	c, ok := extraConn[define.RedisExtraDb]
	if !ok {
		panic(fmt.Errorf("redis cache db[%d] is not initialization", define.RedisExtraDb))
	}
	return c
}

func ExtraRedisWithDB(db int) *redis.Client {
	c, ok := extraConn[db]
	if !ok {
		panic(fmt.Errorf("redis cache db[%d] is not initialization", db))
	}
	return c
}

func InitExtraRedisConn() {
	rc := conf.ExtraRedisConf()
	for db := range rc.DBNums {
		extraConn[db] = initRedisDB(rc.Address, rc.Password, db, rc.PoolSize, rc.UseTLS)
	}
}

func initRedisDB(addr, pwd string, db, poolSize int, tlsEnable bool) *redis.Client {
	op := &redis.Options{
		Addr:       addr,
		Password:   pwd,
		PoolSize:   poolSize,
		MaxRetries: 3,
		DB:         db,
	}
	if tlsEnable {
		op.TLSConfig = &tls.Config{ServerName: strings.Split(addr, ":")[0]}
	}

	client := redis.NewClient(op)
	go redisPing(client, db)
	return client
}

func redisPing(cli *redis.Client, db int) {
	output := true
	// 测试连接
	for range time.Tick(time.Second * 5) {
		if err := cli.Ping().Err(); err != nil {
			// 发送报错邮件
			msg.SendCautionEmail("redis连接出错!", err.Error())
			log.Fatal("redis error", zap.String("addr", cli.Options().Addr), zap.Error(err))
		} else {
			if output {
				log.Info("连接redis成功",
					zap.String("addr", cli.Options().Addr),
					zap.Int("db", db))
				output = false
			}
		}
	}
}

// SetRedisLockStr 利用redis加锁防止多次执行任务
func SetRedisLockStr(keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	res, err := DefaultRedis().SetNX(key, true, define.ActionLockExpireTime).Result()
	if err != nil {
		log.Error("SetRedisLockStr redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

// SetRedisLockWithExp 利用redis加锁防止多次执行任务
func SetRedisLockWithExp(exp time.Duration, keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	res, err := DefaultRedis().SetNX(key, true, exp).Result()
	if err != nil {
		log.Error("SetRedisLockWithExp redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

// SetUsdRedisLockWithExp 利用redis加锁防止多次执行任务
func SetUsdRedisLockWithExp(exp time.Duration, keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	res, err := DefaultRedisWithDB(define.CacheDBNumber4).SetNX(key, true, exp).Result()
	if err != nil {
		log.Error("SetRedisLockWithExp redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

func SetUsdRedisUnLockStr(keyArgs ...string) {
	if len(keyArgs) < 1 {
		return
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	err := DefaultRedisWithDB(define.CacheDBNumber4).Del(key).Err()
	if err != nil {
		log.Error("SetRedisUnLockStr redis error", zap.String("key", key), zap.Error(err))
	}
	return
}

// SetRedisLockWithID 利用redis加锁防止多次执行任务
func SetRedisLockWithID(id int64, keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	var counter int
tag:
	res, err := DefaultRedis().SetNX(key, id, define.ActionLockExpireTime).Result()
	if err != nil {
		log.Error("SetRedisLockWithID redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	if !res {
		counter++
		if counter > 5 {
			return false
		}

		// 获取旧值,与新值进行对比,如果不相同,进行重试
		old, err := DefaultRedis().Get(key).Int64()
		if err != nil && err != redis.Nil {
			log.Error("SetRedisLockWithID redis error", zap.String("key", key), zap.Error(err))
			return false
		}
		if old == id {
			return false
		}
		time.Sleep(time.Millisecond * 500)
		goto tag
	}
	return res
}

func ExistRedisLockStr(keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	return ExistKey(key)
}

// SetRedisLock 利用redis加锁防止多次执行任务
func SetRedisLock(keyArgs string) bool {
	key := keyArgs

	res, err := DefaultRedis().SetNX(key, true, 0).Result()
	if err != nil {
		log.Error("SetRedisLock redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

// SetRedisUnLock 解锁
func SetRedisUnLock(keyArgs string) {
	key := keyArgs

	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("SetRedisUnLock redis error", zap.String("key", key), zap.Error(err))
	}
	return
}

// 解锁
func SetRedisUnLockStr(keyArgs ...string) {
	if len(keyArgs) < 1 {
		return
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("SetRedisUnLockStr redis error", zap.String("key", key), zap.Error(err))
	}
	return
}

func SetRedisLockTime(minutes int, keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	res, err := DefaultRedis().SetNX(key, true, time.Duration(minutes)*time.Minute).Result()
	if err != nil {
		log.Error("SetRedisLockStr redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

// 删除key
func DelKey(key string) {
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Error("DelKey redis error", zap.String("key", key), zap.Error(err))
	}
}

// 更新键过期时间
func ExpireKey(key string, duration time.Duration) {
	err := DefaultRedis().Expire(key, duration).Err()
	if err != nil {
		log.Error("ExpireKey redis error", zap.String("key", key), zap.Error(err))
	}
}

// 更新键过期时间
func ExpireExtraKey(key string, duration time.Duration) {
	err := ExtraRedis().Expire(key, duration).Err()
	if err != nil {
		log.Error("ExtraRedis redis error", zap.String("key", key), zap.Error(err))
	}
}

// 判断键是否存在
func ExistKey(key string) bool {
	result, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Error("ExistKey redis error", zap.String("key", key), zap.Error(err))
	}
	return result == 1
}

// 删除hash fields
func HDelFields(key string, fields ...string) {
	if len(fields) == 0 {
		return
	}
	err := DefaultRedis().HDel(key, fields...).Err()
	if err != nil {
		log.Error("HDelFields redis error", zap.String("key", key), zap.Error(err))
	}
}

func SetRedisLockTimeWithValue(minutes int, value string, keyArgs ...string) bool {
	if len(keyArgs) < 1 {
		return false
	}
	key := strings.Join(keyArgs, ":")
	if !strings.HasPrefix(key, define.CacheKeyProjectPrefix) {
		key = define.CacheKeyProjectPrefix + key
	}

	res, err := DefaultRedis().SetNX(key, value, time.Duration(minutes)*time.Minute).Result()
	if err != nil {
		log.Error("SetRedisLockStr redis error", zap.String("key", key), zap.Error(err))
		return false
	}
	return res
}

func IncrKeyValue(key string, value int64) (int64, error) {
	return DefaultRedis().IncrBy(key, value).Result()
}

const _decrKeyValueLimitZero = `
	local flag
	local key = KEYS[1]
	local val = ARGV[1]

	-- 判断当前值是否小于当前操作值
	local cur = redis.call("GET", key)
	if cur == false then
		return 0
	end
	if tonumber(cur) < tonumber(val) then
		flag = redis.call("DEL", key)
		return 0
	end
	
	-- 更新值
	return redis.call("DECRBY", key, val)
`

func DecrKeyValueLimitZero(key string, value int64) (int64, error) {
	return DefaultRedis().Eval(_decrKeyValueLimitZero, []string{key}, value).Int64()
}
