/*
@Time : 3/12/20 2:21 下午
<AUTHOR> mocha
@File : change
*/
package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"
)

//设置涨跌幅
func SetContractAppliesChange(applies *proto.Applies) {
	b, err := json.Marshal(applies)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	DefaultRedis().HSet(define.CacheKeyApplies, applies.ContractCode, b)
}

//获取涨跌幅
func GetContractApplies(contractCode string) *proto.Applies {
	// 同一服务的同一时间内执行,合并查询
	index, _ := singleflight.MergeDo[*proto.Applies](define.CacheKeyApplies+contractCode, func() (any, error) {
		idx := new(proto.Applies)
		b, err := DefaultRedis().HGet(define.CacheKeyApplies, contractCode).Bytes()
		if err != nil {
			log.Errorf("GetContractApplies get fail,%v", err)
			return idx, nil
		}
		err = json.Unmarshal(b, idx)
		if err != nil {
			log.Errorf("GetContractApplies json unmarshal fail,%v", err)
			return idx, nil
		}
		return idx, nil
	})
	return index
}

// 获取全部涨跌幅
func GetAllContractApplies() (mp map[string]proto.Applies) {
	// 同一服务的同一时间内执行,合并查询
	mp, _ = singleflight.MergeDo[map[string]proto.Applies](define.CacheKeyApplies, func() (any, error) {
		m := make(map[string]proto.Applies)
		b, err := DefaultRedis().HGetAll(define.CacheKeyApplies).Result()
		if err != nil {
			log.Errorf("GetAllContractApplies get fail,%v", err)
			return m, nil
		}
		var index proto.Applies
		for key, val := range b {
			//log.Debugf("s:%v",string(val))
			err = json.Unmarshal(convert.Str2Bytes(val), &index)
			if err != nil {
				log.Errorf("GetAllContractApplies json unmarshal fail,%v", err)
				return m, nil
			}
			m[key] = index
		}
		return m, nil
	})
	return
}
