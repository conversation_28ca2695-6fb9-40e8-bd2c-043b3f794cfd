package cache

import (
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"

	"go.uber.org/zap"
)

func GetAPiAccount(accessKey string) (apiInfo *proto.UserAccountApikey, err error) {
	apiInfo = new(proto.UserAccountApikey)
	data, err := DefaultRedis().HGet(define.CacheKeyUserApiInfos, accessKey).Bytes()
	if err != nil {
		log.Error("GetByBankBusid redis error", zap.Error(err))
		return nil, err
	}
	log.Info("", zap.ByteString("data", data), zap.String("get token key", accessKey))
	err = json.Unmarshal(data, &apiInfo)
	return
}
