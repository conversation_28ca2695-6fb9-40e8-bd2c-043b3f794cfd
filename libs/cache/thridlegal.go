package cache

//func GetMerchantsByName(BusName string) (*model.ThirdMerchants, error) {
//	ThirdMerchants := new(model.ThirdMerchants)
//	encode, err := DefaultRedis().HGet(define.CacheKeyThirdMerchants, BusName).Bytes()
//	if err != nil {
//		if err != redis.Nil {
//			log.Error("GetMerchantsByName redis error", zap.Error(err))
//		}
//		return ThirdMerchants, err
//	}
//
//	err = json.Unmarshal(encode, &ThirdMerchants)
//	if err != nil {
//		log.Error("GetMerchantsByName json unmarshal error", zap.String("BusName", BusName), zap.Error(err))
//		return ThirdMerchants, err
//	}
//	return ThirdMerchants, nil
//}
//func SaveMerchantsByName(BusName string, ac *model.ThirdMerchants) {
//	b, err := json.Marshal(ac)
//	if err != nil {
//		log.Error(" SaveMerchantsByName json marshal error", zap.Error(err))
//		return
//	}
//	err = DefaultRedis().HSet(define.CacheKeyThirdMerchants, BusName, b).Err()
//	if err != nil {
//		log.Errorf("SaveMerchantsByName fail,%v", err)
//		return
//	}
//	return
//}
