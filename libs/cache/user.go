package cache

import (
	"strconv"
	"time"

	"bc/libs/conf"
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/utils"
	//"bc/pkg/core/model"

	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
)

// 根据token中解析的jti到redis获取用户信息(非全部字段)
func GetTokenPayload(jti string) (payload define.TokenPayload, err error) {
	var c *redis.Client
	if conf.IsSimulate() {
		c = ExtraRedis()
	} else {
		c = DefaultRedis()
	}
	data, err := c.Get(define.CacheKeyLoginInfo + jti).Bytes()
	if err != nil {
		return
	}

	err = json.Unmarshal(data, &payload)
	return
}

// 缓存用户风险率加点
func CacheUserRiskPoint(userId int64, code string, rate decimal.Decimal) (err error) {
	key := utils.StrBuilder(convert.Int64String(userId), ":", code)
	err = DefaultRedis().Set(key, rate.String(), 30*time.Second).Err()
	return
}

// 查询用户风险率加点
func GetUserRiskPoint(userId int64, code string) (rate decimal.Decimal, isNil bool) {
	key := utils.StrBuilder(convert.Int64String(userId), ":", code)
	s, err := DefaultRedis().Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			isNil = true
			rate = decimal.Zero
			return
		}
		log.Infof("GetUserRiskPoint fail,%v", err)
		return
	}
	if s == "" {
		return
	}
	rate = nums.NewFromString(s)
	return
}

// 判断用户法币入金提币限制
func CheckLegalWithdrawLimit(userID int64) bool {
	return DefaultRedis().Exists(define.CacheLockLegalWithdraw+strconv.FormatInt(userID, 10)).Val() != 0
}

//func GetMaxUserAvailApiCount() int {
//	data, err := DefaultRedis().Get(define.CacheKeyServerConfigCommon).Bytes()
//	if err != nil {
//		return define.MaxUserAvailApiCount
//	}
//
//	var cfg model.ServerConfig
//	err = json.Unmarshal(data, &cfg)
//	if err != nil {
//		return define.MaxUserAvailApiCount
//	}
//	return cfg.MaxAPICount
//}

func CheckWithdrawWhite(userID int64) bool {
	return DefaultRedis().SIsMember(define.CacheKeyWithdrawWhiteList, userID).Val()
}
