package cache

import (
	"strconv"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/singleflight"
	//"bc/pkg/core/model"

	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
)

func GetThirdLegalPriceRate(busID int, coin string) (map[string]decimal.Decimal, error) {
	// 同一服务的同一时间内执行,合并查询
	key := define.CacheKeyThirdLegalAllRates + strconv.Itoa(busID) + coin
	return singleflight.MergeDo[map[string]decimal.Decimal](key, func() (any, error) {
		res, err := DefaultRedis().HGet(define.CacheKeyThirdLegalAllRates+strconv.Itoa(busID), coin).Bytes()
		if err != nil && err != redis.Nil {
			return nil, err
		}

		rates := make(map[string]decimal.Decimal)
		if err == redis.Nil {
			return rates, nil
		}
		err = json.Unmarshal(res, &rates)
		return rates, err
	})
}

func GetThirdLegalAllPriceRate(busID int) (map[string]map[string]decimal.Decimal, error) {
	// 同一服务的同一时间内执行,合并查询
	key := define.CacheKeyThirdLegalAllRates + strconv.Itoa(busID)
	return singleflight.MergeDo[map[string]map[string]decimal.Decimal](key, func() (any, error) {
		res, err := DefaultRedis().HGetAll(key).Result()
		if err != nil && err != redis.Nil {
			return nil, err
		}

		rates := make(map[string]map[string]decimal.Decimal, len(res))
		for coin, val := range res {
			var rate map[string]decimal.Decimal
			err = json.Unmarshal([]byte(val), &rate)
			if err != nil {
				return nil, err
			}
			rates[coin] = rate
		}
		return rates, nil
	})
}

//func GetThirdLegalLimits(busID int, coin string) (map[string]model.ThirdLegalLimits, error) {
//	// 同一服务的同一时间内执行,合并查询
//	key := define.CacheKeyThirdLegalAllLimits + strconv.Itoa(busID) + coin
//	return singleflight.MergeDo[map[string]model.ThirdLegalLimits](key, func() (any, error) {
//		res, err := DefaultRedis().HGet(define.CacheKeyThirdLegalAllLimits+strconv.Itoa(busID), coin).Bytes()
//		if err != nil && err != redis.Nil {
//			return nil, err
//		}
//		limits := make(map[string]model.ThirdLegalLimits)
//		if err == redis.Nil {
//			return limits, nil
//		}
//		err = json.Unmarshal(res, &limits)
//		return limits, err
//	})
//}

//func GetThirdLegalAllLimits(busID int) (map[string]map[string]model.ThirdLegalLimits, error) {
//	// 同一服务的同一时间内执行,合并查询
//	key := define.CacheKeyThirdLegalAllLimits + strconv.Itoa(busID)
//	return singleflight.MergeDo[map[string]map[string]model.ThirdLegalLimits](key, func() (any, error) {
//		res, err := DefaultRedis().HGetAll(define.CacheKeyThirdLegalAllLimits + strconv.Itoa(busID)).Result()
//		if err != nil && err != redis.Nil {
//			return nil, err
//		}
//
//		limits := make(map[string]map[string]model.ThirdLegalLimits, len(res))
//		for coin, val := range res {
//			var limit map[string]model.ThirdLegalLimits
//			err = json.Unmarshal([]byte(val), &limit)
//			if err != nil {
//				return nil, err
//			}
//			limits[coin] = limit
//		}
//		return limits, nil
//	})
//}
