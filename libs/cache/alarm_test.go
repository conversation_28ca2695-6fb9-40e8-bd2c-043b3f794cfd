package cache

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"testing"
	"time"
)

func TestAlarm(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	PushAlarmRecord(&proto.Alarm{AlarmLevel: 1, Type: 1, Content: "报警内容11111", Time: time.Now()})
	PushAlarmRecord(&proto.Alarm{AlarmLevel: 1, Type: 1, Content: "报警内容11111", Time: time.Now()})
	PushAlarmRecord(&proto.Alarm{AlarmLevel: 1, Type: 1, Content: "报警内容11111", Time: time.Now()})
	PushAlarmRecord(&proto.Alarm{AlarmLevel: 1, Type: 1, Content: "报警内容111115", Time: time.Now()})

}

func TestGetAlarmFirst(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	list, err := GetAlarmFirst(1)
	if err != nil {
		t.Log(err)
		return
	}
	for _, alarm := range list {
		t.Logf("%+v", alarm)
	}
}

func TestGetAlarmList(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	list, err := ListAlarms(1)
	if err != nil {
		t.Log(err)
		return
	}
	for _, alarm := range list {
		t.Logf("%+v", alarm)
	}
}

func TestSpotSourceAdd(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	AddIndexSpot("BTCUSDT", "huobi")
	AddIndexSpot("BTCUSDT", "huobi")
	AddIndexSpot("BTCUSDT", "ftx")
	AddIndexSpot("BTCUSDT", "okex")

	AddIndexSpot("ETCUSDT", "huobi")
	AddIndexSpot("ETCUSDT", "huobi")
	AddIndexSpot("ETCUSDT", "ftx")
	AddIndexSpot("ETCUSDT", "okex")

}

func TestSpotSourceShow(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	result, err := ListIndexSpot("BTCUSDT", time.Now())
	if err != nil {
		t.Log(err)
		return
	}
	t.Logf("%+v", result)
}

func TestSpotSourceForAllContract(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	result, err := ListContractIndexSpot(time.Now())
	if err != nil {
		t.Log(err)
		return
	}
	t.Logf("%+v", result)
}

func TestSetExpire(t *testing.T) {
	log.InitLogger("test", "info", false)
	define.RedisCommonDb = 2
	InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	key := []string{"test", "BTCUSDT"}
	du, err := GetWarnWithExpireTime(key)
	if err != nil {
		t.Log(err)
	}
	SetWarnWithExpireTime(key)
	time.Sleep(2 * time.Second)
	du, err = GetWarnWithExpireTime(key)
	if err != nil {
		t.Log(err)
	}
	t.Logf("%+v", du.String())
}
