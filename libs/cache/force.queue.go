package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"go.uber.org/zap"
)

// SetForceQueue 将强平项目放入队列
func SetForceQueue(srvId string, item *proto.ForceItem) (err error) {
	key := utils.StrBuilderBySep(":", define.CacheForceQueue, srvId)
	b, err := json.Marshal(item)
	if err != nil {
		log.Error("SetForceQueue json marshal fail", zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(key, convert.Int64String(item.ForceId), b).Err()
	return
}

// GetForceQueueList 获取指定合约的强平项目
func GetForceQueueList(srvId string) (list []proto.ForceItem, err error) {
	key := utils.StrBuilderBySep(":", define.CacheForceQueue, srvId)

	s, err := DefaultRedis().HVals(key).Result()
	if err != nil {
		log.Errorf("GetForceQueueList fail,%v,code：%v", err)
	}
	for _, c := range s {
		fi := new(proto.ForceItem)
		err = json.Unmarshal([]byte(c), fi)
		if err != nil {
			continue
		}
		list = append(list, *fi)
	}
	return
}

// DelForceQueueList 移除自动的强平项目
func DelForceQueueList(srvId string, forceId int64) {
	key := utils.StrBuilderBySep(":", define.CacheForceQueue, srvId)
	err := DefaultRedis().HDel(key, convert.Int64String(forceId)).Err()
	if err != nil {
		log.Error("DelForceQueueList", zap.String("srv", srvId), zap.Int64("forceId", forceId))
		return
	}
	return
}
