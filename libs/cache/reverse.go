package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"go.uber.org/zap"
)

// GetAllUsdPriceIndex 获取全部usd合约最新成交价格
func GetAllUsdPriceIndex() map[string]proto.UsdIndexHistory {
	mp := make(map[string]proto.UsdIndexHistory)
	result, err := DefaultRedisWithDB(define.CacheDBNumber4).HGetAll(define.CacheKeyContractIndex).Result()
	if err != nil {
		log.Error("GetAllPriceIndex redis error", zap.Error(err))
		return mp
	}

	var index proto.UsdIndexHistory
	for key, val := range result {
		err = json.Unmarshal(convert.Str2Bytes(val), &index)
		if err != nil {
			log.Error("GetAllPriceIndex json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = index
	}
	return mp
}

func GetAllUsdContractComplexPrice() (m map[string]proto.ComplexPrice) {
	m = make(map[string]proto.ComplexPrice)
	list, err := DefaultRedisWithDB(define.CacheDBNumber4).HVals(define.CacheKeyContractComplexPrice).Result()
	if err != nil {
		log.Warnf("GetContractComplexTradePrice get fail%v", err)
		return
	}
	for _, v := range list {
		index := new(proto.ComplexPrice)
		err = json.Unmarshal([]byte(v), index)
		if err != nil {
			log.Errorf("json unmarshal fail,%v", err)
			continue
		}
		m[index.ContractCode] = *index
	}
	return
}
