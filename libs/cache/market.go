package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"encoding/json"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"strings"
	"time"
)

//设置合约不同市场成交价
func SetContractMarketPrice(c *proto.MarketTrade) (err error) {
	key := utils.StrBuilder(define.CacheKeyContractMarketPrice, c.Symbol)
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractMarketPrice marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(key, c.Source, b).Err()
	return
}

func GeContractMarketPriceBySource(code string, source string) (mt *proto.MarketTrade) {
	key := utils.StrBuilder(define.CacheKeyContractMarketPrice, code)
	result, err := DefaultRedis().HGet(key, source).Bytes()
	if err != nil {
		log.Error("GeContractMarketPriceBySource redis error", zap.Error(err))
		return
	}
	m := new(proto.MarketTrade)
	err = json.Unmarshal(result, m)
	if err != nil {
		log.Errorf("GeContractMarketPriceBySource json unmarshal fail,code:%v,%v", code, err)
		return
	}
	mt = m
	return
}

// 获取合约不同市场价格
func GeContractMarketPrice(code string) (mt map[string]proto.MarketTrade) {
	key := utils.StrBuilder(define.CacheKeyContractMarketPrice, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeContractMarketPrice redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.MarketTrade)
	for source, s := range result {
		m := new(proto.MarketTrade)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeContractMarketPrice json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

//设置不同市场合约深度更新
func SetMarketDepths(holder *proto.DepthHolder) {
	key := utils.StrBuilder(define.CacheKeyContractMarketDepth, holder.Symbol)
	b, err := json.Marshal(holder)
	if err != nil {
		log.Errorf("SetContractMarketPrice marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(key, holder.Source, b).Err()
	return
}

// 获取合约不同市场深度数据
func GeMarketDepth(code string) (mt map[string]proto.DepthHolder) {
	key := utils.StrBuilder(define.CacheKeyContractMarketDepth, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeMarketDepth redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.DepthHolder)
	for source, s := range result {
		m := new(proto.DepthHolder)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeMarketDepth json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

//获取指定来源的深度数据
func GeMarketDepthBySource(code, source string) (mt *proto.DepthHolder) {
	key := utils.StrBuilder(define.CacheKeyContractMarketDepth, code)
	result, err := DefaultRedis().HGet(key, source).Bytes()
	if err != nil {
		log.Error("GeMarketDepthBySource redis error", zap.Error(err))
		return
	}
	m := new(proto.DepthHolder)
	err = json.Unmarshal(result, m)
	if err != nil {
		log.Errorf("GeMarketDepthBySource json unmarshal fail,code:%v,%v", code, err)
		return
	}
	return m
}

func SetDepthMidPrice(code string, price decimal.Decimal) {
	key := utils.StrBuilder(define.CacheKeyDepthMidPrice, ":", code)
	b, err := price.MarshalJSON()
	if err != nil {
		log.Errorf("price marshl fail,%v", err)
		return
	}
	err = DefaultRedis().Set(key, b, 1*time.Minute).Err()
	if err != nil {
		log.Errorf("SetDepthMidPrice fail,%v", err)
		return
	}
}

func GetDepthMidPrice(code string) (result decimal.Decimal, err error) {
	key := utils.StrBuilder(define.CacheKeyDepthMidPrice, ":", code)
	var r []byte
	r, err = DefaultRedis().Get(key).Bytes()
	if err != nil {
		log.Errorf("json.Unmarshal get fail,%v", err)
		return
	}
	err = result.UnmarshalJSON(r)
	if err != nil {
		log.Errorf("GetDepthMidPrice decimal.UnmarshalJson fail,%v", err)
		return
	}
	return

}

//设置合约不同市场指数价格
func SetContractMarketIndexPrice(c *proto.IndexPrice) (err error) {
	key := utils.StrBuilder(define.CacheKeyContractMarketIndexPrice, c.ContractCode)
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractMarketIndexPrice marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(key, c.Source, b).Err()
	return
}

// 获取合约不同市场指数价格
func GeContractMarketIndexPrice(code string) (mt map[string]proto.IndexPrice) {
	key := utils.StrBuilder(define.CacheKeyContractMarketIndexPrice, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeContractMarketIndexPrice redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.IndexPrice)
	for source, s := range result {
		m := new(proto.IndexPrice)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeContractMarketIndexPrice json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

//设置合约不同市场指数价格
func SetContractMarketMarkPrice(c *proto.MarkPrice) (err error) {
	key := utils.StrBuilder(define.CacheKeyContractMarketMarkPrice, c.ContractCode)
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractMarketIndexPrice marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(key, c.Source, b).Err()
	return
}

// 获取合约不同市场指数价格
func GetContractMarketMarkPrice(code string) (mt map[string]proto.MarkPrice) {
	key := utils.StrBuilder(define.CacheKeyContractMarketMarkPrice, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeContractMarketIndexPrice redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.MarkPrice)
	for source, s := range result {
		m := new(proto.MarkPrice)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeContractMarketIndexPrice json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

func GetContractMarketMarkPriceBySource(code, source string) (mt *proto.MarkPrice) {
	key := utils.StrBuilder(define.CacheKeyContractMarketMarkPrice, code)
	result, err := DefaultRedis().HGet(key, source).Bytes()
	if err != nil {
		log.Error("GeContractMarketIndexPrice redis error", zap.Error(err))
		return
	}

	m := new(proto.MarkPrice)
	err = json.Unmarshal(result, m)
	if err != nil {
		log.Errorf("GetContractMarketMarkPriceBySource json unmarshal fail,code:%v,%v", code, err)
		return
	}
	return m
}

//设置合约不同市场现货成交价
func SetContractSpotPrice(c *proto.MarketTrade) (err error) {
	key := utils.StrBuilder(define.CacheKeyContractSpotPrice, c.Symbol)
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractSpotPrice marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(key, c.Source, b).Err()
	return
}

// 获取合约不同市场现货价格
func GeContractSpotPrice(code string) (mt map[string]proto.MarketTrade) {
	key := utils.StrBuilder(define.CacheKeyContractSpotPrice, code)
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Error("GeContractSpotPrice redis error", zap.Error(err))
		return
	}
	mt = make(map[string]proto.MarketTrade)
	for source, s := range result {
		m := new(proto.MarketTrade)
		err = json.Unmarshal([]byte(s), m)
		if err != nil {
			log.Errorf("GeContractSpotPrice json unmarshal fail,code:%v,%v", code, err)
			return
		}
		mt[source] = *m
	}
	return
}

func GeContractSpotPriceBySource(code string, source string) (mt *proto.MarketTrade) {
	key := utils.StrBuilder(define.CacheKeyContractSpotPrice, code)
	result, err := DefaultRedis().HGet(key, source).Bytes()
	if err != nil {
		if err == redis.Nil {
			log.Info("数据为空", zap.String("code", code), zap.String("source", source))
			return
		}
		log.Error("GeContractSpotPriceBySource get err", zap.Error(err), zap.String("code", code), zap.String("source", source))
		return
	}
	m := new(proto.MarketTrade)
	err = json.Unmarshal(result, m)
	if err != nil {
		log.Error("GeContractSpotPriceBySource json unmarshal fail", zap.Error(err))
		return
	}
	mt = m
	return
}

//获取最近的对敲周期
func GetContractCurBucketDuration(code string) (b decimal.Decimal, isNil bool) {
	s, err := DefaultRedis().HGet(define.CacheKeyLastBucketDuration, code).Result()
	if err != nil {
		if err == redis.Nil {
			isNil = true
			return
		}
		return
	}
	b = nums.NewFromString(s)
	return
}

//设置最近的对敲周期
func SetContractCurBucketDuration(code string, b decimal.Decimal) (err error) {
	return DefaultRedis().HSet(define.CacheKeyLastBucketDuration, code, b.String()).Err()
}

//设置合约现货指数价格保护
func SetContractSpotIndexProtect(c *proto.IndexPriceProtect) (err error) {
	b, err := json.Marshal(c)
	if err != nil {
		log.Errorf("SetContractSpotIndexProtect marshal fail,%v", err)
		return err
	}
	err = DefaultRedis().HSet(define.CacheKeyContractSpotIndexProtect, c.Code, b).Err()
	return
}

// 获取合约现货指数价格保护
func GetContractSpotIndexProtect(code string) (ip *proto.IndexPriceProtect) {
	result, err := DefaultRedis().HGet(define.CacheKeyContractSpotIndexProtect, code).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Error("GeContractSpotIndexProtect redis error", zap.Error(err))
		}
		return
	}
	m := new(proto.IndexPriceProtect)
	err = json.Unmarshal(result, m)
	if err != nil {
		log.Errorf("GeContractSpotIndexProtect json unmarshal fail,code:%v,%v", code, err)
		return
	}
	return
}

//移除合约指数价格保护
func DelContractSpotIndexProtect(code string) {
	err := DefaultRedis().HDel(define.CacheKeyContractSpotIndexProtect, code).Err()
	if err != nil {
		log.Error("GeContractSpotIndexProtect redis error", zap.Error(err))
		return
	}
	return
}

//增加合约指数保护当天次数
func IncrContractSpotIndexProtectDayCount(code string, c int64) (count int64, err error) {
	date := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheKeyContractSpotIndexProtectDay, ":", date, ":", code)
	count, err = DefaultRedis().IncrBy(key, c).Result()
	return
}

//获取指定的合约指数范围
func GetContractSpotIndexBaseHistoryByIndex(code string, index int64) *proto.IndexBaseHistory {
	key := utils.StrBuilder(define.CacheKeySpotIndexBaseMinuteHistory, ":", code)
	b, err := DefaultRedis().LIndex(key, index).Bytes()
	if err != nil {
		log.Errorf("GetContractSpotIndexHistoryByIndex redis fail,%v", err)
		return nil
	}
	k := new(proto.IndexBaseHistory)
	err = json.Unmarshal(b, k)
	if err != nil {
		log.Errorf("GetkLineNodeByIndex json unmarshal fail,%v", err)
		return nil
	}
	return k
}

func PushContractSpotIndexBaseHistory(node *proto.IndexBaseHistory) {
	if node == nil {
		return
	}
	key := utils.StrBuilder(define.CacheKeySpotIndexBaseMinuteHistory, ":", node.Code)
	b, err := json.Marshal(node)
	if err != nil {
		log.Error("PushContractSpotIndexHistory json marshal error", zap.String("key", key), zap.Error(err))
		return
	}

	err = DefaultRedis().RPush(key, b).Err()
	if err != nil {
		log.Error("PushContractSpotIndexHistory redis error", zap.String("key", key), zap.Error(err))
		return
	}
	// 保留1千条
	err = DefaultRedis().LTrim(key, -1000, -1).Err()
	if err != nil {
		log.Error("PushBasePriceKLineNode trim redis error", zap.String("key", key), zap.Error(err))
		return
	}
	return
}

// 正序现货基准
func GetContractSpotIndexBaseHistory(code string, start int64, end int64) ([]proto.IndexBaseHistory, error) {
	key := utils.StrBuilder(define.CacheKeySpotIndexBaseMinuteHistory, ":", code)
	encode, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetContractSpotIndexHistory redis error",
			zap.String("key", key),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.Error(err))
		return nil, err
	}

	list := make([]proto.IndexBaseHistory, len(encode))
	for i := range encode {
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &list[i])
		if err != nil {
			log.Error("GetContractSpotIndexHistory json unmarshal error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}
	}
	return list, nil
}

//10次铺单基准价生成铺单基准现货差值历史(计算铺单基准价格时）
//获取指定的合约指数范围
func GetContractDepthSpotIndexBaseHistoryByIndex(code string, index int64) *proto.IndexBaseHistory {
	key := utils.StrBuilder(define.CacheKeyDepthSpotIndexBaseMinuteHistory, ":", code)
	b, err := DefaultRedis().LIndex(key, index).Bytes()
	if err != nil {
		log.Errorf("GetContractSpotIndexHistoryByIndex redis fail,%v", err)
		return nil
	}
	k := new(proto.IndexBaseHistory)
	err = json.Unmarshal(b, k)
	if err != nil {
		log.Errorf("GetkLineNodeByIndex json unmarshal fail,%v", err)
		return nil
	}
	return k
}

func PushContractDepthSpotIndexBaseHistory(node *proto.IndexBaseHistory) {
	if node == nil {
		return
	}
	key := utils.StrBuilder(define.CacheKeyDepthSpotIndexBaseMinuteHistory, ":", node.Code)
	b, err := json.Marshal(node)
	if err != nil {
		log.Error("PushContractSpotIndexHistory json marshal error", zap.String("key", key), zap.Error(err))
		return
	}

	err = DefaultRedis().RPush(key, b).Err()
	if err != nil {
		log.Error("PushContractSpotIndexHistory redis error", zap.String("key", key), zap.Error(err))
		return
	}
	// 保留100条
	err = DefaultRedis().LTrim(key, -100, -1).Err()
	if err != nil {
		log.Error("PushBasePriceKLineNode trim redis error", zap.String("key", key), zap.Error(err))
		return
	}
	return
}

// 正序铺单现货基准
func GetContractDepthSpotIndexBaseHistory(code string, start int64, end int64) ([]proto.IndexBaseHistory, error) {
	key := utils.StrBuilder(define.CacheKeyDepthSpotIndexBaseMinuteHistory, ":", code)
	encode, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetContractSpotIndexHistory redis error",
			zap.String("key", key),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.Error(err))
		return nil, err
	}

	list := make([]proto.IndexBaseHistory, len(encode))
	for i := range encode {
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &list[i])
		if err != nil {
			log.Error("GetContractSpotIndexHistory json unmarshal error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}
	}
	return list, nil
}

//设置合约移动基差值
func SetContractDynamicBaseDiff(code string, diff decimal.Decimal) {
	date := time.Now().Format("2006-01-02 15:04")
	key := utils.StrBuilder(define.CacheKeyContractDynamicBaseDiff, ":", code, ":", date)
	err := DefaultRedis().Set(key, diff.String(), 2*time.Minute).Err()
	if err != nil {
		log.Errorf("SetContractDynamicBaseDiff fail,%v", err)
		return
	}
}

//获取合约移动基差值
func GetContractDynamicBaseDiff(code string) (diff decimal.Decimal, isFund bool) {
	isFund = true
	date := time.Now().Format("2006-01-02 15:04")
	key := utils.StrBuilder(define.CacheKeyContractDynamicBaseDiff, ":", code, ":", date)
	s, err := DefaultRedis().Get(key).Result()
	if err != nil {
		isFund = false
	}
	diff = nums.NewFromString(s)
	return
}

func GetMarketWarnAddress() (list []string, err error) {
	r, err := DefaultRedis().Get(define.CacheMarketWarnReceive).Result()
	if err != nil {
		log.Error("GetMarketWarnAddress fail", zap.Error(err))
		return
	}
	if len(r) == 0 {
		return
	}
	list = strings.Split(r, ",")
	return
}
