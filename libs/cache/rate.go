package cache

import (
	"strings"

	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"go.uber.org/zap"
)

func SaveCoinsRate(rates []proto.CoinRate) {
	for i := range rates {
		b, err := json.Marshal(&rates[i])
		if err != nil {
			log.Error("SaveCoinsRate json marshal error", zap.Error(err))
			continue
		}
		err = DefaultRedis().HSet(define.CacheKeyCoinRate, rates[i].Symbol, b).Err()
		if err != nil {
			log.Error("SaveCoinsRate redis error", zap.String("field", rates[i].Symbol), zap.Error(err))
		}
	}
}

func GetAllCoinsRate() (map[string]proto.CoinRate, error) {
	mp := make(map[string]proto.CoinRate)
	bs, err := DefaultRedis().HGetAll(define.CacheKeyCoinRate).Result()
	if err != nil {
		log.Error("GetAllCoinsRate redis error", zap.Error(err))
		return mp, err
	}

	var rate proto.CoinRate
	for key, val := range bs {
		err = json.Unmarshal(convert.Str2Bytes(val), &rate)
		if err != nil {
			log.Error("GetAllCoinsRate json unmarshal error", zap.Error(err))
			continue
		}
		mp[key] = rate
	}
	return mp, nil
}

func GetCoinRate(coin string) (*proto.CoinRate, error) {
	rate := new(proto.CoinRate)
	b, err := DefaultRedis().HGet(define.CacheKeyCoinRate, strings.ToUpper(coin)).Bytes()
	if err != nil {
		//log.Error("GetCoinRate redis error", zap.Error(err))
		return rate, err
	}

	err = json.Unmarshal(b, &rate)
	if err != nil {
		log.Error("GetCoinRate json unmarshal error", zap.Error(err))
		return rate, err
	}
	return rate, nil
}

func SetUserContractFundingRate(code string, uid int64, fundingRate string) {
	//
}
