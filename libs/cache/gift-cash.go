package cache

import (
	"bc/libs/convert"
	"strconv"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/proto"
	"github.com/go-redis/redis"
)

func GetGiftCashActivity(giftID int) (*proto.GiftInfoList, error) {
	res, err := DefaultRedis().HGet(define.CacheKeyGiftCashActivity, strconv.Itoa(giftID)).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	var data proto.GiftInfoList
	err = json.Unmarshal(res, &data)
	if err != nil {
		return nil, err
	}
	return &data, err
}

func AddGiftCashExpireTask(key, recordID string, expire int64) error {
	return DefaultRedis().ZAdd(key, redis.Z{
		Score:  float64(expire),
		Member: recordID,
	}).Err()
}

func GetGiftCashExpireTask(key string, curTime int64) ([]string, error) {
	return DefaultRedis().ZRangeByScore(key, redis.ZRangeBy{Min: "0", Max: convert.Int64String(curTime)}).Result()
}

func DelGiftCashExpireTask(key string, recordID ...interface{}) error {
	return DefaultRedis().ZRem(key, recordID...).Err()
}
