package cache

import (
	"bc/libs/singleflight"
	"strings"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"go.uber.org/zap"
)

func SaveCoinLegalRate(source string, v proto.CoinLegalPrice) {
	b, err := json.Marshal(&v)
	if err != nil {
		log.Error("SaveCoinLegalRate marshal fail",
			zap.String("source", source),
			zap.Any("v", v),
			zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyCoinLegalRate, strings.ToUpper(source), b).Err()
	if err != nil {
		log.Error("SaveLegalRate hmset fail",
			zap.String("source", source),
			zap.Any("v", v),
			zap.Error(err))
		return
	}
}

func GetAllCoinLegalRate() (map[string]proto.CoinLegalPrice, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.CoinLegalPrice](define.CacheKeyCoinLegalRate, func() (any, error) {
		bs, err := DefaultRedis().HGetAll(define.CacheKeyCoinLegalRate).Result()
		if err != nil {
			return nil, err
		}

		var p proto.CoinLegalPrice
		m := make(map[string]proto.CoinLegalPrice, len(bs))
		for k, v := range bs {
			err = json.Unmarshal([]byte(v), &p)
			if err != nil {
				return nil, err
			}
			m[k] = p
		}
		return m, err
	})
}

func GetCoinLegalRate(source string) (proto.CoinLegalPrice, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[proto.CoinLegalPrice](define.CacheKeyCoinLegalRate+source, func() (any, error) {
		var p proto.CoinLegalPrice
		b, err := DefaultRedis().HGet(define.CacheKeyCoinLegalRate, strings.ToUpper(source)).Bytes()
		if err != nil {
			return p, err
		}

		err = json.Unmarshal(b, &p)
		return p, err
	})
}
