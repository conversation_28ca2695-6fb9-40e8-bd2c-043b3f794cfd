package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/utils"
	"go.uber.org/zap"
)

func AddFollowPosition(tPosID int64, followPosition *proto.FollowPosition) {
	key := utils.StrBuilder(define.DealerFollowPosition, convert.Int64String(tPosID))
	b, err := json.Marshal(followPosition)
	if err != nil {
		return
	}
	err = DefaultRedis().HSet(key, convert.Int64String(followPosition.Id), b).Err()
	if err != nil {
		log.Errorf("AddFollowPosition fail,%v", err)
		return
	}
	return
}

func GetFollowPosition(tPosID int64) (list []proto.FollowPosition) {
	key := utils.StrBuilder(define.DealerFollowPosition, convert.Int64String(tPosID))
	slice := DefaultRedis().HVals(key).Val()
	for _, s := range slice {
		f := new(proto.FollowPosition)
		e := json.Unmarshal([]byte(s), f)
		if e != nil {
			continue
		}
		list = append(list, *f)
	}
	return
}

func DelFollowPosition(tPosID int64, followPosId int64) {
	key := utils.StrBuilder(define.DealerFollowPosition, convert.Int64String(tPosID))
	err := DefaultRedis().HDel(key, convert.Int64String(followPosId)).Err()
	if err != nil {
		log.Errorf("DelFollowPosition fail,%v", err)
		return
	}
	return
}

func AddTraderPos(order *proto.EntrustOrder) (err error) {
	key := define.DealerClosePosList
	id := convert.Int64String(order.ID)
	b, err := json.Marshal(order)
	if err != nil {
		log.Error("AddTraderPos fail", zap.Error(err))
		return
	}
	err = DefaultRedis().HSet(key, id, b).Err()
	if err != nil {
		log.Errorf("AddFollowPosition fail,%v", err)
		return
	}
	return
}

func GetTraderPos() (list []proto.EntrustOrder, err error) {
	key := define.DealerClosePosList
	slice, err := DefaultRedis().HVals(key).Result()
	if err != nil {
		return
	}
	for _, v := range slice {
		a := new(proto.EntrustOrder)
		e := json.Unmarshal([]byte(v), a)
		if e != nil {
			log.Error("GetTraderPos", zap.Error(e))
			continue
		}
		list = append(list, *a)
	}
	return
}

func DelTraderPos(PosID int64) (err error) {
	key := define.DealerClosePosList
	id := convert.Int64String(PosID)
	err = DefaultRedis().HDel(key, id).Err()
	if err != nil {
		log.Errorf("DelTraderPos fail,%v", err)
		return
	}
	return
}
