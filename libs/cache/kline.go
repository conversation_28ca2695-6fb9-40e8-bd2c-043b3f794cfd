package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"time"
)

func getKlineKey(contractCode, timeQuantum string) string {
	return utils.StrBuilderBySep(":", define.CacheKeyKline, contractCode, timeQuantum)
}

//获取kline长度
func GetkLineSize(contractCode, timeQuantum string) int64 {
	key := getKlineKey(contractCode, timeQuantum)
	b, err := DefaultRedis().LLen(key).Result()
	if err != nil {
		log.Errorf("GetkLineSize redis fail,%v", err)
		return 0
	}
	return b
}

func GetKLineNodeByIndex(contractCode, timeQuantum string, index int64) (kline *proto.KLine, err error) {
	key := getKlineKey(contractCode, timeQuantum)
	b, err := DefaultRedis().LIndex(key, index).Bytes()
	if err != nil {
		log.Errorf("GetkLineNodeByIndex redis fail,%v", err)
		if err == redis.Nil {
			err = nil
			return
		}
		return
	}
	k := new(proto.KLine)
	err = json.Unmarshal(b, k)
	if err != nil {
		log.Errorf("GetkLineNodeByIndex json unmarshal fail,%v", err)
		return
	}
	if k.Ts == 0 {
		k.Ts = time.Now().Unix()
	}
	kline = k
	return
}

//获取指定kline
func GetkLineNodeByIndex(contractCode, timeQuantum string, index int64) *proto.KLine {
	key := getKlineKey(contractCode, timeQuantum)
	b, err := DefaultRedis().LIndex(key, index).Bytes()
	if err != nil {
		log.Errorf("GetkLineNodeByIndex redis fail,%v", err)
		return nil
	}
	k := new(proto.KLine)
	err = json.Unmarshal(b, k)
	if err != nil {
		log.Errorf("GetkLineNodeByIndex json unmarshal fail,%v", err)
		return nil
	}
	return k
}

//修改指定的kline节点
func UpdateKlineNodeByIndex(timeQuantum string, index int64, kline *proto.KLine) error {
	key := getKlineKey(kline.ContractCode, timeQuantum)
	b, err := json.Marshal(kline)
	if err != nil {
		log.Error("UpdateKlineNodeByIndex json marshal error", zap.String("key", key), zap.Error(err))
		return err
	}
	err = DefaultRedis().LSet(key, index, b).Err()
	if err != nil {
		log.Errorf("UpdateKlineNodeByIndex lset err:%v,d:%+v", err, *kline)
		return err
	}
	return err
}

func GetKLineNode(contractCode, timeQuantum string, start int64, end int64) ([]proto.KLine, error) {
	key := getKlineKey(contractCode, timeQuantum)
	encode, err := DefaultRedis().LRange(key, start, end).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetKLineNode redis error",
			zap.String("key", key),
			zap.Int64("start", start),
			zap.Int64("end", end),
			zap.Error(err))
		return nil, err
	}

	var node proto.KLine
	list := make([]proto.KLine, 0)

	for i := range encode {
		err = json.Unmarshal(convert.Str2Bytes(encode[i]), &node)
		if err != nil {
			log.Error("GetKLineNode json unmarshal error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}
		list = append(list, node)
	}

	return list, nil
}
func PushKLineNode(node *proto.KLine, timeQuantum string) error {
	key := getKlineKey(node.ContractCode, timeQuantum)
	b, err := json.Marshal(node)
	if err != nil {
		log.Error("PushKLineNode json marshal error", zap.String("key", key), zap.Error(err))
		return err
	}

	err = DefaultRedis().RPush(key, b).Err()
	if err != nil {
		log.Error("PushKLineNode redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	// 保留两千条
	err = DefaultRedis().LTrim(key, -2000, -1).Err()
	if err != nil {
		log.Error("PushBasePriceKLineNode trim redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}

// 正序取K线
func GetKLineNodeASC(contractCode string, timeQuantum string, start int64, end int64) ([]proto.KLine, error) {
	key := utils.StrBuilderBySep(":", define.CacheKeyKline, contractCode, timeQuantum)
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[[]proto.KLine](key, func() (any, error) {
		encode, err := DefaultRedis().LRange(key, start, end).Result()
		if err != nil && err != redis.Nil {
			log.Error("GetKLineNodeASC redis error",
				zap.String("key", key),
				zap.Int64("start", start),
				zap.Int64("end", end),
				zap.Error(err))
			return nil, err
		}

		list := make([]proto.KLine, len(encode))
		for i := range encode {
			err = json.Unmarshal(convert.Str2Bytes(encode[i]), &list[i])
			if err != nil {
				log.Error("GetKLineNodeASC json unmarshal error",
					zap.String("key", key),
					zap.Int64("start", start),
					zap.Int64("end", end),
					zap.Error(err))
				return nil, err
			}
		}
		return list, nil
	})
}

func PushMarket24VolumeHistory(node *proto.Market24hVolumeHistory) error {
	if node == nil {
		return nil
	}
	key := utils.StrBuilder(define.CacheKeyMarket24History, ":", node.Code)
	b, err := json.Marshal(node)
	if err != nil {
		log.Error("PushMarket24VolumeHistory json marshal error", zap.String("key", key), zap.Error(err))
		return err
	}

	err = DefaultRedis().RPush(key, b).Err()
	if err != nil {
		log.Error("PushMarket24VolumeHistory redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	err = DefaultRedis().LTrim(key, -100, -1).Err()
	if err != nil {
		log.Error("PushMarket24VolumeHistory trim redis error", zap.String("key", key), zap.Error(err))
		return err
	}
	return nil
}
