package cache

import (
	"strconv"
	"strings"
	"sync"
	"time"

	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

var coinSetLock sync.Mutex

func SaveCoinList(list []proto.Coin, force bool) {
	var counter int
flag:
	if !coinSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer coinSetLock.Unlock()

	idKeys := make(map[string]define.Placeholder, len(list))
	nameKeys := make(map[string]define.Placeholder, len(list))

	for i := range list {
		nameKeys[list[i].CurrencyName] = define.PlaceholderEntity
		idKeys[strconv.Itoa(list[i].CurrencyId)] = define.PlaceholderEntity
	}

	var delIDs []string

	currIDKeys := DefaultRedis().HKeys(define.CacheKeyCoinListByID).Val()
	for i := range currIDKeys {
		if _, ok := idKeys[currIDKeys[i]]; !ok {
			delIDs = append(delIDs, currIDKeys[i])
		}
	}

	var delNames []string
	currNameKeys := DefaultRedis().HKeys(define.CacheKeyCoinListByName).Val()
	for i := range currNameKeys {
		if _, ok := nameKeys[currNameKeys[i]]; !ok {
			delNames = append(delNames, currNameKeys[i])
		}
	}

	pp := DefaultRedis().TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveCoinList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyCoinListByID, strconv.Itoa(list[i].CurrencyId), data)
		pp.HSet(define.CacheKeyCoinListByName, strings.ToUpper(list[i].CurrencyName), data)
	}
	if len(delIDs) > 0 {
		pp.HDel(define.CacheKeyCoinListByID, delIDs...)
	}
	if len(delNames) > 0 {
		pp.HDel(define.CacheKeyCoinListByName, delNames...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveCoinList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllCoinList() ([]proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[[]proto.Coin](define.CacheKeyCoinListByID, func() (any, error) {
		encode, err := DefaultRedis().HVals(define.CacheKeyCoinListByID).Result()
		if err != nil {
			return nil, err
		}

		var coin proto.Coin
		list := make([]proto.Coin, 0, len(encode))
		for i := range encode {
			//log.Infof("print:%v",string(encode[i]))
			err = json.Unmarshal(convert.Str2Bytes(encode[i]), &coin)
			if err != nil {
				return nil, err
			}
			list = append(list, coin)
		}

		return list, nil
	})
}

func GetAllCoinMapByID() (map[int]proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[int]proto.Coin](define.CacheKeyCoinListByID, func() (any, error) {
		encode, err := DefaultRedis().HGetAll(define.CacheKeyCoinListByID).Result()
		if err != nil {
			log.Error("GetAllCoinMapByID redis error", zap.Error(err))
			return nil, err
		}

		var id int
		var coin proto.Coin
		mp := make(map[int]proto.Coin, len(encode))
		for key, val := range encode {
			err = json.Unmarshal(convert.Str2Bytes(val), &coin)
			if err != nil {
				log.Error("GetAllCoinMapByID json unmarshal error", zap.String("field", key), zap.Error(err))
				return nil, err
			}
			id, _ = strconv.Atoi(key)
			mp[id] = coin
		}
		return mp, nil
	})
}

func GetAllCoinMapByName() (map[string]proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.Coin](define.CacheKeyCoinListByName, func() (any, error) {
		encode, err := DefaultRedis().HGetAll(define.CacheKeyCoinListByName).Result()
		if err != nil {
			log.Error("GetAllCoinMapByName redis error", zap.Error(err))
			return nil, err
		}

		var coin proto.Coin
		mp := make(map[string]proto.Coin, len(encode))
		for key, val := range encode {
			err = json.Unmarshal(convert.Str2Bytes(val), &coin)
			if err != nil {
				log.Error("GetAllCoinMapByName json unmarshal error", zap.String("field", key), zap.Error(err))
				return nil, err
			}
			mp[key] = coin
		}
		return mp, nil
	})
}

func GetCoinByID(coinID int) (*proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.Coin](define.CacheKeyCoinListByID+strconv.Itoa(coinID), func() (any, error) {
		coin := new(proto.Coin)
		encode, err := DefaultRedis().HGet(define.CacheKeyCoinListByID, strconv.Itoa(coinID)).Bytes()
		if err != nil {
			if err != redis.Nil {
				log.Error("GetCoinByID redis error", zap.Error(err))
			}
			return coin, err
		}

		err = json.Unmarshal(encode, &coin)
		if err != nil {
			log.Error("GetCoinByID json unmarshal error", zap.Int("coinID", coinID), zap.Error(err))
			return coin, err
		}
		return coin, nil
	})
}

func GetCoinByName(coinName string) (*proto.Coin, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.Coin](define.CacheKeyCoinListByName+coinName, func() (any, error) {
		coin := new(proto.Coin)
		encode, err := DefaultRedis().HGet(define.CacheKeyCoinListByName, strings.ToUpper(coinName)).Bytes()
		if err != nil {
			if err != redis.Nil {
				log.Error("GetCoinByName redis error", zap.Error(err))
			}
			return coin, err
		}

		err = json.Unmarshal(encode, &coin)
		if err != nil {
			log.Error("GetCoinByName json unmarshal error", zap.String("coinName", coinName), zap.Error(err))
			return coin, err
		}
		return coin, nil
	})
}

var marginCoinSetLock sync.Mutex

func SaveMarginCoinList(list []proto.MarginCurrency, force bool) {
	var counter int
flag:
	if !marginCoinSetLock.TryLock() {
		if !force || counter > 10 {
			return
		}

		counter++
		time.Sleep(time.Second)
		goto flag
	}
	defer marginCoinSetLock.Unlock()

	idKeys := make(map[string]define.Placeholder, len(list))
	nameKeys := make(map[string]define.Placeholder, len(list))

	for i := range list {
		nameKeys[list[i].CurrencyName] = define.PlaceholderEntity
		idKeys[strconv.Itoa(list[i].CurrencyId)] = define.PlaceholderEntity
	}

	var delIDs []string

	currIDKeys := DefaultRedis().HKeys(define.CacheKeyMarginCoinListByID).Val()
	for i := range currIDKeys {
		if _, ok := idKeys[currIDKeys[i]]; !ok {
			delIDs = append(delIDs, currIDKeys[i])
		}
	}

	var delNames []string
	currNameKeys := DefaultRedis().HKeys(define.CacheKeyMarginCoinListByName).Val()
	for i := range currNameKeys {
		if _, ok := nameKeys[currNameKeys[i]]; !ok {
			delNames = append(delNames, currNameKeys[i])
		}
	}

	pp := DefaultRedis().TxPipeline()
	for i := range list {
		data, err := json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveMarginCoinList json error", zap.Error(err))
			return
		}

		pp.HSet(define.CacheKeyMarginCoinListByID, strconv.Itoa(list[i].CurrencyId), data)
		pp.HSet(define.CacheKeyMarginCoinListByName, strings.ToUpper(list[i].CurrencyName), data)
	}
	if len(delIDs) > 0 {
		pp.HDel(define.CacheKeyMarginCoinListByID, delIDs...)
	}
	if len(delNames) > 0 {
		pp.HDel(define.CacheKeyMarginCoinListByName, delNames...)
	}
	_, err := pp.Exec()
	if err != nil {
		log.Error("SaveMarginCoinList redis EXEC error", zap.Error(err))
		return
	}
}

func GetAllMarginCoinList() ([]proto.MarginCurrency, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[[]proto.MarginCurrency](define.CacheKeyMarginCoinListByID, func() (any, error) {
		encode, err := DefaultRedis().HVals(define.CacheKeyMarginCoinListByID).Result()
		if err != nil {
			log.Error("GetAllMarginCoinList redis error", zap.Error(err))
			return nil, err
		}

		var coin proto.MarginCurrency
		list := make([]proto.MarginCurrency, 0, len(encode))
		for i := range encode {
			//log.Infof("print:%v",string(encode[i]))
			err = json.Unmarshal(convert.Str2Bytes(encode[i]), &coin)
			if err != nil {
				log.Error("GetAllMarginCoinList json unmarshal error", zap.Error(err))
				return nil, err
			}
			list = append(list, coin)
		}

		return list, nil
	})
}

func GetAllMarginCoinMapByID() (map[int]proto.MarginCurrency, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[int]proto.MarginCurrency](define.CacheKeyMarginCoinListByID, func() (any, error) {
		encode, err := DefaultRedis().HGetAll(define.CacheKeyMarginCoinListByID).Result()
		if err != nil {
			log.Error("GetAllMarginCoinMapByID redis error", zap.Error(err))
			return nil, err
		}

		var id int
		var coin proto.MarginCurrency
		mp := make(map[int]proto.MarginCurrency, len(encode))
		for key, val := range encode {
			err = json.Unmarshal(convert.Str2Bytes(val), &coin)
			if err != nil {
				log.Error("GetAllMarginCoinMapByID json unmarshal error", zap.String("field", key), zap.Error(err))
				return nil, err
			}
			id, _ = strconv.Atoi(key)
			mp[id] = coin
		}
		return mp, nil
	})
}

func GetAllMarginCoinMapByName() (map[string]proto.MarginCurrency, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.MarginCurrency](define.CacheKeyMarginCoinListByName, func() (any, error) {
		encode, err := DefaultRedis().HGetAll(define.CacheKeyMarginCoinListByName).Result()
		if err != nil {
			log.Error("GetAllMarginCoinMapByName redis error", zap.Error(err))
			return nil, err
		}

		var coin proto.MarginCurrency
		mp := make(map[string]proto.MarginCurrency, len(encode))
		for key, val := range encode {
			err = json.Unmarshal(convert.Str2Bytes(val), &coin)
			if err != nil {
				log.Error("GetAllMarginCoinMapByName json unmarshal error", zap.String("field", key), zap.Error(err))
				return nil, err
			}
			mp[key] = coin
		}
		return mp, nil
	})
}

func GetMarginCoinByID(coinID int) (*proto.MarginCurrency, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.MarginCurrency](define.CacheKeyMarginCoinListByID+strconv.Itoa(coinID), func() (any, error) {
		coin := new(proto.MarginCurrency)
		encode, err := DefaultRedis().HGet(define.CacheKeyMarginCoinListByID, strconv.Itoa(coinID)).Bytes()
		if err != nil {
			if err != redis.Nil {
				log.Error("GetMarginCoinByID redis error", zap.Error(err))
			}
			return coin, err
		}

		err = json.Unmarshal(encode, &coin)
		if err != nil {
			log.Error("GetMarginCoinByID json unmarshal error", zap.Int("coinID", coinID), zap.Error(err))
			return coin, err
		}
		return coin, nil
	})
}

func GetMarginCoinByName(coinName string) (*proto.MarginCurrency, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.MarginCurrency](define.CacheKeyMarginCoinListByName+coinName, func() (any, error) {
		coin := new(proto.MarginCurrency)
		encode, err := DefaultRedis().HGet(define.CacheKeyMarginCoinListByName, strings.ToUpper(coinName)).Bytes()
		if err != nil {
			if err != redis.Nil {
				log.Error("GetMarginCoinByName redis error", zap.Error(err))
			}
			return coin, err
		}

		err = json.Unmarshal(encode, &coin)
		if err != nil {
			log.Error("GetMarginCoinByName json unmarshal error", zap.String("coinName", coinName), zap.Error(err))
			return coin, err
		}
		return coin, nil
	})
}

func GetCoinWalletConfByNameAndProtocol(coinName, protocol string) (proto.WalletCoinConf, error) {
	// 同一服务的同一时间内执行,合并查询
	var val proto.WalletCoinConf
	dict, err := singleflight.MergeDo[map[string]proto.WalletCoinConf](define.CacheKeyWalletCoinDictV2+coinName, func() (any, error) {
		result, err := DefaultRedis().HGet(define.CacheKeyWalletCoinDictV2, coinName).Bytes()
		if err != nil {
			return nil, err
		}
		dict := make(map[string]proto.WalletCoinConf)
		err = json.Unmarshal(result, &dict)
		if err != nil {
			return nil, err
		}
		return dict, nil
	})
	if err != nil {
		return val, err
	}

	if val, ok := dict[protocol]; ok {
		return val, nil
	}
	return val, redis.Nil
}

// map[coinID]map[proto]coinConf{}
func GetAllWalletConfDictFromCache() (map[string]map[string]proto.WalletCoinConf, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]map[string]proto.WalletCoinConf](define.CacheKeyWalletCoinDictV2, func() (any, error) {
		result, err := DefaultRedis().HGetAll(define.CacheKeyWalletCoinDictV2).Result()
		if err != nil {
			log.Errorf("GetAllWalletConfDictFromCache redis HGETALL error, err:%v", err)
			return nil, err
		}
		if len(result) == 0 {
			return nil, redis.Nil
		}

		dict := make(map[string]map[string]proto.WalletCoinConf, len(result))
		for key, val := range result {
			var conf map[string]proto.WalletCoinConf
			err = json.Unmarshal([]byte(val), &conf)
			if err != nil {
				log.Errorf("GetAllWalletConfDictFromCache json error, err:%v", err)
				return nil, err
			}
			dict[key] = conf
		}
		return dict, nil
	})
}

func SaveCoinWalletDict(dict map[string]map[string]proto.WalletCoinConf) {
	if len(dict) == 0 {
		return
	}

	var err error
	var data []byte
	pp := DefaultRedis().TxPipeline()
	for key, val := range dict {
		data, err = json.Marshal(&val)
		if err != nil {
			log.Errorf("SaveCoinWalletDict json error, err:%v", err)
			return
		}
		pp.HSet(define.CacheKeyWalletCoinDictV2, key, data)
	}
	_, err = pp.Exec()
	if err != nil {
		log.Errorf("SaveCoinWalletDict redis EXEC error, err:%v", err)
		return
	}
}

func GetWalletConfFromCache(walletName string) (proto.WalletCoinConf, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[proto.WalletCoinConf](define.CacheKeyWalletCoinConfig+walletName, func() (any, error) {
		var walletConf proto.WalletCoinConf
		result, err := DefaultRedis().HGet(define.CacheKeyWalletCoinConfig, walletName).Bytes()
		if err != nil {
			return walletConf, err
		}
		err = json.Unmarshal(result, &walletConf)
		return walletConf, err
	})
}

// map[walletName]coinConf{}
func GetAllWalletConfFromCache() (map[string]proto.WalletCoinConf, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[map[string]proto.WalletCoinConf](define.CacheKeyWalletCoinConfig, func() (any, error) {
		result, err := DefaultRedis().HVals(define.CacheKeyWalletCoinConfig).Result()
		if err != nil {
			log.Errorf("GetAllWalletConfFromCache redis HVALS error, err:%v", err)
			return nil, err
		}
		if len(result) == 0 {
			return nil, redis.Nil
		}

		var conf proto.WalletCoinConf
		dict := make(map[string]proto.WalletCoinConf, len(result))
		for i := range result {
			err = json.Unmarshal([]byte(result[i]), &conf)
			if err != nil {
				log.Errorf("GetAllWalletConfFromCache json error, err:%v", err)
				return nil, err
			}
			dict[conf.WalletName] = conf
		}
		return dict, nil
	})
}

func SaveAllWalletConf(dict map[string]map[string]proto.WalletCoinConf) {
	if len(dict) == 0 {
		return
	}

	var err error
	var data []byte
	pp := DefaultRedis().TxPipeline()
	for _, v := range dict {
		for _, vv := range v {
			data, err = json.Marshal(&vv)
			if err != nil {
				log.Errorf("SaveAllWalletConf json error, err:%v", err)
				return
			}
			pp.HSet(define.CacheKeyWalletCoinConfig, vv.WalletName, data)
		}
	}
	_, err = pp.Exec()
	if err != nil {
		log.Errorf("SaveAllWalletConf redis EXEC error, err:%v", err)
		return
	}
}
