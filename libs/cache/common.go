package cache

import (
	"fmt"
	"strconv"
	"sync"
	"time"

	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/singleflight"
	"bc/libs/utils"
	//"bc/pkg/core/model"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

func SetAuthCode(account string, code *proto.AuthCodeInfo) error {
	data, err := json.Marshal(code)
	if err != nil {
		return err
	}

	err = DefaultRedis().Set(utils.StrBuilder(define.CacheKeyAuthCode, account), data, define.AuthCodeExpireTime).Err()
	return err
}

func GetAuthCode(key string) (*proto.AuthCodeInfo, error) {
	data, err := DefaultRedis().Get(key).Bytes()
	if err != nil {
		return nil, err
	}

	codeData := new(proto.AuthCodeInfo)
	err = json.Unmarshal(data, codeData)
	return codeData, err
}

// MarkUserAction 校验短信验证码后,redis存储对应的用户操作
func MarkUserAction(account string, action uint8) error {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserAction, account, strconv.Itoa(int(action)))
	err := DefaultRedis().Set(key, account, define.SensitiveActionExpTime).Err()
	return err
}

// 确认用户标记的敏感操作
func CheckUserAction(reqID int64, account string, action uint8, clean bool) error {
	key := utils.StrBuilderBySep(":", define.CacheKeyUserAction, account, strconv.Itoa(int(action)))
	result, err := DefaultRedis().Exists(key).Result()
	if err != nil {
		log.Error("CheckUserAction redis error", zap.Int64("reqID", reqID), zap.Error(err))
		return define.ErrMsgBusy
	}
	if result != 1 {
		log.Error("CheckUserAction time out", zap.Int64("reqID", reqID))
		return define.ErrMsgActionTimeout
	}

	if clean {
		DelKey(key)
	}
	return nil
}

func CacheFollowConfig(cfg *proto.FollowConf) {
	data, err := json.Marshal(cfg)
	if err != nil {
		log.Error("CacheFollowConfig json marshal fail", zap.Error(err))
		return
	}

	err = DefaultRedis().Set(define.CacheKeyFollowConfig, data, 0).Err()
	if err != nil {
		log.Error("CacheFollowConfig redis error", zap.Error(err))
		return
	}
}

func GetFollowConfig() (*proto.FollowConf, error) {
	// 同一服务的同一时间内执行,合并查询
	return singleflight.MergeDo[*proto.FollowConf](define.CacheKeyFollowConfig, func() (any, error) {
		data, err := DefaultRedis().Get(define.CacheKeyFollowConfig).Bytes()
		if err != nil {
			log.Error("GetFollowConfig redis error", zap.Error(err))
			return nil, err
		}
		var cfg proto.FollowConf
		err = json.Unmarshal(data, &cfg)
		return &cfg, err
	})
}

func CacheHotfixPatch(dict map[define.OsType]map[int][]proto.HotfixPatch) {
	var err error
	var data []byte
	for os, patch := range dict {
		key := define.CacheKeyHotfixPatch + strconv.Itoa(int(os))
		for version, list := range patch {
			data, err = json.Marshal(list)
			if err != nil {
				log.Error("CacheHotfixPatch json marshal fail", zap.Error(err))
				continue
			}
			err = DefaultRedis().HSet(key, strconv.Itoa(version), data).Err()
			if err != nil {
				log.Error("CacheHotfixPatch redis fail", zap.String("key", key), zap.Int("version", version), zap.Error(err))
				continue
			}
		}
	}
}

func GetHotfixPatch(os define.OsType, version string) ([]proto.HotfixPatch, error) {
	// 同一服务的同一时间内执行,合并查询
	key := define.CacheKeyHotfixPatch + strconv.Itoa(int(os))
	return singleflight.MergeDo[[]proto.HotfixPatch](key+version, func() (any, error) {
		data, err := DefaultRedis().HGet(key, version).Bytes()
		if err != nil && err != redis.Nil {
			return nil, err
		}
		err = nil

		list := make([]proto.HotfixPatch, 0)
		if len(data) > 0 {
			err = json.Unmarshal(data, &list)
		}
		return list, err
	})
}

//func GetServerConfigHosts() ([]model.ServerHost, error) {
//	// 同一服务的同一时间内执行,合并查询
//	return singleflight.MergeDo[[]model.ServerHost](define.CacheKeyServerConfigHostsV2, func() (any, error) {
//		l, err := DefaultRedis().LRange(define.CacheKeyServerConfigHostsV2, 0, -1).Result()
//		if err != nil {
//			return nil, err
//		}
//		var (
//			host  model.ServerHost
//			hosts = make([]model.ServerHost, 0, len(l))
//		)
//		for i := range l {
//			err = json.Unmarshal([]byte(l[i]), &host)
//			if err != nil {
//				return nil, err
//			}
//
//			hosts = append(hosts, host)
//		}
//
//		return hosts, nil
//	})
//}

//func GetServerConfig() (*model.ServerConfig, error) {
//	// 同一服务的同一时间内执行,合并查询
//	return singleflight.MergeDo[*model.ServerConfig](define.CacheKeyServerConfigCommon, func() (any, error) {
//		data, err := DefaultRedis().Get(define.CacheKeyServerConfigCommon).Bytes()
//		if err != nil {
//			return nil, err
//		}
//
//		var cfg model.ServerConfig
//		err = json.Unmarshal(data, &cfg)
//		if err != nil {
//			return nil, err
//		}
//		return &cfg, nil
//	})
//}

//func GetSliderSwitchOfServerConfig() bool {
//	cfg, err := GetServerConfig()
//	if err != nil {
//		return false
//	}
//	return cfg.IsSlider
//}

//func GetThirdKYCSwitchOfServerConfig() bool {
//	cfg, err := GetServerConfig()
//	if err != nil {
//		return false
//	}
//	return cfg.IsThirdKYC
//}

func SaveOnlineUserStatistic(arg *define.ReqArg) {
	var member string
	switch arg.ReqOs {
	case define.OsAll:
		return
	case define.OsAndroid, define.OsIos:
		member = arg.DeviceID
	default:
		member = arg.DeviceID + arg.ReqIP
	}
	if len(member) == 0 {
		return
	}

	dayKey := fmt.Sprintf(define.CacheKeyOnlineUserStatistic, time.Now().Format(define.TimeFormatDayKey), arg.ReqOs)
	defer DefaultRedis().Expire(dayKey, time.Hour*48)
	DefaultRedis().SAdd(dayKey, member)
	HourKey := fmt.Sprintf(define.CacheKeyOnlineUserStatistic, time.Now().Format(define.TimeFormatHourKey), arg.ReqOs)
	defer DefaultRedis().Expire(HourKey, time.Hour*48)
	DefaultRedis().SAdd(HourKey, member)
}

func SaveOnlineUserStatisticWithUser(user *define.TokenPayload) {
	if user == nil || user.UserID == 0 {
		return
	}
	member := strconv.FormatInt(user.UserID, 10) + "-" + user.InviteParent
	dayKey := fmt.Sprintf(define.CacheKeyOnlineUserStatisticWithUser, time.Now().Format(define.TimeFormatDayKey), user.PlatformID)
	defer DefaultRedis().Expire(dayKey, time.Hour*48)
	DefaultRedis().SAdd(dayKey, member)
	HourKey := fmt.Sprintf(define.CacheKeyOnlineUserStatisticWithUser, time.Now().Format(define.TimeFormatHourKey), user.PlatformID)
	defer DefaultRedis().Expire(HourKey, time.Hour*48)
	DefaultRedis().SAdd(HourKey, member)
}

func SaveValidVersion(list []proto.VersionDetail) {
	var err error
	var key string
	var data []byte

	pip := DefaultRedis().TxPipeline()
	for i := range list {
		key = fmt.Sprintf(define.CacheKeyNewVersion, list[i].PlatformID, list[i].ChannelID, list[i].LangType)
		data, err = json.Marshal(&list[i])
		if err != nil {
			log.Error("SaveValidVersion json marshal error", zap.Error(err))
			return
		}
		pip.HSet(key, strconv.Itoa(int(list[i].OSType)), data)
	}
	_, err = pip.Exec()
	if err != nil {
		log.Error("SaveValidVersion redis error", zap.Error(err))
	}
}

func GetValidVersion(platformID int, channelID define.AppChannel, os define.OsType, lang define.ReqLang) (*proto.VersionDetail, error) {
	// 同一服务的同一时间内执行,合并查询
	key := fmt.Sprintf(define.CacheKeyNewVersion, platformID, channelID, lang)
	return singleflight.MergeDo[*proto.VersionDetail](key, func() (any, error) {
		res, err := DefaultRedis().HGet(key, strconv.Itoa(int(os))).Bytes()
		if err != nil {
			if err == redis.Nil {
				return nil, nil
			}
			return nil, err
		}

		var v proto.VersionDetail
		err = json.Unmarshal(res, &v)
		return &v, err
	})
}

func GetAppVersionStore(platformID int) (map[string]string, error) {
	// 同一服务的同一时间内执行,合并查询
	key := define.CacheKeyAppVersionStore + strconv.Itoa(platformID)
	return singleflight.MergeDo[map[string]string](key, func() (any, error) {
		mp, err := DefaultRedis().HGetAll(key).Result()
		if err == redis.Nil {
			return make(map[string]string), nil
		}
		return mp, err
	})
}

var saveShareImage sync.Mutex

func SaveShareImageResource(list map[int]map[int8]map[define.ReqLang][]proto.ShareImage) {
	if !saveShareImage.TryLock() {
		return
	}
	defer saveShareImage.Unlock()

	var key string
	pipe := DefaultRedis().TxPipeline()
	defer func() { _ = pipe.Close() }()

	delDict := proto.NewShareResourceCacheDelete()
	for typeKey, typeVal := range list {
		for levelKey, levelVal := range typeVal {
			key = utils.StrBuilderBySep(":", define.CacheKeyShareImages, strconv.Itoa(typeKey), strconv.Itoa(int(levelKey)))
			for langKey, langVal := range levelVal {
				delDict.DelLang(typeKey, levelKey, langKey)
				d, err := json.Marshal(langVal)
				if err != nil {
					log.Error("SaveShareImageResource json marshal error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}

				err = pipe.HSet(key, langKey.String(), d).Err()
				if err != nil {
					log.Error("SaveShareImageResource save date error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}
			}
		}
	}

	// 本次更新不存在的key进行删除
	for typeKey, typeVal := range delDict.Final() {
		for levelKey, levelVal := range typeVal {
			key = utils.StrBuilderBySep(":", define.CacheKeyShareImages, strconv.Itoa(typeKey), strconv.Itoa(int(levelKey)))
			for langKey, langVal := range levelVal {
				err := pipe.HDel(key, langKey.String()).Err()
				if err != nil {
					log.Error("SaveShareImageResource clean error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}
			}
		}
	}

	_, err := pipe.Exec()
	if err != nil {
		log.Error("SaveShareImageResource Exec error", zap.Error(err))
		return
	}
	return
}

func GetShareImageResource(shareType int, level int8, language define.ReqLang) ([]proto.ShareImage, error) {
	// 同一服务的同一时间内执行,合并查询
	key := utils.StrBuilderBySep(":", define.CacheKeyShareImages, strconv.Itoa(shareType), strconv.Itoa(int(level)))
	return singleflight.MergeDo[[]proto.ShareImage](key+language.String(), func() (any, error) {
		res, err := DefaultRedis().HMGet(key, language.String(), define.ReqLangEN.String()).Result()
		if err != nil {
			return nil, err
		}
		for _, val := range res {
			if val == nil {
				continue
			}
			switch v := val.(type) {
			case string:
				var list []proto.ShareImage
				err = json.Unmarshal([]byte(v), &list)
				return list, err
			default:
			}
		}
		return nil, nil
	})
}

var saveShareText sync.Mutex

func SaveShareTextResource(list map[int]map[int8]map[define.ReqLang][]proto.ShareText) {
	if !saveShareText.TryLock() {
		return
	}
	defer saveShareText.Unlock()

	var key string
	pipe := DefaultRedis().TxPipeline()
	defer func() { _ = pipe.Close() }()

	delDict := proto.NewShareResourceCacheDelete()
	for typeKey, typeVal := range list {
		for levelKey, levelVal := range typeVal {
			key = utils.StrBuilderBySep(":", define.CacheKeyShareTexts, strconv.Itoa(typeKey), strconv.Itoa(int(levelKey)))
			for langKey, langVal := range levelVal {
				delDict.DelLang(typeKey, levelKey, langKey)
				d, err := json.Marshal(langVal)
				if err != nil {
					log.Error("SaveShareTextResource json marshal error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}

				err = pipe.HSet(key, langKey.String(), d).Err()
				if err != nil {
					log.Error("SaveShareTextResource save date error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}
			}
		}
	}

	// 本次更新不存在的key进行删除
	for typeKey, typeVal := range delDict.Final() {
		for levelKey, levelVal := range typeVal {
			key = utils.StrBuilderBySep(":", define.CacheKeyShareTexts, strconv.Itoa(typeKey), strconv.Itoa(int(levelKey)))
			for langKey, langVal := range levelVal {
				err := pipe.HDel(key, langKey.String()).Err()
				if err != nil {
					log.Error("SaveShareTextResource clean error",
						zap.Any("langVal", langVal), zap.Error(err))
					return
				}
			}
		}
	}

	_, err := pipe.Exec()
	if err != nil {
		log.Error("SaveShareTextResource Exec error", zap.Error(err))
		return
	}
	return
}

func GetShareTextResource(shareType int, level int8, language define.ReqLang) ([]proto.ShareText, error) {
	// 同一服务的同一时间内执行,合并查询
	key := utils.StrBuilderBySep(":", define.CacheKeyShareTexts, strconv.Itoa(shareType), strconv.Itoa(int(level)))
	return singleflight.MergeDo[[]proto.ShareText](key+language.String(), func() (any, error) {
		res, err := DefaultRedis().HMGet(key, language.String(), define.ReqLangEN.String()).Result()
		if err != nil {
			return nil, err
		}

		for _, val := range res {
			if val == nil {
				continue
			}
			switch v := val.(type) {
			case string:
				var list []proto.ShareText
				err = json.Unmarshal([]byte(v), &list)
				return list, err
			default:
			}
		}
		return nil, nil
	})
}
