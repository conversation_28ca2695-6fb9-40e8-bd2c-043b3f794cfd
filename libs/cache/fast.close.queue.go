package cache

import (
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
)

// SetFastCloseQueue  将强平项目放入队列
func SetFastCloseQueue(item *proto.FastCloseItem) (err error) {
	key := define.CacheFastCloseQueue
	filed := utils.StrBuilderBySep(":", nums.Int64String(item.UserId), item.ContractCode)
	b, err := json.Marshal(item)
	if err != nil {
		log.Error("SetForceQueue json marshal fail", zap.Error(err))
		return
	}

	err = DefaultRedis().HSet(key, filed, b).Err()
	return
}

//  获取指定合约的强平项目
func GetFastCloseQueueList() (list []proto.FastCloseItem, err error) {
	key := define.CacheFastCloseQueue
	s, err := DefaultRedis().HVals(key).Result()
	if err != nil {
		log.Errorf("GetForceQueueList fail,%v,code：%v", err)
	}
	for _, c := range s {
		fi := new(proto.FastCloseItem)
		err = json.Unmarshal([]byte(c), fi)
		if err != nil {
			continue
		}
		list = append(list, *fi)
	}
	return
}

// 获取指定强平项目
func GetFastCloseQueue(userId int64, contractCode string) (item *proto.FastCloseItem, err error) {
	key := define.CacheFastCloseQueue
	filed := utils.StrBuilderBySep(":", nums.Int64String(userId), contractCode)
	s, err := DefaultRedis().HGet(key, filed).Bytes()
	if err != nil {
		log.Errorf("GetForceQueue fail,%v", err)
		if err == redis.Nil {
			err = nil
		}
		return
	}
	fi := new(proto.FastCloseItem)
	err = json.Unmarshal(s, fi)
	if err != nil {
		return
	}
	item = fi
	return
}

// 移除自动的项目
func DelFastCloseQueueList(userId int64, contractCode string) {
	key := define.CacheFastCloseQueue
	filed := utils.StrBuilderBySep(":", nums.Int64String(userId), contractCode)
	err := DefaultRedis().HDel(key, filed).Err()
	if err != nil {
		log.Error("DelForceQueueList", zap.String("filed", filed))
		return
	}
	return
}
