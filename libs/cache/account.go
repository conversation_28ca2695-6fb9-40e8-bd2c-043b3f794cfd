package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//交易账户缓存，资金变化请修改缓存

func ClearUserPos() {
	list, err := DefaultRedis().Keys("bc:contract:positions:*").Result()
	if err != nil {
		log.Errorf("keys bc:contract:positions:  fail,%v", err)
		return
	}
	for _, s := range list {
		err = DefaultRedis().Del(s).Err()
		if err != nil {
			log.Errorf("=DefaultRedis().Del(%v).Err() fail,%v", s, err)
			continue
		}
	}
}

//修改交易账户账户余额
func UpdateContractAccount(account *proto.Account) {
	log.Infof("开始修改交易账户缓存：%+v", *account)
	if account == nil {
		log.Errorf("修改合约账户缓存，账户为空")
		return
	}
	key := define.CacheKeyContractKey
	b, err := json.Marshal(account)
	if err != nil {
		log.Errorf("UpdateContractAccount userID:%d, fail,%v", account.UserId, err)
		return
	}
	err = DefaultRedis().HSet(key, convert.Int64String(account.UserId), b).Err()
	if err != nil {
		log.Errorf("UpdateContractAccount userID:%d, fail,%v", account.UserId, err)
		return
	}
	//a, err := GetContractAccount(account.UserId)
	//if err != nil {
	//	log.Infof("查询账户缓存失败:%v,err", account.UserId, err)
	//	return
	//}
	//if a != nil {
	//	log.Infof("用户账户缓存：%+v", a)
	//
	//}

}

//获取指定交易账户资产
func GetContractAccount(userId int64) (account *proto.Account, err error) {
	id := convert.Int64String(userId)
	key := define.CacheKeyContractKey
	if id == "" {
		return nil, nil
	}
	b, err := DefaultRedis().HGet(key, id).Bytes()
	if err != nil {
		log.Errorf("GetContractAccount userID:%d, fail,%v", userId, err)
		return
	}
	a := new(proto.Account)
	err = json.Unmarshal(b, a)
	if err != nil {
		log.Errorf("GetContractAccount unmarshal userID:%d, fail,%v", userId, err)
		return
	}
	account = a
	return
}

//修改交易账户账户余额
func UpdateUsdContractAccount(account *proto.UsdTradeAsset) {
	log.Infof("开始修改交易账户缓存：%+v", *account)
	if account == nil {
		log.Errorf("修改合约账户缓存，账户为空")
		return
	}
	key := define.CacheKeyContractKey + ":" + nums.Int64String(account.UserId)
	b, err := json.Marshal(account)
	if err != nil {
		log.Errorf("UpdateUsdContractAccount userID:%d, fail,%v", account.UserId, err)
		return
	}
	err = DefaultRedisWithDB(define.CacheDBNumber4).HSet(key, account.CurrencyName, b).Err()
	if err != nil {
		log.Errorf("UpdateUsdContractAccount userID:%d, fail,%v", account.UserId, err)
		return
	}
}

//获取所有合约账户
func ListContactAccount() (map[int64]proto.Account, error) {
	key := define.CacheKeyContractKey
	b, err := DefaultRedis().HGetAll(key).Result()
	if err != nil {
		log.Errorf("ListContactAccount fail,%v", err)
		return nil, err
	}
	m := make(map[int64]proto.Account)
	for key, val := range b {
		userId := convert.String2Int64(key)
		a := new(proto.Account)
		err = json.Unmarshal([]byte(val), a)
		if err != nil {
			log.Errorf("ListContactAccount unmarshal fail,%v", err)
			return nil, err
		}
		m[userId] = *a

	}
	return m, nil
}

func AddFullUserContractForcePrice(userId int64, forceMap map[string]string) {
	key := utils.StrBuilderBySep(":", define.CacheKeyFullAccountForcePrice, nums.Int64String(userId))
	b, err := json.Marshal(forceMap)
	if err != nil {
		log.Error("AddFullUserContractForcePrice json marsh")
		return
	}
	err = DefaultRedis().Set(key, b, 0).Err()
	if err != nil {
		log.Errorf("AddFullAccountRiskRate userID:%d, fail,%v", userId, err)
		return
	}
}

//获取全仓用户合约强平价
func GetFullUserContractForcePrice(userId int64) (forceMap map[string]string) {
	forceMap = make(map[string]string)
	key := utils.StrBuilderBySep(":", define.CacheKeyFullAccountForcePrice, nums.Int64String(userId))
	rsp, err := DefaultRedis().Get(key).Bytes()
	if err != nil {
		if err == redis.Nil {
			err = nil
		} else {
			log.Errorf("GetFullAccountRiskRate userID:%d, fail,%v", userId, err)
		}
		return
	}
	err = json.Unmarshal(rsp, &forceMap)
	if err != nil {
		log.Error("GetFullUserContractForcePrice fail json Unmarshal fail", zap.Error(err))
		return
	}
	return
}

func RemoveFullUserContractForcePrice(userId int64) {
	key := utils.StrBuilderBySep(":", define.CacheKeyFullAccountForcePrice, nums.Int64String(userId))
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("RemoveFullAccountRiskRate userID:%d, fail,%v", userId, err)
	}
}

func RemoveUsdFullUserContractForcePrice(userId int64) {
	key := utils.StrBuilderBySep(":", define.CacheKeyFullAccountForcePrice, nums.Int64String(userId))
	err := DefaultRedisWithDB(define.CacheDBNumber4).Del(key).Err()
	if err != nil {
		log.Errorf("RemoveFullAccountRiskRate userID:%d, fail,%v", userId, err)
	}
}

//修改用户风险率
func AddFullAccountRiskRate(userID int64, riskRate decimal.Decimal) {
	key := define.CacheKeyFullRiskRate
	err := DefaultRedis().HSet(key, convert.Int64String(userID), riskRate.StringFixedBank(4)).Err()
	if err != nil {
		log.Errorf("AddFullAccountRiskRate userID:%d, fail,%v", userID, err)
	}
}

//移除账户风险率
//func RemoveFullAccountRiskRate(userID int64) {
//	key := define.CacheKeyFullRiskRate
//	err := DefaultRedis().HDel(key, convert.Int64String(userID)).Err()
//	if err != nil {
//		log.Errorf("RemoveFullAccountRiskRate userID:%d, fail,%v", userID, err)
//	}
//}

//获取账户风险率
func GetFullAccountRiskRate(userId int64) (risk string, err error) {
	key := define.CacheKeyFullRiskRate
	risk, err = DefaultRedis().HGet(key, convert.Int64String(userId)).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		} else {
			log.Errorf("GetFullAccountRiskRate userID:%d, fail,%v", userId, err)
		}
		return
	}
	return
}

func DelFullAccountRiskRate(userId int64) (err error) {
	RemoveFullUserContractForcePrice(userId) //移除用户合约强平价格
	key := define.CacheKeyFullRiskRate
	err = DefaultRedis().HDel(key, convert.Int64String(userId)).Err()
	if err != nil {
		log.Errorf("DelFullAccountRiskRate userID:%d, fail,%v", userId, err)
		return
	}
	return
}

func GetUsdFullAccountRiskRate(userId int64, currencyName string) (risk string, err error) {
	key := define.CacheKeyFullRiskRate + ":" + convert.Int64String(userId)
	risk, err = DefaultRedisWithDB(define.CacheDBNumber4).HGet(key, currencyName).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		} else {
			log.Errorf("GetUsdFullAccountRiskRate userID:%d, fail,%v", userId, err)
		}
		return
	}
	return
}

func DelUsdFullAccountRiskRate(userId int64, currencyName string) (err error) {
	RemoveUsdFullUserContractForcePrice(userId) //移除用户合约强平价格
	key := define.CacheKeyFullRiskRate + ":" + convert.Int64String(userId)
	err = DefaultRedisWithDB(define.CacheDBNumber4).HDel(key, currencyName).Err()
	if err != nil {
		log.Errorf("DelUsdFullAccountRiskRate userID:%d, fail,%v", userId, err)
		return
	}
	return
}

//
////修改用户合约风险率
//func AddFullAccountContractRiskRate(userID int64, contract string, riskRate decimal.Decimal) {
//	key := define.CacheKeyContractRiskRate
//	filed := convert.Int64String(userID) + ":" + contract
//	err := DefaultRedis().HSet(key, filed, riskRate.String()).Err()
//	if err != nil {
//		log.Errorf("AddFullAccountRiskRate fail,%v", err)
//	}
//}
//
////移除账户合约风险率
//func RemoveFullAccountContractRiskRate(userID int64, contract string) {
//	key := define.CacheKeyContractRiskRate
//	filed := convert.Int64String(userID) + ":" + contract
//
//	err := DefaultRedis().HDel(key, filed).Err()
//	if err != nil {
//		log.Errorf("RemoveFullAccountRiskRate fail,%v", err)
//	}
//}
//
////获取账户合约风险率
//func GetFullAccountContractRiskRate(userID int64, contract string) (risk string, err error) {
//	key := define.CacheKeyContractRiskRate
//	filed := convert.Int64String(userID) + ":" + contract
//	risk, err = DefaultRedis().HGet(key, filed).Result()
//	if err != nil {
//		log.Errorf("GetFullAccountRiskRate fail,%v", err)
//		return
//	}
//	return
//}

////修改持仓强平价
//func AddPositionForcePrice(posId int64, forcePrice decimal.Decimal) {
//	key := define.CacheKeyPositionForcePrice
//	err := DefaultRedis().HSet(key, convert.Int64String(posId), forcePrice.String()).Err()
//	if err != nil {
//		log.Errorf("AddPositionForcePrice posId:%d, fail,%v", posId, err)
//	}
//}
//
////移除持仓强平价
//func RemovePositionForcePrice(posId int64) {
//	key := define.CacheKeyPositionForcePrice
//	err := DefaultRedis().HDel(key, convert.Int64String(posId)).Err()
//	if err != nil {
//		log.Errorf("RemovePositionForcePrice posId:%d, fail,%v", posId, err)
//	}
//}
//
////获取持仓强平价
//func GetPositionForcePrice(posId int64) (risk string, err error) {
//	key := define.CacheKeyPositionForcePrice
//	risk, err = DefaultRedis().HGet(key, convert.Int64String(posId)).Result()
//	if err != nil {
//		log.Errorf("GetPositionForcePrice posId:%d, fail,%v", posId, err)
//		return
//	}
//	return
//}

const (
	_doubleKeyResultKey1Locked = 0 // key1锁定中
	_doubleKeyResultKey2Locked = 1 // key2锁定中
	_doubleKeyResultAllDone    = 2 // 全部成功
)
const _atomicCheckAndSetDiffLock = `
	local key1 = KEYS[1]
	local key2 = KEYS[2]
	local val = ARGV[1]

	-- 判断key1是否存在
	local cur = redis.call("EXISTS", key1)
	if cur == 1 then
		return 0
	end

	-- 锁定key2
	local flag = redis.call("GETSET", key2, 1)
	-- 设置超时时间
	redis.call("EXPIRE", key2, val)
	if flag == false then
		-- key原先不存在的情况,相当于加锁成功
		return 2
	end
	return 1
`

// AtomicCheckAndSetDiffLock 原子检查锁A然后上锁B
func AtomicCheckAndSetDiffLock(cli *redis.Client, lockA, lockB string, exp int) (bool, bool, error) {
	val, err := cli.Eval(_atomicCheckAndSetDiffLock, []string{lockA, lockB}, exp).Int()
	return val > _doubleKeyResultKey1Locked, val == _doubleKeyResultAllDone, err
}
