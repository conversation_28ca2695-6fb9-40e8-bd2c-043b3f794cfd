package cache

import (
	"bc/libs/convert"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"time"
)

func AddPosition(u *proto.UserPosition) {
	u.UpdateTime = time.Now()
	key := utils.StrBuilder(define.CacheKeyPosition, u.UserIDToString())
	b, err := json.Marshal(u)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(key, u.IDToString(), b).Err()
	if err != nil {
		log.Error("AddPosition redis error", zap.Int64("posId", u.Id), zap.Error(err))
		return
	}

	err = DefaultRedis().HSet(define.CacheKeyPositionUser, u.UserIDToString(), nil).Err()
	if err != nil {
		log.Error("设置持仓账户缓存失败", zap.Int64("useriD", u.UserId), zap.Error(err))
		return
	}
}

func GetPositionUsers() (l []int64, e error) {
	list, e := DefaultRedis().HKeys(define.CacheKeyPositionUser).Result()
	if e != nil {
		return
	}
	for _, s := range list {
		if s == "" {
			continue
		}
		l = append(l, convert.String2Int64(s))
	}
	return
}

func GetPosition(userId, posID int64) (u *proto.UserPosition, err error) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGet(key, convert.Int64String(posID)).Bytes()
	if err != nil && err != redis.Nil {
		log.Error("GetWithdrawTaskList redis error", zap.Error(err))
		return nil, err
	}
	p := new(proto.UserPosition)
	err = json.Unmarshal(result, p)
	if err != nil {
		return
	}
	return p, nil
}

//查询持仓列表
func ListPositions(userId int64) (list []proto.UserPosition) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetPositions redis error", zap.Error(err))
		return
	}
	for _, val := range result {
		up := new(proto.UserPosition)
		err = json.Unmarshal([]byte(val), up)
		if err != nil {
			log.Errorf("json unmarshl fail,%v", err)
			return nil
		}
		list = append(list, *up)
	}
	return
}

//查询持仓列表
func ListUsdPositions(userId int64) (list []proto.UserUsdPosition) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	result, err := DefaultRedisWithDB(define.CacheDBNumber4).HGetAll(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("ListUsdPositions redis error", zap.Error(err))
		return
	}
	for _, val := range result {
		up := new(proto.UserUsdPosition)
		err = json.Unmarshal([]byte(val), up)
		if err != nil {
			log.Errorf("ListUsdPositions json unmarshl fail,%v", err)
			return nil
		}
		list = append(list, *up)
	}
	return
}

//查询缓存持仓，以持仓id分组
func GetPositionsGroupByPosId(userId int64) (m map[int64]proto.UserPosition) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetPositions redis error", zap.Error(err))
		return
	}
	m = make(map[int64]proto.UserPosition)
	for key, val := range result {
		up := new(proto.UserPosition)
		err = json.Unmarshal([]byte(val), up)
		if err != nil {
			log.Errorf("json unmarshl fail,%v", err)
			return nil
		}
		m[convert.String2Int64(key)] = *up
	}
	return m
}

func ClearUserCachePositions(userId int64) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("ClearUserCachePositions fail,%v", err)
		return
	}
}

func ClearFollowUserCachePositions(userId int64) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userId))
	err := DefaultRedis().Del(key).Err()
	if err != nil {
		log.Errorf("ClearUserCachePositions fail,%v", err)
		return
	}
}

func CountPositions(userId int64) (count int64, err error) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userId))
	count, err = DefaultRedis().HLen(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("CountPositions redis error", zap.Error(err))
		return
	}
	return
}

func RemovePosUser(userId int64) {
	err := DefaultRedis().HDel(define.CacheKeyPositionUser, convert.Int64String(userId)).Err()
	if err != nil {
		log.Error("移除持仓账户缓存失败", zap.Int64("userId", userId), zap.Error(err))
		return
	}
}

func DelUserPosition(userID, posID int64) (err error) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userID))
	err = DefaultRedis().HDel(key, convert.Int64String(posID)).Err()
	if err != nil {
		log.Errorf("RemoveUserPosition redis error:%v", err)
		return err
	}
	return
}

func RemoveUserPosition(userID, posID int64) (err error) {
	key := utils.StrBuilder(define.CacheKeyPosition, convert.Int64String(userID))
	err = DefaultRedis().HDel(key, convert.Int64String(posID)).Err()
	if err != nil {
		log.Errorf("RemoveUserPosition redis error:%v", err)
		return err
	}
	count, err := CountPositions(userID)
	if err != nil {
		log.Error("RemoveUserPosition CountPositions fail", zap.Error(err))
		return
	}
	if count == 0 {
		//移除缓存用户
		err = DefaultRedis().HDel(define.CacheKeyPositionUser, convert.Int64String(userID)).Err()
		if err != nil {
			log.Error("移除持仓账户缓存失败", zap.Int64("userId", userID), zap.Error(err))
			return
		}
		//移除全仓账户风险率
		DelFullAccountRiskRate(userID)
	}
	return
}

func AddUserFollowPosition(u *proto.FollowPosition) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(u.UserId))
	b, err := json.Marshal(u)
	if err != nil {
		log.Errorf("json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(key, convert.Int64String(u.Id), b).Err()
	if err != nil {
		log.Error("AddFollowPosition redis error", zap.Int64("posId", u.Id), zap.Error(err))
		return
	}

	//设置跟单持仓用户
	err = DefaultRedis().HSet(define.CacheKeyFollowPositionUser, convert.Int64String(u.UserId), nil).Err()
	if err != nil {
		log.Error("设置持仓账户缓存失败", zap.Int64("useriD", u.UserId), zap.Error(err))
		return
	}
}

func GetFollowPositionUsers() (l []int64, e error) {
	list, e := DefaultRedis().HKeys(define.CacheKeyFollowPositionUser).Result()
	if e != nil {
		return
	}
	for _, s := range list {
		l = append(l, convert.String2Int64(s))
	}
	return
}

func GetUserFollowPosition(userId, posID int64) (u *proto.FollowPosition, err error) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGet(key, convert.Int64String(posID)).Bytes()
	if err != nil && err != redis.Nil {
		log.Error("GetWithdrawTaskList redis error", zap.Error(err))
		return nil, err
	}
	p := new(proto.FollowPosition)
	err = json.Unmarshal(result, p)
	if err != nil {
		log.Errorf("GetFollowPosition fail,%v", err)
		return
	}
	return p, nil
}

//查询持仓列表
func ListUserFollowPositions(userId int64) (list []proto.FollowPosition) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("ListUserFollowPositions redis error", zap.Error(err))
		return
	}
	for _, val := range result {
		up := new(proto.FollowPosition)
		err = json.Unmarshal([]byte(val), up)
		if err != nil {
			log.Errorf("json unmarshl fail,%v", err)
			return nil
		}
		list = append(list, *up)
	}
	return
}

func GetUserFollowPositions(userId int64) (m map[int64]proto.FollowPosition) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userId))
	result, err := DefaultRedis().HGetAll(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("GetPositions redis error", zap.Error(err))
		return
	}
	m = make(map[int64]proto.FollowPosition)
	for key, val := range result {
		up := new(proto.FollowPosition)
		err = json.Unmarshal([]byte(val), up)
		if err != nil {
			log.Errorf("json unMarshal fail,%v", err)
			return nil
		}
		m[convert.String2Int64(key)] = *up
	}
	return m
}

func CountFollowPositions(userId int64) (count int64, err error) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userId))
	count, err = DefaultRedis().HLen(key).Result()
	if err != nil && err != redis.Nil {
		log.Error("CountFollowPositions redis error", zap.Error(err))
		return
	}
	return
}

func DelUserFollowPosition(userID, posID int64) (err error) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userID))
	err = DefaultRedis().HDel(key, convert.Int64String(posID)).Err()
	if err != nil {
		log.Errorf("RemoveUserPosition redis error:%v", err)
		return err
	}
	return
}

func RemoveUserFollowPosition(userID, posID int64) (err error) {
	key := utils.StrBuilder(define.CacheKeyFollowPosition, convert.Int64String(userID))
	err = DefaultRedis().HDel(key, convert.Int64String(posID)).Err()
	if err != nil {
		log.Errorf("RemoveUserPosition redis error:%v", err)
		return err
	}
	count, err := CountFollowPositions(userID)
	if err != nil {
		return
	}
	if count == 0 {
		err = DefaultRedis().HDel(define.CacheKeyFollowPositionUser, convert.Int64String(userID)).Err()
		if err != nil {
			log.Error("移除跟单持仓账户缓存失败", zap.Int64("userId", userID), zap.Error(err))
			return
		}

	}
	return
}

func AddContractCurrentTrade(order *proto.MatchOrder) {
	key := utils.StrBuilder(define.CacheKeyContractCurrentTrade, ":", order.ContractCode)
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("AddContractCurrentTrade json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().LPush(key, b).Err()
	if err != nil {
		log.Errorf("AddContractCurrentTrade lpush fail,%v", err)
		return
	}
	err = DefaultRedis().LTrim(key, 0, 20).Err()
	if err != nil {
		log.Errorf("AddContractCurrentTrade ltrim fail,%v", err)
		return
	}
}

//获取指定合约最新成交
func ListContractCurrentTrade(code string) (list []proto.MatchOrder) {
	key := utils.StrBuilder(define.CacheKeyContractCurrentTrade, ":", code)
	data, err := DefaultRedis().LRange(key, 0, 20).Result()
	if err != nil {
		log.Errorf("ListContractCurrentTrade lrange fail,%v", err)
		return
	}
	for _, datum := range data {
		o := new(proto.MatchOrder)
		err = json.Unmarshal([]byte(datum), o)
		if err != nil {
			log.Errorf("ListContractCurrentTrade json unmarshal fail,%v", err)
			continue
		}
		list = append(list, *o)
	}
	return
}

func IncrContractNetPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractNetPosCount, code, count).Result()
	return
}

func DcrContractNetPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractNetPosCount, code, -count).Result()
	return
}

func SetContractNetPosition(code string, count int64) (err error) {
	err = DefaultRedis().HSet(define.CacheKeyContractNetPosCount, code, convert.Int64String(count)).Err()
	return
}

func GetContractNetPosition(code string) (result int64, err error) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractNetPosCount, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractNetPosition fail,%v", err)
		}
		return
	}
	result = convert.String2Int64(s)
	return
}

func IncrContractBuyPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractBuyPosCount, code, count).Result()
	return
}

func DcrContractBuyPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractBuyPosCount, code, -count).Result()
	return
}

func SetContractBuyPosition(code string, count int64) (err error) {
	err = DefaultRedis().HSet(define.CacheKeyContractBuyPosCount, code, convert.Int64String(count)).Err()
	return
}

func GetContractBuyPosition(code string) (result int64, err error) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractBuyPosCount, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractBuyPosition fail,%v", err)
		}
		return
	}
	result = convert.String2Int64(s)
	return
}

func IncrContractSellPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractSellPosCount, code, count).Result()
	return
}

func DcrContractSellPosition(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractSellPosCount, code, -count).Result()
	return
}

func SetContractSellPosition(code string, count int64) (err error) {
	err = DefaultRedis().HSet(define.CacheKeyContractSellPosCount, code, convert.Int64String(count)).Err()
	return
}

func GetContractSellPosition(code string) (result int64, err error) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractSellPosCount, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractSellPosition fail,%v", err)
		}
		return
	}
	result = convert.String2Int64(s)
	return
}

func AddContractSelfRealTrade(order *proto.MatchOrder) {
	if order == nil {
		log.Infof("order MOrder is nil")
		return
	}
	key := utils.StrBuilder(define.ContractSelfCurrentTrade, ":", order.ContractCode)
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("AddContractSelfRealTrade json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().LPush(key, b).Err()
	if err != nil {
		log.Errorf("AddContractSelfRealTrade lpush fail,%v", err)
		return
	}
	err = DefaultRedis().LTrim(key, 0, 20).Err()
	if err != nil {
		log.Errorf("AddContractSelfRealTrade ltrim fail,%v", err)
		return
	}
}

//获取指定合约最新真实成交
func ListSelfRealTradeFirst(code string) (order *proto.MatchOrder) {
	key := utils.StrBuilder(define.ContractSelfCurrentTrade, ":", code)
	data, err := DefaultRedis().LIndex(key, 0).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("ListContractCurrentTrade lrange fail,%v", err)
		}
		return
	}
	o := new(proto.MatchOrder)
	err = json.Unmarshal([]byte(data), o)
	if err != nil {
		log.Errorf("ListContractCurrentTrade json unmarshal fail,%v", err)
		return
	}
	order = o
	return
}

//增加最新中鱼成交
func AddContractBaitTrade(order *proto.MatchOrder) {
	if order == nil {
		log.Infof("order MOrder is nil")
		return
	}
	key := utils.StrBuilder(define.ContractSelfCurrentBaitTrade, ":", order.ContractCode)
	b, err := json.Marshal(order)
	if err != nil {
		log.Errorf("AddContractBaitTrade json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().LPush(key, b).Err()
	if err != nil {
		log.Errorf("AddContractBaitTrade lpush fail,%v", err)
		return
	}
	err = DefaultRedis().LTrim(key, 0, 50).Err()
	if err != nil {
		log.Errorf("AddContractBaitTrade ltrim fail,%v", err)
		return
	}
}

//获取指定合约最新中鱼成交
func ListBaitTradeFirst(code string) (order *proto.MatchOrder) {
	key := utils.StrBuilder(define.ContractSelfCurrentBaitTrade, ":", code)
	data, err := DefaultRedis().LIndex(key, 0).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("ListBaitTradeFirst lrange fail,%v", err)
		}
		return
	}
	o := new(proto.MatchOrder)
	err = json.Unmarshal([]byte(data), o)
	if err != nil {
		log.Errorf("ListBaitTradeFirst json unmarshal fail,%v", err)
		return
	}
	order = o
	return
}

//func IncrContractDepthBuyPosition(code string, count int64) (result int64, err error) {
//	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractDepthBuyValid, code, count).Result()
//	return
//}

func GetContractDepthBuy(code string) (result decimal.Decimal) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractDepthBuyValid, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractDepthBuy fail,%v", err)
		}
		return
	}
	return nums.NewFromString(s)
}

func SetContractDepthBuy(code string, count decimal.Decimal) {
	e := DefaultRedis().HSet(define.CacheKeyContractDepthBuyValid, code, count.String()).Err()
	if e != nil {
		log.Errorf("SetContractDepthBuy fail,%v", e)

	}
}

func DelContractDepthBuyPosition() (err error) {
	err = DefaultRedis().Del(define.CacheKeyContractDepthBuyValid).Err()
	if err != nil {
		log.Errorf("DelContractDepthBuyPosition fail,%v", err)
		return err
	}
	return
}

//func IncrContractDepthSellPosition(code string, count int64) (result int64, err error) {
//	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractDepthSellValid, code, -count).Result()
//	return
//}

func GetContractDepthSell(code string) (result decimal.Decimal) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractDepthSellValid, code).Result()
	if err != nil {
		log.Errorf("GetContractDepthSell fail,%v", err)
		return
	}
	return nums.NewFromString(s)
}

func SetContractDepthSell(code string, count decimal.Decimal) {
	e := DefaultRedis().HSet(define.CacheKeyContractDepthSellValid, code, count.String()).Err()
	if e != nil {
		log.Errorf("SetContractDepthSell fail,%v", e)
		return
	}
}

func DelContractDepthSellPosition() (err error) {
	err = DefaultRedis().Del(define.CacheKeyContractDepthSellValid).Err()
	if err != nil {
		log.Errorf("DelContractDepthSellPosition fail,%v", err)
		return err
	}
	return
}

//设置秒成交累计
func IncrContractTradeVolume(code string, count int64) (result int64) {
	t := time.Now().Unix()
	key := utils.StrBuilder(define.CacheKeyContractTradeAmount, ":", code, ":", convert.Int64String(t))
	c := DefaultRedis()
	result = c.IncrBy(key, count).Val()
	c.Expire(key, 30*time.Second)
	return
}

//增加合约大额成交笔数
func IncrContractSideLargeTradeCount(code string, side string, count int64) (result int64) {
	c := DefaultRedis()
	result = c.HIncrBy(define.CacheKeyContractSideTradeCount, code+":"+side, count).Val()
	return
}

//重置合约大额成交笔数
func ResetContractSideLargeTradeCount() {
	DefaultRedis().Del(define.CacheKeyContractSideTradeCount)
}

func IncrContractGivingCount(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractGivingCount, code, count).Result()
	return
}

func DcrContractGivingCount(code string, count int64) (result int64, err error) {
	result, err = DefaultRedis().HIncrBy(define.CacheKeyContractGivingCount, code, -count).Result()
	return
}

func SetContractGivingCount(code string, count int64) (err error) {
	err = DefaultRedis().HSet(define.CacheKeyContractGivingCount, code, convert.Int64String(count)).Err()
	return
}

func GetContractGivingCount(code string) (result int64, err error) {
	s, err := DefaultRedis().HGet(define.CacheKeyContractGivingCount, code).Result()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("GetContractGivingCount fail,%v", err)
		}
		return
	}
	result = convert.String2Int64(s)
	return
}

//设置合约上次对敲上限
func SetContractBucketGivingLimit(bg *proto.Contract24Giving) {
	if bg == nil {
		return
	}
	b, err := json.Marshal(bg)
	if err != nil {
		log.Errorf("SetContractBucketGivingLimit json marshal fail,%v", err)
		return
	}
	err = DefaultRedis().HSet(define.CacheKeyContractLastGiving, bg.Contract, b).Err()
	if err != nil {
		log.Errorf("SetContractBucketGivingLimit hSet fail,%v", err)
	}
	return
}

//获取合约上次对敲放量
func GetContractBucketGivingLimit(code string) (bg *proto.Contract24Giving) {
	b, err := DefaultRedis().HGet(define.CacheKeyContractLastGiving, code).Bytes()
	if err != nil {
		if err != redis.Nil {
			log.Errorf("SetContractBucketGivingLimit hSet fail,%v", err)
		}
		return
	}
	if b == nil {
		return
	}
	bg = new(proto.Contract24Giving)
	err = json.Unmarshal(b, bg)
	if err != nil {
		return
	}
	return
}

func GetContractHourSplitRandIndex(code string) (bg *proto.HourRandV) {
	s := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheKeyContractHourDayRandIndex, ":", code, ":", s)
	b, err := DefaultRedis().Get(key).Bytes()
	if err != nil {
		log.Errorf("GetContractHourSplitRandIndex fail,%v", err)
		return
	}
	if b == nil {
		return
	}
	bg = new(proto.HourRandV)
	err = json.Unmarshal(b, bg)
	if err != nil {
		log.Errorf("GetContractHourSplitRandIndex json unmarshal fail,%v", err)
		return
	}
	return
}

func SetContractHourSplitRandIndex(bg *proto.HourRandV) {
	if bg == nil {
		return
	}
	s := time.Now().Format("2006-01-02")
	key := utils.StrBuilder(define.CacheKeyContractHourDayRandIndex, ":", bg.Code, ":", s)
	b, err := json.Marshal(bg)
	if err != nil {
		log.Errorf("SetContractHourSplitRandIndex fail,%v", err)
		return
	}
	err = DefaultRedis().Set(key, b, 24*time.Hour).Err()
	if err != nil {
		log.Errorf("SetContractHourSplitRandIndex fail,%v", err)
		return
	}
}
