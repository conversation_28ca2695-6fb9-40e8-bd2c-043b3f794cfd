package biance

import (
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"time"
)

//https://api.binance.com/api/v3/ticker/price
//现货价格

//{"symbol":"GASBTC","price":"0.00004750"}
const (
	swapTick = "https://fapi.binance.com/fapi/v1/ticker/price"
)

func GetTickersForSwap(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(swapTick)
	if err != nil {
		log.Error("binance swap GetTickers fail", zap.Error(err))
		return
	}
	defer r.Body.Close()
	if r.StatusCode != http.StatusOK {
		log.Errorf("get binance swap tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Println(string(b))
	var tickers []SwapTicker
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	if len(tickers) == 0 {
		return
	}

	for _, v := range tickers {
		code := v.Symbol
		if _, ok := codes[code]; ok {
			ts := v.Time / 1000
			t := time.Unix(ts, 0)
			if time.Since(t).Seconds() > 60 {
				log.Info("binance 数据时间超时", zap.Any("data", v), zap.Any("time", t), zap.Any("seconds", ts))
				continue
			}
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    nums.NewFromString(v.Price),
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

type SwapTicker struct {
	Symbol string `json:"symbol"`
	Price  string `json:"price"`
	Time   int64  `json:"time"`
}
