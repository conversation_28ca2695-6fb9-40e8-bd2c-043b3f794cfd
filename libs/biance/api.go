package biance

import (
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
	"io/ioutil"
	"net/http"
	"time"
)

//https://api.binance.com/api/v3/ticker/price
//现货价格

//{"symbol":"GASBTC","price":"0.00004750"}
const (
	url   = "https://api.binance.com/api/v3/ticker/price"
	_name = "binance"
)

func GetTickers(codes map[string]struct{}) (list []proto.MarketTrade) {
	r, err := http.Get(url)
	if err != nil {
		return
	}
	defer r.Body.Close()
	if r.StatusCode != http.StatusOK {
		log.Errorf("get hitbtc tickers fail,code:%v", r.StatusCode)
		return
	}
	b, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return
	}
	//fmt.Println(string(b))
	var tickers []tick
	err = json.Unmarshal(b, &tickers)
	if err != nil {
		return
	}
	if len(tickers) == 0 {
		return
	}

	for _, v := range tickers {
		code := v.Code
		if _, ok := codes[code]; ok {
			mk := proto.MarketTrade{
				Symbol:   code,
				DealTime: time.Now().Unix(),
				Price:    v.Price,
				Ts:       time.Now(),
				Source:   _name,
			}
			list = append(list, mk)
		}
	}
	return
}

//{"symbol":"GASBTC","price":"0.00004750"}
type tick struct {
	Code  string          `json:"symbol"`
	Price decimal.Decimal `json:"price"`
}
