/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package biance

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"bc/libs/convert"
	"bc/libs/json"
	"bc/libs/log"
	"github.com/gorilla/websocket"
)

const (
	_wsMarket = "wss://stream.binance.com/ws/stream"
	_sub      = "SUBSCRIBE"
	_unSub    = "UNSUBSCRIBE"
)

//entries

type MarketWsClient struct {
	dialer     *websocket.Dialer
	wsPoint    string
	topics     []string
	con        *websocket.Conn
	lastError  error
	errLock    sync.Mutex
	status     bool
	msgHandler func([]byte)
}

type Config struct {
	WsPoint string
	dialer  *websocket.Dialer
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) Start() {
Retry:
	conn, rsp, err := c.dialer.Dial(c.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to binance fail,err:%v,http rsp:%+v", err, rsp)
		goto Retry
	}
	c.setLastError(nil)
	c.con = conn
	c.subscript()
	c.status = true
	log.Infof("success get websocket client conn for binance")
	c.receive()
}

func (c *MarketWsClient) Subscript(topics []string) {
	log.Infof("topics:%v", topics)
	c.topics = topics
	c.subscript()

}

func (c *MarketWsClient) subscript() {
	if len(c.topics) == 0 {
		fmt.Println("binance 订阅topic数量为0")
		return
	}
	d := BasicData{Method: _sub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	fmt.Printf("binance sub:%v\n", convert.Bytes2Str(b))
	if c.con == nil {
		fmt.Printf("binance 连接为空")
		return
	}
	e := c.con.WriteMessage(websocket.TextMessage, b)
	fmt.Printf("write sub msg:%v", e)
	if e != nil {
		fmt.Printf("write sub msg fail,%v\n", e)
		c.setLastError(e)
	}
}

func (c *MarketWsClient) UnSubscript() {
	d := BasicData{Method: _unSub, Params: c.topics}
	b, err := json.Marshal(d)
	if err != nil {
		log.Errorf("Marshal fail,%v", err)
		return
	}
	e := c.con.WriteMessage(websocket.TextMessage, b)
	if e != nil {
		c.setLastError(e)
	}
}

func (c *MarketWsClient) setLastError(err error) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.lastError = err
}

func (c *MarketWsClient) setConn(conn *websocket.Conn) {
	c.errLock.Lock()
	defer c.errLock.Unlock()
	c.con = conn
}

func (c *MarketWsClient) receive() {
	go func() {
		for {
			if c.reMsg() {
				return
			}
		}
	}()

}

func (c *MarketWsClient) reMsg() bool {
	if !c.status {
		return true
	}
	conn := c.con
	if conn == nil {
		c.setLastError(errors.New("ws conn is null"))
		return true
	}
	conn.SetPingHandler(func(appData string) error {
		c.con.WriteMessage(websocket.PongMessage, nil)
		return nil
	})
	_, b, err := conn.ReadMessage()
	if err != nil {
		log.Errorf("bian readmessage fail,%v", err)
		c.setLastError(err)
		return true
	}
	//fmt.Printf("d:%v,err:%v\n", string(b), err)
	if c.msgHandler != nil {
		go c.msgHandler(b)
	}
	return false
}

func (c *MarketWsClient) Loop() {
	go func() {
		for {
			if !c.status {
				continue
			}
			c.checkError()
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) checkError() {
	if c.con == nil || c.lastError != nil {
		c.status = false
		c.Start()
		return
	}
}

//{
//"method": "SUBSCRIBE",
//"params":
//[
//"btcusdt@aggTrade",
//"btcusdt@depth"
//],
//"id": 1
//}
type BasicData struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	Id     int64    `json:"id"`
}

type ResultPayload struct {
	Event  string `json:"e"`
	Time   int64  `json:"E"`
	Symbol string `json:"s"`
}

//{
//"e": "trade",     // 事件类型
//"E": 123456789,   // 事件时间
//"s": "BNBBTC",    // 交易对
//"t": 12345,       // 交易ID
//"p": "0.001",     // 成交价格
//"q": "100",       // 成交数量
//"b": 88,          // 买方的订单ID
//"a": 50,          // 卖方的订单ID
//"T": 123456785,   // 成交时间
//"m": true,        // 买方是否是做市方。如true，则此次成交是一个主动卖出单，否则是一个主动买入单。
//"M": true         // 请忽略该字段
//}
type ResultTicker struct {
	ResultPayload
	AId      int64  `json:"a"` //归集Id
	Price    string `json:"p"` //成交价格
	Amount   string `json:"q"` //成交数量
	DealTime int64  `json:"T"` //成交时间
	MType    bool   `json:"m"`
	M        bool   `json:"M"`
}
