// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ws

import (
	"bc/libs/define"
	"bc/libs/log"
	"context"
	"errors"
	"time"
)

// <PERSON><PERSON> maintains the set of active clients and broadcasts messages to the
// clients.
type Hub struct {
	//roomManager
	rooms *RoomManager
	//userManager
	users *UserManager

	// registered clients
	clients map[*Client]bool

	// Inbound messages from the clients.
	broadcast chan []byte

	// Register requests from the clients.
	register chan *Client

	// Unregister requests from clients.
	unregister chan *Client

	// register user
	registerUser chan *Client

	//init room chan
	opRoom chan []string

	//Is the data compression
	isSendCompress bool

	//mointor seconds gap
	spaySeconds int64
}

type HubConfig struct {
	SpaySeconds int64 //监控时间间隔
}

func NewHub() *Hub {
	return NewHubWithConfig(nil)
}

//初始化websocket hub
func NewHubWithConfig(config *HubConfig) *Hub {
	h := &Hub{
		rooms:          NewRoomManager(),
		broadcast:      make(chan []byte, 1024),
		register:       make(chan *Client, 1024),
		registerUser:   make(chan *Client, 1024),
		unregister:     make(chan *Client, 1024),
		clients:        make(map[*Client]bool),
		users:          NewUserManager(),
		spaySeconds:    60,
		isSendCompress: false,
	}
	if config != nil {
		h.spaySeconds = config.SpaySeconds
	}
	return h
}

//hub 初始化注册房间
func (h *Hub) AddNewRooms(topics []string) {
	log.Infof("初始化房间:%+v", topics)
	if len(topics) == 0 {
		return
	}
	for _, topic := range topics {
		h.rooms.AddRoom(topic)
	}
}

//向hub注册client到rooms
func (h *Hub) addRoomClient(topic string, client *Client) {
	h.rooms.AddClient(topic, client)
}

//从room 移除client
func (h *Hub) removeRoomClient(topic string, client *Client) {
	h.rooms.RemoveRoomClient(topic, client)
}

func (h *Hub) Run() {
	monitor := time.Tick(time.Duration(h.spaySeconds) * time.Second)
	for {
		select {
		case client := <-h.register:
			t := time.Now()
			client.isCanWrite = true
			h.clients[client] = true
			log.Infof("handle client reg,id:%v,cost:%v", client.clientId, time.Since(t).String())
		case client := <-h.unregister:
			t := time.Now()
			h.removeClient(client)
			log.Infof("handle client remove,id:%v,cost:%v", client.clientId, time.Since(t).String())
		case client := <-h.registerUser:
			t := time.Now()
			h.AddUser(client)
			log.Infof("handle client add user,id:%v,cost:%v", client.clientId, time.Since(t).String())
		case message := <-h.broadcast:
			t := time.Now()
			h.broadcastMessage(message)
			log.Infof("handle  broadcast msg,id:%v,cost:%v", time.Since(t).String())
		case <-monitor:
			t := time.Now()
			h.monitoring()
			log.Infof("handle  monitoring,cost:%v", time.Since(t).String())
		}

	}
}

func (h *Hub) broadcastMessage(message []byte) {
	for client := range h.clients {
		if client != nil && !client.isInvalid {
			func() {
				ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
				defer cancel()

				select {
				case <-ctx.Done():
					return
				case client.send <- message:
					return
				}
			}()
		}
	}
}

//user operate room
func (h *Hub) AddRoom(name string) {
	h.rooms.AddRoom(name)
}

//将链接向用户管理器注册
func (h *Hub) AddUser(client *Client) {
	h.users.AddUserClient(client)
}

//通知用户连接列表移除客户端
func (h *Hub) RemoveUser(client *Client) {
	h.users.RemoveClient(client) //从用户列表中移除连接
}

func (h *Hub) removeClient(client *Client) {
	client.isCanWrite = false
	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		h.rooms.RemoveClient(client) //从rooms中移除连接
		h.users.RemoveClient(client) //从用户列表中移除连接
	}
	_ = client.conn.Close()
	close(client.send)
}

func (h *Hub) SendBroadcast(msg WMessagePack) {
	if h == nil {
		return
	}
	h.broadcast <- msg.Marsha()
}

func (h *Hub) SendUser(userId int64, msg WMessagePack) {
	if h == nil {
		return
	}
	h.users.SendMsgUser(userId, msg.Marsha())
}

func (h *Hub) SendRoomMsg(roomId string, msg WMessagePack) {
	if h == nil {
		return
	}
	msg.Action = "notify"
	h.rooms.SendMsgRoom(roomId, msg.Marsha())
}

func (h *Hub) AddMsgHandler(wsPro string, f MsgHandleFunc) {
	DefaultHandleMap[wsPro] = f
}

func (h *Hub) handleMessage(c *Client, msg []byte) error {
	//c.userId=rand.Int63n(100000)
	////c.RegisterUserClient()
	//h.addRoomClient("hello"+strconv.FormatInt(c.userId,10),c)
	//fmt.Println(string(msg))
	log.Debugf("handleMessage:%v", string(msg))
	var wsPackage WMessageRequest
	wsPackage, err := UnMarshaWsMsg(msg)
	if err != nil {
		log.Infof("not support data type,%v", err)
		return err
	}
	//判断token是否有效
	topic := wsPackage.Topic
	//处理先听的消息
	f, ok := DefaultHandleMap[topic]
	if !ok {
		if wsPackage.Action == define.WsActionSub {
			c.Register2Room(topic)
		} else if wsPackage.Action == define.WsActionUnSub {
			c.UnRegisterFromRoom(topic)
		}
		return errors.New("lack msg handler")
	}
	f(c, wsPackage)
	//DefaultHandleMap[wsPackage.Pro](c, wsPackage.Data)
	return nil
}

func (h *Hub) monitoring() {
	if h == nil {
		return
	}
	log.Warnf("[WS Monitor] monitoring,client size:%v", len(h.clients))
	log.Warnf("[WS Monitor] monitoring,room size:%v", h.rooms.Size())
	log.Warnf("[WS Monitor] monitoring,user size:%v", h.users.Size())
	if h.users.Size() > 0 {
		h.users.users.Each(func(i interface{}, i2 interface{}) {
			c, ok := i2.(*UserClients)
			if !ok {
				return
			}
			c.clients.Each(func(i3 interface{}, i interface{}) {
				log.Warnf("online users client:%+v", i3)
			})
		})
	}
	h.rooms.rooms.Each(func(k interface{}, i2 interface{}) {
		//fmt.Printf("i:%t，i2;%t",i,i2)
		name := k.(string)
		v := i2.(*Room)
		log.Warnf("[WS Monitor] room:%v,client size:%v", name, v.clients.Size())
	})
}
