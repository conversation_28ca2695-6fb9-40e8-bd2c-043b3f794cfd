// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package ws

import (
	"bc/libs/define"
	"bc/libs/log"
	"context"
	"fmt"
	"golang.org/x/time/rate"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer.
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer.
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait.
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer.
	maxMessageSize = 512
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

var upgrade = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// C<PERSON> is a middleman between the websocket connection and the hub.
type Client struct {
	c      context.Context
	cancel func()
	DoFunc func(int64)
	hub    *Hub
	// The websocket connection.
	conn *websocket.Conn
	// Buffered channel of outbound messages.
	send chan []byte
	//send chan cap
	sendCap int
	//client id
	clientId   string
	isCanWrite bool

	//mark the client weather limit rate
	isRateLimit bool
	limiter     *rate.Limiter

	UserId int64
	//Token
	Token string
	//current symbol
	CurSymbol string
	//curDepth level
	CurDepthLevel int
	isInvalid     bool
	Ts            time.Time
}

type clientExtra struct {
	//Token
	Token string
	//current symbol
	CurSymbol string
	//curDepth level
	CurDepthLevel int
}

type ClientConfig struct {
	Hub  *Hub
	Conn *websocket.Conn
	//send chan cap
	SendCap int
	//Token
	Token string
	//current symbol
	SendRate    int
	IsRateLimit bool
	//用户ID,test使用
	userId int64
	DoFunc func(int64)
}

const (
	_defaultCap       = 256
	_defaultLimitRate = 10
)

func NewClient(config *ClientConfig) *Client {
	context, cancel := context.WithCancel(context.Background())
	c := new(Client)
	var sendCap, limitRate = _defaultCap, _defaultLimitRate
	var isLimit bool
	if config != nil {
		c.hub = config.Hub
		c.conn = config.Conn
		sendCap = config.SendCap
		limitRate = config.SendRate
		isLimit = config.IsRateLimit
		c.UserId = config.userId
		c.DoFunc = config.DoFunc
	}
	c.clientId = fmt.Sprintf("%p", c)
	c.send = make(chan []byte, sendCap)
	c.isRateLimit = isLimit
	c.limiter = rate.NewLimiter(rate.Limit(limitRate), limitRate)
	c.c = context
	c.cancel = cancel
	c.Ts = time.Now()
	return c
}

func (c *Client) Destory() {
	//c.hub.unregister <- c
	//c.conn.Close()
	c.cancel()
	//log.Infof("销毁客户端,c：%p", c)

}

//get the client id
func (c *Client) GetName() string {
	return c.clientId
}

func (c *Client) String() string {
	return fmt.Sprintf("cId：%v,uid：%v，isLimit:%v,time:%v", c.clientId, c.UserId, c.isRateLimit, c.Ts.String())
}

//check the client is allow
func (c *Client) IsAllow() bool {
	return c.limiter.Allow()
}

func (c *Client) CanWrite() bool {
	return c.isCanWrite && len(c.send) < c.sendCap
}

func (c *Client) SendMsg(data []byte) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	select {
	case <-ctx.Done():
		return
	case c.send <- data:
		return
	}
}

func (c *Client) Register2Room(name string) {
	c.hub.addRoomClient(name, c)
}

func (c *Client) UnRegisterFromRoom(name string) {
	c.hub.removeRoomClient(name, c)
}

func (c *Client) RegisterUserClient() {
	log.Infof("begin reg user,userid:%v,client info:%+v", c.UserId, c.String())
	c.hub.AddUser(c)
}

func (c *Client) UnRegisterUserClient() {
	log.Infof("begin reg user,userid:%v,client info:%+v", c.UserId, c.String())
	c.hub.RemoveUser(c)
}

func (c *Client) SetRecHeart() {
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	if c.DoFunc != nil && c.UserId > 0 {
		c.DoFunc(c.UserId)
	}
}

// readPump pumps messages from the websocket connection to the hub.
//
// The application runs readPump in a per-connection goroutine. The application
// ensures that there is at most one reader on a connection by executing all
// reads from this goroutine.
func (c *Client) readPump() {
	defer func() {
		c.isInvalid = true
		c.hub.unregister <- c
		c.cancel()
	}()
	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})
	for {
		select {
		case <-c.c.Done():
			return
		default:
			//log.Infof("read 1......")
			_, message, err := c.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Errorf("error: %v", err)
					return
					//fmt.Printf("error:%v\n", err)
				}
				//log.Errorf("ssssssssssssssssss:%v",err)
				return
			}
			log.Infof("message:%v", string(message))
			//message = bytes.TrimSpace(bytes.Replace(message, newline, space, -1))
			if !c.isRateLimit {
				c.hub.handleMessage(c, message)
				continue
			}
			if c.IsAllow() {
				c.hub.handleMessage(c, message)
			} else {
				c.sendRateLimitMsg()
				time.Sleep(2 * time.Second)
				c.Destory()
				return
			}
		}
	}
}

func (c *Client) sendRateLimitMsg() {
	log.Warnf("client:%p request fast", c)
	msg := WMessagePack{}
	msg.Action = define.WsActionNotify
	msg.Ts = time.Now().Unix()
	msg.Code = Fail
	msg.ErrMsg = ErrRateLimit

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	select {
	case <-ctx.Done():
		return
	case c.send <- msg.Marsha():
		return
	}
}

func (c *Client) resetSendChan() {
	c.isCanWrite = false
	c.send = make(chan []byte, c.sendCap)
	c.isCanWrite = true
}

// writePump pumps messages from the hub to the websocket connection.
//
// A goroutine running writePump is started for each connection. The
// application ensures that there is at most one writer to a connection by
// executing all writes from this goroutine.
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		c.isInvalid = true
		ticker.Stop()
		c.cancel()
	}()
	for {
		if len(c.send) == cap(c.send) {
			log.Warnf("发送队列满了,重置队列,client:%p,len:%v", c, len(c.send))
			c.resetSendChan()
		}
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel.
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			if c.hub.isSendCompress {
				message = zipData(message)
			}
			w.Write(message)

			if err := w.Close(); err != nil {
				return
			}
		case <-c.c.Done():
			return
		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			//c.conn.WriteMessage(websocket.TextMessage, []byte("ping"))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
			if c.DoFunc != nil && c.UserId > 0 {
				c.DoFunc(c.UserId)
			}
		}
	}
}

func zipData(d []byte) []byte {
	//d, err := zip.ZipByte(d)
	//if err != nil {
	//	log.Infof("压缩数据出错,%v", err)
	//}
	//return d
	return nil
}

// serveWs handles websocket requests from the peer.
func ServeWs(hub *Hub, w http.ResponseWriter, r *http.Request, f func(int64)) {
	conn, err := upgrade.Upgrade(w, r, nil)
	if err != nil {
		log.Infof("server ws err:%v", err)
		return
	}
	client := NewClient(&ClientConfig{Hub: hub, Conn: conn, SendCap: _defaultCap, SendRate: _defaultLimitRate, IsRateLimit: true, DoFunc: f})
	//client := &Client{hub: hub, conn: conn, send: make(chan []byte, 256), sendCap: 256}
	log.Infof("ws connect pre:%p", client)
	client.hub.register <- client
	log.Infof("ws connect success:%p", client)
	//fmt.Printf("connect:%p\n", client)

	// Allow collection of memory referenced by the caller by doing all work in
	// new goroutines.
	go client.writePump()
	go client.readPump()
}
