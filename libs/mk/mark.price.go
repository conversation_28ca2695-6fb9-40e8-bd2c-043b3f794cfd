package mk

import (
	"bc/libs/cache"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/modern-go/concurrent"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"sync"
)

type MarkPriceEngine struct {
	sync.RWMutex
	//indexChan     chan proto.ComplexPrice       //标记价格队列
	codeMarkPrice map[string]proto.ContractMark //合约标记价格统计
	codeChan      *concurrent.Map
}

func NewMarkEngine() *MarkPriceEngine {
	fe := &MarkPriceEngine{
		codeMarkPrice: make(map[string]proto.ContractMark),
		codeChan:      concurrent.NewMap(),
	}
	return fe
}

func (f *MarkPriceEngine) Notify(mk proto.ComplexPrice) {
	var cmp *CodeMarkPrice
	r, ok := f.codeChan.Load(mk.ContractCode)
	if !ok {
		cmp = NewCodeMarkPrice(mk.ContractCode, f)
		f.codeChan.Store(mk.ContractCode, cmp)
		cmp.Run()
	} else {
		cmp = r.(*CodeMarkPrice)
	}
	cmp.notify(mk)
	//f.indexChan <- mk
}

type CodeMarkPrice struct {
	code string
	c    chan proto.ComplexPrice
	f    *MarkPriceEngine
}

func NewCodeMarkPrice(code string, f *MarkPriceEngine) *CodeMarkPrice {
	cp := &CodeMarkPrice{
		code: code,
		c:    make(chan proto.ComplexPrice, 128),
		f:    f,
	}
	return cp
}

func (c *CodeMarkPrice) notify(mk proto.ComplexPrice) {
	log.Debug("收到标记价格推送", zap.Any("data", mk))
	c.c <- mk
}

func (c *CodeMarkPrice) Run() {
	go func() {

		if c.f == nil {
			log.Error("c.f is nil")
			return
		}
		for mk := range c.c {
			//log.Info("开始处理合约数据",zap.Any("data",mk))
			c.f.compareAndSet(mk)
		}
	}()
}

func (f *MarkPriceEngine) compareAndSet(mk proto.ComplexPrice) {
	f.Lock()
	defer f.Unlock()
	o := f.codeMarkPrice[mk.ContractCode]
	o.Store(mk)
	f.codeMarkPrice[mk.ContractCode] = o
	//log.Debug("设置后价格", zap.Any("data", mk.Price.String()), zap.Any("price", f.codeMarkPrice[mk.ContractCode]))
}

func (f *MarkPriceEngine) Get() map[string]proto.ContractMark {
	f.Lock()
	defer f.Unlock()
	result := make(map[string]proto.ContractMark)
	infoMap := cache.GetAllContractComplexPrice()
	//infoMap:=make(map[string]proto.ComplexPrice)
	log.Debug("mark price before", zap.Any("data", f.codeMarkPrice))
	//log.Debug("last new mark price ", zap.Any("data", infoMap))
	if len(infoMap) > 0 {
		for c, o := range infoMap {
			item := f.codeMarkPrice[c]
			old := item
			item.ContractCode = c
			//如果上次项目最新价位0，以最新价重置
			item.ClosePrice = o.Price
			if o.Price.GreaterThan(decimal.Zero) && o.Price.GreaterThan(item.HighPrice) {
				item.HighPrice = o.Price
			}
			if o.Price.GreaterThan(decimal.Zero) && o.Price.LessThan(item.LowPrice) {
				item.LowPrice = o.Price
			}

			result[c] = item

			//重置旧的数据
			old.Reset(o)
			f.codeMarkPrice[c] = old
		}
	} else {
		for c, item := range f.codeMarkPrice {
			result[c] = item
			item.Reset(proto.ComplexPrice{ContractCode: c})
			f.codeMarkPrice[c] = item
		}
	}

	log.Info("获取上次价格", zap.Any("上次价格", result), zap.Any("下一轮重置后价格", f.codeMarkPrice))
	return result
}

func GetFloatProfitForForceStatWithPosNet(side string, holdAmount, posNet int, perValue, avgPrice decimal.Decimal, index proto.ContractMark) decimal.Decimal {
	var indexPrice decimal.Decimal
	value := nums.NewFromInt(holdAmount).Mul(perValue)
	if posNet > 0 {
		indexPrice = index.LowPrice
	} else if posNet < 0 {
		indexPrice = index.HighPrice
	} else {
		indexPrice = index.ClosePrice
	}
	if indexPrice.LessThanOrEqual(decimal.Zero) {
		indexPrice = index.ClosePrice
	}
	if side == define.OrderBuy {
		return value.Mul(indexPrice.Sub(avgPrice))
	}
	return value.Mul(avgPrice.Sub(indexPrice))
}

func GetFloatProfitForForceStat(side string, holdAmount int, perValue, avgPrice decimal.Decimal, index proto.ContractMark) decimal.Decimal {
	var indexPrice decimal.Decimal
	value := nums.NewFromInt(holdAmount).Mul(perValue)
	if side == define.OrderBuy {
		indexPrice = index.LowPrice
	} else {
		indexPrice = index.HighPrice
	}
	if indexPrice.LessThanOrEqual(decimal.Zero) {
		indexPrice = index.ClosePrice
	}
	if side == define.OrderBuy {
		return value.Mul(indexPrice.Sub(avgPrice))
	}
	return value.Mul(avgPrice.Sub(indexPrice))
}
