package mk

import (
	"bc/libs/cache"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"go.uber.org/zap"
	"math/rand"
	"testing"
)

func TestCeshi(t *testing.T) {
	log.InitLogger("f", "debug", false)
	define.RedisCommonDb = 3
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 3, false)
	cache.GetAllContractComplexPrice()
	me := NewMarkEngine()
	cache.SetContractComplexTradePrice(&proto.ComplexPrice{ContractCode: "BTCUSDT", Price: nums.NewFromFloat(49999)})
	me.Get()
	p := 50000.0

	for i := 0; i < 5; i++ {
		s := proto.ComplexPrice{ContractCode: "BTCUSDT", Price: nums.NewFromFloat(p).Add(nums.NewFromInt(rand.Intn(100)))}
		me.Notify(s)
		//cache.SetContractComplexTradePrice(&s)
	}
	cache.SetContractComplexTradePrice(&proto.ComplexPrice{ContractCode: "BTCUSDT", Price: nums.NewFromFloat(50072)})

	log.Info("data", zap.Any("1111111", me.Get()))
	for i := 0; i < 5; i++ {
		me.Notify(proto.ComplexPrice{ContractCode: "BTCUSDT", Price: nums.NewFromFloat(p).Add(nums.NewFromInt(rand.Intn(100)))})
	}
	cache.SetContractComplexTradePrice(&proto.ComplexPrice{ContractCode: "BTCUSDT", Price: nums.NewFromFloat(50082)})

	log.Info("data", zap.Any("222222222", me.Get()))

	log.Info("data", zap.Any("3333", me.Get()))

}
