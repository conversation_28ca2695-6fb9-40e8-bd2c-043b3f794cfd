/*
@Time : 3/12/20 3:06 下午
<AUTHOR> mocha
@File : contract
*/
package commonsrv

import (
	"database/sql"
	"errors"
	"sort"

	"bc/libs/cache"
	"bc/libs/convert"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

func GetApiContractList(contractList []proto.Contract) (list []proto.ApiContractList) {
	// 获取全部涨跌幅
	changes := cache.GetAllContractApplies()

	var contract proto.ApiContractList
	list = make([]proto.ApiContractList, 0)
	indexMap := cache.GetAllContractComplexPrice()
	for i := range contractList {
		if !contractList[i].Delisted && contractList[i].IsShow {
			contract.ContractCode = contractList[i].ContractCode
			contract.ContractName = contractList[i].ContractName
			contract.ContractNameEn = contractList[i].ContractNameEn
			contract.ContractIcon = contractList[i].ContractIcon
			contract.ContractType = contractList[i].ContractType
			contract.IndexPrecision = contractList[i].IndexPrecision
			contract.FeeMaker = contractList[i].FeeMaker
			contract.FeeTaker = contractList[i].FeeTaker
			contract.Digit = contractList[i].Digit
			contract.IOCLimit = contractList[i].IOCLimit
			contract.IOCBuyLimit = contractList[i].IOCBuyLimit
			contract.IOCSellLimit = contractList[i].IOCSellLimit
			trade := cache.GetContractPriceIndexIgnoreErr(contract.ContractCode)
			index, ok := indexMap[contract.ContractCode]
			if ok {
				contract.IndexPrice = index.Price.StringFixedBank(contract.Digit)
			}
			contract.Price = trade.TradePrice.StringFixedBank(contract.Digit)
			contract.PriceCNY = trade.TradePriceCn.StringFixedBank(contract.Digit)
			contract.Delisted = contractList[i].Delisted
			contract.IsFollow = contractList[i].IsFollow
			contract.PriceCoinName = contractList[i].PriceCoinName
			contract.ParValue = nums.NewFromString(contractList[i].ParValue).String()
			contract.BuyCount, contract.SellCount = trade.BuyCount, trade.SellCount
			contract.IsMaintenance = contractList[i].IsMaintenance
			contract.MaintenanceMarginRatio = nums.NewFromFloat(contractList[i].MaintenanceMarginRatio)
			contract.FullLever = convert.Str2IntSlice(contractList[i].FullLever, ",")
			contract.Leverages = convert.Str2IntSlice(contractList[i].PartLever, ",")
			contract.FollowLever = convert.Str2IntSlice(contractList[i].FollowLever, ",")
			contract.LockFloatFactor = contractList[i].LockFloatFactor
			contract.RedundancyFactor = contractList[i].RedundancyFactor
			chg, ok := changes[contract.ContractCode]
			if ok {
				contract.ChangeRatio = chg.ChangeDaily
				contract.Change = chg.Change
				contract.TradeVolume = nums.NewFromString(chg.TradeV24h)
				contract.HighPrice = nums.NewFromString(chg.HighPrice)
				contract.LowPrice = nums.NewFromString(chg.LowPrice)
			} else {
				contract.Change = decimal.Zero.StringFixedBank(contract.Digit)
			}
			list = append(list, contract)
		}
	}
	return list

}

func GetApiContractDetail(detail *proto.Contract, label *proto.UserLabel, applies *proto.Applies) (reply *proto.ApiContractDetail) {
	reply = &proto.ApiContractDetail{
		ContractCode:           detail.ContractCode,
		ContractName:           detail.ContractName,
		ContractNameEn:         detail.ContractNameEn,
		ContractType:           detail.ContractType,
		Digit:                  detail.Digit,
		IndexPrecision:         detail.IndexPrecision,
		Step:                   detail.Step,
		CoinId:                 detail.CoinId,
		CoinName:               detail.CoinName,
		PriceCoinName:          detail.PriceCoinName,
		MarketName:             detail.PriceCoinName,
		ParValue:               decimal.RequireFromString(detail.ParValue).String(),
		MinOrderVolume:         detail.MinOrderVolume,
		MaxOrderVolume:         detail.MaxOrderVolume,
		FeeMaker:               detail.FeeMaker,
		FeeTaker:               detail.FeeTaker,
		Delisted:               detail.Delisted,
		MaintenanceMarginRatio: detail.MaintenanceMarginRatio,
		FullLever:              convert.Str2IntSlice(detail.FullLever, ","),
		Leverages:              convert.Str2IntSlice(detail.PartLever, ","),
		FollowLever:            convert.Str2IntSlice(detail.FollowLever, ","),
		MinTraderVolume:        detail.MinTraderVolume,
		MaxTraderVolume:        detail.MaxTraderVolume,
		IsFollow:               detail.IsFollow,
		IOCLimit:               detail.IOCLimit,
		IOCBuyLimit:            detail.IOCBuyLimit,
		IOCSellLimit:           detail.IOCSellLimit,
		IsMaintenance:          detail.IsMaintenance,
		LockFloatFactor:        detail.LockFloatFactor,
		RedundancyFactor:       detail.RedundancyFactor,
	}

	// 指数价格
	index := cache.GetContractPriceIndexIgnoreErr(detail.ContractCode)

	reply.IndexPrice = index.TradePrice
	reply.IndexPriceCny = index.TradePriceCn
	reply.BuyPrice = index.BuyPrice.StringFixedBank(detail.IndexPrecision)
	reply.SellPrice = index.SellPrice.StringFixedBank(detail.IndexPrecision)

	ff := cache.GetFundingRate(detail.ContractCode)
	// 获取预测资金费率
	reply.FundFee = ff.FundingRate
	reply.EstimateFundFee = ff.EstimatedRate

	// 获取涨跌幅
	reply.ChangeDaily = applies.ChangeDaily
	reply.Change24h = applies.Change24h
	reply.Change8h = applies.Change8h
	reply.Change4h = applies.Change4h
	reply.Change2h = applies.Change2h
	reply.Change1h = applies.Change1h
	reply.Change30m = applies.Change30m
	reply.Change10m = applies.Change10m
	reply.HighPrice = applies.HighPrice
	reply.LowPrice = applies.LowPrice
	reply.ChangeVolume = applies.Change
	reply.Trade24h = applies.TradeV24h

	if label != nil {
		if label.MinOrderVolume > 0 {
			reply.MinOrderVolume = label.MinOrderVolume
		}
		if label.MaxOrderVolume > 0 {
			reply.MaxOrderVolume = label.MaxOrderVolume
		}
		if label.MaxLever > 0 {
			for i := len(reply.FullLever) - 1; i >= 0; i-- {
				if reply.FullLever[i] > label.MaxLever {
					reply.FullLever = reply.FullLever[:i]
				}
			}
			for i := len(reply.Leverages) - 1; i >= 0; i-- {
				if reply.Leverages[i] > label.MaxLever {
					reply.Leverages = reply.Leverages[:i]
				}
			}
			for i := len(reply.FollowLever) - 1; i >= 0; i-- {
				if reply.FollowLever[i] > label.MaxLever {
					reply.FollowLever = reply.FollowLever[:i]
				}
			}
		}
	}

	return
}

func GetContractList(reqID int64, tx *sqlx.Tx) ([]proto.Contract, error) {
	list, err := cache.GetAllContractList()
	if err != nil || len(list) == 0 {
		list, err = database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractList GetSupportContract error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		log.Info("LoadContractList", zap.Any("list", list))
		cache.SaveContractList(list, false)
	}
	sort.SliceStable(list, func(i, j int) bool {
		return list[i].OrderBy > list[j].OrderBy
	})
	return list, nil
}

func GetContractMap(reqID int64, tx *sqlx.Tx) (map[string]proto.Contract, error) {
	contracts, err := cache.GetAllContractMap()
	if err != nil {
		list, err := database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractMap GetSupportContractsTx error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		cache.SaveContractList(list, false)
		for i := range list {
			contracts[list[i].ContractCode] = list[i]
		}
	}
	return contracts, nil
}

func GetContractMapWithNoSetCache(reqID int64, tx *sqlx.Tx) (map[string]proto.Contract, error) {
	contracts, err := cache.GetAllContractMap()
	if err != nil {
		list, err := database.GetSupportContractsTx(tx)
		if err != nil {
			log.Error("GetContractMap GetSupportContractsTx error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		//cache.SaveContractList(list, false)
		for i := range list {
			contracts[list[i].ContractCode] = list[i]
		}
	}
	return contracts, nil
}

func GetContractDetail(reqID int64, contractCode string, tx *sqlx.Tx) (*proto.Contract, error) {
	detail, err := cache.GetContractInfo(contractCode)
	if err != nil {
		detail, err = database.GetContractByCodeTx(tx, contractCode)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, define.ErrMsgContractNotExist
			}
			log.Error("GetContractDetail GetContractByCode error", zap.Int64("reqID", reqID), zap.Error(err))
			return nil, define.ErrMsgBusy
		}
		log.Info("从数据库中获取合约信息", zap.Any("信息", detail))
	}
	return detail, nil
}

func GetContractLeverRisk(lever int, list []proto.ContractLeverRisk) (rate decimal.Decimal) {
	for _, risk := range list {
		if lever >= risk.MinLever && lever <= risk.MaxLever {
			rate = risk.RiskRate
			break
		}
	}
	return
}

//获取合约的维持保证金率
func GetContractMaintainRate(cMap map[string]proto.Contract) map[string]decimal.Decimal {
	if cMap == nil {
		return nil
	}
	rateMap := make(map[string]decimal.Decimal)
	//计算合约维持保证金率
	//最低维持保证金率=最高风险率/该合约最大杠杆倍数
	maxRiskRate := GetBlowingRate()
	for _, contract := range cMap {
		rate, err := CalMaintainRate(maxRiskRate, contract.MaxValidFullLever())
		if err != nil {
			log.Errorf("计算合约维持保证金率出错：%v,code；%v", err, contract.ContractCode)
			continue
		}
		rateMap[contract.ContractCode] = rate
	}
	return rateMap
}

func CalMaintainRate(maxRiskRate decimal.Decimal, maxLever int) (rate decimal.Decimal, err error) {
	if maxRiskRate.LessThanOrEqual(decimal.Zero) {
		err = errors.New("无效的风险率")
		return
	}
	if maxLever <= 0 {
		err = errors.New("无效的合约杠杠")
		return
	}
	rate = maxRiskRate.Div(nums.NewFromInt(maxLever))
	return
}

func GetContractListForFollow() (map[string]bool, error) {
	contracts, err := cache.GetAllContractList()
	if err != nil {
		return nil, err
	}
	dict := make(map[string]bool, len(contracts))
	for i := range contracts {
		if contracts[i].IsFollow && !contracts[i].Delisted {
			dict[contracts[i].ContractCode] = true
		}
	}
	return dict, nil
}
