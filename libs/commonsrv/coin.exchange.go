package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/config"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"errors"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

var (
	ErrFetchNoPrice      = errors.New("can not get coin price")
	ErrFetchNoValidPrice = errors.New("coin price is not valid")
)

//判断当前是否可以兑换
func IsCanExchange() bool {
	//todo 如果主对冲,辅助对冲无效，返回false
	return true
}

// GetCurrentCoinHedgeSource 获取最新对冲的来源
func GetCurrentCoinHedgeSource() string {
	//获取当前配置源
	source := cache.GetExchangeHedgeSource()
	if source == 0 || source == define.ExchangeHedgeSourceBinance {
		return config.MarketNameSpotBinance
	}

	if source == define.ExchangeHedgeSourceBinance {
		return config.MarketNameSpotHuobi
	}

	return config.MarketNameSpotBinance
}

// GetCoinExchangeBasePrice 获取币种基础价格
func GetCoinExchangeBasePrice(coinName string) (price decimal.Decimal, err error) {
	coin := strings.ToUpper(coinName)
	if coin == define.CoinNameUsdt {
		return define.DecimalOne, nil
	}
	sourceID := GetCurrentCoinHedgeSource()
	if !strings.HasSuffix(coin, "USDT") {
		coin += "USDT"
	}
	return getSpotPrice(coin, sourceID, 30*time.Second)
}

func getSpotPrice(code string, source string, valid time.Duration) (price decimal.Decimal, err error) {
	mk := cache.GeContractSpotPriceBySource(code, source)
	if mk == nil {
		log.Warn("getSpotPrice GeContractSpotPriceBySource error", zap.String("code", code), zap.String("source", source))
		err = ErrFetchNoPrice
		return
	}
	if mk.IsValidTrade(valid) {
		price = mk.Price
		return
	}
	return price, ErrFetchNoValidPrice
}

func GetExchangeCoinDetails(reqID int64, base, target, basic string) (baseCoin, basicCoin, targetCoin proto.Coin, err error) {
	coins, err := cache.GetAllCoinMapByName()
	if err != nil {
		log.Error("GetExchangeCoinDetails GetCoinDetailByName fail",
			zap.Int64("reqID", reqID), zap.Error(err))
		err = define.ErrMsgBusy
		return
	}

	baseCoin, ok := coins[base]
	if !ok {
		log.Error("GetExchangeCoinDetails base coin not found",
			zap.Int64("reqID", reqID), zap.String("base", base))
		err = define.ErrMsgParam
		return
	}
	targetCoin, ok = coins[target]
	if !ok {
		log.Error("GetExchangeCoinDetails target coin not found",
			zap.Int64("reqID", reqID), zap.String("target", target))
		err = define.ErrMsgParam
		return
	}
	basicCoin, ok = coins[basic]
	if !ok {
		log.Error("GetExchangeCoinDetails basic coin not found",
			zap.Int64("reqID", reqID), zap.String("basic", basic))
		err = define.ErrMsgParam
		return
	}
	return
}

//查询兑换对冲配置
func ListExchangeHedgeConfig() map[int]proto.ExchangeHedgeConfig {
	list, err := cache.ListExchangeHedgeConfig()
	if err != nil {
		if err == redis.Nil {
			list, err = database.GetExchangeHedgeConfig(nil)
			if err != nil {
				log.Error("ListExchangeHedgeConfig database.GetExchangeHedgeConfig err", zap.Error(err))
				return nil
			}
			if len(list) > 0 {
				cache.SetExchangeHedgeConfig(list)
			}
		}
	}
	if len(list) == 0 {
		return nil
	}
	mk := make(map[int]proto.ExchangeHedgeConfig)
	for _, hedgeConfig := range list {
		mk[hedgeConfig.ChannelID] = hedgeConfig
	}
	return mk
}

//获取指定兑换指定对冲配置
func GetExchangeHedgeConfig(channelId int) *proto.ExchangeHedgeConfig {
	lm := ListExchangeHedgeConfig()
	if lm == nil {
		return nil
	}
	config, ok := lm[channelId]
	if !ok {
		return nil
	}
	return &config
}
