package commonsrv

import (
	"bc/libs/formul"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
)

// CalForcePriceForWareHouse 获取逐仓强平价
func CalForcePriceForWareHouse(side string, holdAmount int, margin, avgPrice decimal.Decimal, info *proto.Contract) decimal.Decimal {
	maxRiskRate := GetBlowingRate()
	riskAdjust := nums.NewFromInt(1)
	return formul.GetForcePriceForWareHouse(side, holdAmount, info.MaxValidWareLever(), margin, avgPrice, nums.NewFromString(info.ParValue), riskAdjust, maxRiskRate, info.Digit)
}

// CalForcePriceForFollow 获取跟单强平价
func CalForcePriceForFollow(side string, holdAmount int, margin, avgPrice decimal.Decimal, info *proto.Contract) decimal.Decimal {
	maxRiskRate := GetBlowingRate()
	riskAdjust := nums.NewFromInt(1)
	return formul.GetForcePriceForWareHouse(side, holdAmount, info.MaxValidFollowLever(), margin, avgPrice, nums.NewFromString(info.ParValue), riskAdjust, maxRiskRate, info.Digit)
}
