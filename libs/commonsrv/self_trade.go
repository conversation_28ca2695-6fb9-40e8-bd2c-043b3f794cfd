package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
	"strings"
	"time"
)

func GenerateIndexHistory(order *proto.MatchOrder) (index *proto.IndexHistory) {
	//查询合约详情
	c, err := GetContractDetail(order.Id, order.ContractCode, nil)
	if err != nil {
		log.Errorf("GenerateIndexHistory GetContractDetail fail,%v", err)
		return nil
	}

	//推送成交消息
	tradePrice := nums.NewFromFloat(order.TradePrice)
	usd, _ := cache.GetCoinRate(define.LegalCoinNameUSD)
	var priceCn decimal.Decimal
	if strings.ToUpper(c.CoinName) == "USDT" {
		priceCn = tradePrice.Mul(nums.NewFromFloat(usd.PriceCNY)).Round(define.CNYPrecision)
	} else {
		//折合成保证金币种量，换算成美元，折合人民币
		u, _ := cache.GetCoinRate(c.CoinName)
		priceCn = tradePrice.Mul(nums.NewFromFloat(u.PriceUSDT)).Mul(nums.NewFromFloat(usd.PriceCNY)).Round(define.CNYPrecision)
	}

	index = &proto.IndexHistory{
		IndexId:      database.NextID(),
		ContractCode: c.ContractCode,
		TradePrice:   tradePrice,
		TradePriceCn: priceCn,
		PerValue:     nums.NewFromFloat(order.PerValue),
		DealAmount:   order.TradeVolume,
		Side:         order.Side,
		DealTime:     time.Now().Unix(),
		CreateBy:     time.Now(),
		IsMain:       true,
	}

	order.TradePriceCn = index.TradePriceCn.StringFixedBank(2)
	order.TradePriceStr = index.TradePrice.Truncate(c.Digit).StringFixedBank(c.Digit)

	////检测大数成交
	//go checkLargeTrade(c, order)
	//
	////处理中鱼成交
	//go dealRealTradeInfo(order)
	return
}

func dealRealTradeInfo(order *proto.MatchOrder) {
	//判断是否是中鱼成交
	isBait := false
	matchPrice := order.MatchPrice
	tradePrice := nums.NewFromFloat(order.TradePrice)
	if order.MatchPrice.TradePrice.GreaterThan(decimal.Zero) {
		if order.Side == define.OrderBuy {
			max := matchPrice.SellMax
			if max.LessThanOrEqual(decimal.Zero) {
				return
			}
			if tradePrice.GreaterThanOrEqual(max) {
				isBait = true
			}
		} else {
			max := matchPrice.BuyMax
			if max.LessThanOrEqual(decimal.Zero) {
				return
			}
			if tradePrice.LessThanOrEqual(max) {
				isBait = true
			}
		}
		return
	}
	if isBait {
		cache.AddContractBaitTrade(order)
	}
}

func checkLargeTrade(c *proto.Contract, order *proto.MatchOrder) {
	if c == nil {
		return
	}
	depthConfig := GetContractDepthConfig(c.ContractCode)
	if depthConfig == nil {
		log.Error("commonsrv.GetContractDepthConfig nil")
		return
	}
	max := nums.NewFromInt(c.MaxOrderVolume).Mul(depthConfig.LargeTradeFactor)
	if nums.NewFromInt(order.TradeVolume).GreaterThanOrEqual(max) {
		//满足条件
		cache.IncrContractSideLargeTradeCount(c.ContractCode, order.Side, 1)
	}
}

func IsMaintenance(code string) bool {
	c, err := GetContractDetail(0, code, nil)
	if err != nil {
		log.Errorf("OrderOpenPosition GetContractDetail fail,%v", err)
		return false
	}
	return c.IsMaintenance.Bool()
}
