package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/define"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
)

//func ConvertOrderType2MatchType(orderType int) int {
//	if orderType == define.OrderTypeLimit || orderType == define.OrderTypeStop || orderType == define.OrderTypeFollowLimit || orderType == define.OrderTypeFollowStop {
//		return define.MatchTypeLimitStop
//	}
//	if orderType == define.OrderTypeForceCloseOut {
//		return define.MatchTypeForce
//	}
//	return define.MatchTypeDefault
//}

//func GetContractMarketMaxPrice(code, side string) (buy1, buy5, sell1, sell5 decimal.Decimal) {
//	depth := cache.GetContractOriginDepth(code)
//	if depth == nil {
//		return decimal.Zero
//	}
//	return decimal.Zero
//}

//1-指数价格大于等于时触发，2-指数价格小于等于时触发
func GetLimitStopCondation(limit, stop, curPrice decimal.Decimal, side string) (limitCon, stopCon int) {
	if limit.GreaterThan(decimal.Zero) {
		if side == define.OrderBuy {
			if limit.GreaterThanOrEqual(curPrice) {
				// 买单 如果止盈价大于等于当前指数价,应该在合约价格大于等于止盈价时触发(正常逻辑)
				limitCon = define.OrderConditionGreaterOrEqual
			} else {
				// 买单 如果止盈价小于当前指数价,应该在合约价格小于等于止盈价时触发
				limitCon = define.OrderConditionLessOrEqual
			}
		} else {
			if limit.LessThanOrEqual(curPrice) {
				// 卖单 如果止盈价小于等于当前指数价,应该在合约价格小于等于止盈价时触发(正常逻辑)
				limitCon = define.OrderConditionLessOrEqual
			} else {
				// 卖单 如果止盈价大于当前指数价,应该在合约价格大于等于止盈价时触发
				limitCon = define.OrderConditionGreaterOrEqual
			}
		}
	}

	if stop.GreaterThan(decimal.Zero) {
		if side == define.OrderBuy {
			if stop.LessThanOrEqual(curPrice) {
				// 买单 如果止损价小于等于当前指数价,应该在合约价格小于等于止损价时触发(正常逻辑)
				stopCon = define.OrderConditionLessOrEqual
			} else {
				// 买单 如果止损价大于当前指数价,应该在合约价格大于等于止损价时触发
				stopCon = define.OrderConditionGreaterOrEqual
			}
		} else {
			if stop.GreaterThanOrEqual(curPrice) {
				// 卖单 如果止损价大于等于当前指数价,应该在合约价格大于等于止损价时触发(正常逻辑)
				stopCon = define.OrderConditionGreaterOrEqual
			} else {
				// 卖单 如果止损价小于当前指数价,应该在合约价格小于等于止损价时触发
				stopCon = define.OrderConditionLessOrEqual
			}
		}
	}
	return
}

func CalPositionBrokenPrice(position *proto.UserPosition) (brokePrice decimal.Decimal) {
	//破产价＝[(多头开仓价值－空头开仓价值)－（总资产＋Σ其他合约未实现盈亏＋Σ其他合约最低维持保证金）]/净持仓*面值
	info, _ := cache.GetContractInfo(position.ContractCode)
	worth := nums.NewFromInt(position.Volume).Mul(nums.NewFromString(info.ParValue)).Mul(nums.NewFromFloat(position.Price))
	netPos := 0
	if position.Side == define.OrderBuy {
		netPos += position.Volume
	} else {
		worth = worth.Neg()
		netPos -= position.Volume
	}
	return worth.Sub(nums.NewFromFloat(position.Margin).Sub(nums.NewFromFloat(position.Commission))).Div(nums.NewFromInt(netPos).Mul(nums.NewFromString(info.ParValue))).Truncate(info.Digit)
}

func CalPositionBrokenPriceForFollow(position *proto.FollowPosition) (brokePrice decimal.Decimal) {
	//破产价＝[(多头开仓价值－空头开仓价值)－（总资产＋Σ其他合约未实现盈亏＋Σ其他合约最低维持保证金）]/净持仓*面值
	info, _ := cache.GetContractInfo(position.ContractCode)
	worth := nums.NewFromInt(position.Volume).Mul(nums.NewFromString(info.ParValue)).Mul(position.Price)
	netPos := 0
	if position.Side == define.OrderBuy {
		netPos += position.Volume
	} else {
		worth = worth.Neg()
		netPos -= position.Volume
	}
	return worth.Sub(position.Margin).Sub(position.Commission).Div(nums.NewFromInt(netPos).Mul(nums.NewFromString(info.ParValue))).Truncate(info.Digit)
}

func IsValidEntrustStrategy(sy int) bool {
	return sy == define.MatchStrategyDefault || sy == define.MatchStrategyFOK || sy == define.MatchStrategyIOC || sy == define.MatchStrategyMaker
	//	MatchStrategyDefault       = 0   //默认(限价单不能成交则挂单，市价单未成交撤销）
	//MatchStrategyFOK           = 1   //以限价单处理,如果不能全部成交，则撤销单子
	//MatchStrategyIOC           = 2   //未成交部分撤销(市价单使用同样逻辑）
	//MatchStrategyMaker         = 3   //只做maker
}

//获取默认市价委托模式
func GetDefaultEntrustMode() int {
	//当配置值大约最优5挡，优先已系统委托模式
	sysLevel := GetSystemTradeLevel()
	if sysLevel > 5 {
		return define.EntrustModeSystem
	}
	return define.EntrustModeBest5Level
}

func GetSystemTradeLevel() int {
	return 5
}

////检查委托相关信息
//func IsCheckEntrustInfo(userId int64,code, entrustSide string, entrustType, entrustStrategy int, price decimal.Decimal, info *proto.Contract) (err error) {
//	//获取撮合最新深度，判断限价单价格限制
//	depth := cache.GetContractOriginDepth(code)
//	if depth == nil {
//		if entrustType == define.EntrustTypeMarket || entrustStrategy == define.MatchStrategyIOC {
//			log.Error("合约当前深度不足", zap.Any("code", code))
//			return define.ErrMsgDepthInvalid
//		} else {
//			depth = &proto.DepthContainer{ContractCode: info.ContractCode, Digit: info.Digit}
//		}
//	}
//	log.Info("当前深度", zap.Any("depth", depth))
//
//	//获取标记价格及现货指数
//	var marketPrice, spotPrice decimal.Decimal
//	marketPriceEntity := cache.GetContractComplexTradePrice(code)
//	if marketPriceEntity != nil {
//		marketPrice = marketPriceEntity.Price
//		spotPrice = marketPriceEntity.SpotIndexPrice
//	}
//
//	if entrustType == define.EntrustTypeLimit {
//		if price.LessThanOrEqual(decimal.Zero) {
//			return define.ErrMsgParam
//		}
//		//判断价格是否有效，是否在价格变动有效
//		if price.Mod(info.PriceStep).GreaterThan(decimal.Zero) {
//			return define.ErrMsgPriceInvalid
//		}
//		if !IsValidEntrustStrategy(entrustStrategy) {
//			log.Info("无效委托策略", zap.Any("order", entrustSide))
//			return define.ErrMsgParam
//		}
//
//		//最高买价＝max(卖一价，当前标记价格，当前现货指数)*（1+买价限制）
//		maxBuyPrice := decimal.Max(depth.SellFirst, marketPrice, spotPrice).Mul(nums.NewFromInt(1).Add(info.LimitBuyPrice))
//		//最低卖价＝min(买一价，当前标记价格，当前现货指数)*（1－卖价限制）
//		lowSellPrice := decimal.Min(depth.BuyFirst, marketPrice, spotPrice).Mul(nums.NewFromInt(1).Sub(info.LimitSellPrice))
//		//用户买入价格≤最高买价限制 用户卖出价格≥最低卖价限制
//		if entrustSide == define.OrderBuy && price.GreaterThan(maxBuyPrice) {
//			log.Info("用户买单，委托价格大于最高价", zap.String("最高价", maxBuyPrice.String()), zap.Any("价格", map[string]string{"卖一": depth.SellFirst.String(), "标记": marketPrice.String(), "现货指数": spotPrice.String(), "最低买价限制": info.LimitBuyPrice.String()}))
//			return define.ErrMsgPriceOverLimit
//		}
//
//		if entrustSide == define.OrderSell && price.LessThan(lowSellPrice) {
//			log.Info("用户卖单，委托价格低于最低价", zap.String("最低价", lowSellPrice.String()), zap.Any("价格", map[string]string{"买一": depth.BuyFirst.String(), "标记": marketPrice.String(), "现货指数": spotPrice.String(), "最低卖价限制": info.LimitSellPrice.String()}))
//			return define.ErrMsgPriceOverLimit
//		}
//
//	} else {
//		//市价单传递委托策略强制重置
//		if entrustStrategy > 0 {
//			entrustStrategy = define.MatchStrategyDefault
//		}
//	}
//
//
//	if entrustType == define.EntrustTypeMarket || entrustStrategy == define.MatchStrategyIOC && depth != nil {
//		//市价单判断对手盘是否存在
//		if entrustSide == define.OrderBuy {
//			if depth.BuyFirst.LessThanOrEqual(decimal.Zero) {
//				return define.ErrMsgDepthInvalid
//			}
//		} else {
//			if depth.SellFirst.LessThanOrEqual(decimal.Zero) {
//				return define.ErrMsgDepthInvalid
//			}
//		}
//	}
//
//	return
//}

// GetContractTradeMerge 获取合约成交综合信息
func GetContractTradeMerge(code string) (d *proto.DealInfo, err error) {
	d = &proto.DealInfo{
		Code: code,
	}
	depth := cache.GetContractOriginDepth(code)
	if depth != nil {
		dsInfo := depth.GetDepthSimpleInfo()
		d.BuyFirst = dsInfo.BuyFirst
		d.SellFirst = dsInfo.SellFirst
		d.BuyThree = dsInfo.BuyThree
		d.SellThree = dsInfo.SellThree
		d.BuyFive = dsInfo.BuyFive
		d.SellFive = dsInfo.SellFive
	}
	in := cache.GetContractPriceIndex(code)
	if in != nil {
		d.LastDealPrice = in.TradePrice
		d.LastDealTime = in.CreateBy
	}
	return
}
