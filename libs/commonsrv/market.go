package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/msg"
	"bc/libs/proto"
	"encoding/base64"
	"fmt"
	"strings"
	"time"
)

func GetContractConfig(code string) (c *proto.MarketConfig, err error) {
	var list []proto.MarketConfig
	list, err = database.GetContractMarketConfig()
	if err != nil {
		log.Errorf("database.GetContractMarketConfig fail,%v", err)
		return nil, err
	}
	for _, config := range list {
		con := config
		s := config.ContractCodes
		if strings.Contains(s, code) {
			c = &con
		}
	}
	return
}

func SendWarningEmailWithMarket(warningType, content string, contractCode string, marketSubType, extra string) {
	SendWarningEmailWithMarketExpire(warningType, content, contractCode, marketSubType, extra, 5*time.Minute)
}

func SendWarningEmailByMarketWarn(warningType, content string, contractCode string, marketSubType, extra string) {
	SendWarningEmailWithMarketExpire(warningType, content, contractCode, marketSubType, extra, 5*time.Minute)
}

func SendWarningEmailWithMarketExpire(warningType, content string, contractCode string, marketSubType, extra string, duration time.Duration) {
	log.Infof("开始准备发送预警邮件，type:%v,content：%v,code；%v,subType:%v", warningType, content, contractCode, marketSubType)
	var config *proto.MarketConfig
	var err error
	if contractCode != "" { //api状态类，不传contractcode
		config, err = GetContractConfig(contractCode)
		if err != nil {
			log.Errorf("GetContractConfig db fail,%v", err)
			return
		}
		if config == nil || !config.NoticeSwitch {
			log.Infof("发送告警邮件，该合约配置为空获开关为关闭状态，code:%v", contractCode)
			return
		}
	}
	//获取预警邮件地址
	address, err := GetWarnEmails()
	if err != nil {
		log.Errorf("获取报警邮件出错：%v", err)
	}
	var status bool
	if len(address) == 0 {
		log.Infof("行情预警发送的邮箱地址为空")
		status = true
	}

	//同类型是否发送 5分钟过滤
	if warningType != define.WaringTypeAPIStatus {
		typeCacheKey := base64.StdEncoding.EncodeToString([]byte(warningType))
		if !cache.SetRedisLockWithExp(duration, define.CacheMaketWarnTypeKey, contractCode, marketSubType, typeCacheKey) {
			log.Warnf("5分钟内已发送子类型告警，本次忽略，code:%v,,content:%v，last：%v", contractCode, content, content)
			return
		}
	}

	var key = warningType + contractCode
	if extra == define.WSDataManul {
		key += define.WSDataManul
	}
	keyBase64 := base64.StdEncoding.EncodeToString([]byte(key))
	//log.Infof("k3:%v",keyBase64)
	lastContent := cache.GetMarketLastWarn(keyBase64)
	desContent := content + extra
	if extra != define.NoUse && desContent == lastContent {
		log.Warnf("与最后一次发送内容相同，不发送告警,content:%v，last：%v", content, lastContent)
		return
	}

	for _, v := range address {
		s := SendWarningEmailByExmail(v, GetWarningSubject(warningType), GetWarningContent(contractCode, "", content))
		if s {
			status = true
		}
	}
	if status && extra != define.NoUse {
		cache.SetMarketLastWarn(keyBase64, desContent)
	}

	notice := proto.WarnNotice{
		NoticeType:    0,
		NoticeContent: content,
		CreatedBy:     time.Now(),
		HedgeAccount:  "",
		ContractCode:  contractCode,
	}
	switch warningType {
	case define.WaringTypeAsset:
		notice.NoticeType = 1
	case define.WaringTypeTrade:
		notice.NoticeType = 2
	case define.WaringTypeAPIStatus:
		notice.NoticeType = 3
	case define.WaringTypeMarket:
		notice.NoticeType = 4
	}
	if status {
		notice.NoticeStatus = 1
	}

	//数据库操作，插入数据库记录
	err = database.InsertWarnNoticeWithMarket(notice)
	if err != nil {
		log.Errorf("database.InsertWarnNotice fail,%v", err)
		return
	}
}

//发送邮件
func SendWarningEmailByExmail(address, subject, content string) (success bool) {
	log.Debugf("发送邮件：%v,subject；%v,content:%v", address, subject, content)
	if address == "" {
		log.Errorf("邮箱地址不能为空")
		return
	}
	err := msg.SendEmailWithExmail(address, subject, content)
	if err != nil {
		log.Errorf("发送邮件告警失败，address:%v,subject:%v,err:%v", address, subject, err)
		return false
	}
	return true
}

//获取邮件内容
func GetWarningContent(contractCode, account, content string) string {
	return fmt.Sprintf("%s\n%s\n%s", content, contractCode, account)
}

//获取对冲账户主题
func GetWarningSubject(warningType string) string {
	return "行情接口预警" + warningType
}
