package commonsrv

import (
	"bc/libs/xrpcclient/base_third_rpc"
	"context"
	"sort"
	"strconv"
	"strings"

	"bc/libs/address"
	"bc/libs/cache"
	"bc/libs/conf"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	//"bc/pkg/core/db"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// 检查并创建钱包
func CheckWallet(uid int64, coinID int, platformId int) {
	// 获取是否是支持的资产币种
	_, err := cache.GetCoinByID(coinID)
	if err == nil {
		// 判断资产账户
		if !database.WalletExist(uid, coinID) {
			database.CreateWallet(uid, coinID, platformId)
		}
	}

	// 获取是否是支持的交易币种
	_, err = cache.GetMarginCoinByID(coinID)
	if err == nil {
		// 判断交易账户
		if !database.AccountExist(uid, coinID) {
			database.CreateAccount(uid, coinID, platformId)
		}
		// 判断跟单账户
		if !database.FollowAccountExist(uid, coinID) {
			database.CreateFollowAccount(uid, coinID, platformId)
		}
	}
}

// 检查全部资产并在需要时创建钱包
func CheckWalletAndCreate(reqID, uid int64, platformId int) bool {
	// 检查资产账户币种
	wcs, err := GetCoinList()
	if err != nil {
		log.Error("CheckWalletAndCreate GetCoinList fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return false
	}

	var cl []int
	for i := range wcs {
		if wcs[i].Status != 0 {
			cl = append(cl, wcs[i].CurrencyId)
		}
	}

	// 获取缺失的资产币种
	diff, err := database.CheckMissWalletCoin(uid, cl...)
	if err != nil {
		log.Error("CheckWalletAndCreate CheckMissWalletCoin fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return false
	}
	for i := range diff {
		database.CreateWallet(uid, diff[i], platformId)
	}

	// 检查保证金账户币种
	mcs, err := GetMarginCoinList()
	if err != nil {
		log.Error("CheckWalletAndCreate GetMarginCoinList fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return false
	}
	for i := range mcs {
		// 判断交易账户
		if !database.AccountExist(uid, mcs[i].CurrencyId) {
			database.CreateAccount(uid, mcs[i].CurrencyId, platformId)
		}
		// 判断跟单账户
		if !database.FollowAccountExist(uid, mcs[i].CurrencyId) {
			database.CreateFollowAccount(uid, mcs[i].CurrencyId, platformId)
		}
	}
	return true
}

// 获取用户可用余额等信息
//func GetAssetDetail(reqID int64, coinID int, mode define.AssetMode, user *define.TokenPayload) (*proto.AssetDetail, error) {
//	coin, err := GetCoinDetailByID(coinID)
//	if err != nil {
//		return nil, define.ErrMsgBusy
//	}
//	CheckWallet(user.UserID, coinID, user.PlatformID)
//
//	var (
//		assetAccount proto.WalletDetail
//		tradeAccount proto.TradeWalletDetail
//	)
//
//	if mode == define.AssetModeAll || mode == define.AssetModeForWallet {
//		/* 资产账户 */
//		assetAccount, err = GetUserAssetAccount(reqID, user.UserID, coinID)
//		if err != nil {
//			return nil, err
//		}
//	}
//	if mode == define.AssetModeAll || mode == define.AssetModeForTrade {
//		/* 交易账户 */
//		tradeAccount, err = GetUserTradeAccount(reqID, user.UserID, user.ProfitStyle, coinID)
//		if err != nil {
//			return nil, err
//		}
//	}
//
//	asset := &proto.AssetDetail{
//		CoinID:       coin.CurrencyId,
//		CoinName:     coin.CurrencyName,
//		Wallet:       assetAccount,
//		Trade:        tradeAccount,
//		TotalBalance: nums.Float(decimal.NewFromFloat(tradeAccount.Total).Truncate(define.ReplyFloatPrecision).Add(decimal.NewFromFloat(assetAccount.Total).Truncate(define.ReplyFloatPrecision))),
//	}
//
//	// TODO 模拟盘跟单账户暂时不统计
//	if !conf.IsSimulate() && (mode == define.AssetModeAll || mode == define.AssetModeForFollow) {
//		/* 跟单账户 */
//		followAccount, err := GetUserFollowAccount(reqID, user.UserID, user.ProfitStyle, coinID)
//		if err != nil {
//			return nil, err
//		}
//		asset.Follow = followAccount
//		asset.TotalBalance = nums.Float(decimal.NewFromFloat(asset.TotalBalance).Add(followAccount.Total))
//	}
//
//	// 获取usdt价格
//	usdt, _ := cache.GetCoinRate(define.CoinNameUsdt)
//	asset.TotalBalanceCNY = decimal.NewFromFloat(asset.TotalBalance).Mul(decimal.NewFromFloat(usdt.PriceCNY)).Truncate(define.CNYPrecision)
//	return asset, nil
//}

//func GetUserAssetAccount(reqID, userID int64, coinID int) (proto.WalletDetail, error) {
//	var asset proto.WalletDetail
//	wallet, err := database.GetUserWallet(userID, coinID)
//	if err != nil {
//		log.Error("GetUserAssetAccount GetUserWallet error",
//			zap.Int64("reqID", reqID),
//			zap.Int64("userID", userID),
//			zap.Int("coinID", coinID),
//			zap.Error(err))
//		return asset, define.ErrMsgBusy
//	}
//
//	// 获取24小时内总法币交易完成数量
//	totalAmount, err := db.GetLegalTotalAmountWithin24h(nil, userID, coinID)
//	if err != nil {
//		log.Error("GetUserAssetAccount GetLegalTotalAmountWithin24h error",
//			zap.Int64("reqID", reqID),
//			zap.Int64("userID", userID),
//			zap.Int("coinID", coinID),
//			zap.Error(err))
//		return asset, define.ErrMsgBusy
//	}
//
//	// 可提现余额
//	canWithdraw := decimal.NewFromFloat(wallet.Balance).Truncate(define.ReplyFloatPrecision).Sub(decimal.RequireFromString(totalAmount).Truncate(define.ReplyFloatPrecision))
//	if canWithdraw.LessThan(decimal.Zero) {
//		canWithdraw = decimal.Zero
//	}
//
//	asset.Total = nums.Float(decimal.NewFromFloat(wallet.Balance).Truncate(define.ReplyFloatPrecision).Add(decimal.NewFromFloat(wallet.WithdrawLock).Truncate(define.ReplyFloatPrecision)).Truncate(define.ReplyFloatPrecision))
//	asset.Available = wallet.Balance
//	asset.Frozen = wallet.WithdrawLock
//	asset.CanWithdraw = canWithdraw.Truncate(define.FundingRatePrecision).StringFixedBank(define.FundingRatePrecision)
//
//	// 获取usdt价格
//	usdt, _ := cache.GetCoinRate(define.CoinNameUsdt)
//	asset.TotalCNY = nums.NewFromFloat(asset.Total).Mul(decimal.NewFromFloat(usdt.PriceCNY)).Truncate(define.CNYPrecision)
//	return asset, nil
//}

//func GetUserTradeAccount(reqID, userID int64, profitStyle, coinID int) (proto.TradeWalletDetail, error) {
//	var asset proto.TradeWalletDetail
//	// 获取是否是支持的交易币种
//	_, err := cache.GetMarginCoinByID(coinID)
//	if err == nil {
//		account, err := db.GetUserAccount(nil, userID, coinID)
//		if err != nil {
//			log.Error("GetUserTradeAccount GetUserAccount error",
//				zap.Int64("reqID", reqID),
//				zap.Int64("userID", userID),
//				zap.Int("coinID", coinID),
//				zap.Error(err))
//			return asset, define.ErrMsgBusy
//		}
//
//		_, totalMargin, _, initMargin, floatProfit, err := GetUserCoinMargin(nil, userID, profitStyle, coinID)
//		if err != nil {
//			return asset, define.ErrMsgParam
//		}
//
//		asset.Rights = nums.Float(decimal.NewFromFloat(account.Balance).Truncate(define.ReplyFloatPrecision).Add(floatProfit.Truncate(define.ReplyFloatPrecision)).Truncate(define.ReplyFloatPrecision))
//		asset.Total = asset.Rights
//		asset.Float = nums.Float(floatProfit.Sub(account.Diff))
//		asset.Margin = nums.Float(initMargin)
//		asset.TotalMargin = totalMargin
//		asset.Balance = nums.NewFromFloat(account.Balance)
//		asset.Available = account.Available
//		if asset.Available < 0 {
//			asset.Available = 0
//		}
//		asset.CanTransfer = account.Available
//		asset.Diff = account.Diff
//		asset.LockAmount = account.LockAmount
//
//		if !initMargin.IsZero() {
//			asset.CanTransfer = nums.Float(CanTransferAmount(decimal.NewFromFloat(account.Balance).Sub(account.LockAmount), account.GiftBalance, floatProfit, totalMargin).Truncate(define.UsdtAssetFloatPrecision))
//			asset.RiskRate = GetUserAccountRiskRate(userID)
//		}
//		asset.BlowingRate = GetBlowingRate()
//
//		// 获取usdt价格
//		usdt, _ := cache.GetCoinRate(define.CoinNameUsdt)
//		asset.TotalCNY = nums.NewFromFloat(asset.Total).Mul(decimal.NewFromFloat(usdt.PriceCNY)).Truncate(define.CNYPrecision)
//	}
//	return asset, nil
//}

func GetUserFollowAccount(reqID, userID int64, profitStyle, coinID int) (proto.FollowWalletDetail, error) {
	var asset proto.FollowWalletDetail
	// 获取是否是支持的交易币种
	_, err := cache.GetMarginCoinByID(coinID)
	if err == nil {
		account, err := database.GetFollowAccountByUserId(nil, userID, coinID)
		if err != nil {
			log.Error("GetUserFollowAccount GetFollowAccountByUserId error",
				zap.Int64("reqID", reqID),
				zap.Int64("userID", userID),
				zap.Int("coinID", coinID),
				zap.Error(err))
			return asset, define.ErrMsgBusy
		}

		_, totalMargin, initMargin, floatProfit, err := GetUserFollowCoinMargin(nil, userID, profitStyle, coinID)
		if err != nil {
			return asset, define.ErrMsgParam
		}

		asset.Available = account.Balance.Truncate(define.ReplyFloatPrecision)
		asset.Float = floatProfit.Truncate(define.ReplyFloatPrecision)
		asset.Margin = totalMargin.Truncate(define.ReplyFloatPrecision)
		asset.Total = asset.Available.Add(totalMargin).Add(floatProfit).Truncate(define.ReplyFloatPrecision)
		asset.CanTransfer = asset.Available
		asset.LockAmount = account.LockAmount

		if !initMargin.IsZero() {
			asset.CanTransfer = CanTransferAmount(account.Balance.Add(totalMargin), decimal.Zero, floatProfit, totalMargin).Truncate(define.UsdtAssetFloatPrecision)
		}

		// 获取usdt价格
		usdt, _ := cache.GetCoinRate(define.CoinNameUsdt)
		asset.TotalCNY = asset.Total.Mul(decimal.NewFromFloat(usdt.PriceCNY)).Truncate(define.CNYPrecision)
	}
	return asset, nil
}

// 用户持仓保证金之和+浮动盈亏,起始保证金,浮动盈亏
func GetUserCoinMargin(tx *sqlx.Tx, userId int64, profitStyle, coinid int) (margin, totalMargin, totalMarginGift, initMargin, floatProfit decimal.Decimal, err error) {
	return GetCoinMarginDetail(tx, userId, profitStyle, coinid, "", decimal.Zero)
}

// 获取交易账户仓位保证金
func GetCoinMargin(tx *sqlx.Tx, userId int64, coinId int, contractCode string, mp decimal.Decimal) (total decimal.Decimal, floatProfit decimal.Decimal, err error) {
	list, err := database.GetMarginCoinContracts(tx, coinId)
	if err != nil {
		log.Errorf("database.GetMarginCoinContracts fail,%v", err)
		return
	}
	//log.Infof("coinMargin size；%v", len(list))
	for _, code := range list {
		var marketPrice decimal.Decimal
		if code == contractCode {
			marketPrice = mp
		} else {
			index := cache.GetContractComplexTradePrice(code)
			if index != nil {
				marketPrice = index.Price
			}
		}
		//此处浮动盈亏已通过数据库实时计算
		m, err := database.GetCoinMarginStat(tx, userId, code, marketPrice)
		if err != nil {
			log.Errorf("database.GetCoinMargin fail,%v", err)
			continue
		}
		//log.Infof("code:%v", code)
		total = total.Add(nums.NewFromFloat(m.Margin)).Add(nums.NewFromFloat(m.FloatProfit))
		floatProfit = floatProfit.Add(nums.NewFromFloat(m.FloatProfit))
		log.Debugf("userId；%v,code：%v,margin:%v，float:%v,total:%v", userId, code, m.Margin, m.FloatProfit, total.String())

	}
	return
}

// 获取交易账户仓位保证金
func GetCoinMarginDetail(tx *sqlx.Tx, userId int64, profitStyle, coinId int, contractCode string, mp decimal.Decimal) (total, totalMargin, totalMarginGift, initMargin, floatProfit decimal.Decimal, err error) {
	list, err := database.GetMarginCoinContracts(tx, coinId)
	if err != nil {
		log.Errorf("database.GetMarginCoinContracts, userID:%d, fail,%v", userId, err)
		return
	}

	indexs := cache.GetAllPriceIndex()
	//log.Infof("coinMargin size；%v", len(list))
	for _, code := range list {
		var marketPrice decimal.Decimal
		if code == contractCode {
			marketPrice = mp
		} else {
			if profitStyle == define.UserProfitStyleWithTrade {
				// 使用成交价格计算
				marketPrice = indexs[code].TradePrice
			} else {
				// 使用标记价格计算
				index := cache.GetContractComplexTradePrice(code)
				if index == nil {
					continue
				}
				marketPrice = index.Price
			}
			//index := cache.GetContractPriceIndex(code)
			//if index != nil {
			//	buyPrice = index.BuyPrice
			//	sellPrice = index.SellPrice
			//}
		}
		//此处浮动盈亏已通过数据库实时计算
		m, err := database.GetCoinMarginStat(tx, userId, code, marketPrice)
		if err != nil {
			log.Errorf("database.GetCoinMargin , userID:%d, fail,%v", userId, err)
			continue
		}
		//log.Infof("code:%v", code)
		total = total.Add(nums.NewFromFloat(m.Margin)).Add(nums.NewFromFloat(m.FloatProfit))
		totalMargin = totalMargin.Add(nums.NewFromFloat(m.Margin))
		totalMarginGift = totalMarginGift.Add(m.MarginGift)
		initMargin = initMargin.Add(nums.NewFromFloat(m.Margin).Sub(nums.NewFromFloat(m.Commission)))
		floatProfit = floatProfit.Add(nums.NewFromFloat(m.FloatProfit))
		log.Debugf("userId；%v,code：%v,margin:%v，float:%v,total:%v", userId, code, m.Margin, m.FloatProfit, total.String())
	}
	return
}

// 获取交易账户仓位保证金
func GetCoinFollowMargin(tx *sqlx.Tx, userId int64, coinId int, contractCode string, mp decimal.Decimal) (total decimal.Decimal, floatProfit decimal.Decimal, err error) {
	list, err := database.GetMarginCoinContracts(tx, coinId)
	if err != nil {
		log.Errorf("database.GetMarginCoinContracts fail,%v", err)
		return
	}
	//log.Infof("coinMargin size；%v", len(list))
	for _, code := range list {
		var marketPrice decimal.Decimal
		if code == contractCode {
			marketPrice = mp
		} else {
			index := cache.GetContractComplexTradePrice(code)
			if index != nil {
				marketPrice = index.Price
			}
		}
		//此处浮动盈亏已通过数据库实时计算
		m, err := database.GetFollowCoinMarginStat(tx, userId, code, marketPrice)
		if err != nil {
			log.Errorf("database.GetCoinMargin fail,%v", err)
			continue
		}
		//log.Infof("code:%v", code)
		total = total.Add(nums.NewFromFloat(m.Margin)).Add(nums.NewFromFloat(m.FloatProfit))
		floatProfit = floatProfit.Add(nums.NewFromFloat(m.FloatProfit))
		log.Debugf("userId；%v,code：%v,margin:%v，float:%v,total:%v", userId, code, m.Margin, m.FloatProfit, total.String())

	}
	return total.Truncate(define.FloatPrecision), floatProfit.Truncate(define.FloatPrecision), nil
}

////用户持仓保证金之和+浮动盈亏
//func GetUserCoinMargin(tx *sqlx.Tx, userId int64, coinid int) (total decimal.Decimal, err error) {
//	return GetCoinMargin(tx, userId, coinid, "", 0, 0)
//}
//
//
////获取交易账户仓位保证金
//func GetCoinMargin(tx *sqlx.Tx, userId int64, coinId int, contractCode string, buy, sell float64) (total decimal.Decimal, err error) {
//	list, err := database.GetMarginCoinContracts(tx, userId, coinId)
//	if err != nil {
//		log.Errorf("database.GetMarginCoinContracts fail,%v", err)
//		return
//	}
//	for _, s := range list {
//		var buyPrice, sellPrice float64
//		if s == contractCode {
//			buyPrice, sellPrice = buy, sell
//		} else {
//			index := cache.GetContractPriceIndex(s)
//			if index != nil {
//				buyPrice = index.BuyPrice
//				sellPrice = index.SellPrice
//			}
//		}
//		m, err := database.GetCoinMargin(tx, userId, s, buyPrice, sellPrice)
//		if err != nil {
//			log.Errorf("database.GetCoinMargin fail,%v", err)
//			continue
//		}
//		total = total.Add(nums.NewFromString(m))
//
//	}
//	return
//}

func CheckWalletAddr(ctx context.Context, reqID, userID int64, platformID int, coinName, protocol string) (string, error) {
	// 使用币种名称和协议获取币种配置
	coin, err := GetWalletCoinConfByNameAndProtocol(nil, reqID, coinName, protocol)
	if err != nil {
		return "", err
	}

	var coinAddress string
	if coin.CanDeposit {
		// 支持充币才获取地址,根据walletName获取对应的充币地址
		coinAddress, err = GetMultiDepositAddress(ctx, reqID, userID, platformID, coin)
		if err != nil {
			return "", err
		}
	}
	return coinAddress, nil
}

func ConvertCoinProtocol(coinName, protocol string) string {
	if protocol == "" {
		if coinName == define.CoinNameUsdt {
			protocol = define.WalletCoinConfDefaultProtocolUSDT
		} else {
			protocol = define.WalletCoinConfDefaultProtocol
		}
	}
	return protocol
}

// 获取用户可划转交易资产余额
func GetTradeAssetAvailableForTransfer(reqID int64, avail decimal.Decimal, coinID int, userID int64, tx *sqlx.Tx) (decimal.Decimal, error) {
	if avail.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, nil
	}

	list, err := database.GetCoinInitMarginStat(tx, userID, coinID)
	if err != nil {
		log.Error("GetTradeAssetAvailableForTransfer GetCoinInitMarginStat error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return decimal.Zero, define.ErrMsgBusy
	}

	// 可转出数量 = 可用余额*(1-0.005) / (占用初始保证金*杠杆 之和)
	sum := decimal.Zero
	for i := range list {
		if list[i].Lever <= 0 {
			log.Error("GetTradeAssetAvailableForTransfer lever is zero",
				zap.Int64("reqID", reqID),
				zap.Int64("userID", userID),
				zap.Int("coinID", coinID),
				zap.Any("data", list[i]))
			return decimal.Zero, define.ErrMsgBusy
		}
		sum = sum.Add(nums.NewFromString(list[i].InitMargin).Mul(nums.NewFromInt(list[i].Lever)))
	}
	var canTransfer decimal.Decimal
	if sum.IsZero() {
		// 如果没有持仓,那么可划转资产为所有可用余额
		canTransfer = avail
	} else {
		canTransfer = avail.Mul(define.DecimalOne.Sub(define.MainMarginRate)).Div(sum)
		if canTransfer.LessThan(decimal.Zero) {
			canTransfer = decimal.Zero
		}
	}
	return canTransfer.Truncate(define.ReplyFloatPrecision), nil
}

func GetUserAccount(tx *sqlx.Tx, userId int64) (account *proto.Account, err error) {
	account, err = cache.GetContractAccount(userId)
	if err != nil {
		account, err = database.GetTradeAccount(tx, userId, define.CoinNameUsdt)
		if err != nil {
			log.Errorf("db.GetTradeAccount fail,%v", err)
			return nil, err
		}
		cache.UpdateContractAccount(account)
	}
	return
}

func GetWalletCoinConfByNameAndProtocol(tx *sqlx.Tx, reqID int64, coinName string, protocol string) (coin proto.WalletCoinConf, err error) {
	protocol = ConvertCoinProtocol(coinName, protocol)
	coin, err = cache.GetCoinWalletConfByNameAndProtocol(coinName, protocol)
	if err != nil {
		// 从数据库获取全量
		dict, err := database.GetAllWalletCoinDict(tx)
		if err != nil {
			log.Error("GetWalletCoinConfByNameAndProtocol get wallet coin failed",
				zap.Int64("reqID", reqID),
				zap.Error(err))
			return coin, define.ErrMsgBusy
		}
		if len(dict) == 0 {
			log.Error("GetWalletCoinConfByNameAndProtocol wallet coin list is empty",
				zap.Int64("reqID", reqID))
			return coin, define.ErrMsgBusy
		}

		// 过滤最终结果,并将全量缓存到redis
		val, ok := dict[coinName]
		if !ok {
			log.Error("GetWalletCoinConfByNameAndProtocol coinID not exist",
				zap.Int64("reqID", reqID))
			return coin, define.ErrMsgParam
		}
		coin, ok = val[protocol]
		if !ok {
			log.Error("GetWalletCoinConfByNameAndProtocol protocol not exist",
				zap.Int64("reqID", reqID))
			return coin, define.ErrMsgParam
		}
		cache.SaveCoinWalletDict(dict)
	}
	return coin, nil
}

func GetMultiDepositAddress(ctx context.Context, reqID, userId int64, platformID int, coinInfo proto.WalletCoinConf) (addr string, err error) {
	// 根据walletName获取对应的充币地址
	addr, err = database.GetDepositAddressWithWalletName(userId, platformID, coinInfo.WalletName)
	if err != nil {
		log.Error("GetMultiDepositAddress GetDepositAddressWithWalletName error",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return "", define.ErrMsgBusy
	}
	if len(addr) == 0 {
		// 创建新地址
		addr, err = base_third_rpc.CreateDepositAddr(ctx, reqID, coinInfo.WalletName, strconv.FormatInt(userId, 10), coinInfo.NeedTag.Bool())
		if err != nil || len(addr) == 0 {
			log.Errorf("GetDepositAddressV2 NewAddress error",
				zap.Int64("reqID", reqID),
				zap.Error(err))
			return "", define.ErrMsgCWSParam
		}
		// if bch legacy address, convert it
		if (coinInfo.WalletName == "bch" || coinInfo.WalletName == "bsv") && (strings.HasPrefix(addr, "bitcoincash:") || strings.HasPrefix(addr, "bchtest:")) {
			legacyAddress, err := address.ConvertBchToLegacyAddress(addr)
			if err == nil && len(legacyAddress) > 0 {
				addr = legacyAddress
			}
		}
		err = database.CreateUserDepositAddr(coinInfo, userId, platformID, addr)
		if err != nil {
			log.Errorf("GetDepositAddressV2 CreateUserDepositAddr error",
				zap.Int64("reqID", reqID),
				zap.Error(err))
			return "", define.ErrMsgBusy
		}
	}
	return
}

func GetWalletCoinConf(walletName string) (walletConf proto.WalletCoinConf, err error) {
	return cache.GetWalletConfFromCache(walletName)
}

/*
当用户发生交易行为后，需要划入一定数量资金作为保证金存入仓位保证金账户中，为了防止在用户在划转保证金时因价格变动出现风险，这里作出如下限制。
即满足下列不等式时，超出部分才可以转出：
限制提现金额比率：参数，统一设置为1.25
Σ全部合约未实现亏损=每一个合约双向未实现盈亏之和
Σ各合约用户设置杠杆保证金=每个合约双向持仓量*持仓均价*该合约面值/用户该设置杠杆倍数+平仓手续费 的和

可提现金额 ≤ 用户总资产＋min(0，Σ全部合约未实现亏损+赠金余额)－Σ各合约用户设置杠杆保证金*限制提现金额比率
*/
func CanTransferAmount(balance, giftBalance, floatProfit, margin decimal.Decimal) decimal.Decimal {
	return decimal.Max(decimal.Zero, balance.Add(decimal.Min(decimal.Zero, floatProfit.Add(giftBalance))).Sub(margin.Mul(define.TransferLimitRatio)))
}

// GetMultipleAssetDetail 获取用户可用余额等信息
func GetMultipleAssetDetail(reqID int64, lang define.ReqLang, mode define.AssetMode, user *define.TokenPayload) (*proto.AssetDetailV2, error) {
	if !CheckWalletAndCreate(reqID, user.UserID, user.PlatformID) {
		log.Error("GetMultipleAssetDetail CheckWalletAndCreate fail", zap.Int64("reqID", reqID))
		return nil, define.ErrMsgBusy
	}

	var (
		err   error
		asset proto.AssetDetailV2
	)

	// 获取兑其它法币汇率
	p, err := cache.GetCoinLegalRate(define.CoinNameUsdt)
	if err != nil {
		log.Error("GetMultipleAssetDetail GetCoinLegalRate fail", zap.Int64("reqID", reqID), zap.Error(err))
	}
	rate := p.CNY
	newRate := p.Rate(lang)

	if mode == define.AssetModeAll || mode == define.AssetModeForWallet {
		/* 资产账户 */
		asset.Wallet, err = GetUserWalletAsset(reqID, user.UserID)
		if err != nil {
			return nil, err
		}
		asset.Wallet.TotalLegal = asset.Wallet.TotalUSDT.Mul(rate).Truncate(define.LegalPrecision)
		asset.Wallet.TotalLegalV2 = asset.Wallet.TotalUSDT.Mul(newRate).Truncate(define.LegalPrecision)
	}

	if mode == define.AssetModeAll || mode == define.AssetModeForTrade {
		/* 交易账户 */
		asset.Trade, err = GetUserTradeAsset(reqID, user.UserID, user.ProfitStyle)
		if err != nil {
			return nil, err
		}
		asset.Trade.TotalLegal = asset.Trade.Total.Mul(rate).Truncate(define.LegalPrecision)
		asset.Trade.TotalLegalV2 = asset.Trade.Total.Mul(newRate).Truncate(define.LegalPrecision)
	}

	// 模拟盘跟单账户不统计
	if !conf.IsSimulate() && (mode == define.AssetModeAll || mode == define.AssetModeForFollow) {
		/* 跟单账户 */
		asset.Follow, err = GetUserFollowAsset(reqID, user.UserID, user.ProfitStyle)
		if err != nil {
			return nil, err
		}
		asset.Follow.TotalLegal = asset.Follow.Total.Mul(rate).Truncate(define.LegalPrecision)
		asset.Follow.TotalLegalV2 = asset.Follow.Total.Mul(newRate).Truncate(define.LegalPrecision)
	}

	asset.TotalUSDT = asset.Wallet.TotalUSDT.Add(asset.Trade.Total).Add(asset.Follow.Total).Truncate(define.ReplyFloatPrecision)
	asset.TotalLegal = asset.Wallet.TotalLegal.Add(asset.Trade.TotalLegal).Add(asset.Follow.TotalLegal).Truncate(define.LegalPrecision)
	asset.TotalLegalV2 = asset.Wallet.TotalLegalV2.Add(asset.Trade.TotalLegalV2).Add(asset.Follow.TotalLegalV2).Truncate(define.LegalPrecision)
	return &asset, nil
}

func GetUserWalletAsset(reqID, userID int64) (proto.MultipleWallet, error) {
	asset := proto.MultipleWallet{
		List: make([]proto.WalletDetailV2, 0),
	}
	// 检查资产账户币种
	wcs, err := GetCoinList()
	if err != nil {
		log.Error("GetUserWalletAsset GetCoinList fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}
	var cl []int
	dict := make(map[int]proto.Coin, len(wcs))
	for i := range wcs {
		if wcs[i].Status != 0 {
			dict[wcs[i].CurrencyId] = wcs[i]
			cl = append(cl, wcs[i].CurrencyId)
		}
	}

	if len(cl) == 0 {
		return asset, nil
	}

	list, err := database.GetUserMultipleWallet(nil, userID, cl...)
	if err != nil {
		log.Error("GetUserMultipleWallet GetUserWallet error",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}

	var wallet proto.WalletDetailV2
	for i := range list {
		wallet.CoinID = list[i].CurrencyId
		wallet.CoinName = dict[wallet.CoinID].CurrencyName
		wallet.Balance = list[i].Balance
		wallet.Frozen = list[i].WithdrawLock
		wallet.EquivalentUSDT = wallet.Balance.Add(wallet.Frozen)
		if wallet.CoinName != define.CoinNameUsdt {
			// 转换成USDT
			price, err := GetCoinExchangeBasePrice(wallet.CoinName)
			if err != nil || price.LessThanOrEqual(decimal.Zero) {
				log.Error("GetUserMultipleWallet GetCoinExchangeBasePrice fail",
					zap.Int64("reqID", reqID),
					zap.String("coin", wallet.CoinName),
					zap.Error(err))
				wallet.EquivalentUSDT = decimal.Zero
			} else {
				wallet.EquivalentUSDT = wallet.EquivalentUSDT.Mul(price).Truncate(define.FloatPrecision)
			}
		}
		wallet.SortWeight = dict[wallet.CoinID].SortWeight

		// TODO 获取24小时内总法币交易完成数量,修改币种可提数量(法币暂停,取消查询)
		wallet.CanWithdraw = list[i].Balance

		asset.List = append(asset.List, wallet)
		asset.TotalUSDT = asset.TotalUSDT.Add(wallet.EquivalentUSDT)
	}
	sort.SliceStable(asset.List, func(i, j int) bool {
		return asset.List[i].SortWeight > asset.List[j].SortWeight
	})
	return asset, nil
}

func GetUserTradeAsset(reqID, userID int64, profitStyle int) (proto.TradeWalletDetailV2, error) {
	var asset proto.TradeWalletDetailV2
	// 获取保证金账户币种
	mcs, err := GetMarginCoinList()
	if err != nil {
		log.Error("GetUserTradeAsset GetMarginCoinList fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}
	if len(mcs) == 0 {
		return asset, nil
	}

	// TODO 当前仅支持单币种
	coinID := mcs[0].CurrencyId
	account, err := database.GetUserTradeAsset(nil, userID, coinID)
	if err != nil {
		log.Error("GetUserTradeAsset GetUserTradeAsset error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}

	_, asset.TotalMargin, asset.MarginGift, asset.Margin, asset.Float, err = GetUserCoinMargin(nil, userID, profitStyle, coinID)
	if err != nil {
		log.Error("GetUserTradeAsset GetUserCoinMargin error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return asset, define.ErrMsgParam
	}
	asset.Float = asset.Float.Sub(account.Diff)
	asset.TotalMargin = asset.TotalMargin.Sub(asset.MarginGift).Truncate(define.ReplyFloatPrecision)
	asset.Margin = asset.Margin.Truncate(define.ReplyFloatPrecision)
	asset.Float = asset.Float.Truncate(define.ReplyFloatPrecision)

	asset.Rights = account.Balance.Add(asset.Float).Add(account.GiftBalance).Truncate(define.ReplyFloatPrecision)
	asset.Total = asset.Rights
	asset.Balance = account.Balance
	asset.Available = account.Available
	if asset.Available.LessThan(decimal.Zero) {
		asset.Available = decimal.Zero
	}
	asset.CanTransfer = account.Available
	asset.Diff = account.Diff
	asset.LockAmount = account.LockAmount
	asset.TotalGiftReceive = account.TotalGiftReceive
	asset.GiftBalance = account.GiftBalance
	asset.GiftAvailable = account.GiftAvailable
	asset.GiftUsed = account.GiftUsed
	asset.GiftLockAmount = account.GiftLockAmount

	if !asset.Margin.IsZero() {
		asset.CanTransfer = CanTransferAmount(account.Balance.Sub(account.LockAmount), account.GiftBalance, asset.Float, asset.TotalMargin).Truncate(define.UsdtAssetFloatPrecision)
		asset.RiskRate = GetUserAccountRiskRate(userID)
	}
	asset.BlowingRate = GetBlowingRate()
	return asset, nil
}

func GetUserFollowAsset(reqID, userID int64, profitStyle int) (proto.FollowWalletDetail, error) {
	var asset proto.FollowWalletDetail
	// 获取保证金账户币种
	mcs, err := GetMarginCoinList()
	if err != nil {
		log.Error("GetUserFollowAsset GetMarginCoinList fail",
			zap.Int64("reqID", reqID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}
	if len(mcs) == 0 {
		return asset, nil
	}

	// TODO 当前仅支持单币种
	coinID := mcs[0].CurrencyId
	account, err := database.GetFollowAccountByUserId(nil, userID, coinID)
	if err != nil {
		log.Error("GetUserFollowAsset GetFollowAccountByUserId error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return asset, define.ErrMsgBusy
	}

	_, totalMargin, initMargin, floatProfit, err := GetUserFollowCoinMargin(nil, userID, profitStyle, coinID)
	if err != nil {
		log.Error("GetUserFollowAsset GetUserFollowCoinMargin error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return asset, define.ErrMsgParam
	}
	totalMargin = totalMargin.Truncate(define.ReplyFloatPrecision)
	initMargin = initMargin.Truncate(define.ReplyFloatPrecision)
	floatProfit = floatProfit.Truncate(define.ReplyFloatPrecision)

	asset.Available = account.Balance.Truncate(define.ReplyFloatPrecision)
	asset.Float = floatProfit.Truncate(define.ReplyFloatPrecision)
	asset.Margin = totalMargin.Truncate(define.ReplyFloatPrecision)
	asset.Total = asset.Available.Add(totalMargin).Add(floatProfit).Truncate(define.ReplyFloatPrecision)
	asset.CanTransfer = asset.Available
	asset.LockAmount = account.LockAmount

	if !initMargin.IsZero() {
		asset.CanTransfer = CanTransferAmount(account.Balance.Add(totalMargin), decimal.Zero, floatProfit, totalMargin).Truncate(define.UsdtAssetFloatPrecision)
	}
	return asset, nil
}

// 获取同时获取全仓账户资金及持仓
func GetAccountAndPositions(userId int64) (account *proto.Account, positions []proto.UserPosition, err error) {
	var (
		tx *sqlx.Tx
	)
	tx, err = database.DefaultDB().Beginx()
	if err != nil {
		log.Error("database get beginX fail", zap.Error(err))
		return
	}

	defer database.CommitTx(tx, &err, 0, nil)

	account, err = database.GetUserAccountByLock(tx, userId, define.CoinIdUsdt)
	if err != nil {
		log.Errorf("commonSrv.GetContractAccount fail，userId；%v,%v", userId, err)
		return
	}

	positions, err = database.GetUserPositionsByUserIDWithLock(tx, userId)
	if err != nil {
		log.Error("database.GetUserPositionsByUserID fail", zap.Error(err))
		return
	}

	return
}
