package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/convert"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//用户持仓保证金之和+浮动盈亏,起始保证金,浮动盈亏
func GetUserFollowCoinMargin(tx *sqlx.Tx, userId int64, profitStyle, coinid int) (total, totalMargin, initMargin, floatProfit decimal.Decimal, err error) {
	return GetFollowCoinMarginDetail(tx, userId, profitStyle, coinid, "", decimal.Zero)
}

//获取跟单账户仓位保证金
func GetFollowCoinMarginDetail(tx *sqlx.Tx, userId int64, profitStyle, coinId int, contractCode string, mp decimal.Decimal) (total, totalMargin, initMargin, floatProfit decimal.Decimal, err error) {
	list, err := database.GetMarginCoinContracts(tx, coinId)
	if err != nil {
		log.Errorf("database.GetMarginCoinContracts fail,%v", err)
		return
	}

	indexs := cache.GetAllPriceIndex()
	//log.Infof("coinMargin size；%v", len(list))
	for _, code := range list {
		var marketPrice decimal.Decimal
		if code == contractCode {
			marketPrice = mp
		} else {
			if profitStyle == define.UserProfitStyleWithTrade {
				// 使用成交价格计算
				marketPrice = indexs[code].TradePrice
			} else {
				// 使用标记价格计算
				index := cache.GetContractComplexTradePrice(code)
				if index == nil {
					continue
				}
				marketPrice = index.Price
			}
		}
		//此处浮动盈亏已通过数据库实时计算
		m, err := database.GetFollowCoinMarginStat(tx, userId, code, marketPrice)
		if err != nil {
			log.Errorf("database.GetFollowCoinMarginStat fail,%v", err)
			continue
		}
		//log.Infof("code:%v", code)
		total = total.Add(nums.NewFromFloat(m.Margin)).Add(nums.NewFromFloat(m.FloatProfit))
		totalMargin = totalMargin.Add(nums.NewFromFloat(m.Margin))
		initMargin = initMargin.Add(nums.NewFromFloat(m.Margin).Sub(nums.NewFromFloat(m.Commission)))
		floatProfit = floatProfit.Add(nums.NewFromFloat(m.FloatProfit))
		log.Debugf("userId；%v,code：%v,margin:%v，float:%v,total:%v", userId, code, m.Margin, m.FloatProfit, total.String())
	}
	return
}

// 获取用户可划转交易资产余额
func GetFollowAssetAvailableForTransfer(reqID int64, avail decimal.Decimal, coinID int, userID int64, tx *sqlx.Tx) (decimal.Decimal, error) {
	if avail.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, nil
	}

	list, err := database.GetFollowCoinInitMarginStat(tx, userID, coinID)
	if err != nil {
		log.Error("GetFollowAssetAvailableForTransfer GetFollowCoinInitMarginStat error",
			zap.Int64("reqID", reqID),
			zap.Int64("userID", userID),
			zap.Int("coinID", coinID),
			zap.Error(err))
		return decimal.Zero, define.ErrMsgBusy
	}

	// 可转出数量 = 可用余额*(1-0.005) / (占用初始保证金*杠杆 之和)
	sum := decimal.Zero
	for i := range list {
		if list[i].Lever <= 0 {
			log.Error("GetFollowAssetAvailableForTransfer lever is zero",
				zap.Int64("reqID", reqID),
				zap.Int64("userID", userID),
				zap.Int("coinID", coinID),
				zap.Any("data", list[i]))
			return decimal.Zero, define.ErrMsgBusy
		}
		sum = sum.Add(nums.NewFromString(list[i].InitMargin).Mul(nums.NewFromInt(list[i].Lever)))
	}
	var canTransfer decimal.Decimal
	if sum.IsZero() {
		// 如果没有持仓,那么可划转资产为所有可用余额
		canTransfer = avail
	} else {
		canTransfer = avail.Mul(define.DecimalOne.Sub(define.MainMarginRate)).Div(sum)
		if canTransfer.LessThan(decimal.Zero) {
			canTransfer = decimal.Zero
		}
	}
	return canTransfer.Truncate(define.ReplyFloatPrecision), nil
}

//以增量方式更新或添加日统计记录
func UpdateDealerDaySummaryWithIncrement(tx *sqlx.Tx, summary *proto.DealerDaySummary) (err error) {
	var affect int64
	affect, err = database.UpdateDealerDaySummaryWithIncrement(tx, summary)
	if err != nil {
		log.Errorf("UpdateDealerDaySummaryWithIncrement fail,%v", err)
		return
	}
	if affect == 0 {
		err = database.InsertDealerDaySummary(tx, summary)
		if err != nil {
			log.Errorf("UpdateDealerDaySummaryWithIncrement>database.InsertDealerDaySummary fail,%v", err)
			return err
		}
	}
	return
}

func GetDealerNickname(language define.ReqLang, nickname, nicknameEn, phone, email string) string {
	// 交易员昵称
	var result string
	if language == define.ReqLangCN {
		result = getNickname(nickname, nicknameEn, convert.HidePhone(phone), convert.HideEmail(email))
	} else {
		result = getNickname(nicknameEn, nickname, convert.HidePhone(phone), convert.HideEmail(email))
	}
	return result
}

func getNickname(nickname, nicknameExtra, phone, email string) string {
	if nickname != "" {
		return nickname
	} else if nicknameExtra != "" {
		return nicknameExtra
	} else if phone != "" {
		return phone
	} else {
		return email
	}
}
