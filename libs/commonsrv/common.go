package commonsrv

import (
	"math/rand"
	"sort"
	"strings"
	"time"

	"bc/libs/cache"
	"bc/libs/conf"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"bc/libs/utils"
	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

func GetCoinList() ([]proto.Coin, error) {
	list, err := cache.GetAllCoinList()
	if err != nil || len(list) == 0 {
		list, err = database.GetSupportCoins(nil)
	}
	if len(list) > 0 {
		sort.SliceStable(list, func(i, j int) bool {
			return list[i].SortWeight > list[j].SortWeight
		})
	}
	return list, err
}

func GetCoinDetailByID(coinID int) (*proto.Coin, error) {
	coin, err := cache.GetCoinByID(coinID)
	if err != nil {
		coin, err = database.GetCoinById(nil, coinID)
	}
	return coin, err
}

func GetCoinDetailByIDTx(tx *sqlx.Tx, coinID int) (*proto.Coin, error) {
	coin, err := cache.GetCoinByID(coinID)
	if err != nil {
		coin, err = database.GetCoinById(tx, coinID)
	}
	return coin, err
}

func GetCoinDetailByName(tx *sqlx.Tx, coinName string) (*proto.Coin, error) {
	coin, err := cache.GetCoinByName(coinName)
	if err != nil {
		coin, err = database.GetCoinByName(tx, coinName)
	}
	return coin, err
}

func GetMarginCoinList() ([]proto.MarginCurrency, error) {
	list, err := cache.GetAllMarginCoinList()
	if err != nil {
		list, err = database.GetMarginCoins(nil)
		if err == nil {
			cache.SaveMarginCoinList(list, false)
		}
	}
	return list, err
}

func GetMarginCoinDetailByID(coinID int) (*proto.MarginCurrency, error) {
	coin, err := cache.GetMarginCoinByID(coinID)
	if err != nil {
		coin, err = database.GetMarginCoinById(nil, coinID)
	}
	return coin, err
}

func GetMarginCoinDetailByName(coinName string) (*proto.MarginCurrency, error) {
	coin, err := cache.GetMarginCoinByName(coinName)
	if err != nil {
		coin, err = database.GetMarginCoinByName(nil, coinName)
	}
	return coin, err
}

func CheckAuthCode(reqID int64, account, code string, mode uint8, clean bool) error {
	account = strings.ToLower(account)
	key := utils.StrBuilder(define.CacheKeyAuthCode, account)

	// 获取redis中验证码信息
	codeData, err := cache.GetAuthCode(key)
	if err != nil {
		if err == redis.Nil {
			return define.ErrMsgAuthCodeInvalid
		}
		log.Error("CheckAuthCode GetAuthCode error",
			zap.Int64("reqID", reqID),
			zap.String("account", account),
			zap.String("code", code),
			zap.Uint8("mode", mode),
			zap.Error(err))
		return define.ErrMsgBusy
	}
	if codeData.Code != code || code == "" || codeData.Mode != mode {
		return define.ErrMsgAuthCode
	}

	if clean {
		cache.DelKey(key)
	}
	return nil
}

//获取行情报警email
func GetWarnEmails() (emails []string, err error) {
	emails, err = cache.GetWarnEmailsByMarket()
	if err != nil {
		if err == redis.Nil {
			emails, err = database.GetWarnEmailsByMarket()
			if err != nil {
				log.Errorf("database.GetWarnEmails() fail,%v", err)
				return
			}
			cache.SetWarnEmailsByMarket(emails)
		}
	}
	return
}

//获取对冲报警邮件列表
func GetWarnEmailsByHedge() (emails []string, err error) {
	emails, err = cache.GetWarnEmailsByHedge()
	if err != nil {
		if err == redis.Nil {
			emails, err = database.GetWarnEmailsByHedge()
			if err != nil {
				log.Errorf("database.GetWarnEmailsByHedge() fail,%v", err)
				return
			}
			cache.SetWarnEmailsByHedge(emails)
		}
	}
	return
}

//获取随机滑点
func GetRandSlippage(min, max, step decimal.Decimal) (r decimal.Decimal) {
	gap := max.Sub(min)
	if gap.LessThanOrEqual(decimal.Zero) {
		return max
	}
	if step.LessThanOrEqual(decimal.Zero) {
		log.Error("GetRandSlippage step fail", zap.Any("min", min.String()), zap.String("max", max.String()), zap.String("step", step.String()))
		return max
	}

	source := gap.Div(step).IntPart()
	rand.Seed(time.Now().UnixNano())
	randInt := rand.Int63n(source)
	return min.Add(nums.NewFromInt64(randInt).Mul(step))
}

func GetDefaultPlatFormId() int {
	if conf.IsProduct() {
		return define.PlatFormDefault
	}
	return define.PlatForm
}
