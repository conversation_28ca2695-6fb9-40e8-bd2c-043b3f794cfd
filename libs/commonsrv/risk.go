package commonsrv

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/mk"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//全仓账户风险率
//综合风险率=当前用户权益/占用保证金
//＝(用户总资产+Σ各合约未实现盈亏－Σ各合约预计支付资金费用)/Σ各合约实际初始保证金
//＝1/风险度
func CalUserRiskRate(ups *proto.UserPositionStat, balance decimal.Decimal) decimal.Decimal {
	return balance.Add(ups.TotalFloatProfit).Div(ups.TotalRealInitMargin)
}

//单一合约当前预估减仓价格
//强平价=[(多头开仓价值－空头开仓价值)－(总资产＋Σ其他合约未实现盈亏－Σ其他合约最低维持保证金－该合约实际初始保证金*最高风险率)]/(面值*净持仓)
//破产价＝[(多头开仓价值－空头开仓价值)－（总资产＋Σ其他合约未实现盈亏-Σ其他合约最低维持保证金）/净持仓*面值
func CalContractForcePrice(ups *proto.UserPositionStat, totalAsset decimal.Decimal) {
	if ups == nil || ups.ConStatMap == nil || len(ups.ConStatMap) == 0 {
		return
	}
	for code, stat := range ups.ConStatMap {
		fz1 := stat.BuyWorth.Sub(stat.SellWorth)
		fz2 := totalAsset.Add(ups.TotalFloatProfit.Sub(stat.FloatProfit)).Sub(ups.TotalMaintainMargin.Sub(stat.MaintainMargin)).Sub(stat.RealInitMargin.Mul(stat.RiskRate))

		bFz2 := totalAsset.Add(ups.TotalFloatProfit.Sub(stat.FloatProfit)).Sub(ups.TotalMaintainMargin.Sub(stat.MaintainMargin))
		fm := stat.PerValue.Mul(nums.NewFromInt(stat.NetPosVolume))
		if fm.Equal(decimal.Zero) {
			log.Warnf("calContractForcePrice 分母为0，ups:%+v", *ups)
			continue
		}
		stat.ForcePrice = fz1.Sub(fz2).Div(fm).Truncate(stat.Precision)
		stat.BrokPrice = fz1.Sub(bFz2).Div(fm).Truncate(stat.Precision)
		ups.ConStatMap[code] = stat
	}
}

func CreateContractStatMap(posMap map[string][]proto.UserPosition, riskRate decimal.Decimal, IndexInfo map[string]proto.ContractMark, ContractInfo map[string]proto.Contract) (ups *proto.UserPositionStat) {
	ups = &proto.UserPositionStat{
		ConStatMap: make(map[string]proto.UserContractStat),
	}
	for code, positions := range posMap {
		//多头总持仓*多头开仓均价*面值
		index, ok := IndexInfo[code]
		if !ok {
			continue
		}
		info, ok := ContractInfo[code]
		if !ok {
			continue
		}
		cs := proto.UserContractStat{Code: code, Index: index, Precision: info.Digit, PerValue: nums.NewFromString(info.ParValue), RiskRate: riskRate, MaxFullLever: info.MaxValidFullLever()}
		//最低维持保证金率=最高风险率/该合约最大杠杆倍数
		maintainRate := riskRate.Div(nums.NewFromInt(info.MaxValidFullLever()))
		cs.MaintainRate = maintainRate
		var avgPriceBuy, avgPriceSell decimal.Decimal
		var netPos int
		for _, position := range positions {
			if position.Side == define.OrderBuy {
				netPos += position.Volume
			} else {
				netPos -= position.Volume
			}
		}
		for _, position := range positions {
			cs.UserID = position.UserId
			cs.Lever = position.Lever
			cs.InitMargin = cs.InitMargin.Add(nums.NewFromFloat(position.InitMargin))
			f := mk.GetFloatProfitForForceStatWithPosNet(position.Side, position.Volume, netPos, cs.PerValue, nums.NewFromFloat(position.Price), index)
			cs.FloatProfit = cs.FloatProfit.Add(f)
			worth := nums.NewFromInt(position.Volume).Mul(cs.PerValue).Mul(nums.NewFromFloat(position.Price))
			cs.CloseFee = cs.CloseFee.Add(nums.NewFromFloat(position.Commission))
			cs.TotalMargin = cs.TotalMargin.Add(nums.NewFromFloat(position.Margin))
			if position.Side == define.OrderBuy {
				avgPriceBuy = nums.NewFromFloat(position.Price)
				cs.BuyWorth = cs.BuyWorth.Add(worth)
				cs.NetPosVolume += position.Volume
			} else {
				avgPriceSell = nums.NewFromFloat(position.Price)
				cs.SellWorth = cs.SellWorth.Add(worth)
				cs.NetPosVolume -= position.Volume
			}
		}

		//if avgPriceBuy.GreaterThan(decimal.Zero) && avgPriceSell.GreaterThan(decimal.Zero) {
		//	if cs.NetPosVolume == 0 {
		//		cs.FloatProfit = decimal.Zero
		//	} else if cs.NetPosVolume > 0 {
		//		//使用最低价计算浮动盈亏
		//		cs.FloatProfit = mk.GetFloatProfitForForceStat(define.OrderBuy, cs.NetPosVolume, cs.PerValue, avgPriceBuy, index)
		//	} else if cs.NetPosVolume < 0 {
		//		//使用最高价计算浮动盈亏
		//		cs.FloatProfit = mk.GetFloatProfitForForceStat(define.OrderSell, -cs.NetPosVolume, cs.PerValue, avgPriceSell, index)
		//	}
		//}

		cs.AvgPriceBuy = avgPriceBuy
		cs.AvgPriceSell = avgPriceSell

		cs.MaintainMargin = cs.MaintainRate.Mul(cs.BuyWorth.Add(cs.SellWorth))
		cs.RealInitMargin = cs.BuyWorth.Add(cs.SellWorth).Div(nums.NewFromInt(cs.MaxFullLever))

		ups.ConStatMap[code] = cs
		ups.RiskRate = riskRate
		ups.TotalInitMargin = ups.TotalInitMargin.Add(cs.InitMargin)
		ups.TotalRealInitMargin = ups.TotalRealInitMargin.Add(cs.RealInitMargin)
		ups.TotalMaintainMargin = ups.TotalMaintainMargin.Add(cs.MaintainMargin)
		ups.TotalCloseFee = ups.TotalCloseFee.Add(cs.CloseFee)
		ups.TotalFloatProfit = ups.TotalFloatProfit.Add(cs.FloatProfit)

	}
	return ups
}

func CheckForce(userId, id int64, side string, forcePrice decimal.Decimal, cm *proto.ContractMark) (f bool) {
	if cm == nil {
		return false
	}
	index := cm

	defer func() {
		log.Info("检查持仓", zap.Bool("是否强平", f), zap.Any("pos", id), zap.Int64("userId", userId), zap.String("force", forcePrice.String()), zap.String("方向", side), zap.Any("index", index))

	}()

	price := cm.ClosePrice
	f = checkIsMatchForceWithWareHouse(side, forcePrice, cm.ClosePrice)
	if f {
		return
	}

	if !cm.LowPrice.Equal(price) {
		f = checkIsMatchForceWithWareHouse(side, forcePrice, cm.LowPrice)
		if f {
			return
		}
		price = cm.LowPrice
	}

	if !cm.HighPrice.Equal(price) {
		f = checkIsMatchForceWithWareHouse(side, forcePrice, cm.HighPrice)
		if f {
			return
		}
	}

	return false
}

func checkIsMatchForceWithWareHouse(side string, forcePrice decimal.Decimal, complexPrice decimal.Decimal) bool {
	var isTrigger bool
	//判断仓位强平
	if side == define.OrderBuy {
		if complexPrice.LessThanOrEqual(forcePrice) {
			isTrigger = true
		}
	} else {
		if complexPrice.GreaterThanOrEqual(forcePrice) {
			isTrigger = true
		}
	}
	return isTrigger
}
