package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"time"
)

const (
	AcValidDuration = 60 * time.Second
)

func SetUserOpenAC(userId int64, code, side string) {
	duration := AcValidDuration
	c, err := GetContractDetail(0, code, nil)
	if err != nil {
		log.Info("SetUserOpenAC GetContractDetail fail", zap.Error(err))
	}
	if c != nil {
		dur := c.GetACDuration()
		if dur > 0 {
			duration = dur
		}
	}
	cache.SetUserContractAc(userId, code, side, duration)
}

func IsUserInAc(userId int64, code, side string) bool {
	return cache.IsUserInAc(userId, code, side)
}

func UpdateOrderForAc(id int64, slippage decimal.Decimal) {
	err := database.UpdateEntrustOrderForAC(nil, id, slippage)
	if err != nil {
		log.Error("UpdateOrderForAc UpdateEntrustOrderForAC fail", zap.Error(err))
		return
	}
}

func AddNetPosForOpen(code string, side string, count int) {
	var err error
	if side == define.OrderBuy {
		_, err = cache.IncrContractNetPosition(code, int64(count))
		if err != nil {
			log.Errorf("IncrContractNetPosition fail,%v", err)
			return
		}
		_, err = cache.IncrContractBuyPosition(code, int64(count))
		if err != nil {
			log.Errorf("IncrContractBuyPosition fail,%v", err)
			return
		}
		//_, err = cache.IncrContractDepthBuyPosition(code, int64(count))
		//if err != nil {
		//	log.Errorf("IncrContractDepthBuyPosition fail,%v", err)
		//	return
		//}

	} else {
		_, err = cache.DcrContractNetPosition(code, int64(count))
		if err != nil {
			log.Errorf("DcrContractNetPosition fail,%v", err)
			return
		}
		_, err = cache.IncrContractSellPosition(code, int64(count))
		if err != nil {
			log.Errorf("IncrContractSellPosition fail,%v", err)
			return
		}
		//_, err = cache.IncrContractDepthSellPosition(code, int64(-count))
		//if err != nil {
		//	log.Errorf("IncrContractDepthSellPosition fail,%v", err)
		//	return
		//}
	}

}

func AddNetPosForClose(code string, side string, count int) {
	var err error
	if side == define.OrderBuy {
		_, err = cache.DcrContractNetPosition(code, int64(count))
		if err != nil {
			log.Errorf("DcrContractNetPosition fail,%v", err)
			return
		}
		_, err = cache.DcrContractBuyPosition(code, int64(count))
		if err != nil {
			log.Errorf("DcrContractBuyPosition fail,%v", err)
			return
		}
		//_, err = cache.IncrContractDepthSellPosition(code, int64(-count))
		//if err != nil {
		//	log.Errorf("IncrContractDepthSellPosition fail,%v", err)
		//	return
		//}
	} else {
		_, err = cache.IncrContractNetPosition(code, int64(count))
		if err != nil {
			log.Errorf("IncrContractNetPosition fail,%v", err)
			return
		}
		_, err = cache.DcrContractSellPosition(code, int64(count))
		if err != nil {
			log.Errorf("DcrContractSellPosition fail,%v", err)
			return
		}
		//_, err = cache.IncrContractDepthBuyPosition(code, int64(count))
		//if err != nil {
		//	log.Errorf("IncrContractDepthBuyPosition fail,%v", err)
		//	return
		//}
	}
}

func SysNetPos() {
	m, err := database.GetCoreAndFollowContractNetPos()
	if err != nil {
		log.Errorf("database.GetCoreAndFollowContractNetPos fail,%v", err)
		return
	}
	if m == nil {
		return
	}
	for _, c := range m {
		log.Infof("获取合约净持仓，合约：%v,Volume:%v", c.ContractCode, c.Volume)
		cache.SetContractNetPosition(c.ContractCode, c.Volume)
		cache.SetContractBuyPosition(c.ContractCode, c.Buy)
		cache.SetContractSellPosition(c.ContractCode, c.Sell)

	}
}
