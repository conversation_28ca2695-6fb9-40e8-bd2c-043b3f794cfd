/*
@Time : 2019-08-21 19:59
<AUTHOR> mocha
@File : msghandler
*/
package commonsrv

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"

	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/proto"
	"bc/libs/xrpcclient/base_third_rpc"

	"go.uber.org/zap"
)

func GetFsCoinAddress(platformID, walletName string, needTag bool) string {
	newAddr, err := base_third_rpc.CreateDepositAddr(context.Background(), 0, walletName, fmt.Sprintf("fswallet_%s", platformID), needTag)
	if err != nil || len(newAddr) == 0 {
		log.Errorf("生成平台钱包地址有误:%v,addr:%v", err, newAddr)
	}
	return newAddr
}

func GetFsLegalCoinAddress(platformID, walletName string, needTag bool) string {
	newAddr, err := base_third_rpc.CreateDepositAddr(context.Background(), 0, walletName, fmt.Sprintf("fswallet_legal_%s", platformID), needTag)
	if err != nil || len(newAddr) == 0 {
		log.Errorf("生成平台钱包地址有误:%v,addr:%v", err, newAddr)
	}
	return newAddr
}

//检测保证金账户创建
func CheckTradeAccount(coin *proto.MarginCurrency, platformID int) {
	_, err := database.GetFsCapital(coin.CurrencyId, platformID)
	if err != nil && err == sql.ErrNoRows {
		m := proto.FsWallet{CoinId: coin.CurrencyId, CoinName: coin.CurrencyName, Type: define.WalletTypeTrade, PlatformID: platformID}
		err = database.InsertFsWallet(m)
		if err != nil {
			log.Errorf("checkCapitalAccount InsertFsCapital fail:%v", err)
			return
		}
	}
}

func CheckC2cAccount(coin *proto.Coin, platformID int) {
	wallet, err := database.GetC2cWallet(coin.CurrencyId, platformID)
	if err != nil && err == sql.ErrNoRows {
		//1.入库
		wallet = proto.FsWallet{CoinId: coin.CurrencyId, Address: GetFsLegalCoinAddress(strconv.Itoa(platformID), coin.WalletName, coin.NeedTag.Bool()), CoinName: coin.CurrencyName, Type: define.WalletTypeLegal, PlatformID: platformID}
		err = database.InsertFsWallet(wallet)
		if err != nil {
			log.Errorf("CheckC2cAccount InsertFsWallet fail:%v", err)
			return
		}
	}
	if len(wallet.Address) == 0 {
		// 充币地址为空,重新获取,并更新数据库
		err = database.UpdateFsAssetWalletAddress(platformID, coin.CurrencyId, define.WalletTypeLegal, GetFsLegalCoinAddress(strconv.Itoa(platformID), coin.WalletName, coin.NeedTag.Bool()))
		if err != nil {
			log.Errorf("CheckC2cAccount UpdateFsAssetWalletAddress fail:%v", err)
			return
		}
	}
}

//检测资产账户
func CheckWalletAccount(coin *proto.Coin, platformId int) {
	wallet, err := database.GetFsAssetWallet(coin.CurrencyId, platformId)
	if err != nil && err == sql.ErrNoRows {
		//1.入库
		wallet = proto.FsWallet{CoinId: coin.CurrencyId, Address: GetFsCoinAddress(strconv.Itoa(platformId), coin.WalletName, coin.NeedTag.Bool()), CoinName: coin.CurrencyName, Type: define.WalletTypeAsset, PlatformID: platformId}
		err = database.InsertFsWallet(wallet)
		if err != nil {
			log.Errorf("checkWalletAccount InsertFsWallet fail:%v", err)
			return
		}
	}
	if len(wallet.Address) == 0 {
		// 充币地址为空,重新获取,并更新数据库
		err = database.UpdateFsAssetWalletAddress(platformId, coin.CurrencyId, define.WalletTypeAsset, GetFsCoinAddress(strconv.Itoa(platformId), coin.WalletName, coin.NeedTag.Bool()))
		if err != nil {
			log.Errorf("checkWalletAccount UpdateFsAssetWalletAddress fail:%v", err)
			return
		}
	}
}

func ReloadWalletCoins(list []proto.PlatformInfo) error {
	coins, err := database.GetSupportCoins(nil)
	if err != nil {
		log.Errorf("SysAccountInitTask GetOpenCoins fail", zap.Error(err))
	}

	for i := range coins {
		for _, info := range list {
			CheckWalletAccount(&coins[i], info.PlatformID)
			CheckC2cAccount(&coins[i], info.PlatformID)
		}
	}
	return err
}

func ReloadMarginCoins(list []proto.PlatformInfo) (err error) {
	marginCoins, err := database.GetMarginCoins(nil)
	if err != nil {
		log.Errorf("database.GetMarginCoins fail,%v", err)
		return
	}

	for i := range marginCoins {
		for _, info := range list {
			CheckTradeAccount(&marginCoins[i], info.PlatformID)
		}
	}
	return err
}
