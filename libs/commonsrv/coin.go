package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/log"
	"bc/libs/proto"
	"go.uber.org/zap"
	"strings"
)

//获取要订阅的交易对 将币种生成CoinUsdt模式
func GetExchangeCoinSymbols() (list map[string]proto.Coin) {
	coins, err := GetCoinList()
	if err != nil {
		log.Error("GetExchangeCoinSymbols GetCoinList fail", zap.Error(err))
		return
	}
	list = make(map[string]proto.Coin)
	for _, coin := range coins {
		if coin.IsExchange {
			upperName := strings.ToUpper(coin.CurrencyName)
			if upperName != "USD" && upperName != "USDT" {
				key := upperName + "USDT"
				list[key] = coin
			}
		}
	}
	return
}

func GetWalletCoinConfByCoinNameAndProtocol(coinName string, protocol string) (coin proto.WalletCoinConf, err error) {
	return cache.GetCoinWalletConfByNameAndProtocol(coinName, ConvertCoinProtocol(coinName, protocol))
}
