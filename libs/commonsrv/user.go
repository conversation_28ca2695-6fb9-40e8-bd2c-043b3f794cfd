package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"github.com/jmoiron/sqlx"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

//查询用户风险率加点
func GetUserRiskAddPoint(tx *sqlx.Tx, userId int64, code string) decimal.Decimal {
	rate, isNil := cache.GetUserRiskPoint(userId, code)
	if isNil {
		userLabel, err := database.GetUserLabelByContractAndUserId(tx, userId, code)
		if err != nil {
			log.Errorf("GetUserRiskAddPoint->database.GetUserLabelByContractAndUserId userId:%d, fail,%v", userId, err)
			return decimal.Zero
		}
		if userLabel == nil {
			return decimal.Zero
		}
		rate = userLabel.RiskRate
		e := cache.CacheUserRiskPoint(userId, code, rate)
		if e != nil {
			log.Errorf("cache.CacheUserRiskPoint userId:%d, fail,%v", userId, err)
		}
	}
	return rate
}

//查询用户风险率加点
func GetUserRiskAddPoint2(userId int64, code string) decimal.Decimal {
	rate, isNil := cache.GetUserRiskPoint(userId, code)
	if isNil {
		userLabel, err := database.GetUserLabelByContractAndUserId(nil, userId, code)
		if err != nil {
			log.Errorf("GetUserRiskAddPoint->database.GetUserLabelByContractAndUserId userId:%d, fail,%v", userId, err)
			return decimal.Zero
		}
		if userLabel == nil {
			return decimal.Zero
		}
		rate = userLabel.RiskRate
		e := cache.CacheUserRiskPoint(userId, code, rate)
		if e != nil {
			log.Errorf("cache.CacheUserRiskPoint userId:%d, fail,%v", userId, err)
		}
	}
	return rate
}

//获取用户全仓账户风险率
func GetUserAccountRiskRate(userId int64) decimal.Decimal {
	risk, err := cache.GetFullAccountRiskRate(userId)
	if err != nil {
		return decimal.Zero
	}
	if risk != "" {
		//判断是否有持仓,如果没有持仓，直接返回
		count, err := cache.CountPositions(userId)
		if err != nil {
			log.Errorf("cache.CountPositions userId:%d, fail,%v", userId, err)
			return decimal.Zero
		}
		if count > 0 {
			return nums.NewFromString(risk)
		} else {
			_ = cache.DelFullAccountRiskRate(userId)
			return decimal.Zero
		}
	}
	return decimal.Zero
}

// GetUserUsdAccountRiskRate 获取用户全仓账户风险率
func GetUserUsdAccountRiskRate(userId int64, currencyName string) decimal.Decimal {
	risk, err := cache.GetUsdFullAccountRiskRate(userId, currencyName)
	if err != nil {
		return decimal.Zero
	}
	if risk != "" {
		//判断是否有持仓,如果没有持仓，直接返回
		cList := cache.ListUsdPositions(userId)
		var size int
		for _, position := range cList {
			if position.CurrencyName == currencyName {
				size++
			}
		}
		if size > 0 {
			return nums.NewFromString(risk)
		} else {
			_ = cache.DelUsdFullAccountRiskRate(userId, currencyName)
			return decimal.Zero
		}
	}
	return decimal.Zero
}

//获取用户持仓分割仓位配置
func IsUserPositionSplit(tx *sqlx.Tx, uid int64, code string) (bool, error) {
	user, err := database.GetUserInfoByID(uid, tx)
	if err != nil {
		log.Errorf("database.GetUserInfoByID userid；%v，fail,%v", uid, err)
		log.Info("没有查询到当前用户", zap.Any("uid", uid), zap.Error(err))
		return false, define.ErrMsgParam
	}
	log.Info("查询到用户信息", zap.Any("user", user), zap.Bool("split", user.IsSplitPosition.Bool()))
	return user.IsSplitPosition.Bool(), nil
}

// GetPositionAccountType 根据用户账户类型及分仓类型，获取持仓账户类型
func GetPositionAccountType(userAccountType define.AccountType, isSplitPosition bool) define.AccountType {
	if userAccountType == define.UserAccountTypeFullHouse {
		if isSplitPosition {
			return define.AccountTypeByFullHouseSplit
		} else {
			return define.AccountTypeByFullHouse
		}
	}

	if userAccountType == define.UserAccountTypeWareHouse {
		if isSplitPosition {
			return define.AccountTypeByWareHouseSplit
		} else {
			return define.AccountTypeByWareHouse
		}
	}
	return userAccountType
}
