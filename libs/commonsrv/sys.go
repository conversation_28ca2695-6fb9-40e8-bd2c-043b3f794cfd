package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/convert"
	"bc/libs/nums"
	"github.com/shopspring/decimal"
)

const (
	SysKeyBlowingRate              = "sys.blowing.rate"
	SysKeyHedgeEfficiency          = "sys.hedge.efficiency"
	SysKeyHedgeEfficiencyLv1       = "sys.hedge.efficiency.lv1"
	SysKeyHedgeEfficiencyLv2       = "sys.hedge.efficiency.lv2"
	SysKeyHedgeEfficiencyThreshold = "sys.hedge.efficiency.threshold"
	SysKeyHedgeAssetLossLv1        = "sys.hedge.asset.loss.lv1"
	SysKeyHedgeAssetLossLv2        = "sys.hedge.asset.loss.lv2"
	SysKeyHedgeAssetLossLv3        = "sys.hedge.asset.loss.lv3"
	SysKeyBonusLimitDevice         = "sys.bonus.limit.device"
	SysKeyPositionSplitSingleLimit = "sys.position.split.single.limit"
)

func GetSystemValue(key string) (val string) {
	return cache.ListSysValue()[key]
}

func GetBlowingRate() decimal.Decimal {
	v := GetSystemValue(SysKeyBlowingRate)
	return nums.NewFromString(v)
}

func GetHedgeEfficiency() int {
	v := GetSystemValue(SysKeyHedgeEfficiency)
	return convert.String2Int(v)
}

func GetHedgeEfficiencyLv1() int64 {
	v := GetSystemValue(SysKeyHedgeEfficiencyLv1)
	return convert.String2Int64(v)
}

func GetHedgeEfficiencyLv2() int64 {
	v := GetSystemValue(SysKeyHedgeEfficiencyLv2)
	return convert.String2Int64(v)
}

func GetHedgeEfficiencyThreshold() decimal.Decimal {
	v := GetSystemValue(SysKeyHedgeEfficiencyThreshold)
	return convert.NewFromString(v)
}

func GetHedgeAssetLossLv1() decimal.Decimal {
	v := GetSystemValue(SysKeyHedgeAssetLossLv1)
	return convert.NewFromString(v)
}

func GetHedgeAssetLossLv2() decimal.Decimal {
	v := GetSystemValue(SysKeyHedgeAssetLossLv2)
	return convert.NewFromString(v)
}

func GetHedgeAssetLossLv3() decimal.Decimal {
	v := GetSystemValue(SysKeyHedgeAssetLossLv3)
	return convert.NewFromString(v)
}

func GetBonusLimitDevice() int64 {
	v := GetSystemValue(SysKeyBonusLimitDevice)
	return convert.String2Int64(v)
}

func GetPositionSplitSingleLimit() int {
	v := GetSystemValue(SysKeyPositionSplitSingleLimit)
	return convert.String2Int(v)
}
