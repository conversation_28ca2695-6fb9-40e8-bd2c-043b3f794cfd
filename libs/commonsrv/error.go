package commonsrv

import (
	"net/http"
	"time"

	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/proto"
	"github.com/jmoiron/sqlx"
)

// 保存用户交互的错误信息
func SaveApiErrorRecord(reqID, userID int64, os define.OsType, code int, msg, reqURI, ip string) {
	if code == define.ErrCodeNone {
		return
	}

	now := time.Now()
	database.InsertApiErrorRecord(&proto.ApiError{
		OwnDay:     now.Format(define.TimeFormatDay),
		ReqID:      reqID,
		ReqURI:     reqURI,
		ErrCode:    code,
		ErrMsg:     msg,
		UserID:     userID,
		IPAddress:  ip,
		ClientOS:   os,
		CreateTime: now,
	})
}

// 保存系统错误消息
func SaveSysErrorRecord(tx *sqlx.Tx, errType define.SysErrorType, errMsg, contractCode string, userID int64) {
	now := time.Now()
	database.InsertSysErrorRecord(tx, &proto.SysError{
		OwnDay:       now.Format(define.TimeFormatDay),
		ErrType:      errType,
		ErrMsg:       errMsg,
		ContractCode: contractCode,
		UserID:       userID,
		CreateTime:   now,
	})
}

// 保存外部服务错误消息
func SaveExternalErrorRecord(tx *sqlx.Tx, reqID int64, userID int64, sysType define.ExternalSysType, code int, msg, reqURI string) {
	if code == http.StatusOK {
		return
	}

	now := time.Now()
	database.InsertExternalErrorRecord(tx, &proto.ExternalError{
		OwnDay:     now.Format(define.TimeFormatDay),
		SysType:    sysType,
		ReqID:      reqID,
		ReqURI:     reqURI,
		ErrCode:    code,
		ErrMsg:     msg,
		UserID:     userID,
		CreateTime: now,
	})
}

var _uriType = map[string]int{
	"/v1/account/login":                   define.ErrorRecordTypeLogin,          // 0-登录
	"/v1/account/register":                define.ErrorRecordTypeRegister,       // 1-注册
	"/v1/account/forget":                  define.ErrorRecordTypeForgetPwd,      // 2-找回登录密码
	"/v1/user/password/modify":            define.ErrorRecordTypeModifyLoginPwd, // 3-登录密码设置
	"/v1/user/safe/password/login/modify": define.ErrorRecordTypeModifyLoginPwd, // 3-登录密码设置
	"/v1/user/safe/password/fund/modify":  define.ErrorRecordTypeModifyFundPwd,  // 4-资金密码设置
	"/v1/user/account/modify":             define.ErrorRecordTypeModifyAccount,  // 5-用户名设置
	"/v1/user/safe/account/modify":        define.ErrorRecordTypeModifyAccount,  // 5-用户名设置
	"/v1/asset/withdraw":                  define.ErrorRecordTypeWithdraw,       // 6-提现申请
	"/v1/user/safe/totp/modify":           define.ErrorRecordTypeModifyTotp,     // 7-谷歌验证码设置
	"/v1/order/place":                     define.ErrorRecordTypeOrderPlace,     // 8-市价开仓
	"/v1/position/closeout":               define.ErrorRecordTypeOrderClose,     // 9-市价平仓
	"/v1/position/closeout/all":           define.ErrorRecordTypeOrderCloseAll,  // 10-一键平仓
	"/v1/position/set/stop":               define.ErrorRecordTypeSetLimitStop,   // 11-止盈止损
	"/v1/position/cancel/stop":            define.ErrorRecordTypeSetLimitStop,   // 11-止盈止损
	"/v1/order/plan/place":                define.ErrorRecordTypePlanPlace,      // 12-计划委托
	"/v1/order/plan/cancel":               define.ErrorRecordTypePlanCancel,     // 13-计划单撤销
	"/v1/legal/place":                     define.ErrorRecordTypeLegalPlace,     // 14-法币交易
	"/v1/user/payment/add":                define.ErrorRecordTypeBindPayment,    // 15-支付方式设置
	"/v1/user/payment/edit":               define.ErrorRecordTypeBindPayment,    // 15-支付方式设置
	"/v1/user/payment/del":                define.ErrorRecordTypeBindPayment,    // 15-支付方式设置
	"/v1/common/authcode":                 define.ErrorRecordTypeAuthCode,       // 16-获取验证码
	"/v1/common/authcode/safe":            define.ErrorRecordTypeAuthCode,       // 16-获取验证码
	"/v1/user/verify/idnumber":            define.ErrorRecordTypeKYCLevel1,      // 17-KYC1申请
	"/v1/user/verify/other":               define.ErrorRecordTypeKYCLevel1,      // 17-KYC1申请
	"/v1/user/verify/face":                define.ErrorRecordTypeKYCLevel2,      // 18-KYC2申请
	"/v1/user/verify/face/h5":             define.ErrorRecordTypeKYCLevel2,      // 18-KYC2申请
	"/v1/user/verify/manual":              define.ErrorRecordTypeKYCManual,      // 19-人工KYC申请
}

// 保存特定接口报错信息
func CollectErrorReply(reqArg *define.ReqArg, user *define.TokenPayload, code int, msg, reqURI string) {
	switch code {
	case define.ErrCodeNone, define.ErrCodeConfirmationIsRequired, define.ErrCodeSafeFundPassword, define.ErrCodeSafeNeedVerify, define.ErrCodeSafeNeedFundPasswordVerify:
		// 正常逻辑,直接退出
		return
	}

	erType, ok := _uriType[reqURI]
	if !ok {
		// 不在统计的接口列表中
		return
	}

	database.InsertUserErrorLog(&proto.UserError{
		ReqID:       reqArg.ReqID,
		UserID:      user.UserID,
		UserName:    user.UserName,
		OpType:      erType,
		ErrCode:     code,
		ErrMsg:      msg,
		IP:          reqArg.ReqIP,
		Device:      reqArg.Device,
		IMEI:        reqArg.DeviceID,
		OSType:      reqArg.ReqOs,
		Version:     reqArg.Version,
		CreatedTime: time.Now(),
	})
}
