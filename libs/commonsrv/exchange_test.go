package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"go.uber.org/zap"
	"testing"
)

func TestExchange(t *testing.T) {
	log.InitLogger("", "debug", false)
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
	c, err := GetContractDetail(0, "BTCUSDT", nil)
	if err != nil {
		t.Logf("get contract fail,%v", err)
		return
	}
	depth := cache.GetContractTradeDepth(c.ContractCode)
	if depth == nil {
		t.<PERSON>("depth data nil")
		return
	}
	mp, err := GenerateContractPrice(c, "B", 6000, 3, true, 1, 5, true)
	if err != nil {
		t.Logf("GenerateContractPrice fail,%v", err)
		return
	}
	t.Logf("mp:%+v", *mp)
}

func TestExchangeForLimit(t *testing.T) {
	log.InitLogger("", "debug", false)
	define.RedisCommonDb = 2
	cache.InitDefaultRedisDBCon("127.0.0.1:6379", "", 2, false)
	database.InitDefaultDataBase("root:123456@tcp(127.0.0.1:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local")
	c, err := GetContractDetail(0, "BTCUSDT", nil)
	if err != nil {
		t.Logf("get contract fail,%v", err)
		return
	}
	depth := cache.GetContractTradeDepth(c.ContractCode)
	if depth == nil {
		t.Errorf("depth data nil")
		return
	}
	log.Info("depth", zap.Any("data", depth))
	mp, err := GenerateContractPriceForEntrustLimit(c, "S", 6000, 3, true, 1, 5, true, nums.NewFromFloat(38712.9), nil)
	if err != nil {
		t.Logf("GenerateContractPrice fail,%v", err)
		return
	}
	t.Logf("mp:%+v", *mp)
}

func TestRandSlippage(t *testing.T) {
	for i := 0; i < 10; i++ {
		s := GetRandSlippage(nums.NewFromFloat(0.01), nums.NewFromFloat(0.09), nums.NewFromFloat(0.01))
		t.Log(s.String())
	}
}
