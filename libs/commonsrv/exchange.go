package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"errors"
	"github.com/shopspring/decimal"
	"time"
)

const tradeNStep = 2

// 获取最大可开张数
func GetCanOpenMaxVolume(contract *proto.Contract, side string, lever int, balance decimal.Decimal) (int, error) {
	depth := cache.GetContractTradeDepth(contract.ContractCode)
	if depth == nil || (side == define.OrderBuy && len(depth.Sell) == 0) {
		return 0, errors.New("没有可成交的深度")
	}

	var tPrice decimal.Decimal
	max := len(depth.Sell)
	if max > 5 {
		// 取第五档价格
		max = 5
	}
	tPrice = depth.Sell[max-1].PriceD

	if side == define.OrderSell && len(depth.Buy) > 0 && tPrice.LessThanOrEqual(decimal.Zero) {
		// 做空没有卖深度时取买一价
		tPrice = depth.Buy[0].PriceD
	}

	if tPrice.LessThanOrEqual(decimal.Zero) {
		return 0, errors.New("没有可成交的深度价格")
	}

	// 计算最大可开
	// 最大可开张数 = (可用余额*(1-维持保证金率)*杠杆)/(价格*合约面值 + 价格*合约面值*2*手续费率*杠杆)
	leverD := nums.NewFromInt(lever)
	perV := nums.NewFromString(contract.ParValue)
	suffix := tPrice.Mul(perV).Add(tPrice.Mul(perV).Mul(define.DecimalTwo).Mul(decimal.NewFromFloat(contract.FeeTaker)).Mul(leverD))
	if suffix.IsZero() {
		return 0, errors.New("使用深度计算可开仓量失败")
	}
	return nums.Int(balance.Mul(define.DecimalOne.Sub(decimal.NewFromFloat(contract.MaintenanceMarginRatio))).Mul(leverD).Div(suffix)), nil
}

//获取成交价格
func GenerateContractPrice(cg *proto.Contract, side string, amount int, mode int, isOpen bool, accountType define.AccountType, orderType int, isDev bool) (mp *proto.MatchPrice, err error) {
	var (
		tp                  decimal.Decimal
		buyFirst, sellFirst decimal.Decimal
		isForce             bool
	)
	if cg == nil || cg.ContractCode == "" {
		return nil, errors.New("无效的合约信息")
	}
	log.Infof("合约：%v,撮合参数，方向：%v,委托数量:%v,模式：%v,是否开仓：%v,orderType：%v,isDevEnv：%v", cg.ContractCode, side, amount, mode, isOpen, orderType, isDev)
	if mode == 0 {
		mode = define.EntrustModeBest5Level
	}
	if mode != define.EntrustModeDefault && mode != define.EntrustModeBest3Level && mode != define.EntrustModeBest5Level {
		return nil, errors.New("无效的下单模式")
	}
	if side != define.OrderBuy && side != define.OrderSell {
		return nil, errors.New("无效的下单方向")
	}
	volume := nums.NewFromInt(amount) //委托量
	isForce = CheckOrderType(accountType, orderType, isOpen)
	if isForce {
		mode = define.EntrustModeBest5Level
	}
	code := cg.ContractCode
	depth := cache.GetContractTradeDepth(code)
	if depth == nil {
		err = errors.New("没有可成交的深度")
		return
	}
	log.Infof("获取合约;%v，可交易深度：%+v", code, *depth)
	if !isDev && orderType != define.OrderTypeForceCloseOut {
		if time.Now().Unix()-depth.TS >= 90 {
			err = errors.New("行情陈旧")
			log.Errorf("获取合约%v可交易深度大于等于90s没有更新，暂不可成交", code)
			return
		}
	}

	if len(depth.Buy) > 0 {
		buyFirst = depth.Buy[0].PriceD
	}
	if len(depth.Sell) > 0 {
		sellFirst = depth.Sell[0].PriceD
	}

	var data []proto.Depth
	if side == define.OrderBuy {
		data = depth.Sell
	} else {
		data = depth.Buy
	}
	if data == nil || len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约深度数据有误,%+v", code, *depth)
		return
	}

	if len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约对手深度数据无效,%+v", code, *depth)
		return
	}
	log.Debugf("对手盘深度：%v", data)
	mp = &proto.MatchPrice{
		DepthPrice: depth.CurDepthPrice,
		BuyFirst:   buyFirst,
		SellFirst:  sellFirst,
		BuyMax:     depth.BuyMax,
		SellMax:    depth.SellMax,
	}

	level := 1
	takeLevel := 1
	switch mode {
	case define.EntrustModeDefault:
		level = 1
	case define.EntrustModeBest3Level:
		level = 3
	case define.EntrustModeBest5Level:
		level = 5
	default:
		level = 5
	}

	if isForce {
		level = 6
	}

	var tPrice, tVolume, matchPrice decimal.Decimal
	entrustAmount := volume //委托剩余量
	for i, node := range data {
		if node.PriceD.Equal(decimal.Zero) {
			node.PriceD = nums.NewFromString(node.Price)
		}
		log.Infof("depth node:%+v", node)
		var thisDeal decimal.Decimal
		depthVolume := nums.NewFromInt64(node.Amount)
		if entrustAmount.LessThanOrEqual(depthVolume) {
			thisDeal = entrustAmount
		} else {
			thisDeal = depthVolume
		}

		tPrice = tPrice.Add(node.PriceD.Mul(thisDeal))
		tVolume = tVolume.Add(thisDeal)

		entrustAmount = entrustAmount.Sub(thisDeal)
		takeLevel = i + 1
		if entrustAmount.Equal(decimal.Zero) || i >= level-1 {
			break
		}
	}
	if tVolume.GreaterThan(decimal.Zero) {
		tp = tPrice.Div(tVolume).Truncate(cg.Digit)
		if side == define.OrderBuy {
			tp = nums.Ceiling(tp, cg.PriceStep).Truncate(cg.Digit)
		} else {
			tp = nums.Floor(tp, cg.PriceStep).Truncate(cg.Digit)
		}
	} else {
		if isForce {
			tp = data[0].PriceD
		}
	}
	log.Infof("市价单，成交价：%+v,成交总金额;%v,成交量：%v,takeLevel:%v，maxLevel；%v", tp.String(), tPrice.String(), tVolume.String(), takeLevel, len(data))

	matchPrice = tp
	matchVolume := tVolume
	//如果市价5挡以上成交，有部分没有成交的，修改成交价位当前价格加减nStep*最小价格变动
	price := nums.NewFromInt(tradeNStep).Mul(cg.PriceStep).Truncate(cg.Digit)
	if entrustAmount.GreaterThan(decimal.Zero) {
		if takeLevel >= 5 {
			if side == define.OrderBuy {
				tp = tp.Add(price).Truncate(cg.Digit)
			} else {
				if tp.GreaterThan(price) {
					tp = tp.Sub(price).Truncate(cg.Digit)
				}
			}
		}
	}

	if isForce {
		tVolume = volume
	}
	var status int
	if tVolume.IntPart() == 0 {
		status = define.OrderStatusNotDealCancel
	} else {
		if tVolume.IntPart() == int64(amount) {
			status = define.OrderStatusFull
		} else {
			status = define.OrderStatusPartCancel
		}
	}

	mp = &proto.MatchPrice{
		TakeLevel:   takeLevel,
		MatchPrice:  matchPrice,
		MatchVolume: matchVolume,
		TradePrice:  tp,
		TradeVolume: tVolume.IntPart(),
		DepthPrice:  depth.CurDepthPrice,
		BuyFirst:    buyFirst,
		SellFirst:   sellFirst,
		BuyMax:      depth.BuyMax,
		SellMax:     depth.SellMax,
		Status:      status,
	}
	if depth != nil {
		mp.Depth = *depth
	}
	return
}

// GenerateContractPriceForEntrustLimit 限价单逻辑
func GenerateContractPriceForEntrustLimit(cg *proto.Contract, side string, amount int, mode int, isOpen bool, accountType define.AccountType, orderType int, isDev bool, price decimal.Decimal, depth *proto.DepthTradeContainer) (mp *proto.MatchPrice, err error) {
	var (
		tp                  decimal.Decimal
		buyFirst, sellFirst decimal.Decimal
		matchPrice          decimal.Decimal
		isForce             bool
	)
	if cg == nil || cg.ContractCode == "" {
		return nil, errors.New("无效的合约信息")
	}
	log.Infof("合约：%v,撮合参数，方向：%v,委托数量:%v,是否开仓：%v,orderType：%v,isDevEnv：%v", cg.ContractCode, side, amount, isOpen, orderType, isDev)

	if side != define.OrderBuy && side != define.OrderSell {
		return nil, errors.New("无效的下单方向")
	}
	volume := nums.NewFromInt(amount) //委托量
	code := cg.ContractCode
	if depth == nil {
		depth = cache.GetContractTradeDepth(code)
		if depth == nil {
			err = errors.New("没有可成交的深度")
			return
		}
	}
	log.Infof("获取合约;%v，可交易深度：%+v", code, *depth)
	if !isDev && orderType != define.OrderTypeForceCloseOut {
		if time.Now().Unix()-depth.TS >= 90 {
			err = errors.New("行情陈旧")
			log.Errorf("获取合约%v可交易深度大于等于90s没有更新，暂不可成交", code)
			return
		}
	}

	if len(depth.Buy) > 0 {
		buyFirst = depth.Buy[0].PriceD
	}
	if len(depth.Sell) > 0 {
		sellFirst = depth.Sell[0].PriceD
	}

	var data []proto.Depth
	if side == define.OrderBuy {
		data = depth.Sell
	} else {
		data = depth.Buy
	}
	if data == nil || len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约深度数据有误,%+v", code, *depth)
		return
	}

	if len(data) == 0 {
		err = errors.New("not enough levels to trade")
		log.Errorf("code；%v合约对手深度数据无效,%+v", code, *depth)
		return
	}
	log.Debugf("对手盘深度：%v", data)
	mp = &proto.MatchPrice{
		DepthPrice: depth.CurDepthPrice,
		BuyFirst:   buyFirst,
		SellFirst:  sellFirst,
		BuyMax:     depth.BuyMax,
		SellMax:    depth.SellMax,
	}

	level := 6
	takeLevel := 1

	var tPrice, tVolume decimal.Decimal
	entrustAmount := volume //委托剩余量
	for i, node := range data {
		if node.PriceD.Equal(decimal.Zero) {
			node.PriceD = nums.NewFromString(node.Price)
		}
		if side == define.OrderBuy && node.PriceD.GreaterThan(price) {
			break
		}
		if side == define.OrderSell && node.PriceD.LessThan(price) {
			break
		}
		log.Infof("depth node:%+v", node)
		var thisDeal decimal.Decimal
		depthVolume := nums.NewFromInt64(node.Amount)
		if entrustAmount.LessThanOrEqual(depthVolume) {
			thisDeal = entrustAmount
		} else {
			thisDeal = depthVolume
		}

		tPrice = tPrice.Add(node.PriceD.Mul(thisDeal))
		tVolume = tVolume.Add(thisDeal)

		entrustAmount = entrustAmount.Sub(thisDeal)
		takeLevel = i + 1
		if entrustAmount.Equal(decimal.Zero) || i >= level-1 {
			break
		}
	}
	if tVolume.GreaterThan(decimal.Zero) {
		tp = tPrice.Div(tVolume).Truncate(cg.Digit)
		if side == define.OrderBuy {
			tp = nums.Ceiling(tp, cg.PriceStep).Truncate(cg.Digit)
		} else {
			tp = nums.Floor(tp, cg.PriceStep).Truncate(cg.Digit)
		}
	}
	matchPrice = tp
	matchVolume := tVolume
	log.Infof("限价单，成交价：%+v,成交总金额;%v,成交量：%v,takeLevel:%v,maxLevel：%v", tp.String(), tPrice.String(), tVolume.String(), takeLevel, len(data))

	//如果市价5挡以上成交，有部分没有成交的，修改成交价位当前价格加减nStep*最小价格变动
	priceDiff := nums.NewFromInt(tradeNStep).Mul(cg.PriceStep).Truncate(cg.Digit)
	if entrustAmount.GreaterThan(decimal.Zero) {
		if takeLevel >= 5 {
			if side == define.OrderBuy {
				tp = tp.Add(priceDiff).Truncate(cg.Digit)
				if tp.GreaterThan(price) {
					tp = price
				}
			} else {
				if tp.GreaterThan(priceDiff) {
					tp = tp.Sub(priceDiff).Truncate(cg.Digit)
				}
				if tp.LessThan(price) {
					tp = price
				}
			}
		}
	}
	if isForce {
		tVolume = volume
	}
	var status int
	if tVolume.IntPart() == 0 {
		status = define.OrderStatusNotDealCancel
	} else {
		if tVolume.IntPart() == int64(amount) {
			status = define.OrderStatusFull
		} else {
			status = define.OrderStatusPartCancel
		}
	}

	mp = &proto.MatchPrice{
		TakeLevel:   takeLevel,
		MatchPrice:  matchPrice,
		MatchVolume: matchVolume,
		TradePrice:  tp,
		TradeVolume: tVolume.IntPart(),
		DepthPrice:  depth.CurDepthPrice,
		BuyFirst:    buyFirst,
		SellFirst:   sellFirst,
		BuyMax:      depth.BuyMax,
		SellMax:     depth.SellMax,
		Status:      status,
	}
	if depth != nil {
		mp.Depth = *depth
	}
	return
}

//OrderTypeLimit         = 2 // 止盈单
//OrderTypeStop          = 4 // 止损单
//OrderTypeForceCloseOut = 5 // 强平单
//OrderTypePlanCloseOut  = 6 // 条件平仓
//OrderTypeFollowClose   = 7 // 带单平仓(跟单者被动平仓）
//OrderTypeFollowLimit   = 8 // 带单止盈
//OrderTypeFollowStop    = 9 // 带单止损
//判断是否强平或止盈止损单
func CheckOrderType(accountType define.AccountType, orderType int, isOpen bool) bool {
	if !isOpen && accountType.GetUserAccountType() == define.UserAccountTypeFollow {
		return true
	}
	return orderType == define.OrderTypeLimit || orderType == define.OrderTypeStop || orderType == define.OrderTypeFollowLimit || orderType == define.OrderTypeFollowStop || orderType == define.OrderTypeForceCloseOut
}
