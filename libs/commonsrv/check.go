package commonsrv

import (
	"bc/libs/cache"
	"bc/libs/conf"
	"bc/libs/log"
	"github.com/shopspring/decimal"
	"strings"
)

func DealSourceNeedConvert(source, code string) (cCode string) {
	cCode = code
	if code == "BTCUSDT" {
		return
	}
	mc := conf.MarketConfig().SpotWeight
	if sMap, ok := mc[code]; ok {
		if w, ok := sMap[source]; ok {
			if w.IsConvert {
				return GetConvertAfterCode(code)
			}
		}
	}
	return
}

//conMap[合约]map[来源】 需要转换的源
//{"BTCUSDT":{ "okex":{}}
func IsSourceNeedConvert(source, code string, conMap map[string]map[string]struct{}) (cCode string) {

	if conMap == nil {
		return
	}
	convertMap, ok := conMap[code]
	if !ok || convertMap == nil {
		return
	}
	if _, ok = convertMap[source]; ok {
		return GetConvertAfterCode(code)
	}
	return
}

func GetConvertAfterCode(code string) string {
	if strings.HasSuffix(code, "USDT") {
		return strings.ReplaceAll(code, "USDT", "BTC")
	}
	return code
}

func ConvertTradePriceAndCode(code string, price decimal.Decimal) (string, decimal.Decimal) {
	if strings.HasSuffix(code, "BTC") {

		//获取BTC/USDT合约现货指数价格
		btcPrice := cache.GetContractSpotIndexPrice("BTCUSDT")
		if btcPrice == nil {
			log.Errorf("获取不到BTCUSDT现货指数，无法进行指定源价格转换")
			price = decimal.Zero
			return code, price
		}
		//XXXUSDT换算价格=XXXBTC价格*BTCUSDT价格
		code = strings.ReplaceAll(code, "BTC", "USDT")
		price = price.Mul(btcPrice.Price)
		log.Infof("获取现货价格，合约：%v，转换后价格：%v,Btc现货价格：%v", code, price.String(), btcPrice.Price.String())
	}
	return code, price
}
