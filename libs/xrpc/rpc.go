package xrpc

import (
	"net/rpc"
	"net/rpc/jsonrpc"
	"time"

	"bc/libs/define"
	"bc/libs/log"
)

type RClient struct {
	*rpc.Client
	address string
}

func NewClient(address string) (r *RClient, err error) {
	r = &RClient{address: address}
	r.Client, err = rpc.Dial("tcp", address)
	if err != nil {
		log.Errorf("create RPC client fail,%v, err:%v", address, err)
		return
	}
	go r.ping()
	return
}

func NewClientForHttp(address string) (r *RClient, err error) {
	r = &RClient{address: address}
	r.Client, err = rpc.DialHTTP("tcp", address)
	if err != nil {
		log.Errorf("create RPC client fail,%v", address)
		return
	}
	go r.ping()
	return
}

func NewClientForJson(address string) (r *RClient, err error) {
	r = &RClient{address: address}
	r.Client, err = jsonrpc.Dial("tcp", address)
	if err != nil {
		log.Errorf("create RPC client fail,%v", address)
		return
	}
	go r.ping()
	return
}

func (r *RClient) ping() {
	t := time.Tick(time.Second)
	var slog bool
	for range t {
		err := r.Client.Call("RPC.Ping", &define.NoneArg{}, &define.NoneReply{})
		if err != nil {
			if !slog {
				log.Errorf("client.Call(%s, arg, reply) error(%v),r:%+v", "RPC.Ping", err, r)
				slog = true
			}
			if err == rpc.ErrShutdown {
				client, err := rpc.Dial("tcp", r.address)
				if err != nil {
					log.Errorf("create RPC client fail,%v,%v", r.address, err)
					continue
				}
				r.Client = client
				log.Infof("连接%vRpc成功", r.address)
			} else {
				r.Client.Close()

			}
		}
	}
}

func (r *RClient) CallRPC(serviceMethod string, args interface{}, reply interface{}) error {
	if r.Client == nil {
		return define.ErrMsgServerNotAvailable
	}
	return r.Call(serviceMethod, args, reply)
}
