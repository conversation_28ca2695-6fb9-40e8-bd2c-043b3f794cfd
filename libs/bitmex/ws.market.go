/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package bitmex

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"bc/libs/log"
	"github.com/gorilla/websocket"
)

const (
	_wsMarket = "wss://www.bitmex.com/realtime"
	_sub      = "subscribe"
	_unSub    = "unsubscribe"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    chan struct{}
	lastStart                       time.Time
}

type Client struct {
	send           chan []byte
	done           chan struct{}
	isClose        bool
	lock, statLock sync.RWMutex
	mc             *MarketWsClient
	con            *websocket.Conn
	ticker         *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
	timeOut := 20
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to bitmex fail,err:%v,http rsp:%+v", err, rsp)
		if strings.Contains(err.Error(), "429") {
			timeOut += 10
			time.Sleep(time.Duration(timeOut) * time.Second)
		} else {
			time.Sleep(time.Duration(timeOut) * time.Second)
		}

		goto Retry
	}
	c.mc.lastRv = time.Now()
	c.mc.lastStart = time.Now()
	log.Infof("bitmex dial over r1")
	c.con = conn
	c.Receive()
	c.PingLoop()
	//订阅
	c.sub(c.mc.topics)
	log.Infof("bitmex sub over r2")
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func (c *Client) ReadMessage(ctx context.Context) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = c.con.ReadMessage()
			ch <- msg
		}
	}()
	return ch
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

func (c *Client) PingLoop() {
	go func() {
		for range c.ticker.C {
			c.lock.Lock()
			c.con.WriteMessage(websocket.TextMessage, []byte("ping"))
			c.lock.Unlock()
		}
	}()
}

func (c *Client) sub(topic []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	d := BaseAction{Action: _sub, Args: topic}
	b, err := json.Marshal(d)
	if err != nil {
		fmt.Errorf("marshal fail,%v", err)
		return
	}
	c.Write(b)
}

func (c *Client) unSub(topic []string) {
	if c.isClose {
		return
	}
	d := BaseAction{Action: _unSub, Args: topic}
	b, err := json.Marshal(d)
	if err != nil {
		fmt.Errorf("marshal fail,%v", err)
		return
	}
	c.Write(b)
}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := c.ReadMessage(ctx)
		for {
			select {
			case <-c.done:
				log.Error("开始断开发送协成")
				return
			case data := <-c.send:
				c.lock.Lock()
				c.con.WriteMessage(websocket.TextMessage, data)
				c.lock.Unlock()
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				if msg.Type == -1 {
					c.mc.ClientStatus <- struct{}{}
					log.Errorf("bitmex msgType；%v，msg:%+v", msg.Type, string(msg.Msg))
					return
				}
				content := string(msg.Msg)
				if content == "pong" {
					log.Infof("bitmex msg:%+v,msgType；%v", string(msg.Msg), msg.Type)
					continue
				}
				if c.mc.msgHandler != nil {
					c.mc.msgHandler(msg.Msg)
				}

			}

		}
	}()

}

type Config struct {
	WsPoint string
	dialer  *websocket.Dialer
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:       nil,
		wsPoint:      _wsMarket,
		topics:       nil,
		ClientStatus: make(chan struct{}),
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string) {
	if c == nil {
		return
	}
	log.Infof("bitmex sub topics:%v", topics)
	c.topics = topics
	c.client.sub(c.topics)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
	//d := BaseAction{Action: _unSub, Args: c.topics}
	//b, err := json.Marshal(d)
	//if err != nil {
	//	fmt.Errorf("marshal fail,%v", err)
	//	return
	//}

}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
	//d := BaseAction{Action: _unSub, Args: c.topics}
	//b, err := json.Marshal(d)
	//if err != nil {
	//	fmt.Errorf("marshal fail,%v", err)
	//	return
	//}
	//e := c.con.WriteMessage(websocket.TextMessage, b)
	//if e != nil {
	//	c.setLastError(e)
	//}
}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	select {
	case <-c.ClientStatus:
		c.restart()
	default:
		var restart bool
		//if time.Since(c.lastStart).Minutes() > 60 {
		//	restart = true
		//}
		c.rvLock.RLock()
		last := c.lastRv
		c.rvLock.RUnlock()
		if time.Since(last).Seconds() > 15 {
			log.Infof("长时间未获取到数据，开始重启客户端连接")
			restart = true
		}
		if restart {
			c.restart()
		}
	}

}

func (c *MarketWsClient) restart() {
	log.Infof("begin restart client....")
	c.client.close()
	c.client = NewClient(c)
	c.client.start()
}

//d := `{"op": "subscribe", "args": ["funding","trade"]}`
type BaseAction struct {
	Action string   `json:"op"`
	Args   []string `json:"args"`
}

//{"success":true,"subscribe":"funding","request":{"op":"subscribe","args":["funding"]}}

type SubRsp struct {
	Success   bool   `json:"success"`
	Subscribe string `json:"subscribe"`
}

type BasicRsp struct {
	Table  string          `json:"table"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
}

type Funding struct {
	Timestamp        string  `json:"timestamp"`
	Symbol           string  `json:"symbol"`
	FundingInterval  string  `json:"fundingInterval"`
	FundingRate      float64 `json:"fundingRate"`
	FundingRateDaily float64 `json:"fundingRateDaily"`
}

//{
//"table": "trade",
//"action": "insert",
//"data": [{
//"timestamp": "2020-11-25T07:09:49.514Z",
//"symbol": "ETHUSD",
//"side": "Buy",
//"size": 4,
//"price": 594.65,
//"tickDirection": "PlusTick",
//"trdMatchID": "f478a2e3-2f9e-5087-e869-3800f2b34de3",
//"grossValue": 237860,
//"homeNotional": 0.07574323044877863,
//"foreignNotional": 45.04071198636622
//}]
//}

type Ticker struct {
	Contract string  `json:"symbol"`
	Side     string  `json:"side"`
	Price    float64 `json:"price"`
	Amount   int     `json:"size"`
	Ts       string  `json:"timestamp"`
	MatchId  string  `json:"trdMatchID"`
}
