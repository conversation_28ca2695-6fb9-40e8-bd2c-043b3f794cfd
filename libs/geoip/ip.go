package geoip

import (
	"net"
	"strings"

	"bc/libs/define"
	"bc/libs/log"

	"github.com/oschwald/geoip2-golang"
	"go.uber.org/zap"
)

/*
 Brazilian Portuguese (pt-BR) 葡萄牙语
 English (en) 英语
 French (fr) 法语
 German (de) 德语
 Japanese (ja) 日语
 Russian (ru) 俄语
 Simplified Chinese (zh-CN) 简中
 Spanish (es) 西班牙语
*/

const (
	LanguagePortuguese = "pt-BR"
	LanguageEnglish    = "en"
	LanguageFrench     = "fr"
	LanguageGerman     = "de"
	LanguageJapanese   = "ja"
	LanguageRussian    = "ru"
	LanguageChinese    = "zh-CN"
	LanguageSpanish    = "es"
)

func convertLanguage(lang define.ReqLang) string {
	switch lang {
	case define.ReqLangCN, define.ReqLangTC:
		return LanguageChinese
	case define.ReqLangRU:
		return LanguageRussian
	case define.ReqLangDE:
		return LanguageGerman
	default:
		return LanguageEnglish
	}
}

var g *geoip2.Reader

func InitGeoIP(dbFile string) {
	var err error
	g, err = geoip2.Open(dbFile)
	if err != nil {
		log.Fatal("InitGeoIP open file failed", zap.String("filepath", dbFile), zap.Error(err))
	}
}

func ParseCountry(ip net.IP) (*geoip2.Country, error) {
	return g.Country(ip)
}

func ParseCity(ip net.IP) (*geoip2.City, error) {
	return g.City(ip)
}

func GetCountryIsoCode(ipAddr string) (string, bool) {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return "", false
	}

	c, err := ParseCountry(ip)
	if err != nil {
		log.Error("GetCountryIsoCode ParseCountry fail", zap.String("ip", ipAddr), zap.Error(err))
		return "", false
	}

	return c.Country.IsoCode, true
}

func GetFullLocation(ipAddr string, lang define.ReqLang) (string, bool) {
	ip := net.ParseIP(ipAddr)
	if ip == nil {
		return "", false
	}

	city, err := ParseCity(ip)
	if err != nil {
		log.Error("GetFullLocation ParseCity fail", zap.String("ip", ipAddr), zap.Error(err))
		return "", false
	}

	language := convertLanguage(lang)

	var buf strings.Builder
	buf.WriteString(city.Country.Names[language])
	if len(city.Subdivisions) > 0 && len(city.Subdivisions[0].Names) > 0 && len(city.Subdivisions[0].Names[language]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.Subdivisions[0].Names[language])
	} else if len(city.City.Names) > 0 && len(city.City.Names[language]) > 0 {
		buf.WriteString("-")
		buf.WriteString(city.City.Names[language])
	}

	return buf.String(), true
}

func Close() {
	if g != nil {
		_ = g.Close()
	}
}
