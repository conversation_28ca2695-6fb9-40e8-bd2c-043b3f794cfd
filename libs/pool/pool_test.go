package pool

import (
	"bc/libs/proto"
	"fmt"
	"github.com/emirpasic/gods/lists/singlylinkedlist"
	"github.com/emirpasic/gods/maps/treemap"
	"github.com/emirpasic/gods/sets/treeset"
	"github.com/emirpasic/gods/utils"
	"runtime"
	"testing"
	"time"
)

func TestPool(t *testing.T) {
	n := time.Now()
	runtime.NumCPU()
	pool := NewPool(runtime.NumCPU(), 20)
	pool.Run()
	for i := 0; i < 20; i++ {
		pool.AddJobs(&Job{Func: Do, ID: int64(i)})
	}
	pool.Close()
	pool.Wait()
	t.Logf("over:%v", time.Since(n).Seconds())
}

func Do(id int64) {
	time.Sleep(1 * time.Second)
	fmt.Println(id)
}

func TestP(t *testing.T) {
	t.Logf("v:%v", 1<<14)
	m := treemap.NewWithIntComparator() // empty (keys are of type int)
	m.Put(1, "x")                       // 1->x
	m.Put(2, "b")                       // 1->x, 2->b (in order)
	m.Put(3, "a")                       // 1->a, 2->b (in order)
	t.Log(m.Max())
	//m.Each(func(key interface{}, value interface{}) {
	//	if key==2{
	//		t.Log(key)
	//	}
	//})

	//nm:=m.Select(func(key interface{}, value interface{}) bool {
	//	v:=key.(int)
	//	if v<3{
	//		return true
	//	}
	//	return false
	//})
	//nm.Each(func(key interface{}, value interface{}) {
	//	t.Log(key)
	//})

	i := m.Iterator()
	for i.Begin(); i.Next(); {
		t.Log(i.Key(), i.Value())
	}
	t.Log("---------")
	//s:=m.Iterator()
	for i.Prev() {
		t.Log(i.Key(), i.Value())
	}

}

func TestLink(t *testing.T) {
	list := singlylinkedlist.New(1, 2, 5, 3)
	list.Sort(utils.Int64Comparator)
}

func TestTree(t *testing.T) {
	b := treeset.NewWithIntComparator()
	b.Add()
	a := treemap.NewWithIntComparator()
	a.Put(1, 1)
	a.Put(2, 2)
	a.Put(3, 3)
	a.Put(4, 4)
	a.Put(5, 5)
	a.Put(6, 6)
	//t.Log(a.Max())
	i := a.Iterator()
	for i.End(); i.Prev(); {
		t.Log(i.Key())
	}

}

// PlanComparator provides a basic comparison on int
func PlanOrderComparator(a, b interface{}) int {
	aAsserted := a.(proto.PlanBasic)
	bAsserted := b.(proto.PlanBasic)
	switch {
	case aAsserted.Price.GreaterThan(bAsserted.Price):
		return 1
	case aAsserted.Price.LessThan(bAsserted.Price):
		return -1
	default:
		return 0
	}
}
