package invite

import (
	"bytes"
	"fmt"
	"strconv"
)

const (
	CodeLength = 6 // 邀请码长度
)

var (
	generator   = new(Generator) // 邀请码生成器单例
	permutation int64            // 同样的n个字符最多组合数
	uniqueCount int64            // 最大唯一组合数

	codeBase = []byte("pF2PwGveNud3UMJ5TcYhHWBas9mCE7SxjZKnf6rLRVt4Ay8kDXiQ") // 邀请码字符列表 52位
)

type Generator struct{}

type Code struct {
	ID   int64  `json:"id"`   // 编号
	Code string `json:"code"` // 邀请码
}

func init() {
	permutation = factorial(int64(CodeLength))
	uniqueCount = maxUnique(int64(len(codeBase)), int64(CodeLength))
}

// 创建生成器
func GetGenerator() *Generator {
	return generator
}

// 生成邀请码
func (g *Generator) Encode(id int64) string {
	com := id / permutation
	if com >= uniqueCount {
		fmt.Println("id can't be greater than ", strconv.FormatInt(uniqueCount*permutation-1, 10))
		return ""
	}
	per := int(id % permutation)
	chars := combine(com)
	chars = permute(chars, per)
	return string(chars)
}

// 解析邀请码
func (g *Generator) Decode(code string) int64 {
	if len(code) != CodeLength {
		fmt.Println("invalid code, code: ", code)
		return 0
	}
	chars := []byte(code)
	com := deCombine(chars)
	per := dePermute(chars)
	return com*permutation + per
}

// 阶乘
func factorial(n int64) int64 {
	f := int64(1)
	for i := int64(2); i <= n; i++ {
		f *= i
	}
	return f
}

// 求最大唯一组合数
func maxUnique(n, m int64) int64 {
	w := int64(1)
	for i := n - m + 1; i <= n; i++ {
		w *= i
	}
	for j := int64(2); j <= m; j++ {
		w /= j
	}
	return w
}

// 组合字符
func combine(com int64) []byte {
	chars := make([]byte, CodeLength)
	var start, index int
	for index < CodeLength {
		for s := start; s < len(codeBase); s++ {
			c := maxUnique(int64(len(codeBase)-s-1), int64(CodeLength-index-1))
			if com >= c {
				com -= c
				continue
			}
			chars[index] = codeBase[s]
			index++
			start = s + 1
			break
		}
	}
	return chars
}

// 排序得到最终的邀请码
func permute(code []byte, per int) []byte {
	chars := make([]byte, len(code))
	copy(chars, code)
	step := len(code)
	offset := make([]int, len(code))

	for i := step - 1; i >= 0; i-- {
		offset[i] = per % step
		per /= step
		step--
	}

	for i := range code {
		if offset[i] == 0 {
			continue
		}
		chars[i], chars[i-offset[i]] = chars[i-offset[i]], chars[i]
	}
	return chars
}

// 解邀请码
func deCombine(code []byte) int64 {
	offset := make([]int, CodeLength)
	chars := sort(code)

	// 验证邀请码是否有效并赋值offset
	for i := range chars {
		offset[i] = bytes.IndexByte(codeBase, chars[i])
		if offset[i] == -1 {
			fmt.Println("invalid code, code: ", code)
			return 0
		}
	}

	var com int64
	for i := range offset {
		if i == 0 {
			if offset[0] == 0 {
				continue
			}
			for j := 0; j < offset[0]; j++ {
				com += maxUnique(int64(len(codeBase)-j-1), int64(CodeLength-1))
			}
			continue
		}
		if offset[i]-offset[i-1] <= 1 {
			continue
		}
		for k := offset[i-1] + 1; k < offset[i]; k++ {
			com += maxUnique(int64(len(codeBase)-k-1), int64(CodeLength-i-1))
		}
	}
	return com
}

// 获取排序值
func dePermute(code []byte) int64 {
	chars := sort(code)
	offset := make([]int, len(code))
	for i := len(code) - 1; i >= 0; i-- {
		index := bytes.IndexByte(code, chars[i])
		offset[i] = i - index
		code[i], code[i-offset[i]] = code[i-offset[i]], code[i]
	}
	var per int
	step := 1
	for i := range offset {
		per = per*step + offset[i]
		step++
	}
	return int64(per)
}

// 排序 按照字符列表升序排列
func sort(code []byte) []byte {
	chars := make([]byte, len(code))
	var index int
	for i := range codeBase {
		if bytes.IndexByte(code, codeBase[i]) != -1 {
			chars[index] = codeBase[i]
			index++
		}
	}
	return chars
}
