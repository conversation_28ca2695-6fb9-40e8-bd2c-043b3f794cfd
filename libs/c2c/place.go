package c2c

import (
	"bc/libs/log"
	"go.uber.org/zap"
)

func GetExchangeRate(reqID int64, imei string, arg *PlaceRateArg) (*Reply, error) {
	param := &Params{
		IMEI: imei,
		Data: arg,
	}
	result, err := client.Do(reqID, c2cActionFetchRate, param)
	if err != nil {
		log.Error("PlaceOrder client.Do() error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return result, nil
}

func PlaceOrder(reqID int64, imei string, arg *PlaceOrderArg) (*Reply, error) {
	param := &Params{
		IMEI: imei,
		Data: arg,
	}
	result, err := client.Do(reqID, c2cActionPlaceOrder, param)
	if err != nil {
		log.Error("PlaceOrder client.Do() error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return result, nil
}

func CheckPlaceParam(arg *PlaceArg) bool {
	if arg.Mode != PlaceTypeLegal && arg.Mode != PlaceTypeCoin {
		return false
	}
	return true
}

func PlaceLimit(reqID int64) (*Reply, error) {
	result, err := client.Do(reqID, c2cActionPlatformInfo, new(Params))
	if err != nil {
		log.Error("PlaceLimit client.Do() error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return result, nil
}

//审核通知
func AuditNotify(reqID int64, arg *OrderAuditArg) (*Reply, error) {
	param := &Params{
		Data: arg,
	}
	result, err := client.Do(reqID, c2cActionAudit, param)
	if err != nil {
		log.Error("AuditNotify client.Do() error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return result, nil
}

func OrderInfo(reqID int64) (*Reply, error) {
	result, err := client.Do(reqID, c2cActionOrderInfo, new(Params))
	if err != nil {
		log.Error("OrderInfo client.Do() error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	return result, nil
}
