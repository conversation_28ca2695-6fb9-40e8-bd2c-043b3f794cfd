package c2c

import (
	"errors"

	"bc/libs/define"
)

var (
	ParamErr = errors.New("param error")
)

const (
	ErrCodeNoplatformSell     = 2017 // 无商户信息
	ErrCodeNoplatformSellTime = 2018 // 时间错误
	ErrCodeNoRate             = 2019 // 无汇率
	ErrCodeNoOrder            = 2020 // 无订单号
	ErrCodeNoOrderAction      = 2021 // 订单状态错误
	ErrCodeOverPlaceMaxMoney  = 2022 //  订单过大
	ErrCodeOverPlaceMinMoney  = 2023 //  订单过小
)

func ConvertError(code int, msg string) *define.ReplyError {
	var err *define.ReplyError
	if code == ErrCodeNoplatformSell {
		// 暂无商户接单
		err = define.ErrMsgNoPlatformSell
	} else if code == ErrCodeNoplatformSellTime {
		// 不在商户服务时间内
		err = define.ErrMsgNoPlatformSellTime
	} else if code == ErrCodeOverPlaceMinMoney {
		err = define.ErrMsgLegalLessThanAmountLimit
	} else if code == ErrCodeOverPlaceMaxMoney {
		err = define.ErrMsgLegalMoreThanAmountLimit
	} else {
		err, _ = define.NewReplyErrorWithMsg(code, msg).(*define.ReplyError)
	}
	return err
}
