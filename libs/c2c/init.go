package c2c

import "net/http"

var client *Client

func InitC2CClient(url, platformID, secretKey string) {
	client = &Client{
		url:        url,
		PlatformID: platformID,
		SecretKey:  secretKey,
		c:          new(http.Client),
	}
}

func (c *Client) SetURL(url string) {
	if c != nil {
		c.url = url
	}
}

func (c *Client) SetPlatformID(platformID string) {
	if c != nil {
		c.PlatformID = platformID
	}
}

func (c *Client) SetSecretKey(secret string) {
	if c != nil {
		c.SecretKey = secret
	}
}
