package c2c

import (
	"bc/libs/log"
	"go.uber.org/zap"
	"time"

	"bc/libs/json"
	"bc/libs/sign"
)

// 请求签名生成
func (c *Client) genSignBody(p *Params) ([]byte, error) {
	p.PlatformID = c.PlatformID
	p.Ts = time.Now().Unix()

	param, err := sign.Struct2Map(p)
	if err != nil {
		return nil, err
	}

	origin := sign.GetParasStr(param) + c.SecretKey
	param["sig"] = sign.NormalEncode(origin)
	log.Info("PlatFormNotice", zap.Any("param", param), zap.String("origin", origin))

	body, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}

	return body, nil
}

// 回调签名生成
func (c *Client) genCallbackSign(p *LegalCallbackRequest) string {
	param, err := sign.Struct2Map(p)
	if err != nil {
		return ""
	}
	delete(param, "sig")
	origin := sign.GetParasStr(param) + c.SecretKey
	log.Info("genCallbackSign", zap.Any("param", param), zap.String("origin", origin))
	return sign.NormalEncode(origin)
}

func GenCallbackSign(p *LegalCallbackRequest) string {
	return client.genCallbackSign(p)
}
