package c2c

import (
	"bc/libs/define"
	"bc/libs/proto"
	"encoding/json"
	"github.com/shopspring/decimal"
	"net/http"
	"time"
)

type Client struct {
	url        string
	PlatformID string
	SecretKey  string
	c          *http.Client
}

type Params struct {
	Data       interface{} `json:"data"`
	Ts         int64       `json:"ts"`
	IMEI       string      `json:"imei"`
	PlatformID string      `json:"platformid"`
}

type Reply struct {
	Ret  int             `json:"ret"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}

type PlaceRateArg struct {
	// 前端请求参数
	Side     int    `json:"side"`      // 下单方向 1-卖 2买
	Mode     int    `json:"mode"`      // 下单输入类型
	CoinName string `json:"coin_name"` // 币种名称
	Amount   string `json:"amount"`    // 下单数量/下单金额

	// 服务端处理参数
	OrderID  string `json:"order_id"`  // 请求序列
	RealName string `json:"real_name"` // 真实姓名
	Extra    string `json:"extra"`     // 附加信息
}

type PlaceOrderArg struct {
	Mode     int    `json:"mode"`      // 下单输入类型
	CoinName string `json:"coin_name"` // 币种名称
	Amount   string `json:"amount"`    // 下单数量/下单金额

	BusinessId   int             `json:"business_id"`   //汇率商户id
	ExchangeRate decimal.Decimal `json:"exchange_rate"` //下单时汇率
	RateToken    string          `json:"rate_token"`    //下单汇率token

	// 服务端处理参数
	OrderID           string `json:"order_id"` // 渠道平台订单号
	Side              int    `json:"side"`
	RealName          string `json:"real_name"` // 真实姓名
	Extra             string `json:"extra"`     // 附加信息
	Contact           string `json:"contact"`   // 联系方式
	proto.UserPayment        //附带收款信息
}

type PlaceArg struct {
	// 前端请求参数
	Side         int    `json:"side"`          // 下单方向 1-卖 2买
	Mode         int    `json:"mode"`          // 下单输入类型
	CoinName     string `json:"coin_name"`     // 币种名称
	Amount       string `json:"amount"`        // 下单数量/下单金额
	FundPassword string `json:"fund_password"` // 资金密码

	//前端卖参数
	PaymentId int `json:"payment_id"` //收款账户id

	BusinessId   int             `json:"business_id"`   //汇率商户id
	ExchangeRate decimal.Decimal `json:"exchange_rate"` //下单时汇率
	RateToken    string          `json:"rate_token"`    //下单汇率token

	// 服务端处理参数
	OrderID  string `json:"order_id"`  // 渠道平台订单号
	RealName string `json:"real_name"` // 真实姓名
	Extra    string `json:"extra"`     // 附加信息
}

type OrderMarkArg struct {
	// 前端请求参数
	OrderID int64 `json:"order_id"` // 渠道平台订单号
}

const (
	PlaceTypeLegal = iota // 0-输入法币下单
	PlaceTypeCoin         // 1-输入数字币下单
)

type LegalCallbackRequest struct {
	TradeID string `json:"trade_id"` // 三方系统订单号
	OrderID string `json:"order_id"` // 本地订单号
	State   string `json:"state"`    // 状态
	Extra   string `json:"extra"`    // 附加信息(这里代表用户id)
	Ts      string `json:"ts"`       // 时间戳
	Sign    string `json:"sig"`      // 签名
}

type LegalCallbackParam struct {
	TradeID int64                  `json:"trade_id"` // 三方系统订单号
	OrderID int64                  `json:"order_id"` // 本地订单号
	State   define.LegalOrderState `json:"state"`    // 状态
	Extra   int64                  `json:"extra"`    // 附加信息(这里代表用户id)
}

// 登陆请求参数
type LoginArg struct {
	SystemId int64 `json:"system_id"` // 对接平台传入的用户唯一标识, string 的.可以传入 各种
}

// 登录返回内容
type LoginReply struct {
	Token      string `json:"token"`       // token
	PlatformID string `json:"platform_id"` // 平台id
	State      int    `json:"state"`       // 订单状态
}

type PlaceReply struct {
	SystemId           int64           `json:"system_id"`            // 系统id
	PlatFormPrice      decimal.Decimal `json:"platform_price"`       // 平台价格
	BusinessPrice      decimal.Decimal `json:"business_price"`       // 渠道商价格
	Amount             decimal.Decimal `json:"amount"`               // 渠道商价格
	AmountCny          decimal.Decimal `json:"amount_cny"`           // 渠道商价格
	BusinessId         int64           `json:"business_id"`          // 商家收款银行卡名称
	BusinessBankName   string          `json:"business_bank_name"`   // 商家收款银行卡名称
	BusinessBankNumb   string          `json:"business_bank_numb"`   // 商家银行卡账户
	BusinessBankHolder string          `json:"business_bank_holder"` // 商家银行卡收款人
	LimitTime          int64           `json:"limittime"`            // 限制时间\\
	CoinNmae           string          `json:"coin_name"`            // 商家收款银行卡名称
	ToKen              string          `json:"token"`                // 返回token
}

// 订单最大最小限制返回
type PlaceOrderLimit struct {
	Max decimal.Decimal `json:"max"` // 最大
	Min decimal.Decimal `json:"min"` // 最小
}

// 订单最大最小限制返回
type PlatFormPlaceLimit struct {
	Min decimal.Decimal `json:"min_legal_amount"` // 最小
	Max decimal.Decimal `json:"max_legal_amount"` // 最大
}

type GetRateRep struct {
	SystemId      int64           `json:"system_id"`      // 系统id
	PlatFormPrice decimal.Decimal `json:"platform_price"` // 平台价格
	BusinessPrice decimal.Decimal `json:"business_price"` //  商价格
	Amount        decimal.Decimal `json:"amount"`         // 数字币
	AmountCny     decimal.Decimal `json:"amount_cny"`     // 人民币
	Rates         decimal.Decimal `json:"rates"`          //  汇率
	BusinessId    int64           `json:"business_id"`    //商家收款银行卡名称
	LimitTime     int64           `json:"limittime"`      //限制时间\\
	CoinName      string          `json:"coin_name"`      //商家收款银行卡名称
	RedisRateKeys string          `json:"redisrateskeys"` //返回token
}

type OrderAuditArg struct {
	Id   int64 `json:"id"`   // 订单id
	Pass bool  `json:"pass"` // 是否通过 true 通过 false 驳回
}

type OrderInfoArg struct {
	Id int64 `json:"id"` // 订单id
}

type OrderReply struct {
	Id                 int64   `db:"id" json:"id"`                                     //编号
	PlatformId         string  `db:"platform_id" json:"platform_id"`                   //渠道id
	PlatformName       string  `db:"platform_name" json:"platform_name"`               //渠道名称
	PlatformOrderId    string  `db:"platform_order_id" json:"platform_order_id"`       //渠道订单id
	PlatformOrderExtra string  `db:"platform_order_extra" json:"platform_order_extra"` //渠道订单附加信息
	PlatformUserName   string  `db:"platform_user_name" json:"platform_user_name"`     //渠道买家实名信息
	Type               int     `db:"type" json:"type"`                                 //类型: 1 出售; 2 购买
	CoinName           string  `db:"coin_name" json:"coin_name"`                       //电子币种
	Amount             float64 `db:"amount" json:"amount"`                             //电子币数额
	LegalName          string  `db:"legal_name" json:"legal_name"`                     //法币名称
	LegalAmount        float64 `db:"legal_amount" json:"legal_amount"`                 //法币数额
	Fee                float64 `db:"fee" json:"fee"`                                   //手续费
	Payment            int     `db:"payment" json:"payment"`                           //用户选择的支付方式
	BasePrice          float64 `db:"base_price" json:"base_price,omitempty"`           //基础汇率
	PlatformPrice      float64 `db:"platform_price" json:"platform_price"`             //渠道汇率
	BusinessPrice      float64 `db:"business_price" json:"business_price,omitempty"`   //商家汇率
	BusinessId         int64   `db:"business_id" json:"business_id"`                   //商家ID

	BusinessBankCardId    int       `db:"business_bank_id" json:"business_bank_card_id"`    //商家银行卡id
	BusinessBankType      int       `db:"business_bank_type" json:"business_bank_type"`     //商家收款类型
	BusinessBankName      string    `db:"business_bank_name" json:"business_bank_name"`     //商家收款银行卡名称
	BusinessBankNumb      string    `db:"business_bank_numb" json:"business_bank_numb"`     //商家银行卡账户
	BusinessBankHolder    string    `db:"business_bank_holder" json:"business_bank_holder"` //商家银行卡收款人
	State                 int       `db:"state" json:"state"`                               //订单状态: 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 9：补充放款;
	Flag                  int       `db:"flag" json:"flag"`                                 //类型: 0 正常交易; 1 买家申诉; 2 卖家申诉
	Limit                 int       `db:"limit" json:"limit"`                               //付款期限, 分钟
	CreatedTime           time.Time `db:"created_time" json:"created_time"`                 //创建时间
	CreatedTimeStamp      int64     `json:"created_time_stamp"`                             //创建时间
	PayTime               time.Time `db:"pay_time" json:"pay_time"`                         //付款时间
	PayTimeStamp          int64     `json:"pay_time_stamp"`
	UpdateTime            time.Time `db:"update_time" json:"update_time"` //更新时间
	UpdateTimeStamp       int64     `json:"update_time_stamp"`
	PlaformCallbackResult string    `db:"plaform_callback_result" json:"plaform_callback_result,omitempty"` //记录 回调对应平台放币 充值的 返回结果
	PaymentLock           int       `db:"payment_lock" json:"payment_lock"`                                 //订单支持的付款方式
}
