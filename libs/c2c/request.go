package c2c

import (
	"bytes"
	"errors"
	"io/ioutil"
	"net/http"

	"bc/libs/json"
	"bc/libs/log"
	"go.uber.org/zap"
)

func (c *Client) Do(reqID int64, uri string, params *Params) (*Reply, error) {
	log.Info("c.Do()", zap.Int64("reqID", reqID), zap.String("uri", uri), zap.Any("param", params))
	if c == nil {
		log.Error("c.Do() Clinet not initialized", zap.Int64("reqID", reqID))
		return nil, errors.New("service not available")
	}

	// 签名
	body, err := c.genSignBody(params)
	if err != nil {
		log.Error("c.Do() GenSignBody error", zap.Int64("reqID", reqID), zap.Any("param", params), zap.Error(err))
		return nil, ParamErr
	}

	req, err := http.NewRequest(http.MethodPost, c.url+uri, bytes.NewReader(body))
	if err != nil {
		log.Error("c.Do() new request error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.c.Do(req)
	if err != nil {
		log.Error("c.Do() post return error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}
	if resp.Body != nil {
		defer resp.Body.Close()
	}
	if resp.StatusCode != http.StatusOK {
		log.Error("c.Do() post return status error", zap.Int64("reqID", reqID), zap.Int("code", resp.StatusCode))
		return nil, errors.New(http.StatusText(resp.StatusCode))
	}

	body, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Error("c.Do() post request read body error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}

	reply := new(Reply)
	err = json.Unmarshal(body, reply)
	if err != nil {
		log.Error("c.Do() unmarshal respond body error", zap.Int64("reqID", reqID), zap.Error(err))
		return nil, err
	}

	return reply, nil
}
