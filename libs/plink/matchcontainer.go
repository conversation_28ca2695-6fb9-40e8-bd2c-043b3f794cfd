/*
@Time : 3/15/20 9:47 上午
<AUTHOR> mocha
@File : matchcontainer
*/
package plink

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"bc/libs/proto"
	"github.com/emirpasic/gods/maps/treemap"
	"sync"
)

type BuySellContainer struct {
	l            sync.RWMutex
	BuyPriceMap  *treemap.Map
	SellPriceMap *treemap.Map
	symbol       string
	DealInfo     *proto.ComplexPrice
}

func NewPriceList(symbol string) *BuySellContainer {
	return &BuySellContainer{
		symbol:       symbol,
		BuyPriceMap:  treemap.NewWith(FloatDescCompartor), //从大到小
		SellPriceMap: treemap.NewWith(FloatAscCompartor),  //从小到大
		DealInfo:     &proto.ComplexPrice{},
	}
}

func (p *BuySellContainer) RemoveOrder(id int64, side string) *proto.Order {
	var pMap *treemap.Map
	var o *proto.Order
	if side == define.OrderBuy {
		pMap = p.BuyPriceMap
	} else {
		pMap = p.SellPriceMap
	}
	pMap.Each(func(_ interface{}, value interface{}) {
		list, ok := value.(*OrderList)
		if ok {
			temp := list.GetById(id)
			if temp != nil {
				o = temp
				list.Remove(temp)
				//list.RemoveId(id)
			}
		}
	})
	//
	//p.More.Each(func(_ interface{}, value interface{}) {
	//	list, ok := value.(*OrderList)
	//	if ok {
	//		list.GetById(id)
	//		list.RemoveId(id)
	//	}
	//})
	//p.Less.Each(func(_ interface{}, value interface{}) {
	//	list, ok := value.(*OrderList)
	//	if ok {
	//		list.RemoveId(id)
	//	}
	//})
	return o
}

func (p *BuySellContainer) AddOrder(o *proto.Order) {
	p.l.Lock()
	defer p.l.Unlock()
	o.IsMaker = true
	switch o.Side {
	case define.OrderBuy:
		l, fund := p.BuyPriceMap.Get(o.Price)
		if !fund {
			list := NewOrderList(p.BuyPriceMap, o)
			p.BuyPriceMap.Put(o.Price, list)
			return
		}
		list, ok := l.(*OrderList)
		if ok {
			list.Add(o)
		}
	case define.OrderSell:
		l, fund := p.SellPriceMap.Get(o.Price)
		if !fund {
			list := NewOrderList(p.SellPriceMap, o)
			p.SellPriceMap.Put(o.Price, list)
			return
		}
		list, ok := l.(*OrderList)
		if ok {
			list.Add(o)
		}
	}

}

func (p *BuySellContainer) GetSellHeader() (price float64, list *OrderList) {
	if p.SellPriceMap.Size() == 0 {
		return
	}
	k, v := p.SellPriceMap.Min()
	price = k.(float64)
	list = v.(*OrderList)
	return
}

func (p *BuySellContainer) GetBuyerHeader() (price float64, list *OrderList) {
	if p.BuyPriceMap.Size() == 0 {
		return
	}
	k, v := p.BuyPriceMap.Max()
	price = k.(float64)
	list = v.(*OrderList)
	return
}

func (p *BuySellContainer) GetBuyPrice(index int) float64 {
	p.l.RLock()
	defer p.l.RUnlock()
	s := p.BuyPriceMap.Size()
	if s == 0 {
		return 0
	}
	i := index
	if index >= s {
		i = s
	}
	price, ok := p.BuyPriceMap.Keys()[i-1].(float64)
	if ok {
		return price
	}
	return 0
}

func (p *BuySellContainer) GetSellPrice(index int) float64 {
	p.l.RLock()
	defer p.l.RUnlock()
	s := p.SellPriceMap.Size()
	if s == 0 {
		return 0
	}
	i := index
	if index >= s {
		i = s
	}
	if index > s {
		i = s
	}
	price, ok := p.BuyPriceMap.Keys()[i-1].(float64)
	if ok {
		return price
	}
	return 0
}

func (p *BuySellContainer) GetDepth() (buyer, seller map[float64][]proto.Order) {
	p.l.RLock()
	defer p.l.RUnlock()
	buyer = make(map[float64][]proto.Order)
	p.BuyPriceMap.Each(func(key interface{}, value interface{}) {
		price := key.(float64)
		v := value.(*OrderList)
		buyList := v.PValues()

		buyer[price] = buyList
	})

	seller = make(map[float64][]proto.Order)
	p.SellPriceMap.Each(func(key interface{}, value interface{}) {
		price := key.(float64)
		v := value.(*OrderList)
		sell := v.PValues()
		seller[price] = sell
	})

	return
}

func (p *BuySellContainer) GetDepths(digit int32) (depth *proto.DepthContainer) {
	p.l.RLock()
	defer p.l.RUnlock()
	buy, sell := make([]proto.Depth, 0), make([]proto.Depth, 0)
	var currentVolume int64
	var buyLists, sellLists []proto.SimpleOrder
	p.BuyPriceMap.Each(func(key interface{}, value interface{}) {
		price := key.(float64)
		v := value.(*OrderList)
		buyList := v.PValues()
		depth := proto.Depth{Price: nums.NewFromFloat(price).StringFixed(digit)}
		var a int64
		for _, v := range buyList {
			nAmount := v.Volume - v.TradeVolume
			a += int64(nAmount)
			buyLists = append(buyLists, *v.GetSimpleOrder())
		}
		depth.Amount = a
		currentVolume += depth.Amount
		depth.TotalAmount = currentVolume
		buy = append(buy, depth)
	})

	currentVolume = 0
	p.SellPriceMap.Each(func(key interface{}, value interface{}) {
		price := key.(float64)
		v := value.(*OrderList)
		depth := proto.Depth{Price: nums.NewFromFloat(price).StringFixed(digit)}
		var a int64
		for _, v := range v.PValues() {
			nAmount := v.Volume - v.TradeVolume
			//log.Infof("卖盘深度价格：%v，id:%v,下单：%v,已成:%v,剩余:%v", v.Price, v.OrderId, v.Volume, v.TradeVolume, nAmount)
			a += int64(nAmount)
			sellLists = append(sellLists, *v.GetSimpleOrder())
		}
		depth.Amount = a
		currentVolume += a
		depth.TotalAmount = currentVolume
		sell = append(sell, depth)
	})

	log.Infof("价格链表实时数据，合约：%v,买盘:%+v", p.symbol, buyLists)
	log.Infof("价格链表实时数据，合约：%v,卖盘:%+v", p.symbol, sellLists)
	//depth = &proto.DepthContainer{TS: time.Now().Unix(), Buy: buy, Sell: sell, ContractCode: p.symbol, Digit: digit, PriceLimit: digit}
	return
}

func (p *BuySellContainer) Name() string {
	return p.symbol
}
