/*
<AUTHOR> mocha
@File : orderlist_test.go
*/
package plink

import (
	"testing"

	"bc/libs/proto"
	"github.com/emirpasic/gods/lists/doublylinkedlist"
)

func TestOrderList_Get(t *testing.T) {
	l := NewF(&proto.Order{OrderId: 1}, &proto.Order{OrderId: 2})
	l.Each(func(index int, value interface{}) {
		order, _ := value.(*proto.Order)
		t.Logf("%+v", order)
	})
}

func NewF(v ...*proto.Order) *doublylinkedlist.List {
	l := doublylinkedlist.New()
	for _, order := range v {
		l.Add(order)

	}
	return l
}
