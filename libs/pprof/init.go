package pprof

import (
	"context"
	"net"
	"net/http"
	"time"

	"bc/libs/conf"
	"bc/libs/log"
	"github.com/gorilla/mux"
	"go.uber.org/zap"
)

var ps *http.Server

func Init(addr string) {
	if len(addr) == 0 {
		return
	}
	m := mux.NewRouter()
	Wrap(m)
	go httpListen(m, addr)
	log.Infof("init pprof,address；%v", addr)
}

func Stop() {
	if ps != nil {
		_ = ps.Shutdown(context.Background())
	}
}

func httpListen(mux *mux.Router, addr string) {
	ps = &http.Server{
		Handler:      mux,
		ReadTimeout:  time.Duration(int64(time.Second) * conf.ReadTimeout()),
		WriteTimeout: time.Duration(int64(time.Second) * conf.WriteTimeout())}
	ps.SetKeepAlivesEnabled(true)
	l, err := net.Listen("tcp", addr)
	if err != nil {
		log.Fatalf("httpListen error, addr[%s], err:%v", addr, err)
	}
	defer l.Close()
	if err := ps.Serve(l); err != nil {
		// 发送报错邮件
		log.Fatal("server error", zap.Error(err))
	}
}
