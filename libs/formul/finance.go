/*
@Time : 3/12/20 6:28 下午
<AUTHOR> mocha
@File : finance
*/
package formul

import (
	"bc/libs/define"
	"bc/libs/log"
	"bc/libs/nums"
	"github.com/shopspring/decimal"
)

//限定区间函数
func Clamp(x, min, max float64) float64 {
	if x > max {
		return max
	}
	if x < min {
		return min
	}
	return x

}

//获取利率
func GetInterestRate(basicRate, pricingRate float64, gap int) decimal.Decimal {
	g := nums.NewFromFloat(pricingRate).Sub(nums.NewFromFloat(basicRate))
	return g.Div(nums.NewFromInt(gap))
}

//获取起始保证金
//初始保证金 = (开仓张数*买入价*合约面值) / 杠杆倍数
func GetInitMargin(holdAmount, lever, perValue decimal.Decimal, avgPrice decimal.Decimal) decimal.Decimal {
	return holdAmount.Mul(perValue).Mul(avgPrice).Div(lever)
}

////获取保证金率
////initMargin -起始保证金 mainMarginRate 维持保证金率,contractValue-合约价值,floatProfit-未实现盈亏
//func GetMarginRate(initMargin decimal.Decimal, floatProfit decimal.Decimal, contractValue decimal.Decimal, mainMarinRate decimal.Decimal) decimal.Decimal {
//	t := initMargin.Add(floatProfit).Sub(contractValue.Mul(mainMarinRate))
//	return t.Div(initMargin)
//}

//获取全仓保证金率
func GetMarginRate(side string, buyPrice, sellPrice, avgPrice, forcePrice decimal.Decimal) decimal.Decimal {
	var m decimal.Decimal
	if side == define.OrderBuy {
		m = sellPrice.Mul(sellPrice).Sub(forcePrice.Mul(forcePrice))
	} else {
		m = buyPrice.Mul(buyPrice).Sub(forcePrice.Mul(forcePrice))

	}
	s := m.Div(avgPrice.Mul(avgPrice).Sub(forcePrice.Mul(forcePrice)))
	return s.Truncate(6)
}

//获取逐仓保证金率
//多单保证金率=（仓位保证金）／仓位价值=（多单初始保证金+多单占用手续费+多单调整保证金+多单未实现盈亏）／（面值*多单张数*卖出价）
//空单保证金率=（仓位保证金）／仓位价值=（空单初始保证金+空单占用手续费+空单调整保证金+空单未实现盈亏）／（面值*空单张数*买入价）
func GetMarginRateByWareHouse(side string, margin, floatProfit, perValue, complexPrice decimal.Decimal, amount int) decimal.Decimal {
	var m decimal.Decimal
	fm := margin.Add(floatProfit)
	if side == define.OrderBuy {
		m = fm.Div(perValue.Mul(nums.NewFromInt(amount)).Mul(complexPrice))
	} else {
		m = fm.Div(perValue.Mul(nums.NewFromInt(amount)).Mul(complexPrice))
	}
	return m.Truncate(6)
}

//获取逐仓破产价
//多单破产价格 = {多单开仓均价 - [多单持仓保证金/(多单张数*面值)]}  / (1 - 手续费率)
//空单破产价格 = {空单开仓均价+ [空单持仓保证金 /(空单面值*张数)]}  / (1+ 手续费率)
//func GetBrokePriceByWareHouse(side string, avgPrice, margin, perValue, feeRate decimal.Decimal, amount int) decimal.Decimal {
//	var fm, fz decimal.Decimal
//	if side == define.OrderBuy {
//		fm = avgPrice.Sub(margin.Div(perValue.Mul(nums.NewFromInt(amount))))
//		fz = nums.NewFromInt(1).Sub(feeRate)
//	} else {
//		fm = avgPrice.Add(margin.Div(perValue.Mul(nums.NewFromInt(amount))))
//		fz = nums.NewFromInt(1).Add(feeRate)
//	}
//	if fz.Equal(decimal.Zero) {
//		log.Errorf("GetBrokePriceByWareHouse 分子为0")
//		return decimal.Zero
//	}
//	return fm.Div(fz).Truncate(6)
//}

////获取逐仓破产价
//多单破产价格 = [(多单开价*张数*面值)  -   (多单持仓保证金)  ]   /  [  面值*张数*  (1-手续费率）]
//空单破产价格 = [(空单开价*张数*面值)  +  (空单持仓保证金)  ]   /  [  面值*张数*（1+手续费率) ]
func GetBrokePriceByWareHouse(side string, avgPrice, margin, perValue, feeRate decimal.Decimal, amount int) decimal.Decimal {
	var fm, fz decimal.Decimal
	if side == define.OrderBuy {
		fm = avgPrice.Sub(margin.Div(perValue.Mul(nums.NewFromInt(amount))))
		fz = nums.NewFromInt(1).Sub(feeRate)
	} else {
		fm = avgPrice.Add(margin.Div(perValue.Mul(nums.NewFromInt(amount))))
		fz = nums.NewFromInt(1).Add(feeRate)
	}
	if fz.Equal(decimal.Zero) {
		log.Errorf("GetBrokePriceByWareHouse 分子为0")
		return decimal.Zero
	}
	return fm.Div(fz).Truncate(6)
}

//可转出保证金
func GetCanTransferMargin(PositionMargin, floatProfit, takerFee, initMargin decimal.Decimal) decimal.Decimal {
	return PositionMargin.Sub(floatProfit).Sub(takerFee).Sub(initMargin)
}

//计算强平价格
//获取强平价
//多单强平价 ==  最新卖出价 <=   [（开仓价*面值*张数）- 持仓保证金 ]  /   [面值*张数  *（1-（维持保证金率+手续费率) ）]
//空单强平价 ==  最新买入价  >=  [  (开仓价*面值*张数）+ 持仓保证金 ]  /  [  面值*张数 *（1+（维持保证金率+手续费率) ) ]

func GetForcePriceByWareHouse(side string, holdAmount int, margin, avgPrice, perValue, maintainRate, feeTaker decimal.Decimal, digit int32) decimal.Decimal {
	var fm, fz decimal.Decimal
	if side == define.OrderBuy {
		fm = avgPrice.Mul(perValue).Mul(nums.NewFromInt(holdAmount)).Sub(margin)
		fz = perValue.Mul(nums.NewFromInt(holdAmount)).Mul(nums.NewFromInt(1).Sub(maintainRate.Add(feeTaker)))
	} else {
		fm = avgPrice.Mul(perValue).Mul(nums.NewFromInt(holdAmount)).Add(margin)
		fz = perValue.Mul(nums.NewFromInt(holdAmount)).Mul(nums.NewFromInt(1).Add(maintainRate.Add(feeTaker)))

	}
	v := fm.Div(fz).Truncate(digit)
	if v.LessThan(decimal.Zero) {
		v = decimal.Zero
	}
	return v
}

//单一合约当前预估减仓价格
//=[(多头开仓价值－空头开仓价值)－(总资产＋Σ其他合约未实现盈亏－Σ其他合约最低维持保证金－该合约实际初始保证金*风险调整系数*最高风险率)]/(面值*净持仓)
//多头强平价=(张数*面值*开仓均价-（持仓保证金-（张数*面值*开仓均价/合约最大杠杠)*0.5)/面值*张数
//空头头强平价=(-张数*面值*开仓均价-（持仓保证金-（张数*面值*开仓均价/合约最大杠杠)*0.5)/-面值*张数

// GetForcePriceForWareHouse 合约实际处理保证金（实际初始保证金=（空头开仓价值+多头开仓价值）/合约支持的最大杠杆倍数）
func GetForcePriceForWareHouse(side string, holdAmount, contractMaxLever int, margin, avgPrice, perValue, riskAdjustFactor, maxRiskRate decimal.Decimal, digit int32) decimal.Decimal {
	var v decimal.Decimal
	defer func() {
		log.Infof("计算逐仓强平价，方向：%v,持仓：%v,合约最大杠杠;%v，保证金;%v，均价;%v，面值：%v,风险率系数：%v,最大风险率：%v,计算结果;%v", side, holdAmount, contractMaxLever, margin.String(), avgPrice.String(), perValue.String(), riskAdjustFactor.String(), maxRiskRate.String(), v.String())
	}()
	if holdAmount <= 0 || margin.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}
	perValue = perValue.Abs()
	var fm, fz decimal.Decimal
	hold, lever := nums.NewFromInt(holdAmount), nums.NewFromInt(contractMaxLever)
	if riskAdjustFactor.Equal(decimal.Zero) {
		riskAdjustFactor = nums.NewFromInt(1)
	}
	fm = perValue.Mul(hold)
	worth := hold.Mul(perValue).Mul(avgPrice)
	realInitMargin := worth.Div(lever)
	if side == define.OrderBuy {
		fz = worth.Sub(margin.Sub(realInitMargin.Mul(riskAdjustFactor).Mul(maxRiskRate)))
	} else {
		fz = worth.Neg().Sub(margin.Sub(realInitMargin.Mul(riskAdjustFactor).Mul(maxRiskRate)))
		fm = fm.Neg()
	}
	v = fz.Div(fm).Truncate(digit)
	if v.LessThan(decimal.Zero) {
		v = decimal.Zero
	}
	return v
}

////计算强平价格
////获取强平价
////多单强平价  <= （开仓价-持仓保证金  ）/  {1 - [多单张数*面值*（维持保证金率+此合约手续费率）]  }
////空单强平价  >=   (开仓价+持仓保证金 )  /  {1 + [空单张数*面值*（维持保证金率+此合约手续费率) ] }
//
//func GetForcePriceByWareHouse(side string, holdAmount int, margin, avgPrice, perValue, maintainRate, feeTaker decimal.Decimal, digit int32) decimal.Decimal {
//	var fm, fz decimal.Decimal
//	hold := nums.NewFromInt(holdAmount)
//	if side == define.OrderBuy {
//		fm = avgPrice.Sub(margin)
//		fz = nums.NewFromInt(1).Sub(hold.Mul(perValue).Mul(feeTaker.Add(maintainRate)))
//	} else {
//		fm = avgPrice.Add(margin)
//		fz = nums.NewFromInt(1).Add(hold.Mul(perValue).Mul(feeTaker.Add(maintainRate)))
//
//	}
//	v := fm.Div(fz).Truncate(digit)
//	if v.LessThan(decimal.Zero) {
//		v = decimal.Zero
//	}
//	return v
//}

//计算开仓均价
//newHold 新持有
//oldPrice 旧价格
func GetOpenAvgPrice(oldHold, newHold int, oldPrice, newPrice decimal.Decimal) decimal.Decimal {
	oldHoldD := nums.NewFromInt(oldHold)
	newHoldD := nums.NewFromInt(newHold)
	m := oldHoldD.Mul(oldPrice).Add(newHoldD.Mul(newPrice))
	z := oldHoldD.Add(newHoldD)

	if z == decimal.Zero {
		log.Errorf("计算GetOpenAvgPrice fen")
		return decimal.Zero
	}
	return m.Div(z)
}

//计算平仓盈亏
//2.1.1  平仓多单已实现盈亏 = 平仓张数*合约面值*(卖出价 - 开仓均价)
//2.1.2  平仓空单已实现盈亏 = 平仓张数*合约面值*(开仓均价 - 买入价)
func GetCloseProfit(side string, amount int, perValue, avgPrice, tradePrice decimal.Decimal) decimal.Decimal {
	value := nums.NewFromInt(amount).Mul(perValue)
	if side == define.OrderBuy {
		return value.Mul(tradePrice.Sub(avgPrice))
	}
	return value.Mul(avgPrice.Sub(tradePrice))
}

//获取未实现盈亏
//userHold 用户持仓数
//func GetFloatProfit(side string, holdAmount int, perValue, avgPrice, buyPrice, sellPrice decimal.Decimal) decimal.Decimal {
//	value := nums.NewFromInt(holdAmount).Mul(perValue)
//	if side == define.OrderBuy {
//		return value.Mul(sellPrice.Sub(avgPrice))
//	}
//	return value.Mul(avgPrice.Sub(buyPrice))
//}

func GetFloatProfit(side string, holdAmount int, perValue, avgPrice, indexPrice decimal.Decimal) decimal.Decimal {
	if indexPrice.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}
	value := nums.NewFromInt(holdAmount).Mul(perValue)
	if side == define.OrderBuy {
		return value.Mul(indexPrice.Sub(avgPrice))
	}
	return value.Mul(avgPrice.Sub(indexPrice))
}

//未实现盈亏=【数量*（开仓均价-当前价格）】/保证金标记价格
//多头
//未实现盈亏=【数量*（当前价格-开仓均价）】/保证金标记价格
func GetFloatProfitDecimal(side string, holdAmount, openPrice, closePrice, marginMarkPrice decimal.Decimal) decimal.Decimal {
	if marginMarkPrice.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero
	}
	if side == define.OrderBuy {
		return holdAmount.Mul(closePrice.Sub(openPrice)).Div(marginMarkPrice)
	}
	return holdAmount.Mul(openPrice.Sub(closePrice)).Div(marginMarkPrice)
}

//获取盈亏率
func GetForcastProfitRate(floatProfit, initMargin decimal.Decimal) decimal.Decimal {
	if initMargin.Equal(decimal.Zero) {
		log.Infof("初始保证金为0")
		return decimal.Zero
	}
	return floatProfit.Div(initMargin).Truncate(4)
}

//非0正数最大值
func LimitMax(nums ...decimal.Decimal) decimal.Decimal {
	var list []decimal.Decimal
	for _, num := range nums {
		if num.GreaterThan(decimal.Zero) {
			list = append(list, num)
		}
	}
	if len(list) == 0 {
		return decimal.Zero
	}
	return decimal.Max(list[0], list...)
}

//非0正数最小值
func LimitMin(nums ...decimal.Decimal) decimal.Decimal {
	var list []decimal.Decimal
	for _, num := range nums {
		if num.GreaterThan(decimal.Zero) {
			list = append(list, num)
		}
	}
	if len(list) == 0 {
		return decimal.Zero
	}
	return decimal.Min(list[0], list...)
}

// 平仓收益率计算(显示用)
// 收益率： （收益*杠杆）/ （开仓均价*平仓数量*面值*）*100
func CloseProfitRatio(profit, lever, openAvgPrice, closeVolume, parValue decimal.Decimal) decimal.Decimal {
	a := profit.Mul(lever)
	b := openAvgPrice.Mul(closeVolume).Mul(parValue)
	if b.IsZero() {
		return decimal.Zero
	}
	return a.Div(b).Mul(define.DecimalHundred).Truncate(2)
}

//可开张数=金额/（价格*面值/杠杆+2*价格*面值）
func CalTradeVolume(money, perValue, lever, price, feeRate decimal.Decimal) (volume decimal.Decimal) {
	fz := price.Mul(perValue).Div(lever).Add(nums.NewFromInt(2).Mul(price).Mul(perValue).Mul(feeRate))
	if fz.Equal(decimal.Zero) {
		return
	}
	volume = money.Div(fz).Truncate(0)
	return
}

// MaxSubAdjustMargin
// 最多可减少保证金= (当前仓位保证金+未实现亏损)-((当前持仓数量*面值*开仓均价*(1/用户设置杠杆倍数+手续费率))*(1+上浮系数))
func MaxSubAdjustMargin(posAmount, lever int, margin, floatProfit, parValue, avgPrice, feeRate, factor decimal.Decimal) decimal.Decimal {
	if lever == 0 {
		return decimal.Zero
	}
	if floatProfit.IsPositive() {
		// 如果浮动盈亏为正,赋值为0
		floatProfit = decimal.Zero
	}

	// (当前仓位保证金+未实现亏损)
	return margin.Add(floatProfit).Sub(
		// 当前持仓数量*面值*开仓均价
		nums.NewFromInt(posAmount).Mul(parValue).Mul(avgPrice).Mul(
			// (1/用户设置杠杆倍数+手续费率)
			define.DecimalOne.Div(nums.NewFromInt(lever)).Add(feeRate)).Mul(
			// (1+上浮系数))
			define.DecimalOne.Add(factor)))
}
