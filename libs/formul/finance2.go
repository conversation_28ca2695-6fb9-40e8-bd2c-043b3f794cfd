///*
//@Time : 3/12/20 6:28 下午
//<AUTHOR> mocha
//@File : finance
//*/
package formul

//
//import (
//	"bc/libs/define"
//	"bc/libs/log"
//	"bc/libs/nums"
//	"github.com/shopspring/decimal"
//)
//
////限定区间函数
//func Clamp(x, min, max float64) float64 {
//	if x > max {
//		return max
//	}
//	if x < min {
//		return min
//	}
//	return x
//
//}
//
////获取利率
//func GetInterestRate(basicRate, pricingRate float64, gap int) decimal.Decimal {
//	g := nums.NewFromFloat(pricingRate).Sub(nums.NewFromFloat(basicRate))
//	return g.Div(nums.NewFromInt(gap))
//}
//
////interestRate 利率
////premiumIndex 溢价指数
//func GetFinalFundingRate(interestRate, premiumIndex decimal.Decimal) decimal.Decimal {
//	c := Clamp(nums.Float(interestRate.Sub(premiumIndex)), 0.0005, -0.0005)
//	return premiumIndex.Add(nums.NewFromFloat(c))
//}
//
////获取起始保证金
//func GetInitMargin(holdAmount, lever int, perValue decimal.Decimal, avgPrice decimal.Decimal) decimal.Decimal {
//	return nums.NewFromInt(holdAmount).Mul(perValue).Div(nums.NewFromInt(lever).Mul(avgPrice))
//}
//
////获取保证金率
////initMargin -起始保证金 mainMarginRate 维持保证金率,contractValue-合约价值,floatProfit-未实现盈亏
//func GetMarginRate(initMargin decimal.Decimal, floatProfit decimal.Decimal, contractValue decimal.Decimal, mainMarinRate decimal.Decimal) decimal.Decimal {
//	t := initMargin.Add(floatProfit).Sub(contractValue.Mul(mainMarinRate))
//	return t.Div(initMargin)
//}
//
////可转出保证金
//func GetCanTransferMargin(PositionMargin, floatProfit, takerFee, initMargin decimal.Decimal) decimal.Decimal {
//	return PositionMargin.Sub(floatProfit).Sub(takerFee).Sub(initMargin)
//}
//
////获取买卖系数
//func GetBuySellFactor(side string) int {
//	if side == define.OrderBuy {
//		return 1
//	}
//	return -1
//}
//
////计算预估强平价格 逐仓
//// lever 杠杆
////fatcor 指数
//func GetForecastForcePriceByWareHouse(userHold int, avgPrice, perValue, adjust decimal.Decimal, lever, factor int, maintainRate decimal.Decimal) decimal.Decimal {
//	var g decimal.Decimal
//	if factor > 0 {
//		g = nums.NewFromInt(1).Add(nums.NewFromString("0.0005"))
//	} else {
//		g = nums.NewFromInt(1).Sub(nums.NewFromString("0.0005"))
//	}
//	return GetNormalForcePriceByWareHouse(userHold, avgPrice, perValue, adjust, lever, factor, maintainRate).Mul(g)
//}
//
////获取名义强平价格
////1/ (1/开+1/(开*系*杠杆)+调/(系*张*面)-维/（开*系） )*(1+0.0005） # 做空单则 (1-0.0005)）
//func GetNormalForcePriceByWareHouse(userHold int, avgPrice, perValue, adjust decimal.Decimal, lever, factor int, maintainRate decimal.Decimal) decimal.Decimal {
//	l := nums.NewFromInt(lever)
//	fa := nums.NewFromInt(factor)
//	oneD := nums.NewFromInt(1)
//	m := oneD.Div(avgPrice).Add(oneD.Div(avgPrice.Mul(fa).Mul(l))).Add(adjust.Div(fa.Mul(nums.NewFromInt(userHold).Mul(perValue)))).Sub(maintainRate.Div(avgPrice.Mul(fa)))
//	return oneD.Div(m)
//}
//
//func GetNormalForcePriceByWareHouse2(avgPrice decimal.Decimal, lever, factor int, maintainRate decimal.Decimal) decimal.Decimal {
//	l := nums.NewFromInt(lever)
//	fa := nums.NewFromInt(factor)
//
//	rate := avgPrice.Mul(l).Div(l.Mul(fa).Sub(l.Mul(maintainRate)).Add(nums.NewFromInt(1)))
//	return rate
//}
//
////预估强平价格 全仓
////avgPrice 开仓均价
////factor 系数
////amount 张数
////maintainRate 维持保证金率
////balance 余额
////perValue 面值
//func GetForecastForcePriceByFullHouse(avgPrice decimal.Decimal, factor, amount int, maintainRate, balance, perValue decimal.Decimal) decimal.Decimal {
//	var g decimal.Decimal
//	if factor > 0 {
//		g = nums.NewFromInt(1).Add(nums.NewFromString("0.0005"))
//	} else {
//		g = nums.NewFromInt(1).Sub(nums.NewFromString("0.0005"))
//	}
//	rate := GetNormalForcePriceByFullHouse(avgPrice, factor, amount, maintainRate, balance, perValue).Mul(g)
//	return rate
//}
//
////获取全仓强平名义价格
//func GetNormalForcePriceByFullHouse(avgPrice decimal.Decimal, factor, amount int, maintainRate, balance, perValue decimal.Decimal) decimal.Decimal {
//	fa := nums.NewFromInt(factor)
//	amountD := nums.NewFromInt(amount)
//	b := balance.Sub(amountD.Mul(perValue).Div(avgPrice)).Div(fa.Mul(amountD).Mul(perValue)).Add(nums.NewFromInt(1).Div(avgPrice))
//
//	rate := nums.NewFromInt(1).Div(b)
//	return rate
//}
//
////计算强平价格
////func GetForcePrice(avgPrice decimal.Decimal, lever, factor int, maintainRate decimal.Decimal) decimal.Decimal {
////	l := nums.NewFromInt(lever)
////	fa := nums.NewFromInt(lever)
////	rate := avgPrice.Mul(l).Div(l.Add(fa).Sub(maintainRate.Mul(l)))
////	return rate
////}
//
////计算开仓均价
////old 旧持有
////newHold 新持有
////oldPrice 旧价格
////oldFactor 旧系数
//func GetOpenAvgPrice(oldHold, newHold int, oldPrice, newPrice decimal.Decimal, oldFactor, newFactor int) decimal.Decimal {
//	log.Debugf("oldh:%v,newh:%v,op:%v,np:%v,of:%v,nf:%v", oldHold, newHold, oldPrice.String(), newPrice.String(), oldFactor, newFactor)
//	oldHoldD := nums.NewFromInt(oldHold)
//	newHoldD := nums.NewFromInt(newHold)
//	oldFa := nums.NewFromInt(oldFactor)
//	newFa := nums.NewFromInt(newFactor)
//	m := oldHoldD.Mul(oldFa).Mul(oldPrice).Add(newHoldD.Mul(newFa).Mul(newPrice))
//	z := oldHoldD.Mul(oldFa).Add(newHoldD.Mul(newFa))
//
//	if z == decimal.Zero {
//		log.Errorf("计算GetOpenAvgPrice fen")
//		return decimal.Zero
//	}
//	return m.Div(z)
//}
//
////计算持仓盈亏
//func GetHoldProfit(factor int, holdAmount int, perValue decimal.Decimal, avgPrice, marketPrice decimal.Decimal) decimal.Decimal {
//	if avgPrice == decimal.Zero || marketPrice == decimal.Zero {
//		log.Errorf("GetHoldProfit 无效的开仓均价或标记价格")
//		return decimal.Zero
//	}
//	return nums.NewFromInt(factor).Mul(nums.NewFromInt(holdAmount)).Mul(perValue).Mul((nums.NewFromInt(1).Div(avgPrice)).Sub(nums.NewFromInt(1).Div(marketPrice)))
//}
//
////获取未实现盈亏
////userHold 用户持仓数
//func GetFloatProfit(userhold, factor int, perValue decimal.Decimal, avgPrice decimal.Decimal, marketPrice decimal.Decimal) decimal.Decimal {
//	if avgPrice.IsZero() || marketPrice.IsZero() {
//		log.Errorf("GetFloatProfit 无效的开仓均价或标记价格")
//		return decimal.Zero
//	}
//	return nums.NewFromInt(userhold).Mul(perValue).Mul(nums.NewFromInt(factor)).Mul(nums.NewFromInt(1).Div(avgPrice).Sub(nums.NewFromInt(1).Div(marketPrice)))
//}
//
////计算已实现盈亏
////已实现盈亏=（合约张数  合约面值*系数）  （1/开仓均价 - 1/平仓价格）
//func GetHaveBreakProfit(volume, factor int, perValue, avgPrice, closeOutPrice decimal.Decimal) decimal.Decimal {
//	log.Infof("计算已实现盈亏,volume:%v,factor;%v,p:%v,a:%v,close:%v", volume, factor, perValue.String(), avgPrice.String(), closeOutPrice.String())
//	return nums.NewFromInt(volume).Mul(perValue).Mul(nums.NewFromInt(factor)).Mul(nums.NewFromInt(1).Div(avgPrice).Sub(nums.NewFromInt(1).Div(closeOutPrice)))
//}
//
////获取预估盈亏
////closeOutPrice 平仓价
////avgPrice 开仓价
//func GetForcastProfit(userhold, factor int, perValue decimal.Decimal, avgPrice decimal.Decimal, closeOutPrice decimal.Decimal) decimal.Decimal {
//	s := nums.NewFromInt(1).Div(avgPrice).Sub(nums.NewFromInt(1).Div(closeOutPrice))
//	return nums.NewFromInt(userhold).Mul(nums.NewFromInt(factor)).Mul(perValue).Mul(s)
//}
//
////获取预估盈亏率
//func GetForcastProfitRate(userhold, factor int, perValue decimal.Decimal, avgPrice decimal.Decimal, closeOutPrice decimal.Decimal) decimal.Decimal {
//	return nums.NewFromInt(factor).Mul(nums.NewFromInt(1).Sub(avgPrice.Div(closeOutPrice)))
//}
//
////获取预估回报率
//func GetForcastReturnRate(forcatProfitRate decimal.Decimal, lever int) decimal.Decimal {
//	return forcatProfitRate.Mul(nums.NewFromInt(lever))
//}
//
////手续费计算
//func GetTradeFee(dealVolume int, perValue, dealPrice decimal.Decimal, feeRate decimal.Decimal) decimal.Decimal {
//	return nums.NewFromInt(dealVolume).Mul(perValue).Mul(feeRate).Div(dealPrice)
//}
//
///*
//计算可下单数量
//	orderPrice 委托价格
//	parValue 面值
//	lever 杠杆倍数
//	reverseVolume 反向订单数
//	obverseVolume 同向订单数
//	available 账户可用余额
//	fee taker费率
//	maxVolume 合约最大持仓限制-同方向持仓-同方向未成交
//*/
//func GetCanPlaceVolume(orderPrice, parValue, lever, reverseVolume, obverseVolume, available, fee decimal.Decimal, maxVolume int) int {
//	volume := int(available.Mul(orderPrice).Div(parValue.Mul(nums.NewFromInt(1).Div(lever).Add(fee.Mul(nums.NewFromInt(2))))).Add(reverseVolume).Sub(obverseVolume).IntPart())
//	if volume > maxVolume {
//		return maxVolume
//	}
//	return volume
//}
//
//// 计算委托价值
//func GetOrderEntrustValue(volume int, parValue, price decimal.Decimal) float64 {
//	return nums.Float(nums.NewFromInt(volume).Mul(parValue).Div(price).Round(define.FloatPrecision))
//}
//
//// 计算委托手续费
//func GetOrderEntrustFee(entrustValue, takerFee decimal.Decimal) float64 {
//	return nums.Float(entrustValue.Mul(takerFee).Mul(nums.NewFromInt(2)).Round(define.FloatPrecision))
//}
//
//// 计算委托成本
//func GetOrderEntrustCost(initMargin, entrustFee decimal.Decimal) float64 {
//	return nums.Float(initMargin.Add(entrustFee).Round(define.FloatPrecision))
//}
