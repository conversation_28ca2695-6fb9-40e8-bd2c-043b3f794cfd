/*
@Time : 2/7/20 3:31 下午
<AUTHOR> mocha
@File : main
*/
package hitbtc

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"sync"
	"time"

	"bc/libs/log"
	"github.com/gorilla/websocket"
)

const (
	_wsMarket = "wss://api.hitbtc.com/api/2/ws"
)

//entries

type MarketWsClient struct {
	dialer                          *websocket.Dialer
	wsPoint                         string
	topics                          []string
	codes                           []string
	lastError                       error
	lock, errLock, instance, rvLock sync.RWMutex
	status                          bool
	closeHandler                    func(int, string)
	msgHandler                      func([]byte)
	lastRv                          time.Time
	client                          *Client
	ClientStatus                    bool
	lastStart                       time.Time
}

type Config struct {
	WsPoint     string
	dialer      *websocket.Dialer
	timeSeconds int64
}

type Client struct {
	isOk                      bool
	send                      chan []byte
	done                      chan struct{}
	isClose                   bool
	lock, statLock, startLock sync.RWMutex
	mc                        *MarketWsClient
	con                       *websocket.Conn
	ticker                    *time.Ticker
}

func NewClient(mc *MarketWsClient) *Client {
	return &Client{
		send:   make(chan []byte, 128),
		done:   make(chan struct{}),
		mc:     mc,
		con:    nil,
		ticker: time.NewTicker(5 * time.Second),
	}
}

func (c *Client) start() {
	log.Infof("client begin start 0")
Retry:
	conn, rsp, err := websocket.DefaultDialer.Dial(c.mc.wsPoint, nil)
	if err != nil {
		log.Errorf("dial to huobi fail,err:%v,http rsp:%+v", err, rsp)
		time.Sleep(5 * time.Second)
		goto Retry
	}
	c.isOk = true
	c.mc.rvLock.Lock()
	c.mc.lastRv = time.Now()
	c.mc.rvLock.Unlock()
	c.mc.lastStart = time.Now()
	log.Infof("huobi dial over r1")
	c.con = conn
	c.Receive()
	//c.PingLoop()
	//订阅
	c.sub(c.mc.topics, c.mc.codes)
	log.Infof("huobi sub over r2")
}

func (c *Client) close() {
	c.done <- struct{}{}
	c.statLock.Lock()
	c.isClose = true
	c.statLock.Unlock()
	close(c.send)
	c.con.Close()
	c.ticker.Stop()
}

func (c *Client) sub(topic []string, codes []string) {
	if c == nil {
		return
	}
	if c.isClose {
		return
	}
	for _, code := range codes {
		for _, v := range topic {
			d := Sub{
				Sub: v,
				SubParams: SubParams{
					Symbol: code,
					Limit:  1,
				},
			}
			b, err := json.Marshal(d)
			if err != nil {
				fmt.Errorf("marshal fail,%v", err)
				return
			}
			log.Infof("hitbtc sub:%v", string(b))
			c.Write(b)
		}
	}

}

func (c *Client) unSub(topic []string) {
	if c.isClose {
		return
	}
	//var subData [][]byte
	//for _, topic := range topic {
	//	d := UnSub{UnSub: topic}
	//	b, err := json.Marshal(d)
	//	if err != nil {
	//		fmt.Errorf("marshal fail,%v", err)
	//		return
	//	}
	//	subData = append(subData, b)
	//}
	//
	//for _, v := range subData {
	//	c.Write(v)
	//}

}

func (c *Client) Write(data []byte) {
	if c.isClose {
		return
	}
	c.send <- data
}

func (c *Client) Receive() {
	go func() {
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		msgChan := ReadMessage(ctx, c.con)
		for {
			select {
			case <-c.done:
				log.Infof("ws close")
				return
			case data := <-c.send:
				//c.lock.Lock()
				c.con.WriteMessage(websocket.TextMessage, data)
				//c.lock.Unlock()
			case msg := <-msgChan:
				c.mc.rvLock.Lock()
				c.mc.lastRv = time.Now()
				c.mc.rvLock.Unlock()
				//tick.Reset(time.Second)
				if msg.Err != nil {
					fmt.Printf("接收消息失败 msgType；%v，msg:%s, err:%v", msg.Type, msg.Msg, msg.Err)
					return
				}
				fmt.Printf("收到消息 msgType；%v，msg:%s\n", msg.Type, string(msg.Msg))
				c.mc.msgHandler(msg.Msg)
			}

		}

	}()
}

func NewMarketWsClient(config *Config, f func([]byte)) *MarketWsClient {
	c := &MarketWsClient{
		dialer:  nil,
		wsPoint: _wsMarket,
		topics:  nil,
	}
	if config != nil {
		if config.WsPoint != "" {
			c.wsPoint = config.WsPoint
		}
		if config.dialer != nil {
			c.dialer = config.dialer
		} else {
			c.dialer = websocket.DefaultDialer
		}
	}
	c.msgHandler = f
	return c
}

func (c *MarketWsClient) CloseHook(f func(int, string)) {
	c.closeHandler = f
}

func (c *MarketWsClient) Start() {
	if c.client != nil {
		c.client.close()
	}
	c.client = NewClient(c)
	c.client.start()
	c.Loop()
}

func (c *MarketWsClient) Subscript(topics []string, codes []string) {
	if c == nil {
		return
	}
	log.Infof("huobi sub topics:%v", topics)
	c.topics = topics
	c.codes = codes
	c.client.sub(c.topics, codes)

}

func (c *MarketWsClient) unSubscript() {
	c.lock.Lock()
	defer c.lock.Unlock()
}

func (c *MarketWsClient) UnSubscript(topics []string) {
	c.lock.Lock()
	defer c.lock.Unlock()
}

//func (c *MarketWsClient) pong() {
//	c.lock.Lock()
//	defer c.lock.Unlock()
//	pong, err := json.Marshal(&Pong{Pong: compress.GetUinxMillisecond()})
//	if err != nil {
//		log.Errorf("json marshal fail,%v", err)
//		return
//	}
//	c.client.Write(pong)
//}

func (c *MarketWsClient) Loop() {
	log.Infof("begin start market client loop")
	go func() {
		i := 0
		for {
			i++
			log.Debugf("check...................%v", i)
			c.instance.Lock()
			c.check()
			c.instance.Unlock()
			log.Debugf("check over...................%v", i)
			time.Sleep(time.Second)
		}
	}()
}

func (c *MarketWsClient) check() {
	c.rvLock.RLock()
	last := c.lastRv
	c.rvLock.RUnlock()
	if time.Since(last).Seconds() > 20 {
		log.Infof("long time no data")
		c.Restart()
	}
}

func (c *MarketWsClient) Restart() {
	c.client.close()
	time.Sleep(100 * time.Millisecond)
	c.client = NewClient(c)
	c.client.start()
}

//d := `{"op": "subscribe", "args": ["funding","trade"]}`
type BaseAction struct {
	Action string   `json:"op"`
	Args   []string `json:"args"`
}

//{"success":true,"subscribe":"funding","request":{"op":"subscribe","args":["funding"]}}

type SubRsp struct {
	Success   bool   `json:"success"`
	Subscribe string `json:"subscribe"`
}

type BasicRsp struct {
	Table  string          `json:"table"`
	Action string          `json:"action"`
	Data   json.RawMessage `json:"data"`
}

type Ping struct {
	Ping int64 `json:"ping"`
}

type Pong struct {
	Pong int64 `json:"pong"`
}

type OP struct {
	OP string `json:"op"`
	TS int64  `json:"ts"`
}

//str := `{
//  "method": "subscribeTrades",
//  "params": {
//    "symbol": "BTCUSD",
//    "limit": 1
//  },
//  "id": 123
//}`
type Sub struct {
	Sub       string    `json:"method"`
	ID        string    `json:"id"`
	SubParams SubParams `json:"params"`
}

type SubParams struct {
	Symbol string `json:"symbol"`
	Limit  int    `json:"limit"`
}

//TestClient: ws.client_test.go:47: 1 {"jsonrpc":"2.0","method":"snapshotTrades",
//	"params":{
//	"data":[{"id":1070109811,"price":"35311.99","quantity":"0.01573","side":"sell","timestamp":"2021-01-11T05:36:01.763Z"}],"symbol":"BTCUSD"}} <nil>

//{
//"jsonrpc": "2.0",
//"method": "updateTrades",
//"params": {
//"data": [
//{
//"id": 54469813,
//"price": "0.054670",
//"quantity": "0.183",
//"side": "buy",
//"timestamp": "2017-10-19T16:34:25.041Z"
//}
//],
//"symbol": "ETHBTC"
//}
//}
type TickerRsp struct {
	Method string `json:"method"`
	Params struct {
		Data   []Ticker `json:"data"`
		Symbol string   `json:"symbol"`
	} `json:"params"`
}

type Ticker struct {
	Id        int64           `json:"id"`
	Price     decimal.Decimal `json:"price"`
	Quantity  decimal.Decimal `json:"quantity"`
	Side      string          `json:"side"`
	Timestamp string          `json:"timestamp"`
}

type Message struct {
	Type int
	Msg  []byte
	Err  error
}

func ReadMessage(ctx context.Context, conn *websocket.Conn) <-chan Message {
	var msg Message
	ch := make(chan Message)
	go func() {
		for {
			select {
			case <-ctx.Done():
				close(ch)
				return
			default:
			}

			msg.Type, msg.Msg, msg.Err = conn.ReadMessage()
			select {
			case <-ctx.Done():
				close(ch)
				return
			case ch <- msg:
			}
		}
	}()
	return ch
}
