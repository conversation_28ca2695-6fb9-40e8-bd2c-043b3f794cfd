package config

const (
	MarketIDOkex   = 1
	MarketIDHuoBi  = 2
	MarketIDBiance = 3
)

var SpotShotName = map[string]string{
	MarketNameSpotHuobi:    "huobi",
	MarketNameSpotBinance:  "binance",
	MarketNameSpotHitbtc:   "hitBtc",
	MarketNameSpotOkex:     "okex",
	MarketNameSpotBitfinex: "bitfinex",
	MarketNameSpotBittrex:  "bittrex",
	MarketNameSpotCoinbase: "coinbase",
	MarketNameSpotKraken:   "kraken",
	MarketNameSpotPoloniex: "poloniex",
	MarketNameSpotBitstamp: "bitstamp",
	MarketNameSpotFrx:      "ftx",
}

const (
	MarketNameSpotHuobi      = "huobiSpot"
	MarketNameSpotHuobiIndex = "huobiIndex"
	MarketNameSpotBinance    = "binanceSpot"
	MarketNameSpotHitbtc     = "hitBtcSpot"
	MarketNameSpotBitfinex   = "bitfinexSpot"
	MarketNameSpotBittrex    = "bittrexSpot"
	MarketNameSpotCoinbase   = "coinBaseSpot"
	MarketNameSpotKraken     = "krakenSpot"
	MarketNameSpotPoloniex   = "poloniexSpot"
	MarketNameSpotBitstamp   = "bitstampSpot"
	MarketNameSpotZB         = "zbSpot"
	MarketNameSpotOkex       = "okexSpot"
	MarketNameSpotFrx        = "frxSpot"
)

const (
	MarketNameRcHuobi   = "huobiRc"
	MarketNameRcBinance = "binanceRc"
	MarketNameRcOkex    = "okexRc"
)

const (
	MarketNameHuoBi  = "huobi"
	MarketNameBiance = "binance"
	MarketNameOkex   = "okex"
	MarketNameBitMex = "bitMex"
)

var MarketMap = map[int]string{
	MarketIDHuoBi:  MarketNameHuoBi,
	MarketIDBiance: MarketNameBiance,
	MarketIDOkex:   MarketNameOkex,
}

//const MarketCacheDepthHeight = 14

const DEFAULT = "DEFAULT"
