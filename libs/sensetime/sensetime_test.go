package sensetime

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/http"
	"sort"
	"strings"
	"testing"

	"bc/libs/json"
)

func TestGenHeader(t *testing.T) {
	// 获取毫秒时间戳
	timestamp := "1591950925387"
	// 获取随机字符串
	nonce := "YxdAGhN5FMTWfRjZ1G4FiXtP2pyY3txh"
	// 拼接字符串
	list := make([]string, 0, 3)
	list = append(list, timestamp, nonce, senseTime.Key)
	sort.Strings(list)
	joinStr := strings.Join(list, "")
	// 生成签名
	h := hmac.New(sha256.New, []byte(senseTime.Secret))
	h.Write([]byte(joinStr))
	signature := hex.EncodeToString(h.Sum(nil))

	header := make(map[string]string, 4)
	header["key"] = senseTime.Key
	header["timestamp"] = timestamp
	header["nonce"] = nonce
	header["signature"] = signature

	t.Log(parseMap(header))
}

func TestIDNumber(t *testing.T) {
	arg := &IDNumberVerifyArg{
		Name:     "姓名",
		IDNumber: "证件号",
	}
	res, err := json.Marshal(arg)
	if err != nil {
		t.Fatal("json marshal fail", err)
	}

	// 审核信息
	res, err = SenseTimeApi(0, 0, http.MethodPost, IdNumberVerifyAPI, "application/json; charset=utf-8", bytes.NewReader(res))
	if err != nil {
		t.Fatal("request fail", err)
	}
	var result IDNumberVerifyReply
	err = json.Unmarshal(res, &result)
	if err != nil {
		t.Fatal("json unmarshal fail", err)
	}
	t.Logf("success: %+v", result)
}
