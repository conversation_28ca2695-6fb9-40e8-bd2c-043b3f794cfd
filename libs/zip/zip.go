package zip

import (
	"bc/libs/log"
	"bytes"
	"compress/zlib"
	"errors"
	"io/ioutil"
)

// zlib比gzip更适合那种速度跟压缩效果均衡的场景

var errDataLength = errors.New("data length has changed")

// 压缩字节数组
func ZipByte(data []byte) (zipData []byte, err error) {
	if len(data) <= 0 {
		return
	}
	buf := &bytes.Buffer{}
	writer, err := zlib.NewWriterLevel(buf, zlib.BestCompression)
	if err != nil {
		log.Errorf("ZipByte fail,%v", err)
		return
	}
	n, err := writer.Write(data)
	if err != nil {
		return
	}
	err = writer.Close()
	if err != nil {
		return
	}

	if n != len(data) {
		err = errDataLength
		return
	}
	zipData = buf.Bytes()
	return
}

// 解压缩字节数组
func UnzipByte(data []byte) (unzipData []byte, err error) {
	if len(data) <= 0 {
		return
	}
	reader := bytes.NewReader(data)
	closer, err := zlib.NewReader(reader)
	if err != nil {
		return
	}

	unzipData, err = ioutil.ReadAll(closer)
	if err != nil {
		return
	}
	return
}
