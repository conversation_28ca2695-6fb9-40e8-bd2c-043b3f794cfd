#!/usr/bin/env bash

set -e
#set -o xtrace

# 获取环境类型
uname_val=$(uname)
if [[ "$uname_val" == "Darwin" ]]; then
  # Mac OS X 操作系统
  echo "Mac OS 操作系统"
  export MY_GO_PATH="/Users/<USER>/hj_job/web3"
elif [[ "${uname_val:0:4}" == "Linux" ]]; then
  # GNU/Linux操作系统
  echo "Linux OS 操作系统"
  export MY_GO_PATH=${GOPATH%%:*}
elif [[ "${uname_val:0:9}" == "MINGW64_NT" ]]; then
  # Windows NT操作系统
  echo "Windows OS 操作系统"
  export MY_GO_PATH=${GOPATH%%\;*}
fi

# 环境变量
export PROJECT_NAME="TEST Panda"
export BUILD_DIR="basecoin-sopt-main"
export SERVICE_PREFIX="cf-"
export TMP_APP_DIR="/tmp/app/panda_`whoami`"
export REMOTE_APP_DIR=/data/apps/go/bin/cf
export REMOTE_LOG_DIR=/data/logs/cf
export CODE_DIR=$MY_GO_PATH/$BUILD_DIR
export BUILD_TEMP_DIR=/tmp/build/panda
export SKIP_BUILD=N
export WITH_JUMP=N
export ONLY_RESTART=N
export TEST_BUILD=N
export ALL_NODE="1"
export ALL_PROJECT="gateway"

#############################################
# 检查一个字符串是否在 列表中存在
# param1 val
# parma2 list for range
#############################################
function check_in_list() {
  for val in $2; do
    if [[ $val == $1 ]]; then
      echo 0
      return
    fi
  done
  echo 1
}

#############################################
# 检查所传参数 是否在系统列表中并且去重。没有参数表示部署所有系统。
#############################################
function check_input_project() {
  local input_project="$@"
  local result=""

  if [[ -z $SERVER_FILTER ]]; then
    SERVER_FILTER=$ALL_PROJECT
  fi

  if [[ -z $input_project ]]; then
    input_project=$SERVER_FILTER
  fi

  for project in $input_project; do
    if [[ $(check_in_list "$project" "$ALL_PROJECT") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "$SERVER_FILTER") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "$result") == 0 ]]; then
      continue
    fi

    if [[ -z $result ]]; then
      result=$project
    else
      result="$result $project"
    fi
  done

  echo "$result"
}

# 判断参数是否指定了部署哪台服务器
skip_idx=0
while getopts jrktn: opt; do
  case $opt in
  j)
    # 使用中转服务器
    skip_idx=$((OPTIND - 1))
    export WITH_JUMP=Y
    ;;
  r)
    # 仅进行服务重启
    skip_idx=$((OPTIND - 1))
    export ONLY_RESTART=Y
    export SKIP_BUILD=Y
    ;;
  k)
    # 跳过编译
    skip_idx=$((OPTIND - 1))
    export SKIP_BUILD=Y
    ;;
  n)
    skip_idx=$((OPTIND - 1))
    export NODE_FILTER=${OPTARG//,/ }
    ;;
  t)
    # 仅测试编译
    skip_idx=$((OPTIND - 1))
    export TEST_BUILD=Y
    ;;
  *)
    continue
    ;;
  esac
done
shift $skip_idx

if [[ -z $NODE_FILTER ]]; then
  NODE_FILTER=$ALL_NODE
fi
if [[ $TEST_BUILD == "N" ]]; then
  if [[ $ONLY_RESTART == "N" ]]; then
    echo "当前部署节点: $NODE_FILTER"
  else
    echo "当前重启节点: $NODE_FILTER"
  fi
fi

input_project=$(check_input_project "$@")
if [[ -z $input_project ]]; then
  echo "无有效输入"
  exit
fi
if [[ $TEST_BUILD == "N" ]]; then
  if [[ $ONLY_RESTART == "N" ]]; then
    echo "当前部署项目: $input_project"
  else
    echo "当前重启项目: $input_project"
  fi
fi

if [[ $ONLY_RESTART == "N" ]]; then
  # 编译
  if [[ $SKIP_BUILD == "N" ]]; then
    eval ./_build.sh "$@"
    if [[ $? == 1 ]]; then
      rm $BUILD_TEMP_DIR/*
      exit
    fi
    if [[ $TEST_BUILD == "Y" ]]; then
      rm $BUILD_TEMP_DIR/*
      exit
    fi
  fi
fi

for node in $NODE_FILTER; do
  case $node in
  1)
    if [[ $WITH_JUMP == "Y" ]]; then
      export SSH_SERVER=ec2-54-255-183-244.ap-southeast-1.compute.amazonaws.com
      export SSH_PORT=22
    else
      export SSH_SERVER=ec2-54-255-183-244.ap-southeast-1.compute.amazonaws.com
      export SSH_PORT=22
    fi
    export SSH_USER=ec2-user
    export CONFIG=config_dev2
    export SERVER_FILTER="gateway"
    eval ./_deploy.sh "$@"
    ;;
  2)
    if [[ $WITH_JUMP == "Y" ]]; then
      export SSH_SERVER=763dje5w.firewallcloudweb.net
      export SSH_PORT=50225
    else
      export SSH_SERVER=**************
      export SSH_PORT=52168
    fi
    export SSH_USER=centos
    export CONFIG=config_dev2
    export SERVER_FILTER="limit user wallet gateway bucketing price market kline"
    eval ./_deploy.sh "$@"
    ;;
  3)
    if [[ $WITH_JUMP == "Y" ]]; then
      export SSH_SERVER=763dje5w.firewallcloudweb.net
      export SSH_PORT=50226
    else
      export SSH_SERVER=**************
      export SSH_PORT=52168
    fi
    export SSH_USER=centos
    export CONFIG=config_3
    export SERVER_FILTER="force forcefollow forcedealing bucketing third"
    eval ./_deploy.sh "$@"
    ;;
  esac
done

############# 删除本地编译文件 #############
if [[ -z $BUILD_TEMP_DIR || $SKIP_BUILD == "Y" ]]; then
  exit
fi
rm $BUILD_TEMP_DIR/*
