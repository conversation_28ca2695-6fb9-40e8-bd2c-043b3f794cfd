#!/usr/bin/env bash

set -e

#############################################
# 检查一个字符串是否在 列表中存在
# param1 val
# parma2 list for range
#############################################
function check_in_list() {
  for val in $2; do
    if [[ $val == $1 ]]; then
      echo 0
      return
    fi
  done
  echo 1
}

#############################################
# 检查所传参数 是否在系统列表中并且去重。没有参数表示部署所有系统。
#############################################
function check_input_project() {
  local input_project="$@"
  local result=""

  if [[ -z $SERVER_FILTER ]]; then
    SERVER_FILTER=$ALL_PROJECT
  fi

  if [[ -z $input_project ]]; then
    input_project=$SERVER_FILTER
  fi

  for project in $input_project; do
    if [[ $(check_in_list "$project" "$ALL_PROJECT") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "$SERVER_FILTER") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "$result") == 0 ]]; then
      continue
    fi

    if [[ -z $result ]]; then
      result=$project
    else
      result="$result $project"
    fi
  done

  echo "$result"
}

#############################################
# 将所有传入系统名称拼成: 前缀+项目名+后缀的形式,并连接成字符串返回
# 例如   prefix:project-  suffix:.service  系统:core  返回:project-core.service
# param1 prefix
# param1 suffix
#############################################
function make_project_names() {
  local prefix=$1
  local suffix=$2
  local names=""

  for project in $input_project; do
    local name="$prefix$project$suffix"
    if [[ -z $names ]]; then
      names=$name
    else
      names="$names $name"
    fi
  done

  echo "$names"
}

#############################################
# 创建备份目录名
#############################################
function create_backup_dir() {
  local dir_name=$(date +%Y%m%d%H%M%S)

  for project in $input_project; do
    dir_name=$dir_name'_'$project
  done

  echo "$dir_name"
}

#############################################
# 获取输入内容
#############################################
input_project=$(check_input_project "$@")
if [[ -z $input_project ]]; then
  echo "$SSH_SERVER:$SSH_PORT 无需要部署项目"
  exit
fi
service_name_list=$(make_project_names "$SERVICE_PREFIX" .service)
backup_dir_name=$(create_backup_dir)

if [[ $ONLY_RESTART == "N" ]]; then
  echo "$SSH_SERVER:$SSH_PORT 部署项目: $input_project"
  ################  创建服务器目录  ################
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo mkdir -p $TMP_APP_DIR $REMOTE_LOG_DIR $REMOTE_APP_DIR $REMOTE_APP_DIR/$backup_dir_name"
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo chown -R $SSH_USER:$SSH_USER $TMP_APP_DIR $REMOTE_LOG_DIR $REMOTE_APP_DIR $REMOTE_APP_DIR/$backup_dir_name"

  ################  上传可执行文件  ################
  echo "上传执行文件 ..."
  for project in $input_project; do
    scp -i /Users/<USER>/pem/fix-244.pem -P "$SSH_PORT" -r "$BUILD_TEMP_DIR/$project" "$SSH_USER@$SSH_SERVER:$TMP_APP_DIR/$project"
  done

  ################   添加执行权限   ################
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo chmod +x $TMP_APP_DIR/*"

  ################   上传配置文件   ################
  echo "上传配置文件 ..."
  for project in $input_project; do
    scp -i /Users/<USER>/pem/fix-244.pem -P "$SSH_PORT" -r "$CODE_DIR/pkg/$project/cfg/$CONFIG.yaml" "$SSH_USER@$SSH_SERVER:$TMP_APP_DIR/$project.yaml"
  done

  ################   上传IP数据库文件   ################
  if [[ $(check_in_list core "$input_project") == 0 || $(check_in_list user "$input_project") == 0 ]]; then
    if ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "test ! -e $REMOTE_APP_DIR/GeoLite2.mmdb"; then
      # 目标文件不存在,上传
      echo "上传IP数据库文件 ..."
      scp -i /Users/<USER>/pem/fix-244.pem -P "$SSH_PORT" -r "$CODE_DIR/res/GeoLite2-City.mmdb" "$SSH_USER@$SSH_SERVER:$TMP_APP_DIR/GeoLite2.mmdb"
    elif [[ $(shasum "$CODE_DIR/res/GeoLite2-City.mmdb" | awk -F ' ' '{print $1}') != $(ssh -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" sha1sum "$REMOTE_APP_DIR/GeoLite2.mmdb" | awk -F ' ' '{print $1}') ]]; then
      # 目标文件需更新,上传
      echo "上传IP数据库文件 ..."
      scp -i /Users/<USER>/pem/fix-244.pem -P "$SSH_PORT" -r "$CODE_DIR/res/GeoLite2-City.mmdb" "$SSH_USER@$SSH_SERVER:$TMP_APP_DIR/GeoLite2.mmdb"
    fi
  fi

  ############# 拷贝当前运行项目文件至备份目录 #############
  echo "拷贝当前运行项目文件至备份目录 ..."
  set +e
  for project in $input_project; do
    ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo cp $REMOTE_APP_DIR/$project* $REMOTE_APP_DIR/$backup_dir_name/"
  done
  set -e

  ############# 移动项目文件至部署目录 #############
  echo "移动项目文件至部署目录 ..."
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo mv" "$TMP_APP_DIR/* $REMOTE_APP_DIR/"

  echo "创建系统服务 ..."
  for project in $input_project; do
    command="cat > /etc/systemd/system/$SERVICE_PREFIX$project.service << __EOF__
[Unit]
Description=$PROJECT_NAME $project Server.

[Service]
Type=simple
ExecStart=/bin/bash -c \"$REMOTE_APP_DIR/$project -c $REMOTE_APP_DIR/$project.yaml\"
Restart=always
User=$SSH_USER
LimitNOFILE=40960
LimitNPROC=40960

[Install]
WantedBy=multi-user.target
__EOF__"
    ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "echo '$command' | sudo sh"
  done

  ############# 添加服务开机启动并启动服务 #############
  echo "添加服务开机启动并启动服务"
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo systemctl daemon-reload && sudo systemctl enable $service_name_list && sudo systemctl restart $service_name_list && sudo systemctl | grep $SERVICE_PREFIX"

else
  echo "$SSH_SERVER:$SSH_PORT 重启项目: $input_project"
  ################   上传配置文件   ################
  echo "上传配置文件 ..."
  for project in $input_project; do
    scp -i /Users/<USER>/pem/fix-244.pem -P "$SSH_PORT" -r "$CODE_DIR/pkg/$project/cfg/$CONFIG.yaml" "$SSH_USER@$SSH_SERVER:$TMP_APP_DIR/$project.yaml"
  done

  ############# 移动项目文件至部署目录 #############
  echo "移动项目文件至部署目录 ..."
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo mv" "$TMP_APP_DIR/* $REMOTE_APP_DIR/"

  ############# 添加服务开机启动并启动服务 #############
  echo "正在重新启动服务"
  ssh -i /Users/<USER>/pem/fix-244.pem -p "$SSH_PORT" "$SSH_USER@$SSH_SERVER" "sudo systemctl restart $service_name_list && sudo systemctl | grep $SERVICE_PREFIX"

fi
