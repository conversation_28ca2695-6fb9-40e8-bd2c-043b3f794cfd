#!/usr/bin/env bash

set -e

#############################################
# 检查一个字符串是否在 列表中存在
# param1 val
# parma2 list for range
#############################################
function check_in_list() {
  for val in $2; do
    if [[ $val == $1 ]]; then
      echo 0
      return
    fi
  done
  echo 1
}

#############################################
# 检查所传参数 是否在系统列表中并且去重。没有参数表示部署所有系统。
#############################################
function check_input_project() {
  local input_pro="$@"
  local result=()

  if [[ -z $SERVER_FILTER ]]; then
    SERVER_FILTER=$ALL_PROJECT
  fi

  if [[ -z $input_pro ]]; then
    input_pro=$SERVER_FILTER
  fi

  for project in $input_pro; do
    if [[ $(check_in_list "$project" "$ALL_PROJECT") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "$SERVER_FILTER") == 1 ]]; then
      continue
    fi

    if [[ $(check_in_list "$project" "${result[@]}") == 0 ]]; then
      continue
    fi

    result[${#result[@]}]="$project"
  done

  echo "${result[*]}"
}

################    编译项目    ################
export CGO_ENABLED=0
export GOOS=linux
export GOARCH=arm64
gitBranch=$(git rev-parse --abbrev-ref HEAD)
gitDigest=$(git show -s --format=%s)
gitDigest=${gitDigest//\'/}
gitDigest=${gitDigest//\"/}
export BUILD_COMMAND="go build -ldflags \"-s -w -X 'main.goVersion=$(go version)' -X 'main.buildUser=$(whoami)' -X 'main.gitHash=$(git show -s --format=%H)' -X 'main.gitDigest=${gitDigest}' -X 'main.gitBranch=${gitBranch}' -X 'main.buildTime=$(date +'%Y-%m-%d %H:%M:%S %Z')'\" -gcflags \"all=-trimpath=${PWD}\" -asmflags \"all=-trimpath=${PWD}\""

#############################################
# 获取输入内容
#############################################
input_pro=($(check_input_project "$@"))
if [[ -z ${input_pro[*]} ]]; then
  echo "$SERVER don't need to deploy"
  exit
fi
echo "当前编译项目: ${input_pro[*]}"

# 创建指定临时目录并进行项目编译
mkdir -p "$BUILD_TEMP_DIR"
# 创建编译成功计数文件 其中$$为该进程的pid
build_flag="$BUILD_TEMP_DIR/__$$.flag"
# 创建命名管道
touch "$build_flag"
for project in ${input_pro[*]}; do
{
 # 编译项目,执行文件放在指定临时目录
  eval "$BUILD_COMMAND -o $BUILD_TEMP_DIR/$project $CODE_DIR/pkg/$project/main.go"
  if [[ $TEST_BUILD == "Y" ]]; then
      echo "编译测试通过: $project"
  fi
  echo -n "1">>"$build_flag"
} &
done
wait

# 获取成功次数
flagText=$(cat "$build_flag")
rm -f "$build_flag"

# 判断是否和总数相同,不相同可能是编译出错,退出
if [ ${#flagText} != ${#input_pro[@]} ]; then
  echo "编译未全部通过"
  exit 1
fi

#if [[ $TEST_BUILD == "N" ]]; then
#  # 对执行文件进行加壳压缩
#  if [[ ${#input_pro[@]} == 1 ]]; then
#    upx -9 $BUILD_TEMP_DIR/${input_pro[0]}
#  else
#    project_list=$(echo ${input_pro[*]})
#    eval "upx -9 $BUILD_TEMP_DIR/{${project_list// /,}}"
#  fi
#fi

