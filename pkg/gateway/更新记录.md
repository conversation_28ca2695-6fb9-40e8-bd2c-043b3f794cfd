## 更新记录

- 2022-06-28
```text
    新增接口
        公共数据:获取平台风险准备金
        用户-安全中心:用户操作日志获取
        现货-资产流水:交易账户流水记录
            划转记录可传递类型(查询全部时传2+4): 2(1<<2)-转入(从资产账户) 4(1<<3)-转出(到资产账户)
            盈亏记录可传递类型(查询全部时传1+8+16)(其中一个类型:32(1<<6)-交易maker手续费(将合并到'交易手续费'中,不进行单独查询)): 1(1<<0)-交易手续费 8(1<<4)-交易转出 16(1<<5)-交易转入
        反向合约-资产流水:交易账户流水记录
            划转记录可传递类型(查询全部时传4+8+64+128): 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<6(64)-增加保证金 1<<7(128)-减少保证金
            盈亏记录可传递类型(查询全部时传1+2+16+32): 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费
        正向合约-资产流水:交易账户流水记录
            划转记录可传递类型(查询全部时传4+8+128+2048+4096+524288): 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<7(128)-减少保证金 1<<11(2048)-交易账户划转到跟单账户 1<<12(4096)-跟单账户划转到交易账户 1<<19(524288)-增加保证金
            盈亏记录可传递类型(查询全部时传1+2+16+32): 1<<0(1)-交易费用 1<<1(2)-资金费用  1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费
        正向跟单-资产流水:跟单账户流水记录
            划转记录可传递类型(查询全部时传128+2048+4096+8192+16384+524288): 1<<7(128)-减少保证金 1<<11(2048)-交易账户划转到跟单账户 1<<12(4096)-跟单账户划转到交易账户 1<<13(8192)-资产账户划转到跟单账户 1<<14(16384)-跟单账户划转到资产账户 1<<19(524288)-增加保证金
            盈亏记录可传递类型(查询全部时传1+2+16+32+256+512+1024): 1<<0(1)-交易费用 1<<1(2)-资金费用  1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<8(256)-预扣佣金 1<<9(512)-佣金退款 1<<10(1024)-佣金收入
        正向合约-跟单:历史成交
    更新接口
        现货-订单:成交明细(增加筛选条件)
        正向合约-订单:成交记录(增加筛选条件)
        反向合约-订单:成交记录(增加筛选条件)
        资产-充提账户:资产账户流水记录(增加筛选条件)
            财务记录可传递类型(查询全部时传1+2+64+4096+16384+32768+65536): 1(1<<0)-充币 2(1<<1)-提现 64(1<<6)-法币订单收币 4096(1<<12)-法币订单卖出币 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还)
            划转记录可传递类型(查询全部时传4+8+256+512+131072+262144+524288+1048576): 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
            奖励记录可传递类型(查询全部时传16+32+128+1024+2048): 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 128(1<<7)-空投奖励 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励
```
- 2022-05-24
```text
    现货-订单(历史委托) 增加 委托总额 返回字段
        "entrust_value":"123.123" // 委托总额
```
- 2022-05-23
```text
    [现货-交易对(交易对列表获取 和 交易对K线)] [现货-订单(当前委托 和 历史委托)] 增加 成交额 返回字段
        "trade_value":"123.123" // 成交额
    现货-交易对(指定交易对详情) 增加 24小时成交额 返回字段
        "trade_value_24h":"123.123" // 24小时成交额
```
- 2022-05-05
```text
    反向合约-设置止盈止损 增加请求参数
        "entrust_limit":"0" // 止盈委托价 最优五档传0,限价传对应委托价格
        "entrust_stop":"123.123" // 止损委托价 最优五档传0,限价传对应委托价格
    反向合约-获取止盈止损列表|合约止盈止损记录 增加返回字段
        "entrust_limit":"0" // 止盈委托价 0为最优五档 大于0为限价
        "entrust_stop":"123.123" // 止损委托价 0为最优五档 大于0为限价
```
- 2022-04-28
```text
    划转、资产列表和资产详情增加现货资产类型和返回
        asset_type: 1-资产账户 2-交易账户 4-跟单账户 8-usd交易账户 16-现货交易账户
    充提账户划转记录添加新类型
        type: 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
    增加现货相关接口
```
- 2022-04-18
```text
    注册接口增加参数,控制是否必须设置密码才能注册
        "quickly_register": 1 // 是否快速注册 0-否 1-是(不强制设置密码)
```
- 2022-03-23
```text
    用户信息接口,用户选项控制接口
        增加分仓字段
```
- 2022-03-22
```text
    增加赠金接口
```