run_env: "test" #运行环境，prod-生产,test-测试，dev-开发
debug: false # 是否启用调试模式
local_name: "Test PDBit Gateway Server Node 1" # 服务名称
listen_addr: "************:10002" # http服务监听地址
pprof_addr: "************:10001" # 服务运行状态监听地址
listen_addr_for_cws: "************:10003" # cws回调监听地址
discovery: "172.31.36.202:8500" # 服务发现地址
read_timeout: 30 # 读超时时间

default_db: "basecoin:JD#tAJhfNan7Hgu25@tcp(*************:3306)/new_basecoin?charset=utf8&parseTime=true&loc=Local" # 数据库连接

log_file: "/data/logs/pdbit/gateway" # 日志文件位置
log_level: "info" # 日志等级
log_only_file: false # 是否仅打印日志到文件

mail_prefix: "【PDbit】" # 发送邮件前缀(报警邮件用)
caution_receiver: [ ] # 警告邮件通知列表

sensitive_conf: "" # 敏感信息配置文件位置
ip_white_list_for_cws: [ ] # cws回调ip白名单
gin_trusted_proxies: [ "127.0.0.1", "************" ] # 信任的前置代理ip地址池(nginx ip...)

msg_server_url: "http://************:16110" # 新短信邮件服务地址
msg_server_app_id: "025633e8-c0ea-463e-bc27-04c50e8b05b8" # 新短信邮件服务appID
msg_server_secret_key: "954146C06A993123F376A0F3DD7C8076F480B4C837AEB426746410F493800871" # 新短信邮件服务secret

#主从及副本配置
is_slave: false
clone_nums: 1 #副本/服务个数，(配置大于1，触发服务则使用切片方式）
clone_id: 0 #副本id
worker_id: 0

default_redis_conf: { # 默认redis连接
  "address": "************:26379", # 连接地址
  "password": "1qaz2wsx", # 连接密码
  "use_tls": false, # 是否使用tls连接
  "default_db": 2, # 使用的db号
  "pool_size": 20, # 连接池数量
  "db_nums": { # 使用的redis库号,仅会初始化这里定义了的db号 key(db号):value(描述)
    #    0: "db 0",
    #    1: "db 1",
    2: "公共数据",
    #    3: "db 3",
    4: "USD合约",
    #    5: "db 5",
    #    6: "db 6",
    #    7: "db 7",
    #    8: "db 8",
    #    9: "db 9",
    #    10: "db 10",
    #    11: "db 11",
    #    12: "db 12",
    #    13: "db 13",
    #    14: "db 14",
    #    15: "db 15",
  },
}

# api文档配置
doc_conf: {
  version: "1.0", # 文档版本
  host: "pdapi.luckyjerry.com", # 文档地址
  base_path: "/v2", # api路由前缀
  schemes: [ "https" ], # 支持的协议
  title: "PDBit服务文档", # 文档标题
  description: "该服务用于前端与服务端对接.", # 文档说明
}

# api速率限制配置
api_rate: {
  "/v2/usdt/contract/markets": {
    limit: 1000, # 限制时间,单位毫秒
    bursts: 1, # 单位时间限制个数
  },
  "/v2/usd/contract/markets": {
    limit: 1000, # 限制时间,单位毫秒
    bursts: 1, # 单位时间限制个数
  },
   "/v2/spot/symbol/markets": {
      limit: 1000, # 限制时间,单位毫秒
      bursts: 1, # 单位时间限制个数
   },
}

