package main

import (
	"fmt"
	"os"

	"bc/libs/cache"
	"bc/libs/conf"
	"bc/libs/database"
	"bc/libs/log"
	"bc/libs/pprof"
	"bc/pkg/gateway/server"
)

// @title Panda服务API文档
// @version 1.0
// @description 内部接口定义.
// @tag.name 更新记录
// @tag.description.markdown
// @host api.luckyjerry.com
// @BasePath /v2

var (
	gitHash   string
	gitDigest string
	gitBranch string
	buildUser string
	buildTime string
	goVersion string
)

func main() {
	args := os.Args
	if len(args) == 2 && (args[1] == "--version" || args[1] == "-v") {
		fmt.Printf("Git Branch: %s \n", gitBranch)
		fmt.Printf("Git Commit Hash: %s \n", gitHash)
		fmt.Printf("Git Commit Digest: %s \n", gitDigest)
		fmt.Printf("Build User: %s \n", buildUser)
		fmt.Printf("Build TimeStamp: %s \n", buildTime)
		fmt.Printf("GoLang Version: %s \n", goVersion)
		return
	}

	// 初始化配置文件
	conf.Init()

	// 初始化日志
	log.InitLogger(conf.LogFile(), conf.LogLevel(), conf.LogOnlyFile())
	defer log.Close()

	// 初始化服务监控
	pprof.Init(conf.PprofAddr())
	defer pprof.Stop()

	//初始化缓存
	cache.InitDefaultRedisConn()

	// 初始化数据库连接
	database.InitDefaultDB()

	// 初始化服务
	server.Init()
}
