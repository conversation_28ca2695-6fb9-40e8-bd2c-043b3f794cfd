package spot

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// EntrustOrderCurrentList godoc
// @Summary 当前委托
// @Tags 现货-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotCurrentEntrustOrderReply} "成功"
// @Router /spot/order/entrust/current [post]
func EntrustOrderCurrentList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentList", arg, span)
}

// EntrustOrderCurrentPlanList godoc
// @Summary 当前委托(计划单)
// @Tags 现货-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocSpotCurrentPlanEntrustOrderReply} "成功"
// @Router /spot/order/entrust/current/plan [post]
func EntrustOrderCurrentPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentPlanList", arg, span)
}

// EntrustOrderHistoryList godoc
// @Summary 历史委托
// @Tags 现货-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocSpotHistoryEntrustOrderReply} "成功"
// @Router /spot/order/entrust/history [post]
func EntrustOrderHistoryList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryList", arg, span)
}

// EntrustOrderHistoryPlanList godoc
// @Summary 历史委托(计划单)
// @Tags 现货-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocSpotHistoryPlanEntrustOrderReply} "成功"
// @Router /spot/order/entrust/history/plan [post]
func EntrustOrderHistoryPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryPlanList", arg, span)
}

// EntrustOrderTradeDetail godoc
// @Summary 成交明细
// @Tags 现货-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.TradeDetailArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocSpotTradeDetailReply} "成功"
// @Router /spot/order/entrust/trade/detail [post]
func EntrustOrderTradeDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderTradeDetail", arg, span)
}
