package spot

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// Place godoc
// @Summary 下单
// @Tags 现货-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotPlaceArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /spot/order/place [post]
func Place(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Place", arg, span)
}

// EntrustCancel godoc
// @Summary 撤销委托
// @Tags 现货-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotOrderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /spot/order/cancel [post]
func EntrustCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustCancel", arg, span)
}

// EntrustOrderBatchCancel godoc
// @Summary 批量撤单
// @Tags 现货-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotOrderBatchCancelArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /spot/order/batch/cancel [post]
func EntrustOrderBatchCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderBatchCancel", arg, span)
}

// PlanPlace godoc
// @Summary 计划委托下单
// @Tags 现货-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotOrderPlanArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /spot/order/plan/place [post]
func PlanPlace(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanPlace", arg, span)
}

// PlanCancel godoc
// @Summary 计划委托撤销
// @Tags 现货-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotOrderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /spot/order/plan/cancel [post]
func PlanCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanCancel", arg, span)
}
