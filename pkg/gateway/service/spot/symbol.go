package spot

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// SymbolList godoc
// @Summary 交易对列表获取
// @Tags 现货-交易对
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocSpotSymbolListReply} "成功"
// @Router /spot/symbol/list [post]
func SymbolList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SymbolList", arg, span)
}

// SymbolDetail godoc
// @Summary 指定交易对详情
// @Tags 现货-交易对
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotSymbolDetail} "成功"
// @Router /spot/symbol/detail [post]
func SymbolDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SymbolDetail", arg, span)
}

// SymbolKline godoc
// @Summary 交易对K线
// @Tags 现货-交易对
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotKLineArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotKLineData} "成功"
// @Router /spot/symbol/kline [post]
func SymbolKline(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SymbolKline", arg, span)
}

// SymbolMarkets godoc
// @Summary 交易对行情
// @Tags 现货-交易对
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocSymbolMarketReply} "成功"
// @Router /spot/symbol/markets [post]
func SymbolMarkets(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SymbolMarkets", arg, span)
}

// GetSummary godoc
// @Summary 获取摘要信息
// @Tags 现货-交易对
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocSpotSummary} "成功"
// @Router /spot/common/summary [post]
func GetSummary(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetSummary", arg, span)
}
