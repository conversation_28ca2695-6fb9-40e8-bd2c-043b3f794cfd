package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// ContractList godoc
// @Summary 合约列表
// @Tags 正向合约-合约
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtContractList} "成功"
// @Router /usdt/contract/list [post]
func ContractList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractList", arg, span)
}

// ContractMarket godoc
// @Summary 指定合约行情
// @Tags 正向合约-合约
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtIndexHistory} "成功"
// @Router /usdt/contract/market [post]
func ContractMarket(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractMarket", arg, span)
}

// ContractDetail godoc
// @Summary 指定合约详情
// @Tags 正向合约-合约
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtSymbolArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtContractDetail} "成功"
// @Router /usdt/contract/detail [post]
func ContractDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractDetail", arg, span)
}

// ContractRecommend godoc
// @Summary 合约推荐
// @Tags 正向合约-合约
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtQuoteChange} "成功"
// @Router /usdt/contract/recommend [post]
func ContractRecommend(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractRecommend", arg, span)
}

// ContractKline godoc
// @Summary 合约K线
// @Tags 正向合约-合约
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtTradingViewKLineArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtKLineData} "成功"
// @Router /usdt/contract/kline [post]
func ContractKline(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractKline", arg, span)
}

// GetFundingRate godoc
// @Summary 资金费率
// @Tags 正向合约-合约
// @Produce json
// @Success 200 {object} model.DocReply{data=map[string]model.DocUsdtFundingRateReply} "成功"
// @Router /usdt/contract/fundingrate [post]
func GetFundingRate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetFundingRate", arg, span)
}

// ContractMarkets godoc
// @Summary 合约行情
// @Tags 正向合约-合约
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUsdtContractMarketReply} "成功"
// @Router /usdt/contract/markets [post]
func ContractMarkets(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractMarkets", arg, span)
}

// IncreaseRank godoc
// @Summary 当日涨幅榜
// @Tags 正向合约-榜单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtIncreaseRank} "成功"
// @Router /usdt/rank/increase [post]
func IncreaseRank(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "IncreaseRank", arg, span)
}

// BuyRank godoc
// @Summary 看涨人数榜
// @Tags 正向合约-榜单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtPersonRank} "成功"
// @Router /usdt/rank/buy [post]
func BuyRank(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "BuyRank", arg, span)
}

// SellRank godoc
// @Summary 看跌人数榜
// @Tags 正向合约-榜单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtPersonRank} "成功"
// @Router /usdt/rank/sell [post]
func SellRank(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SellRank", arg, span)
}

// Indicator godoc
// @Summary 买卖指标
// @Tags 正向合约-榜单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtContractIndicator} "成功"
// @Router /usdt/rank/indicator [post]
func Indicator(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Indicator", arg, span)
}

// DragSupport godoc
// @Summary 阻力支撑位
// @Tags 正向合约-榜单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtContractSupport} "成功"
// @Router /usdt/rank/dragSupport [post]
func DragSupport(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DragSupport", arg, span)
}

// GetSummary godoc
// @Summary 获取摘要信息
// @Tags 正向合约-合约
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUsdtSummary} "成功"
// @Router /usdt/common/summary [post]
func GetSummary(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetSummary", arg, span)
}
