package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// ActivityUnInfo godoc
// @Summary 活动信息
// @Tags 活动
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocActivityInfoReq} "成功"
// @Router /base/activity/trade/unlogin [post]
func ActivityUnInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ActivityUnInfo", arg, span)
}

// ActivityInfo godoc
// @Summary 本期活动信息
// @Tags 活动
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocActivityInfoReq} "成功"
// @Router /base/activity/trade/info [post]
func ActivityInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ActivityInfo", arg, span)
}

// ActivityMyWard godoc
// @Summary 我的获奖记录
// @Tags 活动
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocMyWardReply} "成功"
// @Router /base/activity/trade/myward [post]
func ActivityMyWard(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ActivityMyWard", arg, span)
}

// UpShare godoc
// @Summary 更新分享
// @Tags 活动
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUpShareArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/activity/trade/upshare [post]
func UpShare(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UpShare", arg, span)
}
