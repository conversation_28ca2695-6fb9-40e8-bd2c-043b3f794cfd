package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// ErrorSubmit godoc
// @Summary 客户端错误日志提交
// @Tags 统计收集
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocClientErrorArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/collect/error/submit [post]
func ErrorSubmit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ErrorSubmit", arg, span)
}

// AppDownloadSubmit godoc
// @Summary APP下载统计
// @Tags 统计收集
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAppDownloadSubmitArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/collect/download/submit [post]
func AppDownloadSubmit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AppDownloadSubmit", arg, span)
}

// CollectDomainDelay godoc
// @Summary 服务域名速度测试结果提交
// @Tags 统计收集
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCollectDomainDelayArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/collect/domain/delay [post]
func CollectDomainDelay(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CollectDomainDelay", arg, span)
}
