package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// TradeLever godoc
// @Summary 获取用户当前交易杠杆倍数
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractMarkArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtTradeLeverReply} "成功"
// @Router /usdt/user/trade/lever [post]
func TradeLever(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TradeLever", arg, span)
}

// SwitchLever godoc
// @Summary 切换用户当前交易杠杆倍数
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtSwitchLeverArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/user/trade/lever/switch [post]
func SwitchLever(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SwitchLever", arg, span)
}

// OrderOpenPosition godoc
// @Summary 开仓
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderOpenArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtOrderOpenRsp} "成功"
// @Router /usdt/order/place [post]
func OrderOpenPosition(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "OrderOpenPosition", arg, span)
}

// EntrustCancel godoc
// @Summary 撤销委托
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderCancelArgs} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/order/cancel [post]
func EntrustCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustCancel", arg, span)
}

// EntrustOrderBatchCancel godoc
// @Summary 批量撤单
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderBatchCancelArgs} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/order/batch/cancel [post]
func EntrustOrderBatchCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderBatchCancel", arg, span)
}

// CloseOut godoc
// @Summary 平仓
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderCloseArgs} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtOrderCloseRsp} "成功"
// @Router /usdt/position/closeout [post]
func CloseOut(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CloseOut", arg, span)
}

// AllOrderLiquidate godoc
// @Summary 一键全平
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtAllLiquidateArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/position/closeout/all [post]
func AllOrderLiquidate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AllOrderLiquidate", arg, span)
}

// PlanPlace godoc
// @Summary 计划委托下单
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderPlanArgs} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/order/plan/place [post]
func PlanPlace(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanPlace", arg, span)
}

// PlanCancel godoc
// @Summary 计划委托撤销
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderCancelArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/order/plan/cancel [post]
func PlanCancel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanCancel", arg, span)
}

// SetStop godoc
// @Summary 设置止盈止损
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtOrderFullStopArgs} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/position/set/stop [post]
func SetStop(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SetStop", arg, span)
}

// CancelStop godoc
// @Summary 删除止盈止损
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtCancelStopPriceArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/position/cancel/stop [post]
func CancelStop(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CancelStop", arg, span)
}

// MarginModify godoc
// @Summary 调整保证金
// @Tags 正向合约-交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtMarginModifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/position/margin/modify [post]
func MarginModify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MarginModify", arg, span)
}
