package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

func TestLegalAudit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestLegalAudit", arg, span)
}
func TestOrderCallBack(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestOrderCallBack", arg, span)
}
func SpyIndexPrice(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SpyIndexPrice", arg, span)
}
func TestFollowBrokerage(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestFollowBrokerage", arg, span)
}
func FundingRateSet(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FundingRateSet", arg, span)
}
func CloseOutCustomTest(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CloseOutCustomTest", arg, span)
}
func CloseOutCustomOrderType(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CloseOutCustomOrderType", arg, span)
}
func DayStatisticsEmail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DayStatisticsEmail", arg, span)
}
func HalfStatisticsEmail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "HalfStatisticsEmail", arg, span)
}
func ErrCollectEmail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ErrCollectEmail", arg, span)
}
func TestMarketWarn(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestMarketWarn", arg, span)
}
func TestIndexPriceWarn(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestIndexPriceWarn", arg, span)
}
func TestCloseWsWarn(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TestCloseWsWarn", arg, span)
}
