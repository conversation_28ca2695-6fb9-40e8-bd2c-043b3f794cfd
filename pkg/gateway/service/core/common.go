package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// CheckUpgrade godoc
// @Summary 版本更新
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUpgradeInfo} "成功"
// @Router /base/common/version [post]
func CheckUpgrade(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CheckUpgrade", arg, span)
}

// GetAppDownload godoc
// @Summary 获取app下载链接
// @Tags 公共数据
// @Produce json
// @Param os query int true "要获取的客户端类型 1-安卓 2-ios" Enums(1,2)
// @Param platform_id query string true "平台id,固定传1"
// @Success 200 {object} model.DocReply{data=model.DocAppDownloadReply} "成功"
// @Router /base/common/download [get]
func GetAppDownload(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetAppDownload", arg, span)
}

// GetHotfixPatch godoc
// @Summary 获取热升级补丁列表
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocHotfixPatch} "成功"
// @Router /base/common/hotfix [post]
func GetHotfixPatch(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetHotfixPatch", arg, span)
}

// Ping godoc
// @Summary ping接口
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocPong} "成功"
// @Router /base/ping [post]
func Ping(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Ping", arg, span)
}

// GetCoinList godoc
// @Summary 获取币种列表
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocCoin} "成功"
// @Router /base/common/coin/list [post]
func GetCoinList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetCoinList", arg, span)
}

// HomeBanner godoc
// @Summary 获取轮播图列表
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocBannerReply} "成功"
// @Router /base/common/banner/list [post]
func HomeBanner(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "HomeBanner", arg, span)
}

// GetNoticeList godoc
// @Summary 获取公告列表
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocNoticeListReply} "成功"
// @Router /base/common/notice/list [post]
func GetNoticeList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetNoticeList", arg, span)
}

// GetNoticeInfo godoc
// @Summary 获取公告详情
// @Tags 公共数据
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocNoticeInfoReply} "成功"
// @Router /base/common/notice/detail [post]
func GetNoticeInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetNoticeInfo", arg, span)
}

// GetImportantNoticeV2 godoc
// @Summary 获取重要通知
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocImportantNoticeReply} "成功"
// @Router /base/common/notice/important/new [post]
func GetImportantNoticeV2(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetImportantNoticeV2", arg, span)
}

// About godoc
// @Summary 关于我们
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocAboutCommunityReply} "成功"
// @Router /base/common/about [post]
func About(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "About", arg, span)
}

// ServerConfig godoc
// @Summary 服务器配置列表
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocServerConfig} "成功"
// @Router /base/common/serverConfig [post]
func ServerConfig(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ServerConfig", arg, span)
}

// GetCoinLegalRate godoc
// @Summary 获取币种法币汇率
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=map[string]model.DocCoinLegalPrice} "成功"
// @Router /base/common/rate [post]
func GetCoinLegalRate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetCoinLegalRate", arg, span)
}

// AppStatus godoc
// @Summary 获取app状态
// @Tags 公共数据
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocAppStatusReply} "成功"
// @Router /base/common/app/status [post]
func AppStatus(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AppStatus", arg, span)
}

// GetRiskProvisions godoc
// @Summary 获取平台风险准备金
// @Tags 公共数据
// @Produce json
// @Param data body model.DocArg{data=model.DocGetRiskProvisionsArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocRiskProvisionsReply} "成功"
// @Router /base/common/provisions-of-risk [post]
func GetRiskProvisions(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetRiskProvisions", arg, span)
}
