package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// LegalExchangeRate godoc
// @Summary 下单获取汇率
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocPlaceRateArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocApiExchangeRate} "成功"
// @Router /base/legal/exchange/rate [post]
func LegalExchangeRate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LegalExchangeRate", arg, span)
}

// LegalPlaceLimit godoc
// @Summary 获取下单限制
// @Tags 法币交易
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocApiLegalOrderLimit} "成功"
// @Router /base/legal/limit [post]
func LegalPlaceLimit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LegalPlaceLimit", arg, span)
}

// C2CLogin godoc
// @Summary 登录获取c2c专用token
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DOcC2CLoginArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocC2CLoginReply} "成功"
// @Router /base/legal/login [post]
func C2CLogin(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "C2CLogin", arg, span)
}

// LegalPlaceOrder godoc
// @Summary 下单获取订单号和token
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.PlaceArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Failure 200 {object} model.DocReply{data=model.DocRemainQuota} "剩余额度不足"
// @Router /base/legal/place [post]
func LegalPlaceOrder(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LegalPlaceOrder", arg, span)
}

// LegalOrderList godoc
// @Summary 获取订单列表
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocApiLegalOrderArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiLegalOrder} "成功"
// @Router /base/legal/order/list [post]
func LegalOrderList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LegalOrderList", arg, span)
}

// ThirdLegalConf godoc
// @Summary 获取三方买币配置信息
// @Tags 法币交易
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocThirdLegalConf{order_conf=map[string]map[string]model.DocThirdLegalOrderConf}} "成功(其他端返回值)"
// @Success 201 {object} model.DocReply{data=model.DocThirdLegalConf{order_conf=[]model.DocThirdLegalOrderConfForAndroid}} "成功(安卓端返回值)"
// @Router /base/legal/third/config [post]
func ThirdLegalConf(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ThirdLegalConf", arg, span)
}

// ThirdLegalMerchants godoc
// @Summary 获取三方买币商家列表
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocThirdLegalOrderMerchantArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocThirdLegalOrderMerchant} "成功"
// @Router /base/legal/third/merchants [post]
func ThirdLegalMerchants(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ThirdLegalMerchants", arg, span)
}

// ThirdLegalOrderPlace godoc
// @Summary 三方买币下单
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocThirdLegalOrderPlaceArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocThirdLegalOrderPlaceReply} "成功"
// @Router /base/legal/third/place [post]
func ThirdLegalOrderPlace(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ThirdLegalOrderPlace", arg, span)
}

// ThirdLegalOrderList godoc
// @Summary 获取三方买币订单记录
// @Tags 法币交易
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocThirdLegalOrder} "成功"
// @Router /base/legal/third/order/list [post]
func ThirdLegalOrderList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ThirdLegalOrderList", arg, span)
}

// ListUserPayments godoc
// @Summary 用户支付方式列表
// @Tags 法币交易
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserPayment} "成功"
// @Router /base/user/payment/list [post]
func ListUserPayments(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ListUserPayments", arg, span)
}

// AddUserPayments godoc
// @Summary 用户支付方式添加
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserPayment} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUserPayment} "成功"
// @Router /base/user/payment/add [post]
func AddUserPayments(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AddUserPayments", arg, span)
}

// UpdateUserPayments godoc
// @Summary 用户支付方式修改
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserPayment} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/payment/edit [post]
func UpdateUserPayments(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UpdateUserPayments", arg, span)
}

// DelUserPayments godoc
// @Summary 用户支付方式移除
// @Tags 法币交易
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserPayment} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/payment/del [post]
func DelUserPayments(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DelUserPayments", arg, span)
}

// UserQuota godoc
// @Summary 用户配额
// @Tags 法币交易
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUserQuotaReply} "成功"
// @Router /base/user/quota [post]
func UserQuota(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserQuota", arg, span)
}
