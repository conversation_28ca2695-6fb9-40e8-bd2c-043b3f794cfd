package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// DealerApply godoc
// @Summary 交易员申请
// @Tags 正向合约-跟单
// @Produce json
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/dealer/apply [post]
func DealerApply(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerApply", arg, span)
}

// GetDealerLever godoc
// @Summary 获取交易员当前交易杠杆倍数
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractMarkArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtDealerLever} "成功"
// @Router /usdt/follow/dealer/lever [post]
func GetDealerLever(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLever", arg, span)
}

// DealerLeverSwitch godoc
// @Summary 切换交易员当前交易杠杆倍数
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtDealerSwitchLeverArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/dealer/lever/switch [post]
func DealerLeverSwitch(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerLeverSwitch", arg, span)
}

// FollowPositionCurrent godoc
// @Summary 当前带单跟单持仓
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtContractPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtCurrentFollowPosition} "成功"
// @Router /usdt/follow/order/hold [post]
func FollowPositionCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowPositionCurrent", arg, span)
}

// FollowEntrustSold godoc
// @Summary 历史成交
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtFollowEntrustSoldArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocFollowSoldTradeReply} "成功"
// @Router /usdt/follow/order/sold [post]
func FollowEntrustSold(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowEntrustSold", arg, span)
}

// MyDealerList godoc
// @Summary 我的交易员列表
// @Tags 正向合约-跟单
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtMyDealerList} "成功"
// @Router /usdt/follow/my/dealer/list [post]
func MyDealerList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyDealerList", arg, span)
}

// MyFollowProfitLoss godoc
// @Summary 我的跟单账户收益情况
// @Tags 正向合约-跟单
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUsdtMyProfitLoss} "成功"
// @Router /usdt/follow/my/profit [post]
func MyFollowProfitLoss(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyFollowProfitLoss", arg, span)
}

// FollowOrderCurrent godoc
// @Summary 当前带单跟单列表
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtContractPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtCurrentFollowOrder} "成功"
// @Router /usdt/follow/current [post]
func FollowOrderCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowOrderCurrent", arg, span)
}

// FollowOrderHistory godoc
// @Summary 历史带单跟单列表
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtFollowPositionHistory} "成功"
// @Router /usdt/follow/history [post]
func FollowOrderHistory(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowOrderHistory", arg, span)
}

// FollowHeadData godoc
// @Summary 带单跟单头部数据
// @Tags 正向合约-跟单
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUsdtFollowHeadData} "成功"
// @Router /usdt/follow/head [post]
func FollowHeadData(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowHeadData", arg, span)
}

// GetLeaderList godoc
// @Summary 交易员列表
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtGetLeaderListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtLeaderListReply} "成功"
// @Router /usdt/follow/dealer/list [post]
func GetLeaderList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetLeaderList", arg, span)
}

// GetLeaderDetail godoc
// @Summary 交易员详情
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocLeaderDetailReply} "成功"
// @Router /usdt/follow/dealer/detail [post]
func GetLeaderDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetLeaderDetail", arg, span)
}

// DealerOrderHistory godoc
// @Summary 交易员历史带单
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiLeaderOrderHistory} "成功"
// @Router /usdt/follow/dealer/order/history [post]
func DealerOrderHistory(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerOrderHistory", arg, span)
}

// DealerOrderCurrent godoc
// @Summary 交易员当前带单
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiLeaderOrderCurrent} "成功"
// @Router /usdt/follow/dealer/order/current [post]
func DealerOrderCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerOrderCurrent", arg, span)
}

// DealerFollowers godoc
// @Summary 交易员当前跟随者列表
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiDealerFollowers} "成功"
// @Router /usdt/follow/dealer/followers [post]
func DealerFollowers(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerFollowers", arg, span)
}

// GetFollowSetting godoc
// @Summary 获取当前跟单设置
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocApiDealerFollowSetting} "成功"
// @Router /usdt/follow/setting/current [post]
func GetFollowSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetFollowSetting", arg, span)
}

// SetFollow godoc
// @Summary 跟单设置
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocApiDealerFollowSetting} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/setting [post]
func SetFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SetFollow", arg, span)
}

// ExitFollow godoc
// @Summary 退出跟单
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocTraderIDArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/exit [post]
func ExitFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ExitFollow", arg, span)
}

// RemoveFollow godoc
// @Summary 移除跟随
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserIDArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/remove [post]
func RemoveFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "RemoveFollow", arg, span)
}

// MyFollowers godoc
// @Summary 我的跟随者
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocApiMyFollowersReply} "成功"
// @Router /usdt/follow/my/followers [post]
func MyFollowers(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyFollowers", arg, span)
}

// GetDealerLeadProfit godoc
// @Summary 获取带单分润统计(日数据)
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiDealerLeadProfit} "成功"
// @Router /usdt/follow/lead/profit [post]
func GetDealerLeadProfit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLeadProfit", arg, span)
}

// GetDealerLeadProfitDetail godoc
// @Summary 获取带单分润详情
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocApiDealerLeadProfitDetailArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiDealerLeadProfitDetail} "成功"
// @Router /usdt/follow/lead/profit/detail [post]
func GetDealerLeadProfitDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLeadProfitDetail", arg, span)
}

// GetFollowMessage godoc
// @Summary 获取带单跟单消息
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocPageArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocApiFollowMessage} "成功"
// @Router /usdt/follow/message [post]
func GetFollowMessage(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetFollowMessage", arg, span)
}

// MarkReadMessage godoc
// @Summary 标记消息已读
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocReadFollowMessageArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/mark/message/readed [post]
func MarkReadMessage(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MarkReadMessage", arg, span)
}

// LeaderPlace godoc
// @Summary 带单开仓
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFollowOrderOpenArgs} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocFollowOrderOpenRsp} "成功"
// @Router /usdt/follow/order/place [post]
func LeaderPlace(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LeaderPlace", arg, span)
}

// FollowCloseOut godoc
// @Summary 带单跟单平仓
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFollowOrderCloseArgs} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocOrderCloseRsp} "成功"
// @Router /usdt/follow/position/closeout [post]
func FollowCloseOut(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowCloseOut", arg, span)
}

// FollowSetStop godoc
// @Summary 带单跟单设置止盈止损
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSetFollowPositionStopArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/position/set/stop [post]
func FollowSetStop(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowSetStop", arg, span)
}

// FollowDealerCurrentSetting godoc
// @Summary 带单员当前带单设置
// @Tags 正向合约-跟单
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocLeaderLimitSetting} "成功"
// @Router /usdt/follow/dealer/setting/current [post]
func FollowDealerCurrentSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowDealerCurrentSetting", arg, span)
}

// FollowDealerSetting godoc
// @Summary 带单员带单设置
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocLeaderLimitSetting} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /usdt/follow/dealer/setting [post]
func FollowDealerSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowDealerSetting", arg, span)
}

// FollowMarginModify godoc
// @Summary 调整跟单逐仓保证金
// @Tags 正向合约-跟单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtMarginModifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Success 1023 {object} model.DocReply{data=model.DocUsdtFollowMarginAdjustValidate} "需要进行确认"
// @Router /usdt/follow/position/margin/modify [post]
func FollowMarginModify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowMarginModify", arg, span)
}
