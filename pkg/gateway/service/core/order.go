package core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// PositionCurrent godoc
// @Summary 当前持仓
// @Tags 正向合约-订单
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocUsdtCurrentPosition} "成功"
// @Router /usdt/order/hold [post]
func PositionCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PositionCurrent", arg, span)
}

// EntrustSold godoc
// @Summary 成交记录
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtEntrustSoldArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtSoldTrade} "成功"
// @Router /usdt/order/sold [post]
func EntrustSold(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustSold", arg, span)
}

// AllEntrustSold godoc
// @Summary 成交记录(包含带单跟单以及普通开仓)
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtEntrustSoldArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtAllSoldTrade} "成功"
// @Router /usdt/all/order/sold [post]
func AllEntrustSold(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AllEntrustSold", arg, span)
}

// PlanOrder godoc
// @Summary 计划单
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtConditionOrder} "成功"
// @Router /usdt/order/plan [post]
func PlanOrder(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanOrder", arg, span)
}

// StopPriceList godoc
// @Summary 止盈止损列表
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtStopPriceListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtStopPriceListReply} "成功"
// @Router /usdt/position/set/list [post]
func StopPriceList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "StopPriceList", arg, span)
}

// ContractStopPriceList godoc
// @Summary 指定合约止盈止损列表
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtStopPriceListReply} "成功"
// @Router /usdt/position/stop/list [post]
func ContractStopPriceList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractStopPriceList", arg, span)
}

// EntrustOrderCurrentList godoc
// @Summary 当前委托
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtCurrentEntrustOrder} "成功"
// @Router /usdt/order/entrust/current [post]
func EntrustOrderCurrentList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentList", arg, span)
}

// EntrustOrderCurrentPlanList godoc
// @Summary 当前委托(计划单)
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtCurrentPlanEntrustOrder} "成功"
// @Router /usdt/order/entrust/current/plan [post]
func EntrustOrderCurrentPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentPlanList", arg, span)
}

// EntrustOrderHistoryList godoc
// @Summary 历史委托
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtHistoryEntrustArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtHistoryEntrustOrder} "成功"
// @Router /usdt/order/entrust/history [post]
func EntrustOrderHistoryList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryList", arg, span)
}

// EntrustOrderHistoryPlanList godoc
// @Summary 历史委托(计划单)
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtHistoryPlanEntrustArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtHistoryPlanEntrustOrder} "成功"
// @Router /usdt/order/entrust/history/plan [post]
func EntrustOrderHistoryPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryPlanList", arg, span)
}

// EntrustOrderHistoryShareResource godoc
// @Summary 历史委托分享资源
// @Tags 正向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdtHistoryEntrustOrderShareResource} "成功"
// @Router /usdt/order/entrust/history/share [post]
func EntrustOrderHistoryShareResource(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryShareResource", arg, span)
}
