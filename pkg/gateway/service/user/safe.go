package user

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// SafeModifyAccount godoc
// @Summary 绑定手机/邮箱
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyAccountArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/account/modify [post]
func SafeModifyAccount(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifyAccount", arg, span)
}

// SafeModifySpareEmail godoc
// @Summary 绑定/修改备用邮箱
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifySpareEmailArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/spare/email/modify [post]
func SafeModifySpareEmail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifySpareEmail", arg, span)
}

// SafeModifyLoginPassword godoc
// @Summary 设置或修改登录密码
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyPasswordArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/password/login/modify [post]
func SafeModifyLoginPassword(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifyLoginPassword", arg, span)
}

// SafeModifyFundPassword godoc
// @Summary 设置或修改资金密码
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyPasswordArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/password/fund/modify [post]
func SafeModifyFundPassword(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifyFundPassword", arg, span)
}

// SafeTradeVerifyMode godoc
// @Summary 修改资金密码验证时效
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyTradeVerifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/password/fund/timeliness [post]
func SafeTradeVerifyMode(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeTradeVerifyMode", arg, span)
}

// SafeVerifyLoginPassword godoc
// @Summary 校验登录密码
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeVerifyPWD} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/password/login/verify [post]
func SafeVerifyLoginPassword(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeVerifyLoginPassword", arg, span)
}

// SafeVerifyFundPassword godoc
// @Summary 校验资金密码
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeVerifyPWD} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/password/fund/verify [post]
func SafeVerifyFundPassword(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeVerifyFundPassword", arg, span)
}

// SafeGetNewTotpSecret godoc
// @Summary 获取验证器私钥
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSafeTotpSecret} "成功"
// @Router /base/user/safe/totp/secret/get [post]
func SafeGetNewTotpSecret(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeGetNewTotpSecret", arg, span)
}

// SafeModifyTotpSecret godoc
// @Summary 绑定验证器私钥
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyTotpSecretArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/totp/modify [post]
func SafeModifyTotpSecret(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifyTotpSecret", arg, span)
}

// SafeModifyLoginVerifyMode godoc
// @Summary 修改登录验证开关
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeModifyLoginVerifyModeArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/login/verify/switch [post]
func SafeModifyLoginVerifyMode(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeModifyLoginVerifyMode", arg, span)
}

// SafeTwoStepVerify godoc
// @Summary 校验安全验证
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeVerifyCode} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/safe/twostep/verify [post]
func SafeTwoStepVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeTwoStepVerify", arg, span)
}

// SafeUserLogs godoc
// @Summary 用户操作日志获取
// @Tags 用户-安全中心
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocSafeUserLogsReply} "成功"
// @Router /base/user/safe/logs [post]
func SafeUserLogs(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SafeUserLogs", arg, span)
}
