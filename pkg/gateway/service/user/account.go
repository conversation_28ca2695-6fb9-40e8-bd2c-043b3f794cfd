package user

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// CheckAccount godoc
// @Summary 检查账号是否存在
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAccountArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocCheckReply} "成功"
// @Router /base/account/check [post]
func CheckAccount(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CheckAccount", arg, span)
}

// Register godoc
// @Summary 注册
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocRegisterArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.RegisterReply} "成功"
// @Router /base/account/register [post]
func Register(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Register", arg, span)
}

// Login godoc
// @Summary 登录
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocLoginArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocLoginReply} "成功"
// @Router /base/account/login [post]
func Login(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Login", arg, span)
}

// ForgetPassword godoc
// @Summary 找回密码
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocForgetPwdArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/account/forget [post]
func ForgetPassword(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ForgetPassword", arg, span)
}

// Logout godoc
// @Summary 登出
// @Tags 用户-账号
// @Produce json
// @Success 200 {object} model.DocReply "成功"
// @Router /base/account/logout [post]
func Logout(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Logout", arg, span)
}

// DeleteAccount godoc
// @Summary 账号注销
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSafeVerifyCode} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/account/delete [post]
func DeleteAccount(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DeleteAccount", arg, span)
}

// UserInfo godoc
// @Summary 用户信息
// @Tags 用户-账号
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocApiUserInfo} "成功"
// @Router /base/user/info [post]
func UserInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserInfo", arg, span)
}

// ModifyUserInfo godoc
// @Summary 用户信息更新(昵称/介绍/头像)
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocModifyUserInfoArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/info/modify [post]
func ModifyUserInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ModifyUserInfo", arg, span)
}

// UserSwitchModify godoc
// @Summary 用户开关控制
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserSwitchArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/switch/modify [post]
func UserSwitchModify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserSwitchModify", arg, span)
}

// UserOptionModify godoc
// @Summary 用户选项控制
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserOptionArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/option/modify [post]
func UserOptionModify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserOptionModify", arg, span)
}

// UserInviteInfo godoc
// @Summary 邀请信息
// @Tags 用户-账号
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocApiUserInviteInfo} "成功"
// @Router /base/user/invite [post]
func UserInviteInfo(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserInviteInfo", arg, span)
}

// InviteList godoc
// @Summary 邀请记录
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocInviteListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocInvitedInfo} "成功"
// @Router /base/user/invite/list [post]
func InviteList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "InviteList", arg, span)
}

// TradeLicenseAgree godoc
// @Summary 标记接受交易协议
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply "成功"
// @Router /base/trade/license/agree [post]
func TradeLicenseAgree(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TradeLicenseAgree", arg, span)
}

// FollowLicenseAgree godoc
// @Summary 标记接受跟单协议
// @Tags 用户-账号
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply "成功"
// @Router /base/follow/license/agree [post]
func FollowLicenseAgree(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowLicenseAgree", arg, span)
}
