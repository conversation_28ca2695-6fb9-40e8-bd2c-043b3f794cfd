package user

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// GetAuthCode godoc
// @Summary 获取验证码
// @Tags 用户-公共接口
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAuthCodeArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/common/authcode [post]
func GetAuthCode(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetAuthCode", arg, span)
}

// GetAuthCodeSafe godoc
// @Summary 获取验证码(易盾)
// @Tags 用户-公共接口
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAuthCodeArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/common/authcode/safe [post]
func GetAuthCodeSafe(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetAuthCodeSafe", arg, span)
}

// CheckAuthCode godoc
// @Summary 校验验证码
// @Tags 用户-公共接口
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCheckCodeArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/common/authcode/verify [post]
func CheckAuthCode(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "CheckAuthCode", arg, span)
}

// GetAreaCodeList godoc
// @Summary 获取区域列表
// @Tags 用户-公共接口
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocCountryAreacode} "成功"
// @Router /base/common/countryareacode [post]
func GetAreaCodeList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetAreaCodeList", arg, span)
}

// ContactUS godoc
// @Summary 联系我们
// @Tags 用户-公共接口
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocContactUsArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/common/contactus [post]
func ContactUS(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContactUS", arg, span)
}
