package user

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// GetUserApiList godoc
// @Summary 查询用户api列表
// @Tags 用户-api管理
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserAPIReply} "成功"
// @Router /base/user/api/list [post]
func GetUserApiList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetUserApiList", arg, span)
}

// GetUserApiDetail godoc
// @Summary 查询用户api信息
// @Tags 用户-api管理
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserApiArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUserAPIReply} "成功"
// @Router /base/user/api/detail [post]
func GetUserApiDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetUserApiDetail", arg, span)
}

// UserApiCreate godoc
// @Summary 用户api创建
// @Tags 用户-api管理
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserApiArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/api/create [post]
func UserApiCreate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserApiCreate", arg, span)
}

// UserApiModify godoc
// @Summary 用户api修改配置
// @Tags 用户-api管理
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserApiArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/api/modify [post]
func UserApiModify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserApiModify", arg, span)
}

// UserApiDelete godoc
// @Summary 用户api删除
// @Tags 用户-api管理
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUserApiArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/api/delete [post]
func UserApiDelete(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UserApiDelete", arg, span)
}
