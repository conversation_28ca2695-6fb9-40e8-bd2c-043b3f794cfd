package user

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// GetUserVerifyState godoc
// @Summary 获取用户认证状态
// @Tags 用户-身份认证
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocVerifyStateInfo} "成功"
// @Router /base/user/verify/state [post]
func GetUserVerifyState(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetUserVerifyState", arg, span)
}

// IdNumberVerify godoc
// @Summary 提交身份信息审核
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocVerifyByIdNameArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/idnumber [post]
func IdNumberVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "IdNumberVerify", arg, span)
}

// ForeignVerify godoc
// @Summary 提交身份信息审核(非大陆)
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocForeignVerifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/other [post]
func ForeignVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ForeignVerify", arg, span)
}

// ManualVerify godoc
// @Summary 提交活体身份信息审核(人工)
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocManualVerifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/manual [post]
func ManualVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ManualVerify", arg, span)
}

// FaceVerify godoc
// @Summary 提交面部认证信息
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFaceVerifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/face [post]
func FaceVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FaceVerify", arg, span)
}

// GetVerifyProgress godoc
// @Summary 获取用户认证进度
// @Tags 用户-身份认证
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocVerifyStateInfo} "成功"
// @Router /base/user/verify/progress [post]
func GetVerifyProgress(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetVerifyProgress", arg, span)
}

// FaceVerifyForH5 godoc
// @Summary 提交面部认证信息(h5)
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFaceVerifyH5Arg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/face/h5 [post]
func FaceVerifyForH5(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FaceVerifyForH5", arg, span)
}

// WithdrawVerify godoc
// @Summary 提交提币面部认证信息
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFaceVerifyArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/withdraw [post]
func WithdrawVerify(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawVerify", arg, span)
}

// WithdrawVerifyForH5 godoc
// @Summary 提交提币面部认证信息(h5)
// @Tags 用户-身份认证
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFaceVerifyH5Arg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/user/verify/withdraw/h5 [post]
func WithdrawVerifyForH5(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawVerifyForH5", arg, span)
}
