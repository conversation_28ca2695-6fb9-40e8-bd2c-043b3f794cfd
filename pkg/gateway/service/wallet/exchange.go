package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// ExchangePreOrder godoc
// @Summary 预兑换,锁定价格,返回订单摘要
// @Tags 资产-兑币
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocExchangePreOrderArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocExchangePreOrder} "成功"
// @Router /base/exchange/order/pre [post]
func ExchangePreOrder(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ExchangePreOrder", arg, span)
}

// ExchangeActualOrder godoc
// @Summary 实际兑换
// @Tags 资产-兑币
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocExchangeActualOrderArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/exchange/order/actual [post]
func ExchangeActualOrder(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ExchangeActualOrder", arg, span)
}

// ExchangeOrderHistory godoc
// @Summary 兑币订单历史记录
// @Tags 资产-兑币
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocExchangeOrderHistoryArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocExchangeOrderHistory} "成功"
// @Router /base/exchange/order/history [post]
func ExchangeOrderHistory(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ExchangeOrderHistory", arg, span)
}
