package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// GiftCashActivityList godoc
// @Summary 赠金活动列表
// @Tags 资产-赠金
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocGiftActivityReply} "成功"
// @Router /base/gift-cash/activity/list [post]
func GiftCashActivityList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GiftCashActivityList", arg, span)
}

// GiftCashReceive godoc
// @Summary 赠金活动领取
// @Tags 资产-赠金
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocGiftReceiveArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/gift-cash/activity/receive [post]
func GiftCashReceive(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GiftCashReceive", arg, span)
}

// GiftCashHead godoc
// @Summary 我的赠金头部数据
// @Tags 资产-赠金
// @Produce json
// @Success 200 {object} model.DocReply{data=model.DocGiftMyHeadReply} "成功"
// @Router /base/gift-cash/my/head [post]
func GiftCashHead(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GiftCashHead", arg, span)
}

// GiftCashRecordList godoc
// @Summary 赠金记录
// @Tags 资产-赠金
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocGiftRecordArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocGiftRecordReply} "成功"
// @Router /base/gift-cash/my/record [post]
func GiftCashRecordList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GiftCashRecordList", arg, span)
}
