package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// MultipleAssetList godoc
// @Summary 多资产账户列表获取
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAssetListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocAssetDetail} "成功"
// @Router /base/asset/list [post]
func MultipleAssetList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MultipleAssetList", arg, span)
}

// MultipleAssetDetail godoc
// @Summary 多资产账户详情获取
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocAssetDetailArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocCoinAssetDetail} "成功"
// @Router /base/asset/detail [post]
func MultipleAssetDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MultipleAssetDetail", arg, span)
}

// AssetTransfer godoc
// @Summary 资产划转
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocApiTransferArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/asset/transfer [post]
func AssetTransfer(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "AssetTransfer", arg, span)
}

// DepositAddress godoc
// @Summary 充币地址
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocGetDepositAddressArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocGetDepositAddressReply} "成功"
// @Router /base/asset/deposit/address [post]
func DepositAddress(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DepositAddress", arg, span)
}

// GetWithdrawDefaultAddress godoc
// @Summary 获取默认提币地址
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocGetWithdrawAddrListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUserWithdrawAddress} "成功"
// @Router /base/asset/withdraw/address/default [post]
func GetWithdrawDefaultAddress(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetWithdrawDefaultAddress", arg, span)
}

// WithdrawAddressList godoc
// @Summary 提币地址列表
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocGetWithdrawAddrListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.GetWithdrawAddrListReply} "成功"
// @Router /base/asset/withdraw/address/list [post]
func WithdrawAddressList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawAddressList", arg, span)
}

// WithdrawAddressAdd godoc
// @Summary 提币地址设置
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocModifyWithdrawAddrArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/asset/withdraw/address/add [post]
func WithdrawAddressAdd(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawAddressAdd", arg, span)
}

// WithdrawAddressDel godoc
// @Summary 提币地址删除
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocDelWithdrawAddrArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/asset/withdraw/address/del [post]
func WithdrawAddressDel(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawAddressDel", arg, span)
}

// WithdrawAddressUpdate godoc
// @Summary 提币地址更新
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocModifyWithdrawAddrArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/asset/withdraw/address/update [post]
func WithdrawAddressUpdate(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WithdrawAddressUpdate", arg, span)
}

// Withdraw godoc
// @Summary 提币申请
// @Tags 资产-钱包
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocWithdrawArg} true "请求参数"
// @Success 200 {object} model.DocReply "成功"
// @Router /base/asset/withdraw [post]
func Withdraw(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "Withdraw", arg, span)
}
