package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// TradeWalletTransferBill godoc
// @Summary 交易账户划转记录
// @Tags 正向合约-资产流水
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtAccountHistory} "成功"
// @Router /usdt/asset/trade/bill/transfer [post]
func TradeWalletTransferBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TradeWalletTransferBill", arg, span)
}

// TradeWalletProfitBill godoc
// @Summary 交易账户盈亏记录
// @Tags 正向合约-资产流水
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtAccountHistory} "成功"
// @Router /usdt/asset/trade/bill/profit [post]
func TradeWalletProfitBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "TradeWalletProfitBill", arg, span)
}

// UsdtTradeAssetBill godoc
// @Summary 交易账户流水记录
// @Tags 正向合约-资产流水
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtTradeAssetBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtTradeAssetBillReply} "成功"
// @Router /usdt/asset/trade/bill [post]
func UsdtTradeAssetBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UsdtTradeAssetBill", arg, span)
}
