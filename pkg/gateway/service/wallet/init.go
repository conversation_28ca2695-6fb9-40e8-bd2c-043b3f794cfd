package wallet

import (
	"context"

	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/xplugins"
	"bc/libs/xrpc"
	"github.com/smallnest/rpcx/client"
	"github.com/smallnest/rpcx/protocol"
)

type Client struct {
	cli *xrpc.Client
	ctx context.Context
}

var instance = new(Client)

func InitClient(ctx context.Context) {
	option := &client.DefaultOption
	option.SerializeType = protocol.JSON
	co := xrpc.ClientOption{
		BasePath:    define.BasePath,
		ServiceName: define.BaseServerNameWallet,
		NodeName:    conf.LocalName(),
		Discovery:   conf.Discovery(),
		Option:      option,
		PoolSize:    5,
	}
	instance.ctx = ctx
	instance.cli = xrpc.NewXClient(co)
}

func Close() {
	instance.cli.ClosePool()
}

func (c *Client) Call(ctx context.Context, method string, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	select {
	case <-c.ctx.Done():
		return nil, define.ErrMsgServerNotAvailable
	default:
	}

	reply := new(define.Reply)
	err := c.cli.Call(context.WithValue(ctx, xplugins.WrapRequestKey, span), method, arg, reply)
	return reply, err
}
