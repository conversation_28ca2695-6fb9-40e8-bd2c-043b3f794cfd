package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// FollowWalletTransferBill godoc
// @Summary 跟单账户划转记录
// @Tags 正向跟单-资产流水
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocFollowBillReply} "成功"
// @Router /usdt/follow/asset/bill/transfer [post]
func FollowWalletTransferBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowWalletTransferBill", arg, span)
}

// FollowWalletProfitBill godoc
// @Summary 跟单账户盈亏记录
// @Tags 正向跟单-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocFollowBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocFollowBillReply} "成功"
// @Router /usdt/follow/asset/bill/profit [post]
func FollowWalletProfitBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowWalletProfitBill", arg, span)
}

// UsdtFollowTradeAssetBill godoc
// @Summary 跟单账户流水记录
// @Tags 正向跟单-资产流水
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdtFollowTradeAssetBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdtFollowTradeAssetBillReply} "成功"
// @Router /usdt/follow/asset/bill [post]
func UsdtFollowTradeAssetBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UsdtFollowTradeAssetBill", arg, span)
}
