package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// SpotTradeWalletTransferBill godoc
// @Summary 交易账户划转记录
// @Tags 现货-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCoinNameArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotAccountHistoryReply} "成功"
// @Router /spot/asset/trade/bill/transfer [post]
func SpotTradeWalletTransferBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SpotTradeWalletTransferBill", arg, span)
}

// SpotTradeWalletProfitBill godoc
// @Summary 交易账户盈亏记录
// @Tags 现货-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCoinNameArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotAccountHistoryReply} "成功"
// @Router /spot/asset/trade/bill/profit [post]
func SpotTradeWalletProfitBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SpotTradeWalletProfitBill", arg, span)
}

// SpotTradeWalletBill godoc
// @Summary 交易账户流水记录
// @Tags 现货-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocSpotAssetBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocSpotAccountHistoryReply} "成功"
// @Router /spot/asset/trade/bill [post]
func SpotTradeWalletBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SpotTradeWalletBill", arg, span)
}
