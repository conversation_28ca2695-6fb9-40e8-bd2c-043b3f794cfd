package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// ReverseTradeWalletTransferBill godoc
// @Summary 交易账户划转记录
// @Tags 反向合约-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCoinNameArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdAccountHistory} "成功"
// @Router /usd/asset/trade/bill/transfer [post]
func ReverseTradeWalletTransferBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ReverseTradeWalletTransferBill", arg, span)
}

// ReverseTradeWalletProfitBill godoc
// @Summary 交易账户盈亏记录
// @Tags 反向合约-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCoinNameArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdAccountHistory} "成功"
// @Router /usd/asset/trade/bill/profit [post]
func ReverseTradeWalletProfitBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ReverseTradeWalletProfitBill", arg, span)
}

// UsdTradeAssetBill godoc
// @Summary 交易账户流水记录
// @Tags 反向合约-资产流水
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdTradeAssetBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdTradeAssetBillReply} "成功"
// @Router /usd/asset/trade/bill [post]
func UsdTradeAssetBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "UsdTradeAssetBill", arg, span)
}
