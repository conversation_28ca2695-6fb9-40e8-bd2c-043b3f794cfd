package wallet

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// WalletBill godoc
// @Summary 资产账户流水记录
// @Tags 资产-充提账户
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocWalletAssetBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBillV2} "成功"
// @Router /base/asset/bill [post]
func WalletBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletBill", arg, span)
}

// WalletWithdrawBill godoc
// @Summary 资产账户提币记录
// @Tags 资产-充提账户
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBill} "成功"
// @Router /base/asset/bill/withdraw [post]
func WalletWithdrawBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletWithdrawBill", arg, span)
}

// WalletRechargeBill godoc
// @Summary 资产账户充币记录
// @Tags 资产-充提账户
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBill} "成功"
// @Router /base/asset/bill/recharge [post]
func WalletRechargeBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletRechargeBill", arg, span)
}

// WalletTransferBill godoc
// @Summary 资产账户划转记录
// @Tags 资产-充提账户
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBill} "成功"
// @Router /base/asset/bill/transfer [post]
func WalletTransferBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletTransferBill", arg, span)
}

// WalletBrokerageBill godoc
// @Summary 资产账户佣金记录
// @Tags 资产-充提账户
// @Produce json
// @Param data body model.DocArg{data=model.DocBrokerageBillArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBill} "成功"
// @Router /base/asset/bill/brokerage [post]
func WalletBrokerageBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletBrokerageBill", arg, span)
}

// WalletLegalBill godoc
// @Summary 资产账户法币记录
// @Tags 资产-充提账户
// @Accept json
// @Produce json
// @Success 200 {object} model.DocReply{data=[]model.DocUserWalletBill} "成功"
// @Router /base/asset/bill/legal [post]
func WalletLegalBill(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "WalletLegalBill", arg, span)
}
