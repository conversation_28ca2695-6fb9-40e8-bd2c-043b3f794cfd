package reverse_core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

func GetDealerLever(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLever", arg, span)
}
func DealerLeverSwitch(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerLeverSwitch", arg, span)
}
func FollowPositionCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowPositionCurrent", arg, span)
}
func MyDealerList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyDealerList", arg, span)
}
func MyFollowProfitLoss(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyFollowProfitLoss", arg, span)
}
func FollowOrderCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowOrderCurrent", arg, span)
}
func FollowOrderHistory(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowOrderHistory", arg, span)
}
func FollowHeadData(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowHeadData", arg, span)
}
func GetLeaderList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetLeaderList", arg, span)
}
func GetLeaderDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetLeaderDetail", arg, span)
}
func DealerOrderHistory(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerOrderHistory", arg, span)
}
func DealerOrderCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerOrderCurrent", arg, span)
}
func DealerFollowers(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "DealerFollowers", arg, span)
}
func GetFollowSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetFollowSetting", arg, span)
}
func SetFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "SetFollow", arg, span)
}
func ExitFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ExitFollow", arg, span)
}
func RemoveFollow(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "RemoveFollow", arg, span)
}
func MyFollowers(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MyFollowers", arg, span)
}
func GetDealerLeadProfit(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLeadProfit", arg, span)
}
func GetDealerLeadProfitDetail(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetDealerLeadProfitDetail", arg, span)
}
func GetFollowMessage(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "GetFollowMessage", arg, span)
}
func MarkReadMessage(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "MarkReadMessage", arg, span)
}
func LeaderPlace(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "LeaderPlace", arg, span)
}
func FollowCloseOut(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowCloseOut", arg, span)
}
func FollowSetStop(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowSetStop", arg, span)
}
func FollowDealerCurrentSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowDealerCurrentSetting", arg, span)
}
func FollowDealerSetting(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowDealerSetting", arg, span)
}
