package reverse_core

import (
	"bc/libs/define"
	"bc/libs/xplugins"
	"github.com/gin-gonic/gin"
)

// PositionCurrent godoc
// @Summary 当前持仓
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocCoinNameArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdCurrentPosition} "成功"
// @Router /usd/order/hold [post]
func PositionCurrent(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PositionCurrent", arg, span)
}

// EntrustSold godoc
// @Summary 成交记录
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdEntrustSoldArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdSoldTrade} "成功"
// @Router /usd/order/sold [post]
func EntrustSold(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustSold", arg, span)
}

// FollowEntrustSold godoc
// @Summary 成交记录(包含带单跟单以及普通开仓)
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdEntrustSoldArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdAllSoldTrade} "成功"
// @Router /usd/all/order/sold [post]
func FollowEntrustSold(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "FollowEntrustSold", arg, span)
}

// PlanOrder godoc
// @Summary 计划单
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdConditionOrder} "成功"
// @Router /usd/order/plan [post]
func PlanOrder(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "PlanOrder", arg, span)
}

// StopPriceList godoc
// @Summary 止盈止损列表
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdStopPriceListArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdStopPriceListReply} "成功"
// @Router /usd/position/set/list [post]
func StopPriceList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "StopPriceList", arg, span)
}

// ContractStopPriceList godoc
// @Summary 指定合约止盈止损列表
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdStopPriceListReply} "成功"
// @Router /usd/position/stop/list [post]
func ContractStopPriceList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "ContractStopPriceList", arg, span)
}

// EntrustOrderCurrentList godoc
// @Summary 当前委托
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdCurrentEntrustOrder} "成功"
// @Router /usd/order/entrust/current [post]
func EntrustOrderCurrentList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentList", arg, span)
}

// EntrustOrderCurrentPlanList godoc
// @Summary 当前委托(计划单)
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdUserContractArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdCurrentPlanEntrustOrder} "成功"
// @Router /usd/order/entrust/current/plan [post]
func EntrustOrderCurrentPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderCurrentPlanList", arg, span)
}

// EntrustOrderHistoryList godoc
// @Summary 历史委托
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdHistoryEntrustArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdHistoryEntrustOrder} "成功"
// @Router /usd/order/entrust/history [post]
func EntrustOrderHistoryList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryList", arg, span)
}

// EntrustOrderHistoryPlanList godoc
// @Summary 历史委托(计划单)
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocUsdHistoryPlanEntrustArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=[]model.DocUsdHistoryPlanEntrustOrder} "成功"
// @Router /usd/order/entrust/history/plan [post]
func EntrustOrderHistoryPlanList(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryPlanList", arg, span)
}

// EntrustOrderHistoryShareResource godoc
// @Summary 历史委托分享资源
// @Tags 反向合约-订单
// @Accept json
// @Produce json
// @Param data body model.DocArg{data=model.DocIDArg} true "请求参数"
// @Success 200 {object} model.DocReply{data=model.DocUsdHistoryEntrustOrderShareResource} "成功"
// @Router /usd/order/entrust/history/share [post]
func EntrustOrderHistoryShareResource(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
	return instance.Call(c.Request.Context(), "EntrustOrderHistoryShareResource", arg, span)
}
