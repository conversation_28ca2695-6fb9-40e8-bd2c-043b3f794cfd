package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocExchangePreOrderArg struct {
	Base   string          `json:"base" example:"BTC" extensions:"x-order=01"`                            // 兑出币种
	Target string          `json:"target" example:"USDT" extensions:"x-order=02"`                         // 兑入币种
	Amount decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 兑出币种使用数量
}

type DocExchangePreOrder struct {
	PreOrderID    string          `json:"pre_order_id" example:"135125123124123221" extensions:"x-order=01"`             // 预兑换id
	Base          string          `json:"base" example:"BTC" extensions:"x-order=02"`                                    // 兑出币种
	Target        string          `json:"target" example:"USDT" extensions:"x-order=03"`                                 // 兑入币种
	BaseAmount    decimal.Decimal `json:"base_amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`    // 兑出币种数量
	TargetAmount  decimal.Decimal `json:"target_amount" swaggertype:"string" example:"123.123" extensions:"x-order=05"`  // 兑入币种数量
	PriceRate     decimal.Decimal `json:"price_rate" swaggertype:"string" example:"123.123" extensions:"x-order=06"`     // 单价
	Fee           decimal.Decimal `json:"fee" swaggertype:"string" example:"123.123" extensions:"x-order=07"`            // 手续费
	ActualArrival decimal.Decimal `json:"actual_arrival" swaggertype:"string" example:"123.123" extensions:"x-order=08"` // 实际到账数量
}

type DocExchangeActualOrderArg struct {
	PreOrderID string `json:"pre_order_id" example:"124125125125" extensions:"x-order=01"` // 预兑换id
}

type DocExchangeOrderHistoryArg struct {
	State define.ExchangeOrderState `json:"state" swaggertype:"integer" example:"0" extensions:"x-order=01"` // 记录状态 -1-已结束 0-全部 1-未完成 100-已取消 200-已完成
}

type DocExchangeOrderHistory struct {
	FromCoinName   string                    `json:"from_coin_name" example:"BTC" extensions:"x-order=01"`                         // 来源币种名称
	ToCoinName     string                    `json:"to_coin_name" example:"USDT" extensions:"x-order=02"`                          // 目标币种名称
	FromAmount     decimal.Decimal           `json:"from_amount" swaggertype:"string" example:"123.123" extensions:"x-order=03"`   // 来源数量
	ToAmount       decimal.Decimal           `json:"to_amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`     // 目标数量
	Price          decimal.Decimal           `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`         // 单价
	RealAmount     decimal.Decimal           `json:"real_amount" swaggertype:"string" example:"123.123" extensions:"x-order=06"`   // 到账数量
	RefundAmount   decimal.Decimal           `json:"refund_amount" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 退回数量
	Fee            decimal.Decimal           `json:"fee" swaggertype:"string" example:"123.123" extensions:"x-order=08"`           // 手续费
	UpdateTimeUnix int64                     `json:"update_time" example:"1613313532" extensions:"x-order=09"`                     // 时间
	ID             string                    `json:"id" example:"2135325436534342342" extensions:"x-order=10"`                     // 订单号
	State          define.ExchangeOrderState `json:"state" swaggertype:"integer" example:"200" extensions:"x-order=11"`            // 订单状态 0-等待确认 100-已取消 200-已完成
}
