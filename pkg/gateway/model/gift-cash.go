package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocGiftActivityReply struct {
	Id              int                     `json:"id" example:"123" extensions:"x-order=01"`                                               // 赠金活动id
	Title           string                  `json:"title" example:"这是标题" extensions:"x-order=02"`                                           // 活动标题
	Content         string                  `json:"content" example:"这是说明" extensions:"x-order=03"`                                         // 活动说明
	TimeLimit       int                     `json:"time_limit" example:"48" extensions:"x-order=04"`                                        // 时间限制 小时
	CloseCount      int                     `json:"close_count" example:"123" extensions:"x-order=05"`                                      // 平仓次数
	TraderDay       int                     `json:"trader_day" example:"123" extensions:"x-order=06"`                                       // 有效交易天数
	Category        define.GiftActivityType `json:"category" swaggertype:"integer" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=07"` // 活动类型 1-首次充值，2-首次交易，3-新手交易量达标，4-活跃用户，5-充值返利，6-邀请好友
	Partake         define.GiftPartake      `json:"partake" swaggertype:"integer" example:"0" enums:"0,1,2" extensions:"x-order=08"`        // 参与者 0-全部用户 1-非代理用户 2-代理
	JoinState       define.GiftJoinState    `json:"join_state" swaggertype:"integer" example:"3" enums:"1,2,3,4,5" extensions:"x-order=09"` // 参与状态 1-未开始 2-进行中 3-待领取 4-已领取 5-已过期
	AmountLimit     decimal.Decimal         `json:"amount_limit" swaggertype:"string" example:"123.123" extensions:"x-order=10"`            // 有效金额限制
	GiftAmount      decimal.Decimal         `json:"gift_amount" swaggertype:"string" example:"123.123" extensions:"x-order=11"`             // 赠金数量
	FriendsRecharge decimal.Decimal         `json:"friends_recharge" swaggertype:"string" example:"123.123" extensions:"x-order=12"`        // 好友充值数量
	ExceedTime      int64                   `json:"exceed_time" example:"1652321123" extensions:"x-order=13"`                               // 过期时间
	RecordID        string                  `json:"record_id" example:"4219513288182382832" extensions:"x-order=14"`                        // 待领取记录id, 状态为 3-待领取 时有效
}

type DocGiftReceiveArg struct {
	ID string `json:"id" example:"123" extensions:"x-order=01"` // 记录id
}

type DocGiftMyHeadReply struct {
	CurrentGiftCash decimal.Decimal `json:"current_gift_cash" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 当前赠金余额
	TotalReceive    decimal.Decimal `json:"total_receive" swaggertype:"string" example:"123.123" extensions:"x-order=02"`     // 累计领取赠金
	TotalUse        decimal.Decimal `json:"total_use" swaggertype:"string" example:"123.123" extensions:"x-order=03"`         // 累计使用赠金
}

type DocGiftRecordArg struct {
	State define.GiftJoinState `db:"state" swaggertype:"integer" example:"4" extensions:"x-order=01"` // 状态 4-已领取 5-已过期
}

type DocGiftRecordReply struct {
	OrderTime int64           `json:"order_time" example:"1652324522" extensions:"x-order=01"`               // 时间
	Title     string          `json:"title" example:"首充大礼包" extensions:"x-order=02"`                         // 赠金名称
	Amount    decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 金额
}
