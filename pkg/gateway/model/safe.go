package model

import "bc/libs/define"

type DocSafeVerifyCode struct {
	PhoneCode      string `json:"phone_code" example:"135839" extensions:"x-order=95"`                     // 两步验证 短信验证码
	EmailCode      string `json:"email_code" example:"235342" extensions:"x-order=96"`                     // 两步验证 邮箱验证码
	SpareEmailCode string `json:"spare_email_code" example:"123124" extensions:"x-order=97"`               // 两步验证 备用邮箱验证码
	TotpCode       string `json:"totp_code" example:"325325" extensions:"x-order=98"`                      // 两步验证 令牌验证码
	FundPassword   string `json:"fund_password" example:"12435fskji3fiskdrjk32==" extensions:"x-order=99"` // 两步验证 资金密码
}

type YiDunRequest struct {
	CaptchaID string `json:"captcha_id" example:"fdasfiejfaskdgjkd" extensions:"x-order=98"`   // 验证码id
	Validate  string `json:"validate" example:"airjeifjksdfjaliefds=" extensions:"x-order=99"` // 提交二次校验的验证数据
}

type DocSafeModifyAccountArg struct {
	NewAccount      string `json:"new_account" example:"***********" extensions:"x-order=01"` // 新账号
	AreaCode        string `json:"area_code" example:"86" extensions:"x-order=02"`            // 手机区号
	CountryCode     string `json:"country_code" example:"CN" extensions:"x-order=03"`         // 国家代码
	Code            string `json:"code" example:"151235" extensions:"x-order=04"`             // 验证码
	VerifySwitchIOS uint8  `json:"login_verify" example:"1" extensions:"x-order=05"`          // 登录验证开关 0-关闭 1-开启
	DocSafeVerifyCode
}

type DocSafeModifySpareEmailArg struct {
	NewEmail string `json:"new_email" example:"<EMAIL>" extensions:"x-order=01"` // 新备用邮箱
	Code     string `json:"code" example:"1352111" extensions:"x-order=02"`               // 验证码
	DocSafeVerifyCode
}

type DocSafeModifyPasswordArg struct {
	NewPassword string `json:"new_password" example:"sdfiajei==" extensions:"x-order=01"` // 新密码
	VerifyType  uint8  `json:"verify_type" example:"0" extensions:"x-order=0"`            // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
	DocSafeVerifyCode
}

type DocSafeModifyTradeVerifyArg struct {
	VerifyType uint8 `json:"verify_type" example:"0" extensions:"x-order=01"` // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
	DocSafeVerifyCode
}

type DocSafeVerifyPWD struct {
	Password string `json:"password" example:"sdfiajei==" extensions:"x-order=01"` // 密码
}

type DocSafeTotpSecret struct {
	Secret string `json:"secret" example:"NFUIENKFSIU3234" extensions:"x-order=01"` // 验证器私钥
}

type DocSafeModifyTotpSecretArg struct {
	Code            string `json:"code" example:"284721" extensions:"x-order=01"`    // 验证码
	VerifySwitchIOS uint8  `json:"login_verify" example:"0" extensions:"x-order=02"` // 登录验证开关 0-关闭 1-开启
	DocSafeVerifyCode
}

type DocSafeModifyLoginVerifyModeArg struct {
	VerifyPhoneIOS uint8 `json:"verify_phone" example:"0" extensions:"x-order=01"` // 是否开启验证手机 0-关 1-开
	VerifyEmailIOS uint8 `json:"verify_email" example:"1" extensions:"x-order=02"` // 是否开启验证邮箱 0-关 1-开
	VerifyTotpIOS  uint8 `json:"verify_totp" example:"0" extensions:"x-order=03"`  // 是否开启验证令牌 0-关 1-开
	DocSafeVerifyCode
}

type DocSafeUserLogsReply struct {
	LoginList []DocSafeUserLoginLogsSubData `json:"login_list" extensions:"x-order=01"` // 登录活动数据列表
	SafeList  []DocSafeUserLogsSubData      `json:"safe_list" extensions:"x-order=02"`  // 安全活动数据列表
}

type DocSafeUserLoginLogsSubData struct {
	DocSafeUserLogsSubData

	IsOnline bool `json:"is_online" example:"true" extensions:"x-order=99"` // 是否在线(仅是查询登录日志的时候有效)
}

type DocSafeUserLogsSubData struct {
	DeviceName string            `json:"device_name" example:"IPhone X" extensions:"x-order=01"`                                                 // 设备名
	IPAddress  string            `json:"ip_address" example:"***************" extensions:"x-order=02"`                                           // IP地址
	IPLocation string            `json:"ip_location" example:"北京-北京" extensions:"x-order=03"`                                                    // IP地区
	OPType     define.UserOPType `json:"op_type" swaggertype:"integer" example:"0" enums:"0,2,3,4,5,6,9,10,11,12,13,17" extensions:"x-order=04"` // 操作类型 0-登录 2-找回登录密码 3-设置登录密码 4-修改登录密码 5-修改资金密码 6-修改手机号 9-设置资金密码 10-设置谷歌验证器 11-修改谷歌验证器 12-设置手机号 13-设置邮箱 17-设置备用邮箱
	CreateTime int64             `json:"create_time" example:"1645255643" extensions:"x-order=05"`                                               // 操作时间戳
}
