package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocAccountArg struct {
	Account string `json:"account" example:"***********" extensions:"x-order=01"` // 账号
}

type DocCheckReply struct {
	Status bool `json:"status" example:"false" extensions:"x-order=01"` // 是否可注册 true-可以注册 false-已存在
}

type DocRegisterArg struct {
	Account         string `json:"account" example:"***********" extensions:"x-order=01"`                             // 账号
	InviteCode      string `json:"invite_code" example:"Ug2jn4" extensions:"x-order=02"`                              // 邀请码
	ChannelID       string `json:"channel_id" example:"21f08867-4944-4abf-88f1-1f4b9f2a927d" extensions:"x-order=03"` // 渠道id(注册奖励活动)
	Code            string `json:"code" example:"139492" extensions:"x-order=04"`                                     // 短信验证码
	AreaCode        string `json:"areacode" example:"86" extensions:"x-order=05"`                                     // 区号
	CountryCode     string `json:"country_code" example:"CN" extensions:"x-order=06"`                                 // 国家区号
	Password        string `json:"password" example:"12432421312fiq3jdij==" extensions:"x-order=07"`                  // 登录密码
	QuicklyRegister uint8  `json:"quickly_register" example:"0" enums:"0,1" extensions:"x-order=08"`                  // 是否快速注册 0-否 1-是(不强制设置密码)
}

type RegisterReply struct {
	DocUser
	Token              string          `json:"token,omitempty" example:"sdfin3i2j3fskd==" extensions:"x-order=94"`    // token
	Reward             decimal.Decimal `json:"reward" swaggertype:"string" example:"123.123" extensions:"x-order=95"` // 奖励
	HasPassword        bool            `json:"has_password" example:"true" extensions:"x-order=96"`                   // 是否已经设置了登录密码
	HasFundPassword    bool            `json:"has_fund_password" example:"true" extensions:"x-order=97"`              // 是否已经设置了资金密码
	HasTotpBind        bool            `json:"has_totp_bind" example:"true" extensions:"x-order=98"`                  // 是否已经绑定了验证器
	LegalWithdrawLimit bool            `json:"legal_withdraw_limit" example:"true" extensions:"x-order=99"`           // 是否因入金限制了提币
}

type DocUser struct {
	UserID              int64                  `json:"user_id" example:"123532523523421" extensions:"x-order=01"`              // 用户id
	UserName            string                 `json:"user_name" example:"17211112222" extensions:"x-order=02"`                // 用户名
	Nickname            string                 `json:"nickname" example:"fadif" extensions:"x-order=03"`                       // 中文昵称
	NicknameEn          string                 `json:"nickname_en" example:"sdfdsaf" extensions:"x-order=04"`                  // 英文昵称
	Introduce           string                 `json:"introduce" example:"sdfad" extensions:"x-order=05"`                      // 中文介绍
	IntroduceEn         string                 `json:"introduce_en" example:"sdfadf" extensions:"x-order=06"`                  // 英文介绍
	Avatar              string                 `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=07"`        // 头像
	InviteCode          string                 `json:"invite_code" example:"Ings2d" extensions:"x-order=08"`                   // 邀请码
	EnableLogin         bool                   `json:"enable_login" example:"true" extensions:"x-order=09"`                    // 是否可登录
	EnableWithdraw      bool                   `json:"enable_withdraw" example:"true" extensions:"x-order=10"`                 // 是否可提现
	EnableTrade         bool                   `json:"enable_trade" example:"true" extensions:"x-order=11"`                    // 是否可交易
	Phone               string                 `json:"phone" example:"17211112222" extensions:"x-order=12"`                    // 手机号
	Email               string                 `json:"email" example:"<EMAIL>" extensions:"x-order=13"`                  // 邮箱
	SpareEmail          string                 `json:"spare_email" example:"<EMAIL>" extensions:"x-order=14"`        // 备用邮箱
	Verify              define.UserVerifyState `json:"verify" swaggertype:"integer" example:"0" extensions:"x-order=15"`       // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
	AreaCode            string                 `json:"area_code" example:"86" extensions:"x-order=16"`                         // 地区区号
	CountryCode         string                 `json:"country_code" example:"CN" extensions:"x-order=17"`                      // 国家代码
	DealerState         define.DealerState     `json:"dealer_state" swaggertype:"integer" example:"1" extensions:"x-order=18"` // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
	Rebate              decimal.Decimal        `json:"rebate" swaggertype:"string" example:"0.123" extensions:"x-order=19"`    // 分佣比例
	FollowApproved      bool                   `json:"follow_approved" example:"true" extensions:"x-order=20"`                 // 是否已经接受了跟单协议
	PlatformID          int                    `json:"platform_id" example:"1" extensions:"x-order=21"`                        // 平台id
	Content             string                 `json:"content" example:"dafds" extensions:"x-order=22"`                        // 备注
	IsAgent             bool                   `json:"is_agent" example:"true" extensions:"x-order=23"`                        // 是否是代理
	WithdrawVerify      int                    `json:"withdraw_verify" example:"1" extensions:"x-order=24"`                    // 提币活体验证状态
	TradeApproved       bool                   `json:"trade_approved" example:"true" extensions:"x-order=25"`                  // 是否已同意交易协议
	EnableSimulator     bool                   `json:"enable_simulator" example:"true" extensions:"x-order=26"`                // 是否开启模拟盘功能
	TradeConfirm        bool                   `json:"trade_confirm" example:"true" extensions:"x-order=27"`                   // 是否开启交易二次确认
	LoginVerifyPhone    bool                   `json:"login_verify_phone" example:"true" extensions:"x-order=28"`              // 登录两步验证手机号开关
	LoginVerifyEmail    bool                   `json:"login_verify_email" example:"true" extensions:"x-order=29"`              // 登录两步验证邮箱开关
	LoginVerifyTotp     bool                   `json:"login_verify_totp" example:"true" extensions:"x-order=30"`               // 登录两步验证验证器开关
	TradeVerifyFund     uint8                  `json:"trade_verify_fund" example:"1" extensions:"x-order=31"`                  // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
	ChangeStyle         int                    `json:"change_style" example:"0" extensions:"x-order=32"`                       // 涨跌样式 0-绿涨红跌 1-红涨绿跌
	ProfitStyle         int                    `json:"profit_style" example:"0" extensions:"x-order=33"`                       // 盈亏计算方式 0-使用标记价格 1-使用成交价格
	IsOpenAPI           bool                   `json:"is_open_api" example:"true" extensions:"x-order=34"`                     // 是否有权限开通api
	ShowAgentRatio      bool                   `json:"show_agent_ratio" example:"true" extensions:"x-order=35"`                // 是否显示代理返佣比例
	ISShowDealer        bool                   `json:"is_show_dealer" example:"true" extensions:"x-order=36"`                  // 是否显示申请交易员
	IsShowDealerSetting bool                   `json:"is_show_dealer_setting" example:"true" extensions:"x-order=37"`          // 是否显示交易员设置
	FastCloseoutConfirm bool                   `json:"fast_closeout_confirm" example:"true" extensions:"x-order=38"`           // 是否开启极速平仓二次确认
	ChannelID           int                    `json:"channel_id" example:"1" extensions:"x-order=39"`                         // 渠道id
	IsSplitPosition     bool                   `json:"is_split_position" example:"false" extensions:"x-order=40"`              // 是否分仓模式
}

type DocLoginArg struct {
	Account  string `json:"account" example:"***********" extensions:"x-order=01"`            // 账号
	Code     string `json:"code" example:"102334" extensions:"x-order=02"`                    // 短信验证码
	Password string `json:"password" example:"dfjija3i2jfiisdi4jf==" extensions:"x-order=03"` // 登录密码
	DocSafeVerifyCode
}

type DocLoginReply struct {
	DocUser
	Token              string `json:"token,omitempty" example:"sdfin3i2j3fskd==" extensions:"x-order=95"` // token
	HasPassword        bool   `json:"has_password" example:"true" extensions:"x-order=96"`                // 是否已经设置了登录密码
	HasFundPassword    bool   `json:"has_fund_password" example:"true" extensions:"x-order=97"`           // 是否已经设置了资金密码
	HasTotpBind        bool   `json:"has_totp_bind" example:"true" extensions:"x-order=98"`               // 是否已经绑定了验证器
	LegalWithdrawLimit bool   `json:"legal_withdraw_limit" example:"true" extensions:"x-order=99"`        // 是否因入金限制了提币
}

type DocForgetPwdArg struct {
	Account     string `json:"account" example:"***********" extensions:"x-order=01"`                // 账号
	NewPassword string `json:"new_password" example:"dfjija3i2jfiisdi4jf==" extensions:"x-order=02"` // 新密码
}

type DocApiUserInfo struct {
	DocUser
	HasPassword        bool `json:"has_password" example:"true" extensions:"x-order=96"`         // 是否已经设置了登录密码
	HasFundPassword    bool `json:"has_fund_password" example:"true" extensions:"x-order=97"`    // 是否已经设置了资金密码
	HasTotpBind        bool `json:"has_totp_bind" example:"true" extensions:"x-order=98"`        // 是否已经绑定了验证器
	LegalWithdrawLimit bool `json:"legal_withdraw_limit" example:"true" extensions:"x-order=99"` // 是否因入金限制了提币
}

type DocModifyUserInfoArg struct {
	Nickname    string `json:"nickname" example:"dfigjisrjg" extensions:"x-order=01"`           // 中文昵称
	NicknameEn  string `json:"nickname_en" example:"dfigjisrjg" extensions:"x-order=02"`        // 英文昵称
	Introduce   string `json:"introduce" example:"dfigjisrjg" extensions:"x-order=03"`          // 中文介绍
	IntroduceEn string `json:"introduce_en" example:"dfigjisrjg" extensions:"x-order=04"`       // 英文介绍
	Avatar      string `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=05"` // 头像
}

type DocUserSwitchArg struct {
	Action  string `json:"action" example:"trade_confirm" enums:"trade_confirm,fast_closeout_confirm" extensions:"x-order=01"` // 功能key trade_confirm-是否开启交易二次确认 fast_closeout_confirm-是否开启一键平仓二次确认 is_split_position-是否开启分仓模式
	Open    bool   `json:"open" example:"true" extensions:"x-order=02"`                                                        // 是否开启
	OpenFix int    `json:"open_fix" example:"1" enums:"0,1" extensions:"x-order=03"`                                           // 是否开启,不支持bool类型的客户端用 0-关闭 1-开启
}

type DocUserOptionArg struct {
	Action string `json:"action" example:"change_style" enums:"change_style,profit_style,is_split_position" extensions:"x-order=01"` // 功能key change_style-涨跌配色(0-绿涨,1-红涨) profit_style-盈亏计算基准(0-使用标记价格,1-使用成交价格)
	Option int    `json:"option" example:"100" extensions:"x-order=02"`                                                              // 选项值
}

type DocApiUserInviteInfo struct {
	RebateRate   decimal.Decimal `json:"rebate_rate" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 返佣比例
	LastReward   string          `json:"last_reward" example:"123.123" extensions:"x-order=02"`                      // 昨日返佣
	TotalReward  string          `json:"total_reward" example:"123.123" extensions:"x-order=03"`                     // 累计返佣
	LastInvited  int             `json:"last_invited" example:"123" extensions:"x-order=04"`                         // 今日邀请
	TotalInvited int             `json:"total_invited" example:"123" extensions:"x-order=05"`                        // 累计邀请
}

type DocInviteListArg struct {
	Extra    string `json:"extra" example:"Ntx2kd" extensions:"x-order=01"` // 子用户邀请码,用于查询二级用户邀请列表
	Page     int    `json:"page" example:"0" extensions:"x-order=02"`       // 页码从0开始
	PageSize int    `json:"page_size" example:"10" extensions:"x-order=03"` // 单页数据量
}

type DocInvitedInfo struct {
	HasInvite   bool   `json:"has_invite" example:"false" extensions:"x-order=01"`         // 是否有下级
	UserID      int64  `json:"user_id" example:"1359129492132951" extensions:"x-order=02"` // 被邀请者
	Code        string `json:"code" example:"Ndi2m6" extensions:"x-order=03"`              // 用户的邀请码
	CreatedTime int64  `json:"created_time" example:"1652235523" extensions:"x-order=04"`  // 创建时间
}

type DocUserAPIReply struct {
	ID         int    `json:"id" example:"123512412" extensions:"x-order=01"`                   // 记录id
	AppName    string `json:"app_name" example:"dfaidsf" extensions:"x-order=02"`               // 备注
	AppKey     string `json:"app_key" example:"fdasgasdga" extensions:"x-order=03"`             // 公钥
	AppSecret  string `json:"app_secret" example:"dgasdgadsf" extensions:"x-order=04"`          // 私钥
	AuthIPs    string `json:"auth_ips" example:"127.0.0.1,***********" extensions:"x-order=05"` // 绑定ip
	CreateTime int64  `json:"create_time" example:"1653453522" extensions:"x-order=06"`         // 创建时间
	State      int    `json:"state" example:"0" enums:"0,1,2" extensions:"x-order=07"`          // 状态 0-正常 1-删除 2-失效(该状态为过期后赋值,库里没有)
}

type DocUserApiArg struct {
	ID     int    `json:"id" example:"*********" extensions:"x-order=01"`                // 记录id
	Remark string `json:"remark" example:"dfasdgf" extensions:"x-order=02"`              // 备注名
	IPList string `json:"ip_list" example:"***********,*******" extensions:"x-order=03"` // 绑定ip列表,以','分隔
	DocSafeVerifyCode
}

type DocAuthCodeArg struct {
	Account  string `json:"account" example:"***********" extensions:"x-order=01"` // 账号
	Mode     uint8  `json:"mode" example:"0" extensions:"x-order=02"`              // 验证码类型 0-常规请求 1-忘记密码 2-用户注册 3-修改手机号 4-新绑定手机号 5-修改邮箱 6-新绑定邮箱 7-重置登录密码 8-重置资金密码 9-安全验证 10-绑定备用邮箱
	AreaCode string `json:"areacode" example:"86" extensions:"x-order=03"`         // 区号
	YiDunRequest
}

type DocCheckCodeArg struct {
	Account string `json:"account" example:"***********" extensions:"x-order=01"` // 账号
	Mode    uint8  `json:"mode" example:"0" extensions:"x-order=02"`              // 验证码类型 0-常规请求 1-忘记密码 2-用户注册 3-修改手机号 4-新绑定手机号 5-修改邮箱 6-新绑定邮箱 7-重置登录密码 8-重置资金密码 9-安全验证 10-绑定备用邮箱
	Code    string `json:"code" example:"86" extensions:"x-order=03"`             // 区号
}

type DocCountryAreacode struct {
	ID             int64  `json:"id" example:"26" extensions:"x-order=01"`              // 数据id
	CountryEn      string `json:"country_en" example:"Bhutan" extensions:"x-order=02"`  // 英文
	CountryCn      string `json:"country_cn" example:"不丹" extensions:"x-order=03"`      // 中文
	CountryTw      string `json:"country_tw" example:"不丹" extensions:"x-order=04"`      // 繁体
	CountryCode    string `json:"country_code" example:"975" extensions:"x-order=05"`   // 编号
	CountryEncrypt string `json:"country_encrypt" example:"BT" extensions:"x-order=06"` // 国家代码
}

type DocContactUsArg struct {
	ContactUS
	YiDunRequest
}

type ContactUS struct {
	RealName string `json:"real_name" example:"dgasdga" extensions:"x-order=01"`     // 姓名
	Mobile   string `json:"mobile" example:"18521312321" extensions:"x-order=02"`    // 手机号
	Email    string `json:"email" example:"<EMAIL>" extensions:"x-order=03"`   // 邮箱
	Content  string `json:"content" example:"agfeufhuwhu2d" extensions:"x-order=04"` // 内容
}
