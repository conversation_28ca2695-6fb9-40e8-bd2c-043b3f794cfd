package model

import (
	"bc/libs/define"
)

type DocClientErrorArg struct {
	List []DocClientErrorLog `json:"list" extensions:"x-order=01"` // 错误列表
}

type DocClientErrorLog struct {
	ErrorType uint16 `json:"error_type" example:"1" extensions:"x-order=01"`          // 错误类型 1-本地异常 2-api response 3-Ws response ... 其它http错误码
	ErrorMsg  string `json:"error_msg" example:"panic...." extensions:"x-order=02"`   // 报错内容
	ErrorTime int64  `json:"error_time" example:"1524234542" extensions:"x-order=03"` // 错误产生时间
}

type DocAppDownloadSubmitArg struct {
	Os           define.OsType `json:"os" swaggertype:"integer" example:"1" extensions:"x-order=01"` // 设备类型（1: android 2: iOS)
	DownloadMode int           `json:"download_mode" example:"1" extensions:"x-order=02"`            // 下载模式 1-极速下载 2-本地下载 3-本地更新 4-备用下载
	AppVersion   string        `json:"app_version" example:"1.2.3" extensions:"x-order=03"`          // 下载版本号
}

type DocCollectDomainDelayArg struct {
	List []DocCollectDomainDelaySubArg `json:"list" extensions:"x-order=01"` // 列表
}

type DocCollectDomainDelaySubArg struct {
	Domain string `json:"domain" example:"api.a.cn" extensions:"x-order=01"` // 域名
	Delay  uint   `json:"delay" example:"123" extensions:"x-order=02"`       // 请求耗时毫秒数(超时传999999)
}
