package model

import (
	"github.com/shopspring/decimal"
)

type DocSpotSymbolListReply struct {
	List []DocMarketSymbolList `json:"list" extensions:"x-order=01"` // 列表
}

type DocMarketSymbolList struct {
	MarketName string              `json:"market_name" example:"USDT" extensions:"x-order=01"` // 市场名
	Symbols    []DocSpotSymbolList `json:"symbols" extensions:"x-order=02"`                    // 交易对
}

type DocSpotSymbolList struct {
	Symbol           string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                                // 交易对
	Icon             string          `json:"icon" example:"https://www.a.cn/btc.png" extensions:"x-order=02"`                  // 图标
	CoinName         string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                                  // 币种名称
	MarketName       string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                               // 结算币种名
	Digit            int32           `json:"digit" example:"1" extensions:"x-order=05"`                                        // 精度
	VolumeDigit      int32           `json:"volume_digit" example:"4" extensions:"x-order=06"`                                 // 交易对数量精度
	Price            decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=07"`             // 成交价格
	PriceCNY         decimal.Decimal `json:"price_cny" swaggertype:"string" example:"123.123" extensions:"x-order=08"`         // 成交价格(人民币)
	ChangeRatio      decimal.Decimal `json:"change_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=09"`      // 涨跌幅百分比
	Change           decimal.Decimal `json:"change" swaggertype:"string" example:"123.123" extensions:"x-order=10"`            // 涨跌
	TradeVolume      decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=11"`      // 成交量
	TradeValue       decimal.Decimal `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=12"`       // 成交额
	FeeMaker         decimal.Decimal `json:"fee_maker" swaggertype:"string" example:"123.123" extensions:"x-order=13"`         // maker手续费率
	FeeTaker         decimal.Decimal `json:"fee_taker" swaggertype:"string" example:"123.123" extensions:"x-order=14"`         // 手续费
	IOCLimit         decimal.Decimal `json:"ioc_limit" swaggertype:"string" example:"123.123" extensions:"x-order=15"`         // ioc委托限价
	IOCBuyLimit      decimal.Decimal `json:"ioc_buy_limit" swaggertype:"string" example:"123.123" extensions:"x-order=16"`     // ioc买方价格限制
	IOCSellLimit     decimal.Decimal `json:"ioc_sell_limit" swaggertype:"string" example:"123.123" extensions:"x-order=17"`    // ioc卖方价格限制
	LockFloatFactor  decimal.Decimal `json:"lock_float_factor" swaggertype:"string" example:"123.123" extensions:"x-order=18"` // 资产冻结上浮系数
	RedundancyFactor decimal.Decimal `json:"redundancy_factor" swaggertype:"string" example:"123.123" extensions:"x-order=19"` // 市价冗余系数
	HighPrice        decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=20"`        // 最高价
	LowPrice         decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=21"`         // 最低价
	Delisted         bool            `json:"delisted" example:"false" extensions:"x-order=22"`                                 // 是否已下架
	IsMaintenance    bool            `json:"is_maintenance" example:"false" extensions:"x-order=23"`                           // 是否系统维护
}

type DocSpotSymbolArg struct {
	Symbol string `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"` // 交易对
}

type DocSpotSymbolDetail struct {
	Symbol           string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                               // 交易对名称
	Icon             string          `json:"icon" example:"https://www.a.cn/btc.png" extensions:"x-order=02"`                 // 图标
	CoinName         string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                                 // 结算币种名
	MarketName       string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                              // 计价币种名
	Digit            int32           `json:"digit" example:"5" extensions:"x-order=05"`                                       // 价格精度
	VolumeDigit      int32           `json:"volume_digit" example:"3" extensions:"x-order=06"`                                // 数量精度
	Step             decimal.Decimal `json:"step" swaggertype:"string" example:"0.001" extensions:"x-order=07"`               // 下单价格步长
	MinOrderVolume   decimal.Decimal `json:"min_order_volume" swaggertype:"string" example:"123.123" extensions:"x-order=08"` // 单笔最小下单量 0为不限制
	MaxOrderVolume   decimal.Decimal `json:"max_order_volume" swaggertype:"string" example:"123.123" extensions:"x-order=09"` // 单笔最大下单量 0为不限制
	MinOrderMoney    decimal.Decimal `json:"min_order_money" swaggertype:"string" example:"123.123" extensions:"x-order=10"`  // 单笔最小下单金额 0为不限制
	MaxOrderMoney    decimal.Decimal `json:"max_order_money" swaggertype:"string" example:"123.123" extensions:"x-order=11"`  // 单笔最大下单金额 0为不限制
	FeeMaker         decimal.Decimal `json:"fee_maker" swaggertype:"string" example:"0.005" extensions:"x-order=12"`          // maker手续费率
	FeeTaker         decimal.Decimal `json:"fee_taker" swaggertype:"string" example:"0.007" extensions:"x-order=13"`          // taker手续费率
	Price            decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=14"`            // 成交价格
	PriceCNY         decimal.Decimal `json:"price_cny" swaggertype:"string" example:"123.123" extensions:"x-order=15"`        // 成交价格(人民币)
	BuyPrice         decimal.Decimal `json:"buy_price" swaggertype:"string" example:"123.123" extensions:"x-order=16"`        // 买入指数价格
	SellPrice        decimal.Decimal `json:"sell_price" swaggertype:"string" example:"123.123" extensions:"x-order=17"`       // 卖出指数价格
	HighPrice        decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=18"`       // 最高价
	LowPrice         decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=19"`        // 最低价
	ChangeDaily      decimal.Decimal `json:"change_daily" swaggertype:"string" example:"123.123" extensions:"x-order=20"`     // 当日涨跌幅
	Change24h        decimal.Decimal `json:"change_24h" swaggertype:"string" example:"123.123" extensions:"x-order=21"`       // 24小时涨跌幅
	Change8h         decimal.Decimal `json:"change_8h" swaggertype:"string" example:"123.123" extensions:"x-order=22"`        // 8小时涨跌幅
	Change4h         decimal.Decimal `json:"change_4h" swaggertype:"string" example:"123.123" extensions:"x-order=23"`        // 4小时涨跌幅
	Change2h         decimal.Decimal `json:"change_2h" swaggertype:"string" example:"123.123" extensions:"x-order=24"`        // 2小时涨跌幅
	Change1h         decimal.Decimal `json:"change_1h" swaggertype:"string" example:"123.123" extensions:"x-order=25"`        // 1小时涨跌幅
	Change30m        decimal.Decimal `json:"change_30m" swaggertype:"string" example:"123.123" extensions:"x-order=26"`       // 30分钟涨跌幅
	Change10m        decimal.Decimal `json:"change_10m" swaggertype:"string" example:"123.123" extensions:"x-order=27"`       // 10分钟涨跌幅
	ChangeVolume     decimal.Decimal `json:"change_volume" swaggertype:"string" example:"123.123" extensions:"x-order=28"`    // 当日涨跌量
	Trade24h         decimal.Decimal `json:"trade_24h" swaggertype:"string" example:"123.123" extensions:"x-order=29"`        // 24小时成交量
	TradeValue24h    decimal.Decimal `json:"trade_value_24h" swaggertype:"string" example:"123.123" extensions:"x-order=30"`  // 24小时成交额
	IOCLimit         decimal.Decimal `json:"ioc_limit" swaggertype:"string" example:"123.123" extensions:"x-order=31"`        // ioc委托限价
	IOCBuyLimit      decimal.Decimal `json:"ioc_buy_limit" swaggertype:"string" example:"123.123" extensions:"x-order=32"`    // ioc买方价格限制
	IOCSellLimit     decimal.Decimal `json:"ioc_sell_limit" swaggertype:"string" example:"123.123" extensions:"x-order=33"`   // ioc卖方价格限制
	LockFloatFactor  decimal.Decimal `json:"lock_float_factor" swaggertype:"string" example:"0.005" extensions:"x-order=34"`  // 资产冻结上浮系数
	RedundancyFactor decimal.Decimal `json:"redundancy_factor" swaggertype:"string" example:"0.005" extensions:"x-order=35"`  // 市价冗余系数
	Delisted         bool            `json:"delisted" example:"false" extensions:"x-order=36"`                                // 是否下架 false-上架,true-下架
	IsMaintenance    bool            `json:"is_maintenance" example:"false" extensions:"x-order=37"`                          // 是否系统维护
}

type DocSpotKLineArg struct {
	Symbol    string `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                                         // 交易对
	Duration  string `json:"duration" example:"1m" enums:"1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w" extensions:"x-order=02"` // k线时间段 1m-1分钟线,5m-5分钟线,15m-15分钟线,30m-30分钟线,1h-1小时线,2h-2小时线,4h-4小时线,6h-6小时线,12h-12小时线,1d-日线,1w-周线
	StartTime int64  `json:"start_time" example:"0" extensions:"x-order=03"`                                            // 开始时间
	EndTime   int64  `json:"end_time" example:"1663344334" extensions:"x-order=04"`                                     // 结束时间
}

type DocSpotKLineData struct {
	Symbol     string         `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                                         // 交易对
	Duration   string         `json:"duration" example:"1m" enums:"1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w" extensions:"x-order=02"` // k线时间段 1m-1分钟线,5m-5分钟线,15m-15分钟线,30m-30分钟线,1h-1小时线,2h-2小时线,4h-4小时线,6h-6小时线,12h-12小时线,1d-日线,1w-周线
	ServerTime int64          `json:"server_time" example:"1661261262" extensions:"x-order=03"`                                  // 服务器时间
	List       []DocSpotKLine `json:"list" extensions:"x-order=04"`                                                              // k线数据
}

type DocSpotKLine struct {
	ID         int64           `json:"id" example:"125135323432423" extensions:"x-order=01"`                       // 记录id
	Symbol     string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                          // 交易对名称
	StartTime  int64           `json:"start_time" example:"1661261262" extensions:"x-order=03"`                    // 开始时间
	EndTime    int64           `json:"end_time" example:"1661261262" extensions:"x-order=04"`                      // 结束时间
	OpenPrice  decimal.Decimal `json:"open_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`  // 开盘价
	ClosePrice decimal.Decimal `json:"close_price" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 收盘价
	HighPrice  decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=07"`  // 最高价
	LowPrice   decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=08"`   // 最低价
	Volume     decimal.Decimal `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=09"`      // 成交数量
	TradeValue decimal.Decimal `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=10"` // 成交额
	Ts         int64           `json:"ts" example:"1661261262" extensions:"x-order=11"`                            // 更新时间
}

type DocSpotSummary struct {
	Trade24HValue decimal.Decimal `json:"trade_24h_value" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 24小时成交额
}

type DocSymbolMarketReply struct {
	List []DocSymbolMarket `json:"list" extensions:"x-order=01"` // 数据列表
}

type DocSymbolMarket struct {
	Symbol      string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                           // 交易对
	Price       decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=02"`        // 最新价
	ChangeRatio decimal.Decimal `json:"change_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 涨跌幅
	HighPrice   decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=04"`   // 最高价
	LowPrice    decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`    // 最低价
	TradeVolume decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 成交量
}
