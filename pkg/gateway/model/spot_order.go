package model

import (
	"github.com/shopspring/decimal"
)

type DocSpotCurrentEntrustOrderReply struct {
	List []DocSpotCurrentEntrustOrder `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotCurrentEntrustOrder struct {
	OrderID       string          `json:"order_id" example:"125123124213" extensions:"x-order=01"`                       // 订单id
	Symbol        string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                             // 交易对
	CoinName      string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                               // 币种名
	MarketName    string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                            // 市场名
	Side          string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 买卖方向 B-买 S-卖
	Mode          int             `json:"mode" example:"0" enums:"0,1,2,3" extensions:"x-order=06"`                      // 委托模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
	State         int             `json:"state" example:"1" enums:"1,100" extensions:"x-order=07"`                       // 成交状态 1-等待成交 100-部分成交
	EntrustType   int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=08"`                  // 委托类型 0-市价 1-限价
	EntrustPrice  decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=09"`  // 委托价格
	EntrustValue  decimal.Decimal `json:"entrust_value" swaggertype:"string" example:"123.123" extensions:"x-order=10"`  // 委托总额
	EntrustVolume decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 委托数量
	TradeVolume   decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=12"`   // 成交数量
	TradeValue    decimal.Decimal `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=13"`    // 成交额
	CreateTime    int64           `json:"create_time" example:"1652312233" extensions:"x-order=14"`                      // 创建时间
	UpdateTime    int64           `json:"update_time" example:"1652312233" extensions:"x-order=15"`                      // 更新时间
}

type DocSpotCurrentPlanEntrustOrderReply struct {
	List []DocSpotCurrentPlanEntrustOrder `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotCurrentPlanEntrustOrder struct {
	OrderID       string          `json:"order_id" example:"1258238583284" extensions:"x-order=01"`                      // 订单id
	Symbol        string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                             // 交易对
	CoinName      string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                               // 币种名
	MarketName    string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                            // 市场名
	Side          string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 买卖方向 B-买 S-卖
	State         int             `json:"state" example:"1" enums:"1,2,3" extensions:"x-order=06"`                       // 状态 0-已撤销 1-未触发 2-已触发 3-触发失败
	Condition     int             `json:"condition" example:"1" enums:"1,2" extensions:"x-order=07"`                     // 触发条件 1 >= 2 <=
	EntrustMode   int             `json:"entrust_mode" example:"0" enums:"0,1,2,3" extensions:"x-order=08"`              // 执行类型 0-默认 1-对手价 2-最优3挡 3-最优5挡
	EntrustType   int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=09"`                  // 委托类型 0-市价 1-限价
	TriggerPrice  decimal.Decimal `json:"trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=10"`  // 触发价格
	EntrustPrice  decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=11"`  // 执行价格
	EntrustValue  decimal.Decimal `json:"entrust_value" swaggertype:"string" example:"123.123" extensions:"x-order=12"`  // 委托总额
	EntrustVolume decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=13"` // 委托数量
	CreateTime    int64           `json:"create_time" example:"1652321123" extensions:"x-order=14"`                      // 创建时间
}

type DocSpotHistoryEntrustOrderReply struct {
	List []DocSpotHistoryEntrustOrder `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotHistoryEntrustOrder struct {
	OrderID       string          `json:"order_id" example:"125123124213" extensions:"x-order=01"`                       // 订单id
	Symbol        string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                             // 交易对
	CoinName      string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                               // 币种名
	MarketName    string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                            // 市场名
	Side          string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 买卖方向 B-买 S-卖
	Mode          int             `json:"mode" example:"0" enums:"0,1,2,3" extensions:"x-order=06"`                      // 委托模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
	State         int             `json:"state" example:"1" enums:"200,201,202" extensions:"x-order=07"`                 // 成交状态 200-全部成交 201-未成已撤 202-部分成交已撤
	EntrustType   int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=08"`                  // 委托类型 0-市价 1-限价
	EntrustPrice  decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=09"`  // 委托价格
	TradePrice    decimal.Decimal `json:"trade_price" swaggertype:"string" example:"123.123" extensions:"x-order=10"`    // 成交均价
	EntrustVolume decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 委托数量
	EntrustValue  decimal.Decimal `json:"entrust_value" swaggertype:"string" example:"123.123" extensions:"x-order=12"`  // 委托总额
	TradeVolume   decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=13"`   // 成交数量
	TradeValue    decimal.Decimal `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=14"`    // 成交额
	CostFee       decimal.Decimal `json:"cost_fee" swaggertype:"string" example:"123.123" extensions:"x-order=15"`       // 交易手续费
	CreateTime    int64           `json:"create_time" example:"1652312233" extensions:"x-order=16"`                      // 创建时间
	UpdateTime    int64           `json:"update_time" example:"1652312233" extensions:"x-order=17"`                      // 更新时间
}

type DocSpotHistoryPlanEntrustOrderReply struct {
	List []DocSpotHistoryPlanEntrustOrder `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotHistoryPlanEntrustOrder struct {
	OrderID       string          `json:"order_id" example:"1258238583284" extensions:"x-order=01"`                      // 订单id
	Symbol        string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                             // 交易对
	CoinName      string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                               // 币种名
	MarketName    string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                            // 市场名
	Side          string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 买卖方向 B-买 S-卖
	State         int             `json:"state" example:"1" enums:"1,2,3" extensions:"x-order=06"`                       // 状态 0-已撤销 1-未触发 2-已触发 3-触发失败
	Condition     int             `json:"condition" example:"1" enums:"1,2" extensions:"x-order=07"`                     // 触发条件 1 >= 2 <=
	EntrustMode   int             `json:"entrust_mode" example:"0" enums:"0,1,2,3" extensions:"x-order=08"`              // 执行类型 0-默认 1-对手价 2-最优3挡 3-最优5挡
	EntrustType   int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=09"`                  // 委托类型 0-市价 1-限价
	TriggerPrice  decimal.Decimal `json:"trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=10"`  // 触发价格
	EntrustPrice  decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=11"`  // 执行价格
	EntrustValue  decimal.Decimal `json:"entrust_value" swaggertype:"string" example:"123.123" extensions:"x-order=12"`  // 委托总额
	EntrustVolume decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=13"` // 委托数量
	CreateTime    int64           `json:"create_time" example:"1652321123" extensions:"x-order=14"`                      // 创建时间
	UpdateTime    int64           `json:"update_time" example:"1652321123" extensions:"x-order=15"`                      // 更新时间
}

type TradeDetailArg struct {
	OrderID   string `json:"order_id" example:"16344324235325" extensions:"x-order=01"`    // 订单id
	Symbol    string `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`            // 交易对名
	Side      string `json:"side" example:"B" enums:"B,S" extensions:"x-order=03"`         // 方向 B-买入 S-卖出
	LimitDays int    `json:"limit_days" example:"30" enums:"7,30" extensions:"x-order=04"` // 数据限制天数 当前仅支持7天或30天
}

type DocSpotTradeDetailReply struct {
	List []DocSpotTradeDetail `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotTradeDetail struct {
	TradeID     string          `json:"trade_id" example:"125151231245" extensions:"x-order=01"`                     // 成交id
	Symbol      string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`                           // 交易对
	CoinName    string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                             // 币种名
	MarketName  string          `json:"market_name" example:"USDT" extensions:"x-order=04"`                          // 市场名
	Side        string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                        // 买卖方向 B-买 S-卖
	Price       decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=06"`        // 成交价
	TradeVolume decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 成交数量
	TradeValue  decimal.Decimal `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=08"`  // 成交金额
	Commission  decimal.Decimal `json:"commission" swaggertype:"string" example:"123.123" extensions:"x-order=09"`   // 手续费
	TradeTime   int64           `json:"trade_time" example:"1652342234" extensions:"x-order=10"`                     // 成交时间
}
