package model

import (
	"time"

	"github.com/shopspring/decimal"
)

type DocUsdtContractList struct {
	ContractCode           string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`                                 // 合约code
	ContractName           string          `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`                                 // 合约中文名
	ContractNameEn         string          `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"`                              // 合约英文名
	ContractIcon           string          `json:"contract_icon" example:"https://www.a.cn/bu.png" extensions:"x-order=04"`                 // 合约图标
	IndexPrecision         int32           `json:"index_precision" example:"1" extensions:"x-order=05"`                                     // 合约指数价格精度
	Digit                  int32           `json:"digit" example:"1" extensions:"x-order=06"`                                               // 精度
	Price                  string          `json:"price" example:"123.123" extensions:"x-order=07"`                                         // 成交价格
	PriceCNY               string          `json:"price_cny" example:"123.123" extensions:"x-order=08"`                                     // 成交价格(人民币)
	IndexPrice             string          `json:"index_price" example:"123.123" extensions:"x-order=09"`                                   // 指数价格
	ChangeRatio            string          `json:"change_ratio" example:"123.123" extensions:"x-order=10"`                                  // 涨跌幅百分比
	Change                 string          `json:"change" example:"123.123" extensions:"x-order=11"`                                        // 涨跌
	BuyCount               int64           `json:"buy_count" example:"123" extensions:"x-order=12"`                                         // 看涨人数
	SellCount              int64           `json:"sell_count" example:"123" extensions:"x-order=13"`                                        // 看跌人数
	Delisted               bool            `json:"delisted" example:"false" extensions:"x-order=14"`                                        // 是否已下架
	IsFollow               bool            `json:"is_follow" example:"false" extensions:"x-order=15"`                                       // 是否可带单开仓
	PriceCoinName          string          `json:"price_coin_name" example:"BTC" extensions:"x-order=16"`                                   // 币种名称
	ParValue               string          `json:"par_value" example:"0.01" extensions:"x-order=17"`                                        // 面值
	TradeVolume            decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=18"`             // 成交量
	FeeMaker               decimal.Decimal `json:"maker_fee" swaggertype:"string" example:"123.123" extensions:"x-order=19"`                // maker手续费率
	FeeTaker               float64         `json:"fee_taker" example:"123.123" extensions:"x-order=20"`                                     // 手续费
	IOCLimit               decimal.Decimal `json:"ioc_limit" swaggertype:"string" example:"123.123" extensions:"x-order=21"`                // ioc委托限价
	IOCBuyLimit            decimal.Decimal `json:"ioc_buy_limit" swaggertype:"string" example:"123.123" extensions:"x-order=22"`            // ioc买方价格限制
	IOCSellLimit           decimal.Decimal `json:"ioc_sell_limit" swaggertype:"string" example:"123.123" extensions:"x-order=23"`           // ioc卖方价格限制
	IsMaintenance          bool            `json:"is_maintenance" example:"false" extensions:"x-order=24"`                                  // 是否系统维护
	MaintenanceMarginRatio decimal.Decimal `json:"maintenance_margin_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=25"` // 维持保证金率
	FullLever              []int           `json:"full_lever" example:"1,2,3,4" extensions:"x-order=26"`                                    // 全仓杠杆倍率
	Leverages              []int           `json:"leverages" example:"1,3,4" extensions:"x-order=27"`                                       // 逐仓杠杆倍数
	FollowLever            []int           `json:"follow_lever" example:"1,2" extensions:"x-order=28"`                                      // 跟单杠杆倍数
	LockFloatFactor        decimal.Decimal `json:"lock_float_factor" swaggertype:"string" example:"123.123" extensions:"x-order=29"`        // 资产冻结上浮系数
	RedundancyFactor       decimal.Decimal `json:"redundancy_factor" swaggertype:"string" example:"123.13" extensions:"x-order=30"`         // 市价冗余系数
	HighPrice              decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=31"`               // 最高价
	LowPrice               decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=32"`                // 最低价
}

type DocUsdtSymbolArg struct {
	ContractCode string `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"` // 合约代码
}

type DocUsdtIndexHistory struct {
	ContractCode string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`                        // 合约代码
	IndexPrice   decimal.Decimal `json:"index_price" swaggertype:"string" example:"42521.1" extensions:"x-order=02"`     // 指数价
	TradePrice   decimal.Decimal `json:"trade_price" swaggertype:"string" example:"42521.1" extensions:"x-order=03"`     // 成交价
	TradePriceCn decimal.Decimal `json:"trade_price_cn" swaggertype:"string" example:"272521.1" extensions:"x-order=04"` // 成交价(cny)
	IndexCny     decimal.Decimal `json:"index_cny" swaggertype:"string" example:"272521.1" extensions:"x-order=05"`      // 指数价(cny)
	BuyPrice     decimal.Decimal `json:"buy_price" swaggertype:"string" example:"42521.0" extensions:"x-order=06"`       // 买价
	SellPrice    decimal.Decimal `json:"sell_price" swaggertype:"string" example:"42521.2" extensions:"x-order=07"`      // 卖价
	PerValue     decimal.Decimal `json:"per_value" swaggertype:"string" example:"0.01" extensions:"x-order=08"`          // 合约面值
	BuyCount     int64           `json:"buy_count" example:"100" extensions:"x-order=09"`                                // 看多人数
	SellCount    int64           `json:"sell_count" example:"200" extensions:"x-order=10"`                               // 看空人数
	Side         string          `json:"side" example:"B" extensions:"x-order=11"`                                       // 成交方向
	CreateBy     time.Time       `json:"create_by" example:"2020-06-01T17:25:14.67323649+08:00" extensions:"x-order=12"` // 时间
}

type DocUsdtContractDetail struct {
	ContractCode           string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`                          // 合约code
	ContractName           string          `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`                          // 合约中文名
	ContractNameEn         string          `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"`                       // 合约英文名
	ContractType           int8            `json:"contract_type" example:"0" extensions:"x-order=04"`                                // 合约类型 0:币本位 1:金本位
	VolumeDigit            int32           `json:"volume_digit" example:"1" extensions:"x-order=05"`                                 // 合约数量精度
	Digit                  int32           `json:"digit" example:"1" extensions:"x-order=06"`                                        // 合约精度
	IndexPrecision         int32           `json:"index_precision" example:"1" extensions:"x-order=07"`                              // 合约指数价格精度
	MarkPrecision          int32           `json:"mark_precision" example:"1" extensions:"x-order=08"`                               // 标记价格精度
	Step                   float64         `json:"step" example:"0.1" extensions:"x-order=09"`                                       // 下单价格步长
	CoinId                 int             `json:"coin_id" example:"1" extensions:"x-order=10"`                                      // 结算币种id
	CoinName               string          `json:"coin_name" example:"BTC" extensions:"x-order=11"`                                  // 结算币种名
	PriceCoinName          string          `json:"price_coin_name" example:"USDT" extensions:"x-order=12"`                           // 计价币种名
	MinOrderVolume         decimal.Decimal `json:"min_order_volume" swaggertype:"string" example:"0.01" extensions:"x-order=13"`     // 单笔最小下单量 张
	MaxOrderVolume         decimal.Decimal `json:"max_order_volume" swaggertype:"string" example:"1" extensions:"x-order=14"`        // 单笔最大下单量 张
	FeeMaker               string          `json:"maker_fee" example:"0.003" extensions:"x-order=15"`                                // maker手续费率
	FeeTaker               string          `json:"taker_fee" example:"0.005" extensions:"x-order=16"`                                // taker手续费率
	Delisted               bool            `json:"delisted" example:"false" extensions:"x-order=17"`                                 // 是否下架 false-上架,true-下架
	MaintenanceMarginRatio float64         `json:"maintenance_margin_ratio" example:"0.05" extensions:"x-order=18"`                  // 维持保证金率
	FullLever              []int           `json:"full_lever" example:"10,20,50" extensions:"x-order=19"`                            // 全仓杠杆倍率
	Leverages              []int           `json:"leverages" example:"10,20,50" extensions:"x-order=20"`                             // 逐仓杠杆倍数
	FollowLever            []int           `json:"follow_lever" example:"10,20,50" extensions:"x-order=21"`                          // 跟单杠杆倍数
	MinTraderVolume        int             `json:"min_trader_volume" example:"1" extensions:"x-order=22"`                            // 交易员最小下单张数
	MaxTraderVolume        int             `json:"max_trader_volume" example:"100" extensions:"x-order=23"`                          // 交易员最小下单张数
	IsFollow               bool            `json:"is_follow" example:"false" extensions:"x-order=24"`                                // 是否可下带单
	FundFee                string          `json:"fund_fee" example:"0.0002" extensions:"x-order=25"`                                // 资金费率
	EstimateFundFee        string          `json:"estimate_fund_fee" example:"0.0003" extensions:"x-order=26"`                       // 预测资金费率
	IndexPrice             decimal.Decimal `json:"index_price" swaggertype:"string" example:"46234.2" extensions:"x-order=27"`       // 指数价格
	IndexPriceCny          decimal.Decimal `json:"index_price_cny" swaggertype:"string" example:"273523.24" extensions:"x-order=28"` // 指数折合人民币价格
	BuyPrice               string          `json:"buy_price" example:"46234.1" extensions:"x-order=29"`                              // 买入指数价格
	SellPrice              string          `json:"sell_price" example:"46234.3" extensions:"x-order=30"`                             // 卖出指数价格
	HighPrice              string          `json:"high_price" example:"46234.9" extensions:"x-order=31"`                             // 最高价
	LowPrice               string          `json:"low_price" example:"46230.1" extensions:"x-order=32"`                              // 最低价
	ChangeDaily            string          `json:"change_daily" example:"0.002" extensions:"x-order=33"`                             // 当日涨跌幅
	Change24h              string          `json:"change_24_h" example:"0.002" extensions:"x-order=34"`                              // 24小时涨跌幅
	Change8h               string          `json:"change_8_h" example:"0.002" extensions:"x-order=35"`                               // 8小时涨跌幅
	Change4h               string          `json:"change_4_h" example:"0.002" extensions:"x-order=36"`                               // 4小时涨跌幅
	Change2h               string          `json:"change_2_h" example:"0.002" extensions:"x-order=37"`                               // 2小时涨跌幅
	Change1h               string          `json:"change_1_h" example:"0.002" extensions:"x-order=38"`                               // 1小时涨跌幅
	Change30m              string          `json:"change_30_m" example:"0.002" extensions:"x-order=39"`                              // 30分钟涨跌幅
	Change10m              string          `json:"change_10_m" example:"0.002" extensions:"x-order=40"`                              // 10分钟涨跌幅
	ChangeVolume           string          `json:"change_volume" example:"23.1" extensions:"x-order=41"`                             // 当日涨跌量
	Trade24h               string          `json:"trade_24_h" example:"235263.1" extensions:"x-order=42"`                            // 24小时交易量
	IOCLimit               decimal.Decimal `json:"ioc_limit" swaggertype:"string" example:"2" extensions:"x-order=43"`               // ioc委托限价
	IOCBuyLimit            decimal.Decimal `json:"ioc_buy_limit" swaggertype:"string" example:"2" extensions:"x-order=44"`           // ioc买方价格限制
	IOCSellLimit           decimal.Decimal `json:"ioc_sell_limit" swaggertype:"string" example:"2" extensions:"x-order=45"`          // ioc卖方价格限制
	LockFloatFactor        decimal.Decimal `json:"lock_float_factor" swaggertype:"string" example:"0.005" extensions:"x-order=46"`   // 资产冻结上浮系数
	RedundancyFactor       decimal.Decimal `json:"redundancy_factor" swaggertype:"string" example:"10" extensions:"x-order=47"`      // 市价冗余系数
	IsMaintenance          bool            `json:"is_maintenance" example:"false" extensions:"x-order=48"`                           // 是否系统维护
	ParValue               string          `json:"par_value" example:"0.01" extensions:"x-order=49"`                                 // 合约面值
}

type DocUsdtQuoteChange struct {
	ContractCode   string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`    // 合约代码
	ContractName   string          `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`    // 合约名称
	ContractNameEn string          `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"` // 合约英文名称
	IndexStr       string          `json:"index_str" example:"42523.1" extensions:"x-order=04"`        // 合约指数(字符串)
	IndexCNY       decimal.Decimal `json:"index_cny" example:"274523.52" extensions:"x-order=05"`      // 人民币指数价格
	QuoteChange    string          `json:"quote_change" example:"0.001" extensions:"x-order=06"`       // 合约涨跌幅
	Kline          []float64       `json:"kline" extensions:"x-order=07"`                              // k线数据
}

type DocUsdtTradingViewKLineArg struct {
	ContractCode string `json:"contract_code" example:"BUCUSDT" extensions:"x-order=01"`                                   // 合约code
	Duration     string `json:"duration" example:"1m" enums:"1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w" extensions:"x-order=02"` // k线时间段
	StartTime    int64  `json:"start_time" example:"1312421232" extensions:"x-order=03"`                                   // 开始时间
	EndTime      int64  `json:"end_time" example:"1312421232" extensions:"x-order=04"`                                     // 结束时间
}

type DocUsdtKLineData struct {
	ContractCode   string         `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`                                   // 合约code
	ContractName   string         `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`                                   // 合约中文名
	ContractNameEn string         `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"`                                // 合约英文名
	Duration       string         `json:"duration" example:"1m" enums:"1m,5m,15m,30m,1h,2h,4h,6h,12h,1d,1w" extensions:"x-order=04"` // k线时间段
	ServerTime     int64          `json:"server_time" example:"1623424552" extensions:"x-order=05"`                                  // 服务器时间
	List           []DocUsdtKLine `json:"list" extensions:"x-order=06"`                                                              // k线数据
}

type DocUsdtKLine struct {
	ID           int64   `json:"id" example:"241245229591923" extensions:"x-order=01"`    // id
	ContractCode string  `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"` // 合约code
	StartTime    int64   `json:"start_time" example:"1623152132" extensions:"x-order=03"` // k线起始时间
	EndTime      int64   `json:"end_time" example:"1623152163" extensions:"x-order=04"`   // k线结束时间
	OpenPrice    float64 `json:"open_price" example:"123.123" extensions:"x-order=05"`    // 开盘价
	ClosePrice   float64 `json:"close_price" example:"123.123" extensions:"x-order=06"`   // 收盘价
	HighPrice    float64 `json:"high_price" example:"123.123" extensions:"x-order=07"`    // 最高价
	LowPrice     float64 `json:"low_price" example:"123.123" extensions:"x-order=08"`     // 最低价
	Volume       int64   `json:"volume" example:"123123513" extensions:"x-order=09"`      // 数量
	PerValue     float64 `json:"per_value" example:"123.123" extensions:"x-order=10"`     // 面值
	Ts           int64   `json:"ts" example:"1523241231" extensions:"x-order=12"`         // 更新时间
}

type DocUsdtFundingRateReply struct {
	ContractCode  string `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`   // 合约代码
	FundingRate   string `json:"funding_rate" example:"0.0034" extensions:"x-order=02"`     // 当期资产费率
	CurrentTime   int64  `json:"current_time" example:"1512312412" extensions:"x-order=03"` // 当期资产费率时间
	EstimatedRate string `json:"estimated_rate" example:"0.000124" extensions:"x-order=04"` // 下期资产费率
	NextTime      int64  `json:"next_time" example:"1525235243" extensions:"x-order=05"`    // 下期
}

type DocUsdtIncreaseRank struct {
	ContractCode   string  `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`    // 合约代码
	ContractName   string  `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`    // 合约名称
	ContractNameEn string  `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"` // 合约英文名称
	IndexStr       string  `json:"index_str" example:"1234.124" extensions:"x-order=04"`       // 合约指数(字符串)
	IncreaseRatio  float64 `json:"increase_ratio" example:"0.03" extensions:"x-order=05"`      // 合约涨幅
}

type DocUsdtPersonRank struct {
	ContractCode   string `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`    // 合约代码
	ContractName   string `json:"contract_name" example:"BTCUSDT" extensions:"x-order=02"`    // 合约名称
	ContractNameEn string `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=03"` // 合约英文名称
	IndexStr       string `json:"index_str" example:"1234.123" extensions:"x-order=04"`       // 合约指数(字符串)
	Amount         int64  `json:"amount" example:"241" extensions:"x-order=05"`               // 人数
}

type DocUsdtContractIndicator struct {
	ID           int64     `json:"id" example:"1251252153832" extensions:"x-order=01"`                               // id
	ContractCode string    `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                          // 合约名
	Side         string    `json:"side" example:"S" extensions:"x-order=03"`                                         // 方向
	Index        string    `json:"index" example:"1234.124" extensions:"x-order=04"`                                 // 指数价
	IndexCNY     string    `json:"index_cny" example:"12515.125" extensions:"x-order=05"`                            // 指数价(cny)
	Price        float64   `json:"price" example:"123.123" extensions:"x-order=06"`                                  // 成交价
	Timestamp    int64     `json:"timestamp" example:"1253532623" extensions:"x-order=07"`                           // 秒时间戳
	CreateTime   time.Time `json:"create_time" example:"2020-06-01T17:25:14.67323649+08:00" extensions:"x-order=08"` // 时间
}

type DocUsdtContractSupport struct {
	ID            int64           `json:"id" example:"123523532512" extensions:"x-order=01"`                        // id
	ContractCode  string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                  // 合约代码
	OpenPrice     float64         `json:"open_price" example:"123.123" extensions:"x-order=03"`                     // 开盘价
	ClosePrice    float64         `json:"close_price" example:"123.123" extensions:"x-order=04"`                    // 收盘价
	HighPrice     float64         `json:"high_price" example:"123.123" extensions:"x-order=05"`                     // 最高价
	LowPrice      float64         `json:"low_price" example:"123.123" extensions:"x-order=06"`                      // 最低价
	Volume        float64         `json:"volume" example:"123.123" extensions:"x-order=07"`                         // 数量
	DragStr       string          `json:"drag_str" example:"123.123" extensions:"x-order=09"`                       // 阻力位
	DragCNYStr    string          `json:"drag_cny_str" example:"123.123" extensions:"x-order=10"`                   // 阻力位(cny)
	SupportStr    string          `json:"support_str" example:"123.123" extensions:"x-order=11"`                    // 支撑位
	SupportCNYStr string          `json:"support_cny_str" example:"123.123" extensions:"x-order=12"`                // 支撑位(cny)
	Timestamp     int64           `json:"timestamp" example:"1253532623" extensions:"x-order=13"`                   // 秒时间戳
	TradeVolume   decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"1241" extensions:"x-order=14"` // 成交量
}

type DocUsdtSummary struct {
	Trade24HValue decimal.Decimal `json:"trade_24h_value" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 24小时成交额
}

type DocUsdtContractMarketReply struct {
	List []DocUsdtContractMarket `json:"list" extensions:"x-order=01"` // 数据列表
}

type DocUsdtContractMarket struct {
	Symbol      string          `json:"symbol" example:"BTCUSDT" extensions:"x-order=01"`                            // 合约
	Price       decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=02"`        // 最新价
	ChangeRatio decimal.Decimal `json:"change_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 涨跌幅
	HighPrice   decimal.Decimal `json:"high_price" swaggertype:"string" example:"123.123" extensions:"x-order=04"`   // 最高价
	LowPrice    decimal.Decimal `json:"low_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`    // 最低价
	TradeVolume decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 成交量
}
