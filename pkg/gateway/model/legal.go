package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocPlaceRateArg struct {
	Side     int    `json:"side" example:"2" enums:"1,2" extensions:"x-order=01"` // 下单方向 1-卖 2买
	Mode     int    `json:"mode" example:"0" enums:"0,1" extensions:"x-order=02"` // 下单输入类型 0-输入法币下单 1-输入数字币下单
	CoinName string `json:"coin_name" example:"USDT" extensions:"x-order=03"`     // 币种名称
	Amount   string `json:"amount" example:"123.123" extensions:"x-order=04"`     // 下单数量/下单金额
}

type DocApiExchangeRate struct {
	ExchangeRate string `json:"exchange_rate" example:"123.123" extensions:"x-order=01"`                // 实时汇率
	BusinessID   int64  `json:"business_id" example:"1" extensions:"x-order=02"`                        // 商户id
	RateToken    string `json:"rate_token" example:"abcdefghijklmnopqrstuvwxy" extensions:"x-order=03"` // 汇率下单token
	Amount       string `json:"amount" example:"123.123" extensions:"x-order=04"`                       // 下单币的数量
	AmountCn     string `json:"amount_cn" example:"123.123" extensions:"x-order=05"`                    // 人民币数量
}

type DocApiLegalOrderLimit struct {
	ValueMin  string `json:"value_min" example:"123.123" extensions:"x-order=01"`  // 最小下单价值
	ValueMax  string `json:"value_max" example:"123.123" extensions:"x-order=02"`  // 最大下单价值
	LegalRate string `json:"legal_rate" example:"123.123" extensions:"x-order=03"` // 单价汇率
}

type DOcC2CLoginArg struct {
	OrderID int64 `json:"order_id" example:"***********" extensions:"x-order=01"` // 本地订单id
}

type DocC2CLoginReply struct {
	Token string `json:"token" example:"dgaing1eie131id3jka2ljd==" extensions:"x-order=01"` // token
}

type PlaceArg struct {
	Side         int             `json:"side" example:"2" enums:"1,2" extensions:"x-order=01"`                   // 下单方向 1-卖 2买
	Mode         int             `json:"mode" example:"0" enums:"0,1" extensions:"x-order=02"`                   // 下单输入类型 0-输入法币下单 1-输入数字币下单
	CoinName     string          `json:"coin_name" example:"USDT" extensions:"x-order=03"`                       // 币种名称
	Amount       string          `json:"amount" example:"123.123" extensions:"x-order=04"`                       // 下单数量/下单金额
	FundPassword string          `json:"fund_password" example:"di2an4ig2ej1ij==" extensions:"x-order=05"`       // 资金密码
	PaymentId    int             `json:"payment_id" example:"1245124" extensions:"x-order=06"`                   // 收款账户id
	BusinessId   int             `json:"business_id" example:"1" extensions:"x-order=07"`                        // 汇率商户id
	ExchangeRate decimal.Decimal `json:"exchange_rate" example:"123.123" extensions:"x-order=08"`                // 下单时汇率
	RateToken    string          `json:"rate_token" example:"abcdefghijklmnopqrstuvwxy" extensions:"x-order=09"` // 下单汇率token
}

type DocRemainQuota struct {
	Remain decimal.Decimal `json:"remain" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 剩余数量
}

type DocApiLegalOrderArg struct {
	State define.LegalOrderApiState `json:"state" swaggertype:"integer" example:"1" extensions:"x-order=01"` // 订单状态 1-进行中 2-已完成 3-已取消
}

type DocApiLegalOrder struct {
	OrderID        int64                  `json:"order_id" example:"1251221512" extensions:"x-order=01"`                                   // 本地订单号
	CoinID         int                    `json:"coin_id" example:"36" extensions:"x-order=02"`                                            // 币种id
	CoinName       string                 `json:"coin_name" example:"USDT" extensions:"x-order=03"`                                        // 币种名称
	OrderType      int                    `json:"order_type" example:"2" enums:"1,2" extensions:"x-order=04"`                              // 订单类型 1-出售 2-购买
	State          define.LegalOrderState `json:"state" swaggertype:"integer" example:"1" enums:"1,2,3,4,5,6,7,8" extensions:"x-order=05"` // 订单状态 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8:后台审核失败
	Amount         string                 `json:"amount" example:"123.123" extensions:"x-order=06"`                                        // 订单数量
	LegalAmount    string                 `json:"legal_amount" example:"123.123" extensions:"x-order=07"`                                  // 法币数量
	CreateTime     int64                  `json:"create_time" example:"**********" extensions:"x-order=08"`                                // 创建时间
	CloseTime      int64                  `json:"close_time" example:"**********" extensions:"x-order=09"`                                 // 完成时间
	PlatformPrice  string                 `json:"platform_price" example:"123.123" extensions:"x-order=10"`                                // 平台汇率
	PaymentType    int                    `json:"payment_type" example:"1" extensions:"x-order=11"`                                        // 收款类型 1-银行 2-支付宝
	BankName       string                 `json:"bank_name" example:"fadf" extensions:"x-order=12"`                                        // 开户行
	BankBranchName string                 `json:"bank_branch_name" example:"adf" extensions:"x-order=13"`                                  // 分行名称
	BankNumb       string                 `json:"bank_numb" example:"***************" extensions:"x-order=14"`                             // 收款账户
	AccountHolder  string                 `json:"account_holder" example:"dfae" extensions:"x-order=15"`                                   // 收款人
	OcrAddress     string                 `json:"ocr_address" example:"https://www.a.cn/a.png" extensions:"x-order=16"`                    // ocr地址
}

type DocUserPayment struct {
	ID             int    `json:"id" example:"1" extensions:"x-order=01"`                               // 收款账户id标识
	Type           int    `json:"type" example:"1" extensions:"x-order=02"`                             // 收款方式，1-银行卡 2-支付宝
	BankName       string `json:"bank_name" example:"dgasdg" extensions:"x-order=03"`                   // 开户行
	BankBranchName string `json:"bank_branch_name" example:"sgaads" extensions:"x-order=04"`            // 开户行支行
	BankNumb       string `json:"bank_numb" example:"***************" extensions:"x-order=05"`          // 收款账户（银行卡号、支付宝账户）
	AccountHolder  string `json:"account_holder" example:"13412" extensions:"x-order=06"`               // 收款人
	OcrAddress     string `json:"ocr_address" example:"https://www.a.cn/a.png" extensions:"x-order=07"` // 二维码地址
}

type DocUserQuotaReply struct {
	DocUserQuotaConfig
	WithdrawMaxQuota decimal.Decimal `json:"withdraw_max_quota" swaggertype:"string" example:"123.123" extensions:"x-order=99"` // 本次提币最大额度
}

type DocUserQuotaConfig struct {
	WithdrawOnce      decimal.Decimal `json:"withdraw_once" swaggertype:"string" example:"123.123" extensions:"x-order=01"`        // 提币单笔限额
	WithdrawDaily     decimal.Decimal `json:"withdraw_daily" swaggertype:"string" example:"123.123" extensions:"x-order=02"`       // 提币每日限额
	WithdrawBasic     decimal.Decimal `json:"withdraw_basic" swaggertype:"string" example:"123.123" extensions:"x-order=03"`       // 基本限额(非认证用户每日可提)
	LegalQuotaMinCNY  decimal.Decimal `json:"legal_quota_min_cny" swaggertype:"string" example:"123.123" extensions:"x-order=04"`  // 法币交易单笔最小限额(CNY)
	LegalQuotaMinUSDT decimal.Decimal `json:"legal_quota_min_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 法币交易单笔最小限额(USDT)
	LegalQuotaCNY     decimal.Decimal `json:"legal_quota_cny" swaggertype:"string" example:"123.123" extensions:"x-order=06"`      // 法币交易单笔最大限额(CNY)
	LegalQuotaUSDT    decimal.Decimal `json:"legal_quota_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=07"`     // 法币交易单笔最大限额(USDT)
}

type DocThirdLegalConf struct {
	DefaultPayCoin     string                 `json:"default_pay_coin" example:"USD" extensions:"x-order=01"`     // 默认支付币种
	DefaultDigitalCoin string                 `json:"default_digital_coin" example:"BTC" extensions:"x-order=02"` // 默认数字币种
	PayCoinList        []DocThirdLegalSubCoin `json:"pay_coins" extensions:"x-order=03"`                          // 支付币种列表
	DigitalCoinList    []DocThirdLegalSubCoin `json:"digital_coins" extensions:"x-order=04"`                      // 数字币种列表
	PayMode            []DocLegalPayMode      `json:"pay_mode" extensions:"x-order=05"`                           // 支付方式配置
	OrderConf          interface{}            `json:"order_conf" extensions:"x-order=06"`                         // 下单配置
}

type DocThirdLegalSubCoin struct {
	Name string `json:"name" example:"USD" extensions:"x-order=01"`                      // 名称
	Icon string `json:"icon" example:"https://www.a.cn/usd.png" extensions:"x-order=02"` // 图标
}

type DocLegalPayMode struct {
	PayID   int    `json:"pay_id" example:"1" extensions:"x-order=01"`                    // 支付方式ID 应按位递增 1<<0 1<<1 1<<2 1<<3
	PayName string `json:"pay_name" example:"VISA" extensions:"x-order=02"`               // 支付方式名
	Logo    string `json:"logo" example:"https://www.a.cn/v.png" extensions:"x-order=03"` // 支付方式图标
}

type DocThirdLegalOrderConfForAndroid struct {
	DigitalCoin string                   `json:"digital_coin" example:"BTC" extensions:"x-order=01"` // 币种名
	SubConf     []DocThirdLegalOrderConf `json:"sub_conf" extensions:"x-order=02"`                   // 下级配置
}

type DocThirdLegalOrderConf struct {
	PayCoin             string          `json:"pay_coin" example:"USD" extensions:"x-order=01"`                            // 支付币种
	PriceRate           decimal.Decimal `json:"price_rate" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 参考汇率
	DocThirdLegalLimits                 // 下单限制
}

type DocThirdLegalLimits struct {
	MinPay  decimal.Decimal `json:"min_pay" swaggertype:"string" example:"123.123" extensions:"x-order=96"`  // 最小支付
	MaxPay  decimal.Decimal `json:"max_pay" swaggertype:"string" example:"123.123" extensions:"x-order=97"`  // 最大支付
	MinCoin decimal.Decimal `json:"min_coin" swaggertype:"string" example:"123.123" extensions:"x-order=98"` // 最小买入
	MaxCoin decimal.Decimal `json:"max_coin" swaggertype:"string" example:"123.123" extensions:"x-order=99"` // 最大买入
}

type DocThirdLegalOrderMerchantArg struct {
	PayCoin           string               `json:"pay_coin" example:"USD" extensions:"x-order=01"`                                     // 法币币种名
	PayCoinAmount     decimal.Decimal      `json:"pay_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=02"`     // 法币数量
	DigitalCoin       string               `json:"digital_coin" example:"BTC" extensions:"x-order=03"`                                 // 数字币币种名
	DigitalCoinAmount decimal.Decimal      `json:"digital_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 数字币种数量
	UseMode           DocLegalOrderUseMode `json:"use_mode" swaggertype:"integer" example:"1" extensions:"x-order=05"`                 // 用户输入方式 1-用户输入的是支付币种 2-用户输入的是数字币种
}

type DocLegalOrderUseMode uint8

const (
	LegalOrderUseModePayCoin     DocLegalOrderUseMode = 1 // 用户输入的是支付币种
	LegalOrderUseModeDigitalCoin DocLegalOrderUseMode = 2 // 用户输入的是数字币种
)

type DocThirdLegalOrderMerchant struct {
	BusID              int             `json:"bus_id" example:"1" extensions:"x-order=01"`                                         // 商家id
	BusName            string          `json:"bus_name" example:"sdfdsg" extensions:"x-order=02"`                                  // 商家名字
	BusLogo            string          `json:"bus_logo" example:"https://www.a.cn/a.png" extensions:"x-order=03"`                  // 商家url
	PayMode            int             `json:"pay_mode" example:"3" extensions:"x-order=04"`                                       // 支付方式(位运算,和配置接口中的支付方式id进行与运算,pay_mode&id==id则显示该id对应的支付方式图标)
	Market             string          `json:"market" example:"dfadg" extensions:"x-order=05"`                                     // 描述
	Agreement          string          `json:"agreement" example:"dfaidjf" extensions:"x-order=06"`                                // 协议信息
	AgreementUrl       string          `json:"agreement_url" example:"https://www.a.cn" extensions:"x-order=07"`                   // 协议url
	ExpectedArrivalMin int             `json:"expected_arrival_min" example:"1" extensions:"x-order=08"`                           // 预计最小到账时间(分钟)
	ExpectedArrivalMax int             `json:"expected_arrival_max" example:"5" extensions:"x-order=09"`                           // 预计最大到账时间(分钟)
	PriceRate          decimal.Decimal `json:"price_rate" swaggertype:"string" example:"123.123" extensions:"x-order=10"`          // 汇率
	PayCoinAmount      decimal.Decimal `json:"pay_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=11"`     // 法币数量
	DigitalCoinAmount  decimal.Decimal `json:"digital_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=12"` // 数字币种数量
}

type DocThirdLegalOrderPlaceArg struct {
	BusID             int                  `json:"bus_id" example:"1" extensions:"x-order=01"`                                         // 商家id
	PayCoin           string               `json:"pay_coin" example:"USD" extensions:"x-order=02"`                                     // 法币币种名
	PayCoinAmount     decimal.Decimal      `json:"pay_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=03"`     // 法币数量
	DigitalCoin       string               `json:"digital_coin" example:"BTC" extensions:"x-order=04"`                                 // 数字币币种名
	DigitalCoinAmount decimal.Decimal      `json:"digital_coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 数字币种数量
	UseMode           DocLegalOrderUseMode `json:"use_mode" swaggertype:"integer" example:"1" enums:"1,2" extensions:"x-order=06"`     // 用户输入方式 1-用户输入的是支付币种 2-用户输入的是数字币种
}

type DocThirdLegalOrderPlaceReply struct {
	ThirdUrl string `json:"third_url" example:"https://www.b.cn" extensions:"x-order=01"` // 三方跳转地址
}

type DocThirdLegalOrder struct {
	OrderID     string          `json:"order_id" example:"125124123123" extensions:"x-order=01"`                                       // 订单ID
	BusName     string          `json:"bus_name" example:"dfasd" extensions:"x-order=02"`                                              // 商家名称
	CoinName    string          `json:"coin_name" example:"BTC" extensions:"x-order=03"`                                               // 币种名字
	LegalName   string          `json:"legal_name" example:"USD" extensions:"x-order=04"`                                              // 使用法币
	Address     string          `json:"address" example:"0xdfaienfinidf" extensions:"x-order=05"`                                      // 冲币地址
	CoinAmount  decimal.Decimal `json:"coin_amount" swaggertype:"string" example:"123.123" extensions:"x-order=06"`                    // 购买币种数量
	LegalAmount decimal.Decimal `json:"legal_amount" swaggertype:"string" example:"123.123" example:"123.123" extensions:"x-order=07"` // 支付法币数量
	Fee         decimal.Decimal `json:"fee" swaggertype:"string" example:"123.123" extensions:"x-order=08"`                            // 手续费
	Rates       decimal.Decimal `json:"rates" swaggertype:"string" example:"123.123" extensions:"x-order=09"`                          // 汇率
	OrderType   int             `json:"order_type" example:"1" enums:"1,2" extensions:"x-order=10"`                                    // 1买2卖
	Status      int             `json:"status" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=11"`                                // 1确认中 2发送中 3订单取消 4订单提交中 5订单失败 6订单成功
	PayMode     int64           `json:"pay_mode" example:"1" extensions:"x-order=12"`                                                  // 支付方式(位运算,和配置接口中的支付方式id进行与运算,pay_mode&id==id则显示该id对应的支付方式图标)
	CreateTime  int64           `json:"create_time" example:"1641245523" extensions:"x-order=13"`                                      // 创建时间
	UpdateTime  int64           `json:"update_time" example:"1641245523" extensions:"x-order=14"`                                      // 更新时间
}
