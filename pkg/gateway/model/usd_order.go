package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocUsdCurrentPosition struct {
	TotalRights decimal.Decimal      `json:"total_rights" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 总权益
	TotalFloat  decimal.Decimal      `json:"total_float" swaggertype:"string" example:"123.123" extensions:"x-order=02"`  // 总浮动盈亏
	List        []UsdCurrentPosition `json:"list" extensions:"x-order=03"`                                                // 持仓列表
}

type UsdCurrentPosition struct {
	PositionId     int64             `json:"position_id" example:"123512512312323" extensions:"x-order=01"`                 // 用户持仓ID
	ContractCode   string            `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                        // 合约code
	ContractName   string            `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                        // 中文合约名
	ContractNameEn string            `json:"contract_name_en" example:"BTCUSD" extensions:"x-order=04"`                     // 英文合约名
	Side           string            `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 合约方向 B-持多 S-持空
	AccountType    int8              `json:"account_type" example:"1" enums:"1,2" extensions:"x-order=06"`                  // 持仓类型 1全仓 2逐仓
	Lever          int               `json:"lever" example:"40" extensions:"x-order=07"`                                    // 杠杆倍数
	Price          decimal.Decimal   `json:"price" swaggertype:"string" example:"234.1234" extensions:"x-order=08"`         // 持仓均价
	Volume         decimal.Decimal   `json:"volume" swaggertype:"string" example:"123.124" extensions:"x-order=09"`         // 持仓张数
	VolumeLock     decimal.Decimal   `json:"volume_lock" swaggertype:"string" example:"124.123" extensions:"x-order=10"`    // 持仓冻结额张数
	ForcePrice     decimal.Decimal   `json:"force_price" swaggertype:"string" example:"123.123" extensions:"x-order=11"`    // 强平价
	Limit          decimal.Decimal   `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=12"`          // 止盈价
	Stop           decimal.Decimal   `json:"stop" swaggertype:"string" example:"0" extensions:"x-order=13"`                 // 止损价
	Margin         decimal.Decimal   `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=14"`         // 保证金
	AdjustMargin   decimal.Decimal   `json:"adjust_margin" swaggertype:"string" example:"123.123" extensions:"x-order=15"`  // 调整保证金
	InitMargin     decimal.Decimal   `json:"init_margin" swaggertype:"string" example:"123.123" extensions:"x-order=16"`    // 初始保证金
	FloatProfit    decimal.Decimal   `json:"float_profit" swaggertype:"string" example:"123.123" extensions:"x-order=17"`   // 未实现盈亏
	Available      decimal.Decimal   `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=18"`      // 可用余额
	ProfitRatio    decimal.Decimal   `json:"profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=19"`   // 盈亏率
	MarginRatio    decimal.Decimal   `json:"margin_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=20"`   // 保证金率
	ContractIndex  decimal.Decimal   `json:"contract_index" swaggertype:"string" example:"123.123" extensions:"x-order=21"` // 合约指数
	BuyPrice       decimal.Decimal   `json:"buy_price" swaggertype:"string" example:"123.123" extensions:"x-order=22"`      // 买入价
	SellPrice      decimal.Decimal   `json:"sell_price" swaggertype:"string" example:"123.123" extensions:"x-order=23"`     // 卖出价
	CurrentPrice   decimal.Decimal   `json:"current_price" swaggertype:"string" example:"123.123" extensions:"x-order=24"`  // 当前价
	Commission     decimal.Decimal   `json:"commission" swaggertype:"string" example:"123.123" extensions:"x-order=25"`     // 手续费
	UserId         int64             `json:"user_id" example:"12412941925912924" extensions:"x-order=26"`                   // 用户id
	PlatformID     int               `json:"platform_id" example:"1" extensions:"x-order=27"`                               // 平台id
	CurrencyName   string            `json:"currency_name" example:"BTC" extensions:"x-order=28"`                           // 币种名
	ShareResource  *UsdShareResource `json:"share_resource" extensions:"x-order=29"`                                        // 分享资源
}

type UsdShareResource struct {
	ImageBackground string `json:"image_background" example:"https://www.a.cn/t.png" extensions:"x-order=01"` // 背景图
	TitleColor      string `json:"title_color" example:"#FFAAFF" extensions:"x-order=02"`                     // 标题颜色
	TitleVertical   uint16 `json:"title_vertical" example:"560" extensions:"x-order=03"`                      // 标题垂直位置
	ContentColor    string `json:"content_color" example:"#FFAAFF" extensions:"x-order=04"`                   // 内容区文字颜色
	TitleText       string `json:"title_text" example:"hahaha" extensions:"x-order=05"`                       // 标题内容
	TitleSize       uint16 `json:"title_size" example:"18" extensions:"x-order=06"`                           // 标题字号
}

type DocUsdEntrustSoldArg struct {
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"` // 合约代码
	Offset       string `json:"offset" example:"O" enums:"O,C" extensions:"x-order=02"` // 方向 O-开仓 C-平仓
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=03"`        // 币种
	LimitDays    int    `json:"limit_days" example:"30" extensions:"x-order=04"`        // 数据限制天数(当前仅支持7天或30天)
}

type DocUsdSoldTrade struct {
	ContractCode   string            `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"`                             // 合约代码
	ContractName   string            `json:"contract_name" example:"BTCUSD" extensions:"x-order=02"`                             // 中文合约名
	ContractNameEn string            `json:"contract_name_en" example:"BTCUSD" extensions:"x-order=03"`                          // 英文合约名
	OrderClient    int               `json:"order_client" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=04"`               // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	AccountType    int               `json:"account_type" example:"1" enums:"1,2" extensions:"x-order=05"`                       // 账户模式 1：全仓 2：逐仓
	TradeId        int64             `json:"trade_id" example:"***************" extensions:"x-order=06"`                         // 成交编号
	Lever          int               `json:"lever" example:"100" extensions:"x-order=07"`                                        // 杠杆
	OrderType      int               `json:"order_type" example:"0" enums:"0,1,2,4,5" extensions:"x-order=08"`                   // 订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	Offset         string            `json:"offset" example:"O" enums:"O,C" extensions:"x-order=09"`                             // 开平方向 O: 开仓 C: 平仓
	Side           string            `json:"side" example:"B" enums:"B,S" extensions:"x-order=10"`                               // B买S卖
	Available      decimal.Decimal   `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=11"`           // 可用余额
	Balance        decimal.Decimal   `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=12"`             // 账户资产
	Commission     decimal.Decimal   `json:"commission" swaggertype:"string" example:"123.123" extensions:"x-order=13"`          // 手续费
	Price          decimal.Decimal   `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=14"`               // 成交价格
	Profit         decimal.Decimal   `json:"profit" swaggertype:"string" example:"123.123" extensions:"x-order=15"`              // 实现盈亏
	CloseProfit    decimal.Decimal   `json:"close_profit" swaggertype:"string" example:"123.123" extensions:"x-order=16"`        // 平仓盈亏
	Limit          decimal.Decimal   `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=17"`               // 止盈价（触发止盈时持仓带来的）
	Stop           decimal.Decimal   `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=18"`                // 止损价（触发止损时持仓带来的）
	TotalProfit    decimal.Decimal   `json:"total_profit" swaggertype:"string" example:"123.123" extensions:"x-order=19"`        // 累计已实现盈亏
	TradeAmount    decimal.Decimal   `json:"trade_amount" swaggertype:"string" example:"123.123" extensions:"x-order=20"`        // 成交金额
	TradeValue     decimal.Decimal   `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=21"`         // 成交价值
	EntrustVolume  decimal.Decimal   `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=22"`      // 委托量
	Volume         decimal.Decimal   `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=23"`              // 成交张数
	TradeTime      int64             `json:"trade_time" example:"1523142522" extensions:"x-order=24"`                            // 成交时间
	UserId         int64             `json:"user_id" example:"3295932483242341123" extensions:"x-order=25"`                      // 用户id
	EntrustOrderId int64             `json:"entrust_order_id" example:"1242195129491292" extensions:"x-order=26"`                // 委托订单id
	EntrustType    int               `json:"entrust_type" example:"0" enums:"0,1,2" extensions:"x-order=27"`                     // 委托类型 0-市价单 1-限价单 2-强平单
	EntrustMode    int               `json:"entrust_mode" example:"3" enums:"1,2,3" extensions:"x-order=28"`                     // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustStatus  int               `json:"entrust_status" example:"200" enums:"0,100,101,102,200,201" extensions:"x-order=29"` // 委托状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
	CurrencyName   string            `json:"currency_name" example:"BTC" extensions:"x-order=30"`                                // 保证金币种名
	ShareResource  *UsdShareResource `json:"share_resource" extensions:"x-order=31"`                                             // 分享资源
}

type DocUsdAllSoldTrade struct {
	TradeID        int64             `json:"trade_id" example:"12512512412321321" extensions:"x-order=01"`                               // 交易id
	ContractCode   string            `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                                     // 合约代码
	ContractName   string            `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                                     // 中文合约名
	ContractNameEn string            `json:"contract_name_en" example:"BTCUSD" extensions:"x-order=04"`                                  // 英文合约名
	AccountType    int               `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=05"`                             // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Side           string            `json:"side" example:"B" enums:"B,S" extensions:"x-order=06"`                                       // 交易方向 B买S卖
	Offset         string            `json:"offset" example:"O" enums:"O,C" extensions:"x-order=07"`                                     // O: 开仓 C: 平仓
	CurrentPrice   decimal.Decimal   `json:"current_price" swaggertype:"string" example:"123.123" extensions:"x-order=08"`               // 当前价
	Price          decimal.Decimal   `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=09"`                       // 成交价格
	OpenAvgPrice   decimal.Decimal   `json:"open_avg_price" swaggertype:"string" example:"123.123" extensions:"x-order=10"`              // 开仓均价
	TradeValue     decimal.Decimal   `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=11"`                 // 成交价值
	TradeAmount    decimal.Decimal   `json:"trade_amount" swaggertype:"string" example:"123.123" extensions:"x-order=12"`                // 成交金额
	Profit         decimal.Decimal   `json:"profit" swaggertype:"string" example:"123.123" extensions:"x-order=13"`                      // 已实现盈亏
	CloseProfit    decimal.Decimal   `json:"close_profit" swaggertype:"string" example:"123.123" extensions:"x-order=14"`                // 平仓盈亏
	ParValue       decimal.Decimal   `json:"par_value" swaggertype:"string" example:"123.123" extensions:"x-order=15"`                   // 合约面值
	Volume         decimal.Decimal   `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=16"`                      // 成交张数
	EntrustVolume  decimal.Decimal   `json:"entrust_volume" swaggertype:"string" example:"123" extensions:"x-order=17"`                  // 委托量
	Lever          int               `json:"lever" example:"20" extensions:"x-order=18"`                                                 // 杠杆
	OrderType      int               `json:"order_type" example:"0" enums:"0,1,2,4,5" extensions:"x-order=19"`                           // 订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	TradeTimeUnix  int64             `json:"trade_time" example:"1526223452" extensions:"x-order=20"`                                    // 成交时间
	IsFollow       bool              `json:"is_follow" example:"false" extensions:"x-order=21"`                                          // 是否是跟单系统订单
	IsTrader       bool              `json:"is_trader" example:"true" extensions:"x-order=22"`                                           // 是否是交易员
	OrderClient    define.OsType     `json:"order_client" swaggertype:"integer" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=23"` // 下单客户端类型（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	EntrustOrderId int64             `json:"entrust_order_id" example:"12512512912939223" extensions:"x-order=24"`                       // 委托订单id
	EntrustType    int               `json:"entrust_type" example:"0" enums:"0,1,2" extensions:"x-order=25"`                             // 委托类型 0-市价单 1-限价单 2-强平单
	EntrustMode    int               `json:"entrust_mode" example:"3" enums:"1,2,3" extensions:"x-order=26"`                             // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustStatus  int               `json:"entrust_status" example:"200" enums:"0,100,101,102,200,201" extensions:"x-order=27"`         // 委托状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
	ShareResource  *UsdShareResource `json:"share_resource" extensions:"x-order=28"`                                                     // 分享数据
}

type DocUsdUserContractArg struct {
	UserID       int64  `json:"user_id" swaggerignore:"true"`                           // 用户id
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"` // 合约代码
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=02"`        // 币种名
}

type DocUsdConditionOrder struct {
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=01"`                          // 币种名
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                       // 合约code
	ContractName    string          `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                       // 中文合约名
	ContractNameEn  string          `json:"contract_name_en" example:"BTCUSD" extensions:"x-order=04"`                    // 英文合约名
	PlanOrderID     int64           `json:"plan_order_id" example:"123124124002312" extensions:"x-order=05"`              // 条件单id
	Status          int             `json:"status" example:"2" enums:"0,1,2,3" extensions:"x-order=06"`                   // 条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败）
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=07"`                         // 委托方向 B-买 S-卖
	CreateTime      int64           `json:"create_time" example:"**********" extensions:"x-order=08"`                     // 提交时间
	Condition       int8            `json:"condition" example:"1" enums:"1,2" extensions:"x-order=09"`                    // 条件 1 >=, 2 <=
	Volume          decimal.Decimal `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=10"`        // 下单张数
	TriggerPrice    decimal.Decimal `json:"trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 触发价格
	OrderTime       int64           `json:"order_time" example:"**********" extensions:"x-order=12"`                      // 触发时间 10位秒时间戳 未触发时为0
	AccountType     int8            `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=13"`               // 账户模式 1全仓 2逐仓 3-带单
	Lever           int             `json:"lever" example:"20" extensions:"x-order=14"`                                   // 杠杆倍数
	TriggerType     int             `json:"trigger_type" example:"1" enums:"1,2,4" extensions:"x-order=15"`               // 触发类型 1-条件单 2-止盈单 4-止损单
	Mode            int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=16"`                       // 委托类型，1-对手价，2-最优3挡 3-最后5挡
	EntrustType     int             `json:"entrust_type" example:"1" extensions:"x-order=17"`                             // 执行类型 0-市价 1-限价
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=18"` // 执行价格
	EntrustStrategy int             `json:"entrust_strategy" example:"3" enums:"1,2,3" extensions:"x-order=19"`           // 委托策略 0-默认 1-fok 2-ioc 3-maker
}

type DocUsdStopPriceListArg struct {
	UserID      int64 `json:"user_id" swaggerignore:"true"`                                 // 用户id
	PlanOrderID int64 `json:"plan_order_id" example:"124125125125" extensions:"x-order=01"` // 持仓id
	PlatformID  int   `json:"platform_id" swaggerignore:"true"`                             // 平台id
}

type DocUsdStopPriceListReply struct {
	PlanCloseOrderID int64  `json:"plan_close_order_id" example:"125125125" extensions:"x-order=01"` // 止盈止损id
	ContractCode     string `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`          // 合约代码
	CurrencyName     string `json:"currency_name" example:"BTC" extensions:"x-order=03"`             // 保证金币种
	Side             string `json:"side" example:"B" enums:"B,S" extensions:"x-order=04"`            // 持仓方向
	Amount           string `json:"amount" example:"123.232" extensions:"x-order=05"`                // 数量
	Limit            string `json:"limit" example:"123.123" extensions:"x-order=06"`                 // 止盈价
	Stop             string `json:"stop" example:"123.123" extensions:"x-order=07"`                  // 止损价
	EntrustLimit     string `json:"entrust_limit" example:"123.123" extensions:"x-order=08"`         // 止盈委托价
	EntrustStop      string `json:"entrust_stop" example:"123.123" extensions:"x-order=09"`          // 止损委托价
	CreateTime       int64  `json:"create_time" example:"1232521321" extensions:"x-order=10"`        // 创建时间(秒时间戳)
}

type DocUsdCurrentEntrustOrder struct {
	OrderID         string          `json:"order_id" example:"1251251251221" extensions:"x-order=01"`                      // 订单id
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                        // 合约代码
	ContractName    string          `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                        // 合约名称
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=04"`                           // 币种名
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 多空方向 B-多 S-空
	Offset          string          `json:"offset" example:"O" enums:"O,C" extensions:"x-order=06"`                        // 开平方向 O-开 C-平
	AccountType     int             `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=07"`                // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `json:"lever" example:"20" extensions:"x-order=08"`                                    // 账户杠杆
	State           int             `json:"state" example:"0" enums:"0,100,101,102,200,201" extensions:"x-order=09"`       // 成交状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
	Mode            int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=10"`                        // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustTimeUnix int64           `json:"entrust_time" example:"**********" extensions:"x-order=11"`                     // 委托时间
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=12"`  // 委托价格
	TradeVolume     decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=13"`   // 成交数量
	EntrustVolume   decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=14"` // 委托数量
	Limit           decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=15"`          // 止盈价格
	Stop            decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=16"`           // 止损价
}

type DocUsdCurrentPlanEntrustOrder struct {
	OrderID         string          `json:"order_id" example:"123412412512512" extensions:"x-order=01"`                    // 订单id
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                        // 合约代码
	ContractName    string          `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                        // 合约名称
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=04"`                           // 币种名
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 多空方向 B-多 S-空
	AccountType     int             `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=06"`                // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `json:"lever" example:"20" extensions:"x-order=07"`                                    // 账户杠杆
	Condition       int             `json:"condition" example:"1" enums:"1,2" extensions:"x-order=08"`                     // 触发条件 1 >= 2 <=
	EntrustMode     int             `json:"entrust_mode" example:"3" enums:"1,2,3" extensions:"x-order=09"`                // 执行类型 1-对手价 2-最优3挡 3-最优5挡
	EntrustTimeUnix int64           `json:"entrust_time" example:"**********" extensions:"x-order=10"`                     // 委托时间
	EntrustVolume   decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 委托数量
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=12"`  // 执行价格
	TriggerPrice    decimal.Decimal `json:"trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=13"`  // 触发价格
	Limit           decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=14"`          // 止盈价格
	Stop            decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=15"`           // 止损价
}

type DocUsdHistoryEntrustArg struct {
	UserID       int64  `json:"user_id" swaggerignore:"true"`                           // 用户id
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"` // 合约代码
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=02"`        // 币种名
	Offset       string `json:"offset" example:"C" enums:"O,C" extensions:"x-order=03"` // 开平仓方向 O-开仓 C-平仓
	Filter       int    `json:"filter" example:"1" enums:"0,1" extensions:"x-order=04"` // 筛选状态 0-全部 1-已完成
}

type DocUsdHistoryEntrustOrder struct {
	OrderID         string          `json:"order_id" example:"12353152323123" extensions:"x-order=01"`                                  // 订单id
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                                     // 合约代码
	ContractName    string          `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                                     // 合约名称
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=04"`                                        // 币种名
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                                       // 多空方向 B-多 S-空
	Offset          string          `json:"offset" example:"O" enums:"O,C" extensions:"x-order=06"`                                     // 开平方向 O-开 C-平
	AccountType     int             `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=07"`                             // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	EntrustType     int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=08"`                               // 委托类型 0-市价 1-限价
	Mode            int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=09"`                                     // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	OrderClient     define.OsType   `json:"order_client" swaggertype:"integer" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=10"` // 下单设备 1 android端 2 ios端 3 web 4 h5端 5 openApi 6 系统
	Lever           int             `json:"lever" example:"10" extensions:"x-order=11"`                                                 // 账户杠杆
	State           int             `json:"state" example:"200" enums:"0,100,101,102,200,201" extensions:"x-order=12"`                  // 成交状态 101-未成交已撤 200-全部成交 201-部分成交已撤
	Source          int             `json:"source" example:"0" enums:"0,1,2,4,5" extensions:"x-order=13"`                               // 成交来源 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	EntrustTimeUnix int64           `json:"entrust_time" example:"1522323452" extensions:"x-order=14"`                                  // 委托时间
	UpdateTimeUnix  int64           `json:"update_time" example:"1522323452" extensions:"x-order=15"`                                   // 更新时间
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.132" extensions:"x-order=16"`               // 委托价格
	TradePrice      decimal.Decimal `json:"trade_price" swaggertype:"string" example:"123.132" extensions:"x-order=17"`                 // 成交均价
	Limit           decimal.Decimal `json:"limit" swaggertype:"string" example:"123.132" extensions:"x-order=18"`                       // 止盈价格
	Stop            decimal.Decimal `json:"stop" swaggertype:"string" example:"123.132" extensions:"x-order=19"`                        // 止损价
	CloseProfit     decimal.Decimal `json:"close_profit" swaggertype:"string" example:"123.132" extensions:"x-order=20"`                // 收益
	ParValue        decimal.Decimal `json:"par_value" swaggertype:"string" example:"123.132" extensions:"x-order=21"`                   // 合约面值
	TradeVolume     decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.132" extensions:"x-order=22"`                // 成交数量
	EntrustVolume   decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.132" extensions:"x-order=23"`              // 委托数量
}

type DocUsdHistoryPlanEntrustArg struct {
	UserID       int64  `json:"user_id" swaggerignore:"true"`                           // 用户id
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"` // 合约代码
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=02"`        // 币种名
	Filter       int    `json:"filter" example:"0" extensions:"x-order=03"`             // 筛选状态 0-全部 1-计划单 2-止盈止损
}

type DocUsdHistoryPlanEntrustOrder struct {
	OrderID         string          `json:"order_id" example:"12451325123213" extensions:"x-order=01"`                     // 订单id
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                        // 合约代码
	ContractName    string          `json:"contract_name" example:"BTCUSD" extensions:"x-order=03"`                        // 合约名称
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=04"`                           // 币种名
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                          // 多空方向 B-多 S-空
	AccountType     int             `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=06"`                // 账户模式 1：全仓 2：逐仓 3: 带单跟单
	Lever           int             `json:"lever" example:"20" extensions:"x-order=07"`                                    // 账户杠杆
	State           int             `json:"state" example:"2" enums:"2,3" extensions:"x-order=08"`                         // 状态 2-触发成功 3-触发成功,委托失败
	EntrustType     int             `json:"entrust_type" example:"1" enums:"0,1" extensions:"x-order=09"`                  // 委托类型 0-市价 1-限价
	TriggerIndex    int             `json:"trigger_index" example:"1" enums:"0,1" extensions:"x-order=10"`                 // 触发标的 0-成交价 1-标记价格
	TriggerType     int             `json:"trigger_type" example:"1" enums:"1,2,3" extensions:"x-order=11"`                // 类型 1-计划单 2-止盈 4-止损
	EntrustMode     int             `json:"entrust_mode" example:"3" enums:"1,2,3" extensions:"x-order=12"`                // 执行模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustTimeUnix int64           `json:"entrust_time" example:"1522323452" extensions:"x-order=13"`                     // 委托时间
	UpdateTimeUnix  int64           `json:"update_time" example:"1522323452" extensions:"x-order=14"`                      // 更新时间
	EntrustVolume   decimal.Decimal `json:"entrust_volume" swaggertype:"string" example:"123.123" extensions:"x-order=15"` // 委托数量
	TriggerPrice    decimal.Decimal `json:"trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=16"`  // 触发价格
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=17"`  // 执行价格
	Limit           decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=18"`          // 止盈价格
	Stop            decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=19"`           // 止损价
}

type DocUsdHistoryEntrustOrderShareResource struct {
	ContractCode  string            `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"`                        // 合约代码
	CurrencyName  string            `json:"currency_name" example:"BTC" extensions:"x-order=02"`                           // 币种名
	Side          string            `json:"side" example:"B" enums:"B,S" extensions:"x-order=03"`                          // 多空方向 B-平空 S-平多
	OpenAvgPrice  decimal.Decimal   `json:"open_avg_price" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 开仓均价
	TradePrice    decimal.Decimal   `json:"trade_price" swaggertype:"string" example:"123.123" extensions:"x-order=05"`    // 成交均价
	CloseProfit   decimal.Decimal   `json:"close_profit" swaggertype:"string" example:"123.123" extensions:"x-order=06"`   // 收益
	ProfitRatio   decimal.Decimal   `json:"profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=07"`   // 收益率
	CurrentPrice  decimal.Decimal   `json:"current_price" swaggertype:"string" example:"123.123" extensions:"x-order=08"`  // 当前最新价
	ShareResource *UsdShareResource `json:"share_resource" extensions:"x-order=09"`                                        // 分享资源
}
