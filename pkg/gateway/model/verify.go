package model

import "bc/libs/define"

type DocVerifyStateInfo struct {
	VerifyID       int64                  `json:"verify_id" example:"123214" extensions:"x-order=01"`              // 认证记录id
	State          define.UserVerifyState `json:"state" swaggertype:"integer" example:"1" extensions:"x-order=02"` // 认证状态 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
	ErrCode        int                    `json:"err_code" example:"0" extensions:"x-order=03"`                    // 认证错误码
	ErrMsg         string                 `json:"err_msg" example:"" extensions:"x-order=04"`                      // 认证错误信息
	Name           string                 `json:"name" example:"sa gd" extensions:"x-order=05"`                    // 姓名
	Surname        string                 `json:"surname" example:"sa" extensions:"x-order=06"`                    // 姓
	Forename       string                 `json:"forename" example:"gd" extensions:"x-order=07"`                   // 名
	Number         string                 `json:"number" example:"1343253259329" extensions:"x-order=08"`          // 证件号码
	CardType       int                    `json:"card_type" example:"1" extensions:"x-order=09"`                   // 证件类型 0-大陆证件 1-非大陆证件
	StaticIdentity bool                   `json:"static_identity" example:"false" extensions:"x-order=10"`         // 是否固定了身份信息
}

type DocVerifyByIdNameArg struct {
	Name   string `json:"name" example:"sdfa" extensions:"x-order=01"`             // 姓名
	Number string `json:"number" example:"13523543534643" extensions:"x-order=02"` // 身份证号
	YiDunRequest
}

type DocForeignVerifyArg struct {
	Surname     string `json:"surname" example:"da" extensions:"x-order=01"`                   // 姓
	Forename    string `json:"forename" example:"dn" extensions:"x-order=02"`                  // 名
	FullName    string `json:"full_name" example:"dn da" extensions:"x-order=03"`              // 全名
	Number      string `json:"number" example:"13532523532" extensions:"x-order=04"`           // 身份证号
	CountryCode string `json:"country_code" example:"KR" extensions:"x-order=05"`              // 国家编号
	Photo       string `json:"photo" example:"https://www.a.cn/a.png" extensions:"x-order=06"` // 证件照
}

type DocManualVerifyArg struct {
	AvatarPhoto string `json:"avatar_photo" example:"https://www.a.cn/a.png" extensions:"x-order=01"` // 人像面
	EmblemProto string `json:"emblem_proto" example:"https://www.a.cn/b.png" extensions:"x-order=02"` // 国徽面
	HoldProto   string `json:"hold_proto" example:"https://www.a.cn/c.png" extensions:"x-order=03"`   // 手持证件照
}

type DocFaceVerifyArg struct {
	ProtoBuf    []byte `json:"proto_buf" example:"12,5,35,11,62,111,51" extensions:"x-order=01"` // 活体数据二进制数据
	ProtoBufHex string `json:"proto_buf_hex" example:"8ac2d2231ae232c" extensions:"x-order=02"`  // 活体数据16进制字符串
}

type DocFaceVerifyH5Arg struct {
	Base64Image string `json:"base64_image" example:"dafini2if2i3jti2h483==" extensions:"x-order=01"` // h5静默活体认证通过后返回的base64_image(活体认证中的一帧人脸图片)
}
