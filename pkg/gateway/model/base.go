package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocArg struct {
	Data interface{} `json:"data" swaggertype:"object" extensions:"x-order=01"` // 请求数据
}

type DocReply struct {
	Ret  int         `json:"ret" example:"0" extensions:"x-order=01"`           // 状态
	Msg  string      `json:"msg" example:"" extensions:"x-order=02"`            // 状态消息
	Data interface{} `json:"data" swaggertype:"object" extensions:"x-order=03"` // 返回数据
}

type DocIDArg struct {
	ID int64 `json:"id" example:"12512512412" extensions:"x-order=01"` // 记录id
}

type DocUpgradeInfo struct {
	NeedUpgrade   bool   `json:"needUpgrade" example:"false" extensions:"x-order=01"`                            // 需要更新
	VersionString string `json:"version" example:"1.2.3" extensions:"x-order=02"`                                // 文字版本号
	Content       string `json:"content" example:"1.xxx\n2.yyy\n3.zzz" extensions:"x-order=03"`                  // 更新内容
	ForceUpgrade  bool   `json:"force" example:"false" extensions:"x-order=04"`                                  // 是否强制更新
	Link          string `json:"link" example:"https://www.a.cn/downloads/" extensions:"x-order=05"`             // 下载界面
	Url           string `json:"url" example:"https://www.a.cn/downloads/xxx.apk" extensions:"x-order=06"`       // 下载链接
	ExtraUrl      string `json:"extra_url" example:"https://www.a.cn/downloads/xxx.apk" extensions:"x-order=07"` // 额外下载链接
	CreatedTime   int64  `json:"created_time" example:"1652341244" extensions:"x-order=08"`                      // 更新时间(时间戳)
}

type DocAppDownloadReply struct {
	OS   define.OsType                    `json:"os" swaggertype:"integer" example:"2" extensions:"x-order=01"` // 客户端类型 1 android 2 ios
	List map[string]DocAppDownloadSubData `json:"list" extensions:"x-order=02"`                                 // 下载链接 键为下载方式 fast-极速下载 fast_2-极速下载2 local-本地下载 spare-备用下载 app_store-苹果商店 test_flight-Testflight google_play-谷歌商店
}

type DocAppDownloadSubData struct {
	Version string `json:"version" example:"1.2.3" extensions:"x-order=01"`        // 版本号
	URL     string `json:"url" example:"//www.a.cn/x.ipa" extensions:"x-order=02"` // 下载地址
}

type DocHotfixPatch struct {
	OsType     define.OsType `json:"os_type" swaggertype:"integer" enums:"1,2" example:"2" extensions:"x-order=01"` // 客户端类型 1-安卓 2-ios
	Version    int           `json:"version" example:"1238" extensions:"x-order=02"`                                // 客户端版本
	PatchName  string        `json:"patch_name" example:"hotfix-trade.js" extensions:"x-order=03"`                  // 补丁名称
	PatchURL   string        `json:"patch_url" example:"https://www.a.cn/hotfix-trade.js" extensions:"x-order=04"`  // 补丁下载地址
	CreateTime int64         `json:"create_time" example:"1652342233" extensions:"x-order=05"`                      // 创建时间
}

type DocPong struct {
	IP string `json:"ip" example:"***************" extensions:"x-order=01"` // 当前客户端ip
}

type DocCoin struct {
	DocExchangeCoinConfig
	CanTransfer       bool                `json:"can_transfer" example:"false" extensions:"x-order=01"`                          // false: 不支持划转 true:支持划转
	CurrencyId        int                 `json:"currency_id" example:"36" extensions:"x-order=02"`                              // 币种id
	CurrencyName      string              `json:"currency_name" example:"USDT" extensions:"x-order=03"`                          // 币种名称
	FullName          string              `json:"full_name" example:"USDT" extensions:"x-order=04"`                              // 币全称
	Icon              string              `json:"icon" example:"https://www.a.cn/usdt.png" extensions:"x-order=05"`              // 币图标
	MaxTransfer       float64             `json:"max_transfer" example:"123.123" extensions:"x-order=06"`                        // 资产划转单笔最大数量
	MaxWithdraw       float64             `json:"max_withdraw" example:"123.123" extensions:"x-order=07"`                        // 单笔最大提币
	MinTransfer       float64             `json:"min_transfer" example:"123.123" extensions:"x-order=08"`                        // 资产划转单笔最小数量
	MinWithdraw       float64             `json:"min_withdraw" example:"123.123" extensions:"x-order=09"`                        // 单笔最小提币
	BasicWithdraw     decimal.Decimal     `json:"basic_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=10"` // 基础单日限额(未认证用户)
	DailyWithdraw     decimal.Decimal     `json:"daily_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 高级单日限额
	MinerFee          float64             `json:"miner_fee" example:"123.123" extensions:"x-order=12"`                           // 单笔提币手续费
	PayConfirms       int                 `json:"pay_confirms" example:"20" extensions:"x-order=13"`                             // 入账需要确认数
	Precision         int32               `json:"precision" example:"8" extensions:"x-order=14"`                                 // 精度（小数位）
	WithdrawPrecision int32               `json:"withdraw_precision" example:"6" extensions:"x-order=145"`                       //提币精度（小数位）
	RechargeEnable    bool                `json:"recharge_enable" example:"true" extensions:"x-order=16"`                        // 充值 true：开启 false：禁止
	Status            int                 `json:"status" example:"1" extensions:"x-order=17"`                                    // 0：下架 1：正常
	Tag               string              `json:"tag" example:"ERC20" extensions:"x-order=18"`                                   // 币种标识 如usdt ERC20
	WalletName        string              `json:"wallet_name" example:"usdt2" extensions:"x-order=19"`                           // 钱包名称
	WithdrawEnable    bool                `json:"withdraw_enable" example:"true" extensions:"x-order=20"`                        // 提现 true：开启 false：禁止
	WithdrawReview    float64             `json:"withdraw_review" example:"123.123" extensions:"x-order=21"`                     // 单次提现审核数量
	WithdrawTime      int                 `json:"withdraw_time" example:"2" extensions:"x-order=22"`                             // 单日提币次数
	SortWeight        float64             `json:"sort_weight" example:"0.123" extensions:"x-order=23"`                           // 资产显示排序权重
	ForwardEnable     bool                `json:"forward_enable" example:"true" extensions:"x-order=24"`                         // 是否可用于正向合约
	ReverseEnable     bool                `json:"reverse_enable" example:"false" extensions:"x-order=25"`                        // 是否可用于反向合约
	ThirdLegalEnable  bool                `json:"third_legal_enable" example:"true" extensions:"x-order=26"`                     // 是否支持三方平台买币
	ThirdLegalDefault bool                `json:"third_legal_default" example:"true" extensions:"x-order=27"`                    // 是否是三方买币默认币种
	Multiple          bool                `json:"multiple" example:"true" extensions:"x-order=28"`                               // 多协议币种
	NeedTag           bool                `json:"need_tag" example:"false" extensions:"x-order=29"`                              // 是否需要标签
	Protocols         []DocWalletCoinConf `json:"protocols" extensions:"x-order=30"`                                             // 协议列表
}

type DocExchangeCoinConfig struct {
	IsExchange               bool            `json:"is_exchange" example:"true" extensions:"x-order=86"`                                         // 是否支持兑换
	MinExchange              decimal.Decimal `json:"min_exchange" swaggertype:"string" example:"123.123" extensions:"x-order=87"`                // 最小交易量
	MaxExchange              decimal.Decimal `json:"max_exchange" swaggertype:"string" example:"123.123" extensions:"x-order=88"`                // 最大交易量
	MinExchangeMoney         decimal.Decimal `json:"min_exchange_money" swaggertype:"string" example:"123.123" extensions:"x-order=89"`          // 最大交易量
	DailyExchangeMoney       decimal.Decimal `json:"daily_exchange_money" swaggertype:"string" example:"123.123" extensions:"x-order=90"`        // 每日最大交易额
	MaxExchangeCoinPoint     decimal.Decimal `json:"max_exchange_coin_point" swaggertype:"string" example:"123.123" extensions:"x-order=91"`     // 币本位加点
	MaxExchangeUsdtPoint     decimal.Decimal `json:"max_exchange_usdt_point" swaggertype:"string" example:"123.123" extensions:"x-order=92"`     // 兑换美元加点
	ExchangeFeeRate          decimal.Decimal `json:"exchange_fee_rate" swaggertype:"string" example:"123.123" extensions:"x-order=93"`           // 兑换费率
	ExchangePriceRange       decimal.Decimal `json:"exchange_price_range" swaggertype:"string" example:"123.123" extensions:"x-order=94"`        // 兑换价格范围
	ExchangePriceStep        decimal.Decimal `json:"exchange_price_step" swaggertype:"string" example:"123.123" extensions:"x-order=95"`         // 兑换价格步长(该币种兑USDT)
	ExchangeReversePriceStep decimal.Decimal `json:"exchange_reverse_price_step" swaggertype:"string" example:"123.123" extensions:"x-order=96"` // 反向兑换价格步长(USDT兑该币种)
	ExchangePrecision        int32           `json:"exchange_precision" example:"6" extensions:"x-order=97"`                                     // 兑币精度
	IsHedge                  bool            `json:"is_hedge" example:"true" extensions:"x-order=98"`                                            // 是否支持对冲
	HedgeWarnThreshold       decimal.Decimal `json:"hedge_warn_threshold" swaggertype:"string" example:"123.123" extensions:"x-order=99"`        // 对冲账户余额不足报警阈值
}

type DocWalletCoinConf struct {
	WalletName        string          `json:"wallet_name" example:"usdt2" extensions:"x-order=01"`                            // 大钱包对应币种名
	CoinID            int             `json:"coin_id" example:"36" extensions:"x-order=02"`                                   // 币种id
	CoinName          string          `json:"coin_name" example:"USDT" extensions:"x-order=03"`                               // 币种名称
	Protocol          string          `json:"protocol" example:"ERC20" extensions:"x-order=04"`                               // 币种协议
	CanDeposit        bool            `json:"can_deposit" example:"true" extensions:"x-order=05"`                             // 支持充币
	CanWithdraw       bool            `json:"can_withdraw" example:"true" extensions:"x-order=06"`                            // 支持提币
	NeedTag           bool            `json:"need_tag" example:"false" extensions:"x-order=07"`                               // 是否需要标签
	MinWithdraw       decimal.Decimal `json:"min_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=08"`    // 最小提现数量
	MaxWithdraw       decimal.Decimal `json:"max_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=09"`    // 单次最大提现数量
	BasicWithdraw     decimal.Decimal `json:"basic_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=10"`  // 基础单日限额(未认证用户)
	DailyWithdraw     decimal.Decimal `json:"daily_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=11"`  // 高级单日限额
	WithdrawFee       decimal.Decimal `json:"withdraw_fee" swaggertype:"string" example:"123.123" extensions:"x-order=12"`    // 提现手续费
	WithdrawReview    decimal.Decimal `json:"withdraw_review" swaggertype:"string" example:"123.123" extensions:"x-order=13"` // 提现需要审核数
	WithdrawTime      int             `json:"withdraw_time" example:"2" extensions:"x-order=14"`                              // 每日提币次数限制
	WithdrawPrecision int32           `json:"withdraw_precision" example:"6" extensions:"x-order=15"`                         // 提币输入最大精度位数
	ConfirmNum        int             `json:"confirm_num" example:"20" extensions:"x-order=16"`                               // 充提币确认数
	Weight            float64         `json:"weight" example:"0.7" extensions:"x-order=17"`                                   // 排序权重 从大到小排序
}

type DocBannerReply struct {
	Image string `json:"image" example:"https://www.a.cn/b.png" extensions:"x-order=01"` // 图片
	Link  string `json:"link" example:"https://www.a.cn/" extensions:"x-order=02"`       // 跳转链接
}

type DocNoticeListReply struct {
	ID        int    `json:"id" example:"123" extensions:"x-order=01"`                                    // 公告id
	IsTop     bool   `json:"is_top" example:"false" extensions:"x-order=02"`                              // 是否置顶
	CreatedAt int64  `json:"created_at" example:"1535245542" extensions:"x-order=03"`                     // 创建时间
	Title     string `json:"title" example:"震惊" extensions:"x-order=04"`                                  // 标题
	Link      string `json:"link" example:"https://www.a.cn/notice?id=123&lan=2" extensions:"x-order=05"` // 跳转链接
}

type DocNoticeInfoReply struct {
	ID        int    `json:"id" example:"124123" extensions:"x-order=01"`             // 公告id
	CreatedAt int64  `json:"created_at" example:"1652342342" extensions:"x-order=02"` // 发布时间
	Title     string `json:"title" example:"震惊" extensions:"x-order=03"`              // 标题
	Content   string `json:"content" example:"无内容" extensions:"x-order=04"`           // 内容
}

type DocImportantNoticeReply struct {
	ID         int    `json:"id" example:"123" extensions:"x-order=01"`                    // 通知id
	Title      string `json:"title" example:"紧急通知" extensions:"x-order=02"`                // 标题
	Content    string `json:"content" example:"过年了" extensions:"x-order=03"`               // 内容
	Link       string `json:"link" example:"https://www.a.cn/" extensions:"x-order=04"`    // 跳转链接
	NoticeType int    `json:"notice_type" example:"1" enums:"1,2" extensions:"x-order=05"` // 通知类型 1-文本通知 2-图片通知
}

type DocAboutCommunityReply struct {
	SupportEmail string         `json:"support_email" example:"<EMAIL>" extensions:"x-order=01"` // 客服邮箱
	Communities  []DocCommunity `json:"communities" extensions:"x-order=02"`                      // 社群列表
}

type DocCommunity struct {
	Name     string `json:"name" example:"Wechat" extensions:"x-order=01"`                  // 社群名称(只会返回英文名称)
	Icon     string `json:"icon" example:"https://www.a.cn/wx.png" extensions:"x-order=02"` // 社群图标
	Account  string `json:"account" example:"pdfex" extensions:"x-order=03"`                // 账户名
	Link     string `json:"link" example:"https://www.a.cn/qr.png" extensions:"x-order=04"` // 链接(微信为二维码链接)
	IsWechat bool   `json:"is_wechat" example:"true" extensions:"x-order=05"`               // 是否是微信(微信连接为二维码图片链接,需要特殊处理)
}

type DocServerConfig struct {
	HostList             []string        `json:"host_list" extensions:"x-order=01"`                                        // 域名列表
	ServerHosts          []DocServerHost `json:"server_hosts" extensions:"x-order=02"`                                     // 域名列表
	PublicURL            string          `json:"public_url" example:"x.cn" extensions:"x-order=03"`                        // 公共域名
	PrivateURL           string          `json:"private_url" example:"x.cn" extensions:"x-order=04"`                       // 私有域名
	IsSlider             bool            `json:"is_slider" example:"false" extensions:"x-order=05"`                        // 是否开启网易滑块
	IsThirdKYC           bool            `json:"is_third_kyc" example:"true" extensions:"x-order=06"`                      // 是否走三方KYC
	ChinaMobileChecking  bool            `json:"china_mobile_checking" example:"false" extensions:"x-order=07"`            // 是否开启国内手机号限制
	GestureInterval      int             `json:"gesture_interval" example:"480" extensions:"x-order=08"`                   // 手势密码间隔(分钟)
	MaxAPICount          int             `json:"max_api_count" example:"1" extensions:"x-order=09"`                        // 最大api个数
	TraderLatelyPosition int             `json:"trader_lately_position" example:"10" extensions:"x-order=10"`              // 交易员持仓数据显示
	TraderLatelyHistory  int             `json:"trader_lately_history" example:"10" extensions:"x-order=11"`               // 交易员历史数据条数
	ShowFollowUsers      int             `json:"show_follow_users" example:"10" extensions:"x-order=12"`                   // 跟随着盈利从大到小人数
	YDAppID              string          `json:"yd_app_id" example:"2353234dg43g3b" extensions:"x-order=13"`               // 易盾验证码id
	BlowingRate          decimal.Decimal `json:"blowing_rate" swaggertype:"string" example:"0.05" extensions:"x-order=14"` // 爆仓率
}

type DocServerHost struct {
	Recommend   bool   `json:"recommend" example:"false" extensions:"x-order=01"`       // 是否推荐
	HostAddress string `json:"host_address" example:"api.x.cn" extensions:"x-order=02"` // 主机地址
}

type DocCoinLegalPrice struct {
	USD        decimal.Decimal `json:"usd" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 美元价格
	CNY        decimal.Decimal `json:"cny" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 人民币价格
	KRW        decimal.Decimal `json:"krw" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 韩元价格
	VND        decimal.Decimal `json:"vnd" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 越南盾价格
	IDR        decimal.Decimal `json:"idr" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 印尼盾价格
	RUB        decimal.Decimal `json:"rub" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 卢布价格
	EUR        decimal.Decimal `json:"eur" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 欧元价格
	UpdateTime int64           `json:"update_time" example:"1651234422" extensions:"x-order=99"`           // 更新时间
}

type DocUserIDArg struct {
	UserID int64 `json:"user_id" example:"12423141213241" extensions:"x-order=01"` // 用户id
}

type DocPageArg struct {
	Page  int `json:"page" example:"0" extensions:"x-order=98"`   // 页码,从0开始
	Count int `json:"count" example:"10" extensions:"x-order=99"` // 单页数据量
}

type DocCoinNameArg struct {
	CoinName string `json:"coin_name" example:"BTC" extensions:"x-order=01"` // 币种名称
}

type DocAppStatusReply struct {
	Version string `json:"version" example:"1.0.0" extensions:"x-order=01"` // 版本号
}

type DocGetRiskProvisionsArg struct {
	CoinName string `json:"coin_name" example:"USDT" extensions:"x-order=01"` // 币种名
}

type DocRiskProvisionsReply struct {
	List []DocRiskProvisionsSubData `json:"list" extensions:"x-order=01"` // 数据列表
}

type DocRiskProvisionsSubData struct {
	Amount  decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 风险准备金
	OwnDate int64           `json:"own_date" example:"1652452255" extensions:"x-order=02"`                 // 归属日期时间戳
}
