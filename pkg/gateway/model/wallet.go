package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocGetDepositAddressArg struct {
	CoinId   int    `json:"coin_id" example:"1" extensions:"x-order=01"`      // 币种id
	Protocol string `json:"protocol" example:"ERC20" extensions:"x-order=02"` // 币种协议,没有可传空
}

type DocGetDepositAddressReply struct {
	CoinId       int    `json:"coin_id" example:"1" extensions:"x-order=01"`                       // 币种id
	VerifyNumber int    `json:"verify_number" example:"20" extensions:"x-order=02"`                // 充提币确认数
	Address      string `json:"address" example:"0x23ifjd92j4g2ds82k4kd2" extensions:"x-order=03"` // 地址
	Tag          string `json:"tag" example:"235123" extensions:"x-order=04"`                      // tag
	Protocol     string `json:"protocol" example:"ERC20" extensions:"x-order=05"`                  // 协议
}

type DocGetWithdrawAddrListArg struct {
	UserID   int64  `json:"user_id" example:"252135893832432" extensions:"x-order=01"` // 用户id
	CoinName string `json:"coin_name" example:"BTC" extensions:"x-order=02"`           // 币种名称
	Protocol string `json:"protocol" example:"ERC20" extensions:"x-order=03"`          // 协议
}

type DocUserWithdrawAddress struct {
	ID           int64  `json:"id" example:"125124123123" extensions:"x-order=01"`              // 记录id
	UserID       int64  `json:"user_id" example:"1359223325325215929" extensions:"x-order=02"`  // 用户id
	CoinName     string `json:"coin_name" example:"USDT" extensions:"x-order=03"`               // 币种名称
	CoinProtocol string `json:"coin_protocol" example:"ERC20" extensions:"x-order=04"`          // 币种协议
	CoinTag      string `json:"coin_tag" example:"325923923" extensions:"x-order=05"`           // 币种tag
	Addr         string `json:"addr" example:"0x23598nf3k2jgr8j34jk3" extensions:"x-order=06"`  // 提币地址
	AddrAlias    string `json:"addr_alias" example:"我的地址" extensions:"x-order=07"`              // 地址别名
	IsDefault    bool   `json:"is_default" example:"true" extensions:"x-order=08"`              // 是否是默认地址
	IsTrust      bool   `json:"is_trust" example:"false" extensions:"x-order=09"`               // 是否是信任地址
	IsDefaultNum uint8  `json:"is_default_num" example:"1" enums:"0,1" extensions:"x-order=10"` // 是否是默认地址 0-否 1-是
	IsTrustNum   uint8  `json:"is_trust_num" example:"0" enums:"0,1" extensions:"x-order=11"`   // 是否是信任地址 0-否 1-是
	CreateTime   int64  `json:"create_time" example:"1523324215" extensions:"x-order=12"`       // 创建时间戳
}

type GetWithdrawAddrListReply struct {
	HasDefault bool                     `json:"has_default" example:"false" extensions:"x-order=01"` // 是否有默认地址
	List       []DocUserWithdrawAddress `json:"list" extensions:"x-order=02"`                        // 地址列表
}

type DocModifyWithdrawAddrArg struct {
	VerifyID     int64  `json:"verify_id" example:"1235435235" extensions:"x-order=01"`            // 安全校验id
	ID           int64  `json:"id" example:"31431531231" extensions:"x-order=02"`                  // 记录id(添加时传0)
	CoinName     string `json:"coin_name" example:"USDT" extensions:"x-order=03"`                  // 币种名称
	CoinProtocol string `json:"coin_protocol" example:"ERC20" extensions:"x-order=04"`             // 币种协议
	CoinTag      string `json:"coin_tag" example:"235325324" extensions:"x-order=05"`              // 币种tag
	Addr         string `json:"addr" example:"0xdfj23f3ij34i2ji34342fdf2" extensions:"x-order=06"` // 提币地址
	AddrAlias    string `json:"addr_alias" example:"矿场" extensions:"x-order=07"`                   // 地址别名
	IsDefaultNum uint8  `json:"is_default_num" example:"0" enums:"0,1" extensions:"x-order=08"`    // 是否是默认地址 0-否 1-是
	IsTrustNum   uint8  `json:"is_trust_num" example:"1" enums:"0,1" extensions:"x-order=09"`      // 是否是信任地址 0-否 1-是
}

type DocDelWithdrawAddrArg struct {
	ID int64 `json:"id" example:"1243151" extensions:"x-order=01"` // 记录id
}

type DocWithdrawArg struct {
	VerifyID       int64  `json:"verify_id" example:"1325321523523" extensions:"x-order=01"`                         // 校验id
	CoinName       string `json:"coin_name" example:"BTC" extensions:"x-order=02"`                                   // 币种名称
	Amount         string `json:"amount" example:"123.1421" extensions:"x-order=03"`                                 // 提现金额
	Address        string `json:"address" example:"0x23532kf34jfk34ji3" extensions:"x-order=04"`                     // 到账地址
	Tag            string `json:"tag" example:"234652343" extensions:"x-order=05"`                                   // 币种tag
	Protocol       string `json:"protocol" example:"ERC20" extensions:"x-order=06"`                                  // 协议
	Code           string `json:"code" example:"252333" extensions:"x-order=07"`                                     // 短信验证码
	PhoneCode      string `json:"phone_code" example:"252333" extensions:"x-order=08"`                               // 两步验证 短信验证码
	EmailCode      string `json:"email_code" example:"252333" extensions:"x-order=09"`                               // 两步验证 邮箱验证码
	SpareEmailCode string `json:"spare_email_code" example:"252333" extensions:"x-order=10"`                         // 两步验证 备用邮箱验证码
	TotpCode       string `json:"totp_code" example:"252333" extensions:"x-order=11"`                                // 两步验证 令牌验证码
	FundPassword   string `json:"fund_password" example:"gadji2ji2ii3jfiJDFIj2i43fjKDFjk==" extensions:"x-order=12"` // 两步验证 资金密码
}

type DocAssetListArg struct {
	AssetType define.AssetType `json:"asset_type" swaggertype:"integer" example:"1" enums:"0,1,2,4,8,16" extensions:"x-order=01"` // 资产类型,多项相加 0-全部 1-资产账户 2-交易账户 4-跟单账户 8-usd交易账户 16-现货交易账户
}

type DocAssetDetailArg struct {
	AssetType define.AssetType `json:"asset_type" swaggertype:"integer" example:"8" enums:"1,2,4,8,16" extensions:"x-order=01"` // 资产类型 1-资产账户 2-交易账户 4-跟单账户 8-usd交易账户 16-现货交易账户
	CoinName  string           `json:"coin_name" example:"BTC" extensions:"x-order=02"`                                         // 币种名称
}

type DocAssetDetail struct {
	TotalUSDT  decimal.Decimal            `json:"total_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=01"`  // 总折合USDT
	TotalLegal decimal.Decimal            `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 总折合法币
	Wallet     DocMultipleWallet          `json:"wallet" extensions:"x-order=03"`                                             // 资产账户
	Trade      DocTradeWalletDetail       `json:"trade" extensions:"x-order=04"`                                              // 交易账户
	Follow     DocFollowWalletDetail      `json:"follow" extensions:"x-order=05"`                                             // 跟单账户
	UsdTrade   DocMultipleTradeWallet     `json:"usd_trade" extensions:"x-order=06"`                                          // usd交易账户
	SpotTrade  DocMultipleSpotAssetDetail `json:"spot_trade" extensions:"x-order=07"`                                         // 现货交易账户
}

type DocMultipleTradeWallet struct {
	TotalUSDT  decimal.Decimal      `json:"total_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=01"`  // 总折合USDT
	TotalLegal decimal.Decimal      `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 总折合法币
	List       []DocCoinAssetDetail `json:"list" extensions:"x-order=03"`                                               // 账户列表
}

type DocTradeWalletDetail struct {
	Total            decimal.Decimal `json:"total" swaggertype:"string" example:"123.123" extensions:"x-order=01"`              // 总资产
	TotalLegal       decimal.Decimal `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=02"`        // 总折合法币
	Rights           decimal.Decimal `json:"rights" swaggertype:"string" example:"123.123" extensions:"x-order=03"`             // 权益
	Float            decimal.Decimal `json:"float" swaggertype:"string" example:"123.123" extensions:"x-order=04"`              // 未实现盈亏
	Margin           decimal.Decimal `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=05"`             // 仓位保证金
	TotalMargin      decimal.Decimal `json:"total_margin" swaggertype:"string" example:"123.123" extensions:"x-order=06"`       // 总保证金 初始保证金+手续费
	Balance          decimal.Decimal `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=07"`            // 账户资产
	Available        decimal.Decimal `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=08"`          // 可用余额
	CanTransfer      decimal.Decimal `json:"can_transfer" swaggertype:"string" example:"123.123" extensions:"x-order=09"`       // 可划转余额
	LockAmount       decimal.Decimal `json:"lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=10"`        // 委托冻结
	Diff             decimal.Decimal `json:"diff" swaggertype:"string" example:"123.123" extensions:"x-order=11"`               // 待结算差值
	BlowingRate      decimal.Decimal `json:"blowing_rate" swaggertype:"string" example:"123.123" extensions:"x-order=12"`       // 平仓风险率
	RiskRate         decimal.Decimal `json:"risk_rate" swaggertype:"string" example:"123.123" extensions:"x-order=13"`          // 账户风险率
	TotalGiftReceive decimal.Decimal `json:"total_gift_receive" swaggertype:"string" example:"123.123" extensions:"x-order=14"` // 累计入账赠金
	GiftAvailable    decimal.Decimal `json:"gift_available" swaggertype:"string" example:"123.123" extensions:"x-order=15"`     // 赠金可用
	GiftBalance      decimal.Decimal `json:"gift_balance" swaggertype:"string" example:"123.123" extensions:"x-order=16"`       // 赠金
	GiftUsed         decimal.Decimal `json:"gift_used" swaggertype:"string" example:"123.123" extensions:"x-order=17"`          // 累计使用赠金
	GiftLockAmount   decimal.Decimal `json:"gift_lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=18"`   // 赠金委托冻结
	MarginGift       decimal.Decimal `json:"margin_gift" swaggertype:"string" example:"123.123" extensions:"x-order=19"`        // 赠金仓位保证金
}

type DocFollowWalletDetail struct {
	Rights      decimal.Decimal `json:"rights" swaggertype:"string" example:"123.123" extensions:"x-order=01"`       // 权益
	Total       decimal.Decimal `json:"total" swaggertype:"string" example:"123.123" extensions:"x-order=02"`        // 总资产
	TotalCNY    decimal.Decimal `json:"total_cny" swaggertype:"string" example:"123.123" extensions:"x-order=03"`    // 总资产(CNY)
	TotalLegal  decimal.Decimal `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=04"`  // 总折合法币
	Available   decimal.Decimal `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=05"`    // 可用余额
	Margin      decimal.Decimal `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=06"`       // 保证金
	Float       decimal.Decimal `json:"float" swaggertype:"string" example:"123.123" extensions:"x-order=07"`        // 未实现盈亏
	CanTransfer decimal.Decimal `json:"can_transfer" swaggertype:"string" example:"123.123" extensions:"x-order=08"` // 可划转余额
	LockAmount  decimal.Decimal `json:"lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=09"`  // 委托冻结
}

type DocMultipleWallet struct {
	TotalUSDT  decimal.Decimal   `json:"total_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=01"`  // 总折合USDT
	TotalLegal decimal.Decimal   `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 总折合法币
	List       []DocWalletDetail `json:"list" extensions:"x-order=03"`                                               // 账户列表
}

type DocWalletDetail struct {
	CoinID      int             `json:"coin_id" example:"1" extensions:"x-order=01"`                                 // 币种id
	CoinName    string          `json:"coin_name" example:"BTC" extensions:"x-order=02"`                             // 币种名
	Balance     decimal.Decimal `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=03"`      // 可用资产
	Frozen      decimal.Decimal `json:"frozen" swaggertype:"string" example:"123.123" extensions:"x-order=04"`       // 冻结
	CanWithdraw decimal.Decimal `json:"can_withdraw" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 可提币数量
	SortWeight  float64         `json:"sort_weight" example:"123.123" extensions:"x-order=06"`                       // 排序权重
}

type DocCoinAssetDetail struct {
	CoinName         string          `json:"coin_name" example:"BTC" extensions:"x-order=01"`                                   // 币种名称
	Total            decimal.Decimal `json:"total" swaggertype:"string" example:"123.123" extensions:"x-order=02"`              // 总资产
	TotalUSDT        decimal.Decimal `json:"total_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=03"`         // 总折合USDT
	Rights           decimal.Decimal `json:"rights" swaggertype:"string" example:"123.123" extensions:"x-order=04"`             // 权益
	FloatProfit      decimal.Decimal `json:"float_profit" swaggertype:"string" example:"123.123" extensions:"x-order=05"`       // 未实现盈亏
	Margin           decimal.Decimal `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=06"`             // 仓位保证金
	TotalMargin      decimal.Decimal `json:"total_margin" swaggertype:"string" example:"123.123" extensions:"x-order=07"`       // 总保证金 初始保证金+手续费
	Balance          decimal.Decimal `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=08"`            // 账户资产
	Available        decimal.Decimal `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=09"`          // 可用余额
	CanTransfer      decimal.Decimal `json:"can_transfer" swaggertype:"string" example:"123.123" extensions:"x-order=10"`       // 可划转余额
	LockAmount       decimal.Decimal `json:"lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=11"`        // 冻结
	Diff             decimal.Decimal `json:"diff" swaggertype:"string" example:"123.123" extensions:"x-order=12"`               // 待结算差值
	BlowingRate      decimal.Decimal `json:"blowing_rate" swaggertype:"string" example:"123.123" extensions:"x-order=13"`       // 平仓风险率
	RiskRate         decimal.Decimal `json:"risk_rate" swaggertype:"string" example:"123.123" extensions:"x-order=14"`          // 账户风险率
	TotalGiftReceive decimal.Decimal `json:"total_gift_receive" swaggertype:"string" example:"123.123" extensions:"x-order=15"` // 累计入账赠金
	GiftAvailable    decimal.Decimal `json:"gift_available" swaggertype:"string" example:"123.123" extensions:"x-order=16"`     // 赠金可用
	GiftBalance      decimal.Decimal `json:"gift_balance" swaggertype:"string" example:"123.123" extensions:"x-order=17"`       // 赠金
	GiftUsed         decimal.Decimal `json:"gift_used" swaggertype:"string" example:"123.123" extensions:"x-order=18"`          // 累计使用赠金
	GiftLockAmount   decimal.Decimal `json:"gift_lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=19"`   // 赠金委托冻结
	MarginGift       decimal.Decimal `json:"margin_gift" swaggertype:"string" example:"123.123" extensions:"x-order=20"`        // 赠金仓位保证金
}

type DocApiTransferArg struct {
	CoinName  string           `json:"coin_name" example:"BTC" extensions:"x-order=01"`                                      // 币种名称
	FromAsset define.AssetType `json:"from_asset" swaggertype:"integer" example:"1" enums:"1,2,4,8" extensions:"x-order=02"` // 转出账户资产类型 1-资产账户 2-交易账户 4-跟单账户 8-usd交易账户 16-现货交易账户
	ToAsset   define.AssetType `json:"to_asset" swaggertype:"integer" example:"2" enums:"1,2,4,8" extensions:"x-order=03"`   // 转入账户资产类型 1-资产账户 2-交易账户 4-跟单账户 8-usd交易账户 16-现货交易账户
	Amount    decimal.Decimal  `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`                // 划转数量
	CaptchaID string           `json:"captcha_id" example:"dfu32f2i3hf2ih34i2" extensions:"x-order=05"`                      // 验证码id
	Validate  string           `json:"validate" example:"N3234il2j==" extensions:"x-order=06"`                               // 提交二次校验的验证数据
}

type DocWalletAssetBillArg struct {
	BillType  define.WalletBillType  `json:"bill_type" swaggertype:"integer" example:"1" extensions:"x-order=01"` // 资产类型(需要多项直接相加进行传参) 0-全部 1(1<<0)-充币 2(1<<1)-提现 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 64(1<<6)-法币订单收币 128(1<<7)-空投奖励 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励 4096(1<<12)-法币订单卖出币 8192(1<<13)-异常资产扣除 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还) 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
	CoinName  string                 `json:"coin_name" example:"USDT" extensions:"x-order=02"`                    // 币种
	State     define.WalletBillState `json:"state" swaggertype:"integer" example:"2" extensions:"x-order=03"`     // 状态 0-全部 充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账
	LimitDays int                    `json:"limit_days" example:"30" enums:"0,7,30" extensions:"x-order=04"`      // 数据限制天数(当前仅支持7天或30天,传0默认30天)
}

type DocUserWalletBillV2 struct {
	BillId           string                 `json:"bill_id" example:"1253534256436" extensions:"x-order=01"`                    // 记录id
	UserId           int64                  `json:"user_id" example:"1253534256436" extensions:"x-order=02"`                    // 用户标识
	Amount           decimal.Decimal        `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=03"`      // 数量
	Balance          decimal.Decimal        `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=04"`     // 用户账户可用数量
	LockAmount       decimal.Decimal        `json:"lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 冻结数量
	PlatformID       int                    `json:"platform_id" example:"1" extensions:"x-order=06"`                            // 平台id
	CurrencyId       int                    `json:"currency_id" example:"36" extensions:"x-order=07"`                           // 币种id
	CurrencyName     string                 `json:"currency_name" example:"USDT" extensions:"x-order=08"`                       // 币种名称
	Tx               string                 `json:"tx" example:"0x239fj2kjdfjkji2jfjd" extensions:"x-order=09"`                 // 交易哈希
	Type             define.WalletBillType  `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=10"`             // 类型 1(1<<0)-充币 2(1<<1)-提现 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 64(1<<6)-法币订单收币 128(1<<7)-空投奖励 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励 4096(1<<12)-法币订单卖出币 8192(1<<13)-异常资产扣除 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还) 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
	Status           define.WalletBillState `json:"status" swaggertype:"integer" example:"1" extensions:"x-order=11"`           // 状态 充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账
	CreatedTimeUnix  int64                  `json:"created_time" example:"1641235522" extensions:"x-order=12"`                  // 创建时间
	ApprovedTimeUnix int64                  `json:"approved_time" example:"1641235522" extensions:"x-order=13"`                 // 提现审核时间
	Tag              string                 `json:"tag" example:"ERC20" extensions:"x-order=14"`                                // 币种标签 如usdt的ERC20
	ToAddr           string                 `json:"to_addr" example:"0xdgi23h4kw82hfijf" extensions:"x-order=15"`               // 目标地址
}

type DocUserWalletBill struct {
	Amount           float64                `json:"amount" example:"123.123" extensions:"x-order=01"`                 // 数量
	Balance          float64                `json:"balance" example:"123.123" extensions:"x-order=02"`                // 用户账户可用数量
	BillId           int64                  `json:"bill_id" example:"125315134124" extensions:"x-order=03"`           // 记录id
	CreatedTimeUnix  int64                  `json:"created_time" example:"1651235522" extensions:"x-order=04"`        // 创建时间
	ApprovedTimeUnix int64                  `json:"approved_time" example:"1651235522" extensions:"x-order=05"`       // 提现审核时间
	CurrencyId       int                    `json:"currency_id" example:"36" extensions:"x-order=06"`                 // 币种id
	CurrencyName     string                 `json:"currency_name" example:"USDT" extensions:"x-order=07"`             // 币种名称
	LockAmount       float64                `json:"lock_amount" example:"123.123" extensions:"x-order=08"`            // 冻结数量
	Status           define.WalletBillState `json:"status" swaggertype:"integer" example:"1" extensions:"x-order=09"` // 状态 充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账
	Tx               string                 `json:"tx" example:"0x32tngi23kfnk23jk4sd" extensions:"x-order=10"`       // 交易哈希
	Type             define.WalletBillType  `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=11"`   // 类型 1(1<<0)-充币 2(1<<1)-提现 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 64(1<<6)-法币订单收币 128(1<<7)-空投奖励 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励 4096(1<<12)-法币订单卖出币 8192(1<<13)-异常资产扣除 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还) 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
	UserId           int64                  `json:"user_id" example:"135435436235254" extensions:"x-order=12"`        // 用户标识
	PlatformID       int                    `json:"platform_id" example:"1" extensions:"x-order=13"`                  // 平台id
	Tag              string                 `json:"tag" example:"ERC20" extensions:"x-order=14"`                      // 币种标签 如usdt的ERC20
	ToAddr           string                 `json:"to_addr" example:"0xdfiaeifhidgwa1211dg" extensions:"x-order=15"`  // 目标地址
}

type DocBrokerageBillArg struct {
	Mode define.WalletBillType `json:"mode" swaggertype:"integer" example:"1" extensions:"x-order=01"` // 记录类型 1(1<<0)-充币 2(1<<1)-提现 4(1<<2)-划转到交易账户 8(1<<3)-划转到资产账户 16(1<<4)-邀请佣金奖励 32(1<<5)-代理佣金奖励 64(1<<6)-法币订单收币 128(1<<7)-空投奖励 256(1<<8)-资产账户划转到跟单账户 512(1<<9)-跟单账户划转到资产账户 1024(1<<10)-佣金收入 2048(1<<11)-活动奖励 4096(1<<12)-法币订单卖出币 8192(1<<13)-异常资产扣除 16384(1<<14)-币币兑换(兑出) 32768(1<<15)-币币兑换(兑入) 65536(1<<16)-币币兑换(兑出退还) 131072(1<<17)-资产账户划转到usd合约账户 262144(1<<18)-usd合约账户划转到资产账户 524288(1<<19)-资产账户划转到现货交易账户 1048576(1<<20)-现货交易账户划转到资产账户
}

type DocMultipleSpotAssetDetail struct {
	TotalUSDT  decimal.Decimal      `json:"total_usdt" swaggertype:"string" example:"123.123" extensions:"x-order=01"`  // 总折合USDT
	TotalLegal decimal.Decimal      `json:"total_legal" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 总折合法币(固定cny)
	List       []DocSpotAssetDetail `json:"list" extensions:"x-order=03"`                                               // 账户列表
}

type DocSpotAssetDetail struct {
	CoinName    string          `json:"coin_name" example:"BTC" extensions:"x-order=01"`                             // 币种名
	Balance     decimal.Decimal `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=02"`      // 账户资产
	Available   decimal.Decimal `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=03"`    // 可用余额
	LockAmount  decimal.Decimal `json:"lock_amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`  // 委托开仓冻结
	CanTransfer decimal.Decimal `json:"can_transfer" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 可划转数量
}
