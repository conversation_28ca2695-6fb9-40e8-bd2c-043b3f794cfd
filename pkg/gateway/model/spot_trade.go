package model

import (
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocSpotPlaceArg struct {
	Symbol       string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                            // 交易对
	Side         string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=02"`                         // 买卖方向 B-买 S-卖
	NToken       string          `json:"ncr" example:"dfisegijicc22i3" extensions:"x-order=03"`                        // 随机字符串,用于昉连点
	Amount       decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`        // 下单数量
	Money        decimal.Decimal `json:"money" swaggertype:"string" example:"123.123" extensions:"x-order=05"`         // 下单金额，市价必须
	EntrustPrice decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 委托价格;限价单传递价格
	EntrustType  int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=07"`                 // 委托类型 0-市价 1-限价
	Mode         int             `json:"mode" example:"0" enums:"0,1,2,3" extensions:"x-order=08"`                     // 下单模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
}

type DocSpotOrderIDArg struct {
	OrderID string `json:"order_id" example:"1235218581283123" extensions:"x-order=01"` // 订单id
}

type DocSpotOrderBatchCancelArg struct {
	OrderIds []string `json:"order_ids" example:"10001,10002" extensions:"x-order=01"` // 订单id列表
	Symbol   string   `json:"symbol" example:"BTC/USDT" extensions:"x-order=02"`       // 交易对
}

type DocSpotOrderPlanArg struct {
	Symbol          string          `json:"symbol" example:"BTC/USDT" extensions:"x-order=01"`                            // 交易对
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=02"`                         // 买卖方向 B-买 S-卖
	Price           decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=03"`         // 触发价
	Amount          decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`        // 委托币数量
	Money           decimal.Decimal `json:"money" swaggertype:"string" example:"123.123" extensions:"x-order=05"`         // 委托价值
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=06"` // 委托执行价
	EntrustType     int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=07"`                 // 委托类型 0-市价 1-限价
	EntrustStrategy int             `json:"entrust_strategy" example:"0" enums:"0,1,2,3" extensions:"x-order=08"`         // 委托策略 0-默认 1-fok 2-ioc 3-maker
	Mode            int             `json:"mode" example:"0" enums:"0,1,2,3" extensions:"x-order=09"`                     // 下单模式 0-默认 1-对手价 2-最优3挡 3-最优5挡
}

type DocSpotAssetBillArg struct {
	BillType  define.SpotTradeAccountBillType `json:"bill_type" swaggertype:"integer" example:"0" extensions:"x-order=01"` // 记录类型(获取多种类型数据则传对应类型相加之和) 1(1<<0)-交易taker手续费 2(1<<2)-转入(从资产账户) 4(1<<3)-转出(到资产账户) 8(1<<4)-交易转出 16(1<<5)-交易转入 32(1<<6)-交易maker手续费
	CoinName  string                          `json:"coin_name" example:"BTC" extensions:"x-order=02"`                     // 币种
	LimitDays int                             `json:"limit_days" example:"30" extensions:"x-order=03"`                     // 数据限制天数(当前仅支持7天或30天)
}

type DocSpotAccountHistoryReply struct {
	List []DocSpotAccountHistory `json:"list" extensions:"x-order=01"` // 列表
}

type DocSpotAccountHistory struct {
	Id           int64                           `json:"id" example:"**************" extensions:"x-order=01"`                      // 流水号
	UserId       int64                           `json:"user_id" example:"*************" extensions:"x-order=02"`                  // 用户id
	Type         define.SpotTradeAccountBillType `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=03"`           // 类型 1(1<<0)-交易taker手续费 2(1<<2)-转入(从资产账户) 4(1<<3)-转出(到资产账户) 8(1<<4)-交易转出 16(1<<5)-交易转入 32(1<<6)-交易maker手续费
	Balance      decimal.Decimal                 `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=04"`   // 账户资产
	Available    decimal.Decimal                 `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 可用余额
	Amount       decimal.Decimal                 `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=06"`    // 数量
	CurrencyName string                          `json:"currency_name" example:"BTC" extensions:"x-order=07"`                      // 币种
	CreatedTime  int64                           `json:"created_time" example:"**********" extensions:"x-order=08"`                // 创建时间
}
