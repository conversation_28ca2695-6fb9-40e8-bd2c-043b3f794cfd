package model

import (
	"bc/libs/alias"
	"time"

	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocUsdUserContractMarkArg struct {
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=01"`        // 币种名
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"` // 合约代码
}

type DocUsdTradeLeverReply struct {
	AccountType  int  `json:"account_type" example:"1" enums:"1,2" extensions:"x-order=01"` // 账户模式 1-全仓 2-逐仓
	MaxLever     int  `json:"max_lever" example:"80" extensions:"x-order=02"`               // 用户最大可设置杠杆倍数 0-不限制
	CrossLever   int  `json:"cross_lever" example:"50" extensions:"x-order=03"`             // 全仓杠杆
	LongLever    int  `json:"long_lever" example:"30" extensions:"x-order=04"`              // 逐仓多方向杠杆
	ShortLever   int  `json:"short_lever" example:"70" extensions:"x-order=05"`             // 逐仓空方向杠杆
	HoldingLong  bool `json:"holding_long" example:"false" extensions:"x-order=06"`         // 是否持有多方向订单
	HoldingShort bool `json:"holding_short" example:"true" extensions:"x-order=07"`         // 是否持有空方向订单
}

type DocUsdSwitchLeverArg struct {
	CoinName     string `json:"coin_name" example:"BTC" extensions:"x-order=01"`              // 币种名
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`       // 合约代码
	AccountType  int    `json:"account_type" example:"1" enums:"1,2" extensions:"x-order=03"` // 账户模式 1-全仓 2-逐仓
	CrossLever   int    `json:"cross_lever" example:"50" extensions:"x-order=04"`             // 全仓杠杆
	LongLever    int    `json:"long_lever" example:"30" extensions:"x-order=05"`              // 逐仓多方向杠杆
	ShortLever   int    `json:"short_lever" example:"70" extensions:"x-order=06"`             // 逐仓空方向杠杆
}

type DocUsdOrderOpenArg struct {
	ContractCode string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"`                // 合约代码
	CurrencyName string          `json:"currency_name" example:"BTC" extensions:"x-order=02"`                   // 保证金币种名
	Side         string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=03"`                  // 方向 B-买 S-卖
	Leverage     int             `json:"leverage" example:"100" extensions:"x-order=04"`                        // 杠杆
	HoldType     int             `json:"hold_type" example:"1" enums:"1,2" extensions:"x-order=05"`             // 账户模式 1-全仓 2-逐仓
	Mode         int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=06"`                // 下单模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustType  int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=07"`          // 委托类型 0-市价 1-限价
	Amount       decimal.Decimal `json:"amount" swaggertype:"string" example:"123.124" extensions:"x-order=08"` // 下单数量
	Limit        decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=09"`  // 止盈价
	Stop         decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=10"`   // 止损价
	Price        decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=11"`  // 委托价格;限价单传递价格
	NToken       string          `json:"ncr" example:"vasdirjeij" extensions:"x-order=12"`                      // 随机串,防止重放
	PositionId   string          `json:"position_id" example:"15359435992839498" extensions:"x-order=13"`       // 持仓id,加仓用
}

type DocUsdOrderOpenRsp struct {
	Order     *UsdEntrustOrder `json:"order" extensions:"x-order=01"`                                 // 订单信息
	Available string           `json:"available,omitempty" example:"123.123" extensions:"x-order=02"` // 最新可用
}

type UsdEntrustOrder struct {
	OrderID            int64           `json:"order_id" example:"1251251242412412" extensions:"x-order=01"`                       // 订单id
	UserID             int64           `json:"user_id" example:"1254352315151" extensions:"x-order=02"`                           // 用户id
	PlatformID         int             `json:"platform_id" example:"1" extensions:"x-order=03"`                                   // 平台id
	ContractCode       string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=04"`                            // 合约
	PositionID         int64           `json:"position_id" example:"12532532545234" extensions:"x-order=05"`                      // 平仓持仓id
	EntrustType        int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=06"`                      // 委托类型 0-市价 1-限价
	EntrustStrategy    int             `json:"entrust_strategy" example:"3" enums:"1,2,3" extensions:"x-order=07"`                // 委托策略 0-默认 1-fok 2-ioc 3-maker
	Offset             string          `json:"offset" example:"O" enums:"O,C" extensions:"x-order=08"`                            // O-开仓 C-平仓
	Mode               int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=09"`                            // 下单模式 1-对手价 2-最优3挡 3-最优5挡
	Side               string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=10"`                              // 委托方向 B/S 平仓为持仓反方向
	OrderType          int             `json:"order_type" example:"0" enums:"0,1,2,4,5" extensions:"x-order=11"`                  // 订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	Price              decimal.Decimal `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=12"`              // 委托价格;限价单传递价格
	Volume             decimal.Decimal `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=13"`             // 委托张数
	AccountType        int             `json:"account_type" example:"1" enums:"1,2,3" extensions:"x-order=14"`                    // 账户类型 1-全仓 2-逐仓 3-跟单
	Lever              int             `json:"lever" example:"100" extensions:"x-order=15"`                                       // 杠杠
	AssetLock          decimal.Decimal `json:"asset_lock" swaggertype:"string" example:"123.123" extensions:"x-order=16"`         // 账户锁定金额
	TradeVolume        decimal.Decimal `json:"trade_volume" swaggertype:"string" example:"123.123" extensions:"x-order=17"`       // 成交张数
	TradePrice         decimal.Decimal `json:"trade_price" swaggertype:"string" example:"123.123" extensions:"x-order=18"`        // 成交均价
	LastMatchPrice     decimal.Decimal `json:"last_match_price" swaggertype:"string" example:"123.123" extensions:"x-order=19"`   // 最后撮合价
	CostFee            decimal.Decimal `json:"cost_fee" swaggertype:"string" example:"123.123" extensions:"x-order=20"`           // 成交手续费
	CostAsset          decimal.Decimal `json:"cost_asset" swaggertype:"string" example:"123.123" extensions:"x-order=21"`         // 成交资产
	CloseProfit        decimal.Decimal `json:"close_profit" swaggertype:"string" example:"123.123" extensions:"x-order=22"`       // 平仓盈亏
	Profit             decimal.Decimal `json:"profit" swaggertype:"string" example:"123.123" extensions:"x-order=23"`             // 成交资产
	CreateTime         time.Time       `json:"create_time" example:"2020-06-01T17:25:14.67323649+08:00" extensions:"x-order=24"`  // 委托时间
	UpdateTime         time.Time       `json:"update_time" example:"2020-06-01T17:25:14.67323649+08:00" extensions:"x-order=25"`  // 更新时间
	Mark               int             `json:"mark" example:"0" enums:"0,1" extensions:"x-order=26"`                              // 用户标记 1-用户标记撤销
	ExtraID            int64           `json:"extra_id" example:"12312412251" extensions:"x-order=27"`                            // 源id
	FollowOrderID      int64           `json:"follow_order_id" example:"21412412412512" extensions:"x-order=28"`                  // 跟随委托单ID
	TriggerType        int             `json:"trigger_type" example:"0" enums:"0,1" extensions:"x-order=29"`                      // 止盈止损触发标的 0-成交价,1-标记价格
	LimitAmount        decimal.Decimal `json:"limit_amount" swaggertype:"string" example:"123.123" extensions:"x-order=30"`       // 止盈张数
	StopAmount         decimal.Decimal `json:"stop_amount" swaggertype:"string" example:"123.123" extensions:"x-order=31"`        // 止损张数
	TriggerTypeLimit   int             `json:"trigger_type_limit" example:"0" enums:"0,1" extensions:"x-order=32"`                // 止盈触发标的 0-成交价,1-标记价格
	TriggerTypeStop    int             `json:"trigger_type_stop" example:"0" enums:"0,1" extensions:"x-order=33"`                 // 止损触发标的 0-成交价,1-标记价格
	EntrustTypeLimit   int             `json:"entrust_type_limit" example:"0" enums:"0,1" extensions:"x-order=34"`                // 止盈委托类型 0-市价 1-限价
	EntrustTypeStop    int             `json:"entrust_type_stop" example:"0" enums:"0,1" extensions:"x-order=35"`                 // 止损委托类型 0-市价 1-限价
	TriggerEntrustType int             `json:"trigger_entrust_type" example:"0" enums:"0,1" extensions:"x-order=36"`              // 委托类型 0-市价 1-限价
	Limit              decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=37"`              // 止盈价格
	Stop               decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=38"`               // 止损价格
	EntrustLimit       decimal.Decimal `json:"entrust_limit" swaggertype:"string" example:"123.123" extensions:"x-order=39"`      // 止盈委托执行价
	EntrustStop        decimal.Decimal `json:"entrust_stop" swaggertype:"string" example:"123.123" extensions:"x-order=40"`       // 止损委托执行价
	PlanTriggerPrice   decimal.Decimal `json:"plan_trigger_price" swaggertype:"string" example:"123.123" extensions:"x-order=41"` // 止盈止损触发价
	ExpectPrice        decimal.Decimal `json:"expect_price" swaggertype:"string" example:"123.123" extensions:"x-order=42"`       // 预计成交价
	IsUseBrokenPrice   bool            `json:"is_use_broken_price" example:"false" extensions:"x-order=43"`                       // 是否使用的破产价格
	BrokenContract     bool            `json:"broken_contract" example:"false" extensions:"x-order=44"`                           // 是否爆仓合约
	CurrencyName       string          `json:"currency_name" example:"BTC" extensions:"x-order=45"`                               // 保证金币种名
}

type DocUsdOrderCancelArgs struct {
	ID int64 `json:"id" example:"135352345423543" extensions:"x-order=01"` // 委托订单id
}

type DocUsdOrderBatchCancelArgs struct {
	OrderIds     []int64 `json:"order_ids" example:"23124124,12512532532,23123123" extensions:"x-order=01"` //委托订单列表
	ContractCode string  `json:"contract_code" example:"BTCUSD" extensions:"x-order=02"`                    //查询指定合约
}

type DocUsdOrderCloseArgs struct {
	PositionId   int64           `json:"position_id" example:"125352345324524" extensions:"x-order=01"`                // 持仓id
	CloseType    int             `json:"close_type" example:"1" enums:"1,2" extensions:"x-order=02"`                   // 平仓方式 1-部分平仓 2-全部平仓
	EntrustType  int             `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=03"`                 // 委托类型 0-市价 1-限价
	EntrustPrice decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 限价价格
	Amount       decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=05"`        // 数量
	Mode         int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=06"`                       // 下单模式 1-对手价 2-最优3挡 3-最优5挡
	NToken       string          `json:"ncr" example:"d235gdag345df212" extensions:"x-order=07"`                       // 随机串,防止重放
}

type DocUsdOrderCloseRsp struct {
	Order *UsdEntrustOrder `json:"Order" extensions:"x-order=01"` // 订单信息
}

type DocUsdAllLiquidateArg struct {
	ContractCode string `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"` //查询指定合约
}

type DocUsdOrderPlanArgs struct {
	ContractCode    string          `json:"contract_code" example:"BTCUSD" extensions:"x-order=01"`                       // 合约代码
	CurrencyName    string          `json:"currency_name" example:"BTC" extensions:"x-order=02"`                          // 保证金币种名
	HoldType        int             `json:"hold_type" example:"1" enums:"1,2,3" extensions:"x-order=03"`                  // 账户类型 1-全仓 2-逐仓 3-跟单
	Side            string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=04"`                         // 方向 B-开多 S-开空
	Price           string          `json:"price" example:"123.123" extensions:"x-order=05"`                              // 触发价
	Amount          decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=06"`        // 数量
	Lever           int             `json:"lever" example:"100" extensions:"x-order=07"`                                  // 杠杆
	Limit           string          `json:"limit" example:"123.123" extensions:"x-order=08"`                              // 止盈价格
	Stop            string          `json:"stop" example:"123.123" extensions:"x-order=09"`                               // 止损价格
	EntrustType     int             `json:"entrust_type" example:"1" enums:"0,1" extensions:"x-order=10"`                 // 委托类型 0-市价 1-限价
	EntrustStrategy int             `json:"entrust_strategy" example:"2" enums:"0,1,2,3" extensions:"x-order=11"`         // 委托策略 0-默认 1-fok 2-ioc 3-maker
	EntrustPrice    decimal.Decimal `json:"entrust_price" swaggertype:"string" example:"123.123" extensions:"x-order=12"` // 委托执行价
	Mode            int             `json:"mode" example:"3" enums:"1,2,3" extensions:"x-order=13"`                       // 下单模式 1-对手价 2-最优3挡 3-最优5挡
}

type DocUsdOrderCancelArg struct {
	OrderId int64 `json:"order_id" example:"1243215325325" extensions:"x-order=01"` // 订单id
}

type DocUsdOrderFullStopArgs struct {
	PlanCloseOrderID int64           `json:"plan_close_order_id" example:"***********" extensions:"x-order=01"`            // 止盈止损id,更新时使用
	PositionId       int64           `json:"position_id" example:"1231242135412" extensions:"x-order=02"`                  // 持仓id
	Limit            decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=03"`         // 止盈价
	Stop             decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=04"`          // 止损价
	EntrustLimit     decimal.Decimal `json:"entrust_limit" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 止盈委托价格
	EntrustStop      decimal.Decimal `json:"entrust_stop" swaggertype:"string" example:"123.123" extensions:"x-order=06"`  // 止损委托价格
	Amount           decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=07"`        // 平仓数量
}

type DocUsdCancelStopPriceArg struct {
	PlanCloseOrderID int64 `json:"plan_close_order_id" example:"***********" extensions:"x-order=01"` // 止盈止损id
}

type DocUsdMarginModifyArg struct {
	PositionId int64           `json:"position_id" example:"****************" extensions:"x-order=01"`        // 持仓id
	Amount     decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 数量
	Side       string          `json:"side" example:"add" enums:"add,sub" extensions:"x-order=03"`            // 方向 add-增加 sub-减少
}

type DocUsdAccountHistory struct {
	Id            int64                          `json:"id" example:"************" extensions:"x-order=01"`                             // 流水号
	PositionId    int64                          `json:"position_id" example:"*************" extensions:"x-order=02"`                   // 持仓id
	UserId        int64                          `json:"user_id" example:"***********" extensions:"x-order=03"`                         // 用户id
	CreatedTime   int64                          `json:"created_time" example:"**********" extensions:"x-order=04"`                     // 创建时间
	CurrencyId    int                            `json:"currency_id" example:"1" extensions:"x-order=05"`                               // 币种id
	CurrencyName  string                         `json:"currency_name" example:"BTC" extensions:"x-order=06"`                           // 币种
	Type          define.UsdTradeAccountBillType `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=07"`                // 类型 1-交易费用(1<<0) 2-资金费用(1<<1) 4-转入(从资产账户)(1<<2) 8-转出(到资产账户)(1<<3) 16-平仓盈亏(1<<4) 32-平仓手续费(1<<5) 64-增加保证金(1<<6) 128-减少保证金(1<<7) 256-强平退回(1<<8) 512-爆仓结算手续费(1<<9)
	Amount        decimal.Decimal                `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=08"`         // 数量
	RawAmount     decimal.Decimal                `json:"raw_amount" swaggertype:"string" example:"123.123" extensions:"x-order=09"`     // 原始数量
	Available     decimal.Decimal                `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=10"`      // 可用余额
	Balance       decimal.Decimal                `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=11"`        // 账户资产
	Margin        decimal.Decimal                `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=12"`         // 保证金
	AccountRights decimal.Decimal                `json:"account_rights" swaggertype:"string" example:"123.123" extensions:"x-order=13"` // 权益
	PlatformID    int                            `json:"platform_id" example:"1" extensions:"x-order=14"`                               // 平台id
	Momo          string                         `json:"momo" example:"dfa31" extensions:"x-order=15"`                                  // 备注
	ExtraID       int64                          `json:"extra_id" example:"***********" extensions:"x-order=16"`                        // 额外标记id
}

type DocUsdTradeAssetBillArg struct {
	BillType  define.UsdTradeAccountBillType `json:"bill_type" swaggertype:"integer" example:"0" extensions:"x-order=01"` // 记录类型(获取多种类型数据则传对应类型相加之和) 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-增加保证金 1<<7(128)-减少保证金 1<<8(256)-强平退回 1<<9(512)-爆仓结算手续费
	CoinName  string                         `json:"coin_name" example:"BTC" extensions:"x-order=02"`                     // 币种
	LimitDays int                            `json:"limit_days" example:"30" extensions:"x-order=03"`                     // 数据限制天数(当前仅支持7天或30天)
}

type DocUsdTradeAssetBillReply struct {
	List []DocUsdTradeAssetBill `json:"list" extensions:"x-order=01"` // 数据列表
}

type DocUsdTradeAssetBill struct {
	Id           int64                          `json:"id" example:"****************" extensions:"x-order=01"`                           // 流水号
	PositionId   int64                          `json:"position_id" example:"323151235241233" extensions:"x-order=02"`                   // 持仓id
	CurrencyName string                         `json:"currency_name" example:"123.123" extensions:"x-order=03"`                         // 币种
	Amount       decimal.Decimal                `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`           // 数量
	Available    decimal.Decimal                `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=05"`        // 可用余额
	Balance      decimal.Decimal                `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=06"`          // 账户资产
	CreatedTime  alias.UnixTime                 `json:"created_time" swaggertype:"integer" example:"**********" extensions:"x-order=07"` // 创建时间
	Type         define.UsdTradeAccountBillType `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=08"`                  // 类型 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-增加保证金 1<<7(128)-减少保证金 1<<8(256)-强平退回 1<<9(512)-爆仓结算手续费
}
