package model

import (
	"bc/libs/alias"
	"bc/libs/define"
	"github.com/shopspring/decimal"
)

type DocUsdtDealerLever struct {
	BuyLever  int `json:"buy_lever" example:"20" extensions:"x-order=01"`  // 多方向杠杆
	SellLever int `json:"sell_lever" example:"50" extensions:"x-order=02"` // 空方向杠杆
}

type DocUsdtDealerSwitchLeverArg struct {
	ContractCode string `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"` // 合约代码
	Side         string `json:"side" example:"B" enums:"B,S" extensions:"x-order=02"`    // 多空方向 B-多 S-空
	Lever        int    `json:"lever" example:"20" extensions:"x-order=03"`              // 新杠杆倍数
}

type DocUsdtContractPageArg struct {
	ContractCode string `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"` // 合约代码
	Page         int    `json:"page" example:"0" extensions:"x-order=02"`                // 页码 从0开始
	Count        int    `json:"count" example:"10" extensions:"x-order=03"`              // 单页数据量
}

type DocUsdtCurrentFollowPosition struct {
	TotalRights decimal.Decimal         `json:"total_rights" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 总权益
	TotalFloat  decimal.Decimal         `json:"total_float" swaggertype:"string" example:"123.123" extensions:"x-order=02"`  // 总浮动盈亏
	Available   decimal.Decimal         `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=03"`    // 可用资产
	List        []DocUsdtFollowPosition `json:"list" extensions:"x-order=04"`                                                // 持仓列表
}

type DocUsdtCurrentFollowOrder struct {
	Total     int                     `json:"total" example:"10" extensions:"x-order=01"`                               // 总条数
	Available decimal.Decimal         `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 可用资产
	List      []DocUsdtFollowPosition `json:"list" extensions:"x-order=03"`                                             // 持仓列表
}

type DocUsdtFollowPosition struct {
	Id               int64              `json:"id" example:"123124124" extensions:"x-order=01"`                                // 用户持仓ID
	ContractCode     string             `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                       // 合约代码
	ContractName     string             `json:"contract_name" example:"BTCUSDT" extensions:"x-order=03"`                       // 中文合约名
	ContractNameEn   string             `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=04"`                    // 英文合约名
	ParValue         decimal.Decimal    `json:"par_value" swaggertype:"string" example:"0.01" extensions:"x-order=05"`         // 合约面值
	Side             string             `json:"side" example:"B" enums:"B,S" extensions:"x-order=06"`                          // B买S卖
	ContractIndex    float64            `json:"contract_index" example:"123.123" extensions:"x-order=07"`                      // 合约指数
	Volume           int                `json:"volume" example:"100" extensions:"x-order=08"`                                  // 持仓张数
	Lever            int                `json:"lever" example:"50" extensions:"x-order=09"`                                    // 杠杆
	TraderUid        int64              `json:"trader_uid" example:"1241251212231243" extensions:"x-order=10"`                 // 交易员用户id
	TraderNickname   string             `json:"trader_nickname" example:"haha" extensions:"x-order=11"`                        // 交易员昵称
	FollowPositionId int64              `json:"follow_position_id" example:"12412411231232" extensions:"x-order=12"`           // 跟随持仓id
	IndexPrecision   int                `json:"index_precision" example:"1" extensions:"x-order=13"`                           // 合约指数精度
	Limit            decimal.Decimal    `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=14"`          // 止盈价
	Stop             decimal.Decimal    `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=15"`           // 止损价
	Price            decimal.Decimal    `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=16"`          // 持仓均价
	Margin           decimal.Decimal    `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=17"`         // 保证金
	InitMargin       decimal.Decimal    `json:"init_margin" swaggertype:"string" example:"123.123" extensions:"x-order=18"`    // 初始保证金
	AdjustMargin     decimal.Decimal    `json:"adjust_margin" swaggertype:"string" example:"123.123" extensions:"x-order=19"`  // 调整保证金
	Commission       decimal.Decimal    `json:"commission" swaggertype:"string" example:"123.123" extensions:"x-order=20"`     // 手续费
	ProfitRatio      decimal.Decimal    `json:"profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=21"`   // 盈亏率
	ForcePrice       decimal.Decimal    `json:"force_price" swaggertype:"string" example:"123.123" extensions:"x-order=22"`    // 强平价
	FloatProfit      decimal.Decimal    `json:"float_profit" swaggertype:"string" example:"123.123" extensions:"x-order=23"`   // 未实现盈亏
	FundingAmount    decimal.Decimal    `json:"funding_amount" swaggertype:"string" example:"123.123" extensions:"x-order=24"` // 资金费用
	OpenPositionTime int64              `json:"open_position_time" example:"1652312441" extensions:"x-order=25"`               // 开仓时间
	ShareResource    *UsdtShareResource `json:"share_resource" extensions:"x-order=26"`                                        // 分享资源
}

type DocUsdtMyDealerList struct {
	TraderUID   int64           `json:"trader_uid" example:"12512512421123" extensions:"x-order=01"`                 // 交易员用户id
	Avatar      string          `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=02"`             // 头像
	Nickname    string          `json:"nickname" example:"123****1234" extensions:"x-order=03"`                      // 昵称
	MaxAmount   decimal.Decimal `json:"max_amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`   // 最大跟单本金 0为不限制
	TotalAmount decimal.Decimal `json:"total_amount" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 累计跟单本金
	ProfitLoss  decimal.Decimal `json:"profit_loss" swaggertype:"string" example:"123.123" extensions:"x-order=06"`  // 净收益
	Rebate      decimal.Decimal `json:"rebate" swaggertype:"string" example:"0.123" extensions:"x-order=07"`         // 分佣比例 需要乘100
}

type DocUsdtMyProfitLoss struct {
	TotalProceeds  decimal.Decimal `json:"total_proceeds" swaggertype:"string" example:"123.123" extensions:"x-order=01"`  // 累计净收益
	TotalPrincipal decimal.Decimal `json:"total_principal" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 累计使用本金
}

type DocUsdtFollowPositionHistory struct {
	ID               int64              `json:"id" example:"1241241252113213" extensions:"x-order=01"`                             // 持仓id
	ContractCode     string             `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                           // 合约代码
	ContractName     string             `json:"contract_name" example:"BTCUSDT" extensions:"x-order=03"`                           // 中文合约名
	ContractNameEn   string             `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=04"`                        // 英文合约名
	Side             string             `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                              // 方向 B买S卖
	CloseOrderType   int                `json:"close_order_type" example:"0" extensions:"x-order=06"`                              // 平仓类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单 6：条件平仓 7-带单平仓(跟单者被动平仓 8-带单止盈 9-带单止损 101:自主平仓 102:带单平仓 103:止盈平仓 104:止损平仓 105:强制平仓 106:带单止盈 107:带单止损 108:带单开仓
	EntrustType      int                `json:"entrust_type" example:"0" enums:"0,1" extensions:"x-order=07"`                      // 委托类型 0-市价 1-限价
	Volume           int                `json:"volume" example:"123" extensions:"x-order=08"`                                      // 张数
	TraderUID        int64              `json:"trader_uid" example:"123124124123" extensions:"x-order=09"`                         // 交易员uid
	FollowPositionID int64              `json:"follow_position_id" example:"12412412312312" extensions:"x-order=10"`               // 跟随上级订单id
	TraderNickname   string             `json:"trader_nickname" example:"haha" extensions:"x-order=11"`                            // 交易员昵称
	Lever            int                `json:"lever" example:"20" extensions:"x-order=12"`                                        // 杠杆倍数
	IndexPrecision   int                `json:"index_precision" example:"1" extensions:"x-order=13"`                               // 合约指数精度
	ParValue         decimal.Decimal    `json:"par_value" swaggertype:"string" example:"123.123" extensions:"x-order=14"`          // 合约面值
	OpenPrice        decimal.Decimal    `json:"open_price" swaggertype:"string" example:"123.123" extensions:"x-order=15"`         // 开仓价
	ClosePrice       decimal.Decimal    `json:"close_price" swaggertype:"string" example:"123.123" extensions:"x-order=16"`        // 平仓价
	InitMargin       decimal.Decimal    `json:"init_margin" swaggertype:"string" example:"123.123" extensions:"x-order=17"`        // 初始保证金
	CloseProfit      decimal.Decimal    `json:"close_profit" swaggertype:"string" example:"123.123" extensions:"x-order=18"`       // 平仓盈亏
	CloseProfitRatio decimal.Decimal    `json:"close_profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=19"` // 盈亏率
	Commission       decimal.Decimal    `json:"commission" swaggertype:"string" example:"123.123" extensions:"x-order=20"`         // 总手续费
	FundingAmount    decimal.Decimal    `json:"funding_amount" swaggertype:"string" example:"123.123" extensions:"x-order=21"`     // 资金费用
	Withholding      decimal.Decimal    `json:"withholding" swaggertype:"string" example:"123.123" extensions:"x-order=22"`        // 预扣分佣
	OpenTimeUnix     int64              `json:"open_time" example:"1642233442" extensions:"x-order=23"`                            // 开仓时间
	CloseTimeUnix    int64              `json:"close_time" example:"1642233442" extensions:"x-order=24"`                           // 平仓时间
	ShareResource    *UsdtShareResource `json:"share_resource" extensions:"x-order=25"`                                            // 分享数据
}

type DocUsdtFollowHeadData struct {
	IsTrader       bool            `json:"is_trader" example:"true" extensions:"x-order=01"`                                // 是否是交易员
	Withholding    decimal.Decimal `json:"withholding" swaggertype:"string" example:"123.123" extensions:"x-order=02"`      // 预计分佣
	TotalBrokerage decimal.Decimal `json:"total_brokerage" swaggertype:"string" example:"123.123" extensions:"x-order=03"`  // 累计分佣
	DayProfit      decimal.Decimal `json:"day_profit" swaggertype:"string" example:"123.123" extensions:"x-order=04"`       // 今日盈亏
	DayProfitRatio decimal.Decimal `json:"day_profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=05"` // 今日收益率
}

type DocUsdtGetLeaderListArg struct {
	Nickname string `json:"nickname" example:"可" extensions:"x-order=01"`                      // 昵称
	SortType int    `json:"sort_type" example:"0" enums:"0,1,2,3,4,5" extensions:"x-order=02"` // 排序规则 0-综合排名 1-近期收益率 2-累计收益 3-近期胜率 4-交易量 5-总收益率
	Page     int    `json:"page" example:"0" extensions:"x-order=03"`                          // 页码,从0开始
	Count    int    `json:"count" example:"10" extensions:"x-order=04"`                        // 单页数据量
}

type DocUsdtLeaderListReply struct {
	TraderID             int64           `json:"trader_id" example:"124214214521" extensions:"x-order=01"`                              // 交易员id
	Avatar               string          `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=02"`                       // 头像
	Nickname             string          `json:"nickname" example:"haha" extensions:"x-order=03"`                                       // 昵称
	Introduce            string          `json:"introduce" example:"aaa" extensions:"x-order=04"`                                       // 介绍
	ProfitRatio          decimal.Decimal `json:"profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=05"`           // 收益率
	TotalProfit          decimal.Decimal `json:"total_profit" swaggertype:"string" example:"123.123" extensions:"x-order=06"`           // 累计收益
	WinRate              decimal.Decimal `json:"win_rate" swaggertype:"string" example:"123.123" extensions:"x-order=07"`               // 胜率
	FollowNumber         int             `json:"follow_number" example:"10" extensions:"x-order=08"`                                    // 当前跟单人数
	FollowLimit          int             `json:"follow_limit" example:"20" extensions:"x-order=09"`                                     // 限制最大跟单人数
	LimitFollowContracts []string        `json:"limit_follow_contracts" example:"BTCUSDT,ETHUSDT" extensions:"x-order=10"`              // 跟单合约列表
	LimitFollowThreshold decimal.Decimal `json:"limit_follow_threshold" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 跟单最低本金 0不限制
	Following            bool            `json:"following" example:"false" extensions:"x-order=12"`                                     // 是否跟随中
}

type DocTraderIDArg struct {
	TraderID int64 `json:"trader_id" example:"12423141213241" extensions:"x-order=01"` // 交易员id
}

type DocLeaderDetailReply struct {
	UserID               int64             `json:"user_id" example:"124124125125123" extensions:"x-order=01"`                         // 用户id
	Avatar               string            `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=02"`                   // 头像
	Nickname             string            `json:"nickname" example:"gsdg" extensions:"x-order=03"`                                   // 昵称
	Introduce            string            `json:"introduce" example:"gsindgsda" extensions:"x-order=04"`                             // 介绍
	FollowNumber         int               `json:"follow_number" example:"3" extensions:"x-order=05"`                                 // 当前跟单人数
	HistoryFollowNumber  int               `json:"history_follow_number" example:"20" extensions:"x-order=06"`                        // 累计跟单人数
	FollowLimit          int               `json:"follow_limit" example:"15" extensions:"x-order=07"`                                 // 限制最大跟单人数
	LimitFollowContracts []string          `json:"limit_follow_contracts" example:"BTCUSDT,ETHUSDT" extensions:"x-order=08"`          // 跟单合约列表
	LimitFollowMode      define.FollowMode `json:"limit_follow_mode" swaggertype:"integer" example:"3" extensions:"x-order=09"`       // 跟单方式 1-固定张数 2-固定比例 多种相加
	LimitFollowThreshold decimal.Decimal   `json:"limit_follow_threshold" swaggertype:"string" example:"100" extensions:"x-order=10"` // 跟单最低本金,0不限制
	TotalWin             int               `json:"total_win" example:"123" extensions:"x-order=11"`                                   // 总盈利笔数
	TotalLoss            int               `json:"total_loss" example:"123" extensions:"x-order=12"`                                  // 总亏损笔数
	StayDays             int               `json:"stay_days" example:"123" extensions:"x-order=13"`                                   // 入驻天数
	Rebate               decimal.Decimal   `json:"rebate" swaggertype:"string" example:"0.1" extensions:"x-order=14"`                 // 分佣比例 需要乘100
	RecentProfitRate     decimal.Decimal   `json:"recent_profit_rate" swaggertype:"string" example:"123.123" extensions:"x-order=15"` // 近期收益率
	TotalProfit          decimal.Decimal   `json:"total_profit" swaggertype:"string" example:"123.123" extensions:"x-order=16"`       // 累计收益
	RecentWinRate        decimal.Decimal   `json:"recent_win_rate" swaggertype:"string" example:"23.123" extensions:"x-order=17"`     // 近期胜率
	TotalProfitRate      decimal.Decimal   `json:"total_profit_rate" swaggertype:"string" example:"123.123" extensions:"x-order=18"`  // 累计收益率
	AdeptContracts       string            `json:"adept_contracts" example:"BTC" extensions:"x-order=19"`                             // 擅长合约
	Following            bool              `json:"following" example:"false" extensions:"x-order=20"`                                 // 是否跟随中
	DocAPIFollowConf
}

type DocAPIFollowConf struct {
	MinMultiple decimal.Decimal `json:"min_multiple" swaggertype:"string" example:"1.0" extensions:"x-order=96"`  // 最小跟单比例(单笔)
	MaxMultiple decimal.Decimal `json:"max_multiple" swaggertype:"string" example:"20.0" extensions:"x-order=97"` // 最大跟单比例(单笔)
	MinNumber   int             `json:"min_number" example:"1" extensions:"x-order=98"`                           // 最小跟单张数(单笔)
	MaxNumber   int             `json:"max_number" example:"100" extensions:"x-order=99"`                         // 最大跟单张数(单笔)
}

type DocTraderIDPageArg struct {
	TraderID int64 `json:"trader_id" example:"12321412412" extensions:"x-order=01"` // 交易员id
	Page     int   `json:"page" example:"0" extensions:"x-order=02"`                // 页码,从0开始
	Count    int   `json:"count" example:"10" extensions:"x-order=03"`              // 单页数据量
}

type DocApiLeaderOrderHistory struct {
	ID               int64           `json:"id" example:"124124124" extensions:"x-order=01"`                                    // 持仓id
	ContractCode     string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                           // 合约代码
	ContractName     string          `json:"contract_name" example:"BTCUSDT" extensions:"x-order=03"`                           // 中文合约名
	ContractNameEn   string          `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=04"`                        // 英文合约名
	Side             string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`                              // 方向 B买S卖
	Lever            int             `json:"lever" example:"20" extensions:"x-order=06"`                                        // 杠杆倍数
	IndexPrecision   int             `json:"index_precision" example:"1" extensions:"x-order=07"`                               // 合约指数精度
	OpenPrice        decimal.Decimal `json:"open_price" swaggertype:"string" example:"123.123" extensions:"x-order=08"`         // 开仓价
	ClosePrice       decimal.Decimal `json:"close_price" swaggertype:"string" example:"123.123" extensions:"x-order=09"`        // 平仓价
	CloseProfitRatio decimal.Decimal `json:"close_profit_ratio" swaggertype:"string" example:"123.123" extensions:"x-order=10"` // 盈亏率
	OpenTimeUnix     int64           `json:"open_time" example:"1641235522" extensions:"x-order=11"`                            // 开仓时间
	CloseTimeUnix    int64           `json:"close_time" example:"1656123332" extensions:"x-order=12"`                           // 平仓时间
}

type DocApiLeaderOrderCurrent struct {
	ID             int64           `json:"id" example:"1245215123412" extensions:"x-order=01"`         // 持仓id
	ContractCode   string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`    // 合约代码
	ContractName   string          `json:"contract_name" example:"BTCUSDT" extensions:"x-order=03"`    // 中文合约名
	ContractNameEn string          `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=04"` // 英文合约名
	Side           string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=05"`       // 方向 B买S卖
	Lever          int             `json:"lever" example:"50" extensions:"x-order=06"`                 // 杠杆倍数
	IndexPrecision int             `json:"index_precision" example:"1" extensions:"x-order=07"`        // 合约指数精度
	OpenPrice      decimal.Decimal `json:"open_price" example:"123.123" extensions:"x-order=08"`       // 开仓价
	ProfitRatio    decimal.Decimal `json:"profit_ratio" example:"123.123" extensions:"x-order=09"`     // 盈亏率
	ContractIndex  decimal.Decimal `json:"contract_index" example:"123.123" extensions:"x-order=10"`   // 合约指数(当前价格)
	OpenTimeUnix   int64           `json:"open_time" example:"1642235522" extensions:"x-order=11"`     // 开仓时间
}

type DocApiDealerFollowers struct {
	Avatar      string          `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=01"`             // 用户头像
	Nickname    string          `json:"nickname" example:"agdsdf" extensions:"x-order=02"`                           // 用户昵称
	TotalCost   decimal.Decimal `json:"total_cost" swaggertype:"string" example:"123.123" extensions:"x-order=03"`   // 累计跟随本金
	TotalProfit decimal.Decimal `json:"total_profit" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 累计跟随收益
}

type DocApiDealerFollowSetting struct {
	PlatformID           int               `json:"platform_id" example:"1" extensions:"x-order=01"`                                       // 平台id
	Quantity             int               `json:"quantity" example:"1" extensions:"x-order=02"`                                          // 下单跟随数量
	TraderID             int64             `json:"trader_id" example:"12312312" extensions:"x-order=03"`                                  // 交易员id
	Contracts            string            `json:"contracts" example:"BTC,USDT" extensions:"x-order=04"`                                  // 跟随合约,逗号分隔
	Proportion           decimal.Decimal   `json:"proportion" swaggertype:"string" example:"1.1" extensions:"x-order=05"`                 // 下单跟随比例
	StopProfit           decimal.Decimal   `json:"stop_profit" swaggertype:"string" example:"123.123" extensions:"x-order=06"`            // 止盈比例
	StopLoss             decimal.Decimal   `json:"stop_loss" swaggertype:"string" example:"123.123" extensions:"x-order=07"`              // 止损比例
	MaxAmount            decimal.Decimal   `json:"max_amount" swaggertype:"string" example:"123.123" extensions:"x-order=08"`             // 最大授权资金
	LimitFollowContracts []string          `json:"limit_follow_contracts" example:"BTCUSDT,ETHUSDT" extensions:"x-order=09"`              // 跟单合约列表
	LimitFollowMode      define.FollowMode `json:"limit_follow_mode" swaggertype:"integer" example:"1" extensions:"x-order=10"`           // 跟单方式 1-固定张数 2-固定比例 多种相加
	LimitFollowThreshold decimal.Decimal   `json:"limit_follow_threshold" swaggertype:"string" example:"123.123" extensions:"x-order=11"` // 跟单最低本金
}

type DocApiMyFollowersReply struct {
	DocMyFollowersStatistics
	List []DocMyFollowers `json:"list" extensions:"x-order=99"` // 跟单者列表
}

type DocMyFollowersStatistics struct {
	FollowNumber int             `json:"follow_number" example:"5" extensions:"x-order=01"`                         // 当前带单人数
	FollowLimit  int             `json:"follow_limit" example:"20" extensions:"x-order=02"`                         // 总可带单人数
	NextLevel    decimal.Decimal `json:"next_level" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 下个级别需要的总资金数(可用+所有持仓初始保证金), 0为不能继续升级
}

type DocMyFollowers struct {
	UserID         int64           `json:"user_id" example:"12315215123" extensions:"x-order=01"`                          // 跟随者id
	Avatar         string          `json:"avatar" example:"https://www.a.cn/a.png" extensions:"x-order=02"`                // 头像
	Nickname       string          `json:"nickname" example:"1sdf2" extensions:"x-order=03"`                               // 用户昵称
	TotalPrincipal decimal.Decimal `json:"total_principal" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 跟单总本金
	FollowTimeUnix int64           `json:"follow_time" example:"1642235523" extensions:"x-order=05"`                       // 跟随时间
}

type DocApiDealerLeadProfit struct {
	TotalProfit decimal.Decimal `json:"total_profit" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 累计分润资金
	WinProfit   decimal.Decimal `json:"win_profit" swaggertype:"string" example:"123.123" extensions:"x-order=02"`   // 盈利分润
	LossProfit  decimal.Decimal `json:"loss_profit" swaggertype:"string" example:"123.123" extensions:"x-order=03"`  // 亏损退回
	TimeUnix    int64           `json:"time" example:"1642233342" extensions:"x-order=04"`                           // 分润时间
}

type DocApiDealerLeadProfitDetailArg struct {
	OwnDay int64 `json:"own_day" example:"1641234412" extensions:"x-order=01"` // 归属时间
	DocPageArg
}

type DocApiDealerLeadProfitDetail struct {
	Nickname string          `json:"nickname" example:"1231251251" extensions:"x-order=01"`                 // 用户昵称
	Profit   decimal.Decimal `json:"profit" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 分润
}

type DocApiFollowMessage struct {
	ID             int64  `json:"id" example:"12451251221521" extensions:"x-order=01"`                          // 消息id
	Nickname       string `json:"nickname" example:"sg32df" extensions:"x-order=02"`                            // 发送者昵称
	Category       int8   `json:"category" example:"1" enums:"1,2,3,4,5,6,7,8,9,10,11" extensions:"x-order=03"` // 类型 1-开仓成功 2-开仓失败 3-平仓成功 4-平仓失败 5-爆仓 6-停止跟随 7-获取返佣 8-审核成功 9-审核失败 10-身份停用 11-身份撤销
	Title          string `json:"title" example:"dgadinwe" extensions:"x-order=04"`                             // 标题
	Content        string `json:"content" example:"dgasdgae" extensions:"x-order=05"`                           // 内容
	IsReaded       bool   `json:"is_readed" example:"false" extensions:"x-order=06"`                            // 是否已读
	CreateTimeUnix int64  `json:"create_time" example:"1642341223" extensions:"x-order=07"`                     // 创建时间
}

type DocReadFollowMessageArg struct {
	IDList []int64 `json:"id_list" example:"1231451251,21513253453421" extensions:"x-order=01"` // id列表
}

type DocFollowOrderOpenArgs struct {
	ContractCode string          `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`              // 合约代码
	NToken       string          `json:"ncr" example:"13tdsignai" extensions:"x-order=02"`                     // 唯一请求id,防止重复请求
	Side         string          `json:"side" example:"B" enums:"B,S" extensions:"x-order=03"`                 // 方向 B买 S卖
	Amount       int             `json:"amount" example:"123" extensions:"x-order=04"`                         // 下单张数
	Leverage     int             `json:"leverage" example:"20" extensions:"x-order=05"`                        // 杠杆倍数
	Mode         int             `json:"mode" example:"3" extensions:"x-order=06"`                             // 下单模式 1-对手价 2-最优3挡 3-最优5挡
	Limit        decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=07"` // 止盈价
	Stop         decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=08"`  // 止损价
	TriggerType  int             `json:"trigger_type" example:"0" enums:"0,1" extensions:"x-order=09"`         // 止盈止损触发标的 0-成交价,1-标记价格
}

type DocFollowOrderOpenRsp struct {
	Order     *UsdtEntrustOrder `json:"order" extensions:"x-order=01"`                                 // 委托订单
	Available string            `json:"available,omitempty" example:"123.123" extensions:"x-order=02"` // 可用
}

type DocFollowOrderCloseArgs struct {
	NToken     string `json:"ncr" example:"1235sdnfii23fkisd" extensions:"x-order=01"`    // 唯一请求id,防止重复请求
	PositionId int64  `json:"position_id" example:"125124312412" extensions:"x-order=02"` // 持仓id
	Mode       int    `json:"mode" example:"3" extensions:"x-order=03"`                   // 下单模式 1-对手价 2-最优3挡 3-最优5挡
}

type DocOrderCloseRsp struct {
	Order *UsdtEntrustOrder `json:"order" extensions:"x-order=01"` // 委托订单
}

type DocSetFollowPositionStopArg struct {
	PositionId int64           `json:"position_id" example:"124125123" extensions:"x-order=01"`              // 持仓id
	Limit      decimal.Decimal `json:"limit" swaggertype:"string" example:"123.123" extensions:"x-order=02"` // 止盈价
	Stop       decimal.Decimal `json:"stop" swaggertype:"string" example:"123.123" extensions:"x-order=03"`  // 止损价
}

type DocLeaderLimitSetting struct {
	FollowContract  []string          `json:"follow_contract" example:"BTCUSDT,ETHUSDT" extensions:"x-order=01"`               // 跟单合约列表
	FollowMode      define.FollowMode `json:"follow_mode" swaggertype:"integer" example:"1" extensions:"x-order=02"`           // 跟单方式 1-固定张数 2-固定比例 多种相加
	FollowThreshold decimal.Decimal   `json:"follow_threshold" swaggertype:"string" example:"123.123" extensions:"x-order=03"` // 跟单最低本金
}

type DocFollowBillReply struct {
	ID            int64           `json:"id" example:"124125123123" extensions:"x-order=01"`          // 流水记录id
	UserId        int64           `json:"user_id" example:"123515325134" extensions:"x-order=02"`     // 用户id
	PositionId    int64           `json:"position_id" example:"523512321412" extensions:"x-order=03"` // 持仓id
	CurrencyID    int             `json:"currency_id" example:"36" extensions:"x-order=04"`           // 币种id
	CurrencyName  string          `json:"currency_name" example:"USDT" extensions:"x-order=05"`       // 币种名
	Type          int             `json:"type" example:"1" extensions:"x-order=06"`                   // 类型 1:开仓手续费(1<<0) 2:资金费用(1<<1) 4:从资产账户转入(1<<2) 8:划转到资产账户(1<<3) 16:平仓盈亏(1<<4) 32:平仓手续费(1<<5) 64:模拟盘补充资产(1<<6) 128:减少保证金(1<<7) 256:预扣佣金(1<<8) 512:佣金退款(1<<9) 1024:佣金收入(1<<10) 2048:交易账户划转到跟单账户(1<<11) 4096:跟单账户划转到交易账户(1<<12) 8192:资产账户划转到跟单账户(1<<13) 16384:跟单账户划转到资产账户(1<<14) 32768-强平退回(1<<15) 65536-模拟盘减少资产(1<<16) 131072-爆仓结算手续费(1<<17) 262144-异常资产扣除(1<<18) 524288-增加保证金(1<<19)
	CreateTime    int64           `json:"create_time" example:"1652312432" extensions:"x-order=07"`
	Balance       decimal.Decimal `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=08"`        // 资产
	Available     decimal.Decimal `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=09"`      // 可用
	Amount        decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=10"`         // 数量
	Margin        decimal.Decimal `json:"margin" swaggertype:"string" example:"123.123" extensions:"x-order=11"`         // 保证金
	AccountRights decimal.Decimal `json:"account_rights" swaggertype:"string" example:"123.123" extensions:"x-order=12"` // 账户权益
}

type DocFollowBillArg struct {
	PositionID int64 `json:"position_id" example:"**********" extensions:"x-order=01"` // 持仓id
}

type DocUsdtFollowTradeAssetBillArg struct {
	BillType  define.AccountBillType `json:"bill_type" swaggertype:"integer" example:"0" extensions:"x-order=01"` // 记录类型(获取多种类型数据则传对应类型相加之和) 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-模拟盘补充资产 1<<7(128)-减少保证金 1<<8(256)-预扣佣金 1<<9(512)-佣金退款 1<<10(1024)-佣金收入 1<<11(2048)-交易账户划转到跟单账户 1<<12(4096)-跟单账户划转到交易账户 1<<13(8192)-资产账户划转到跟单账户 1<<14(16384)-跟单账户划转到资产账户 1<<15(32768)-强平退回 1<<16(65536)-模拟盘减少资产 1<<17(131072)-爆仓结算手续费 1<<18(262144)-异常资产扣除 1<<19(524288)-增加保证金 1<<20(1048576)-赠金领取 1<<21(2097152)-赠金失效 1<<22(4194304)-赠金回收
	CoinName  string                 `json:"coin_name" example:"USDT" extensions:"x-order=02"`                    // 币种
	LimitDays int                    `json:"limit_days" example:"30" extensions:"x-order=03"`                     // 数据限制天数(当前仅支持7天或30天)
}

type DocUsdtFollowTradeAssetBillReply struct {
	List []DocUsdtFollowTradeAssetBill `json:"list" extensions:"x-order=01"` // 数据列表
}

type DocUsdtFollowTradeAssetBill struct {
	Id           int64                  `json:"id" example:"123123123123124241" extensions:"x-order=01"`                         // 流水号
	PositionId   int64                  `json:"position_id" example:"5737573874637583" extensions:"x-order=02"`                  // 持仓id
	CurrencyName string                 `json:"currency_name" example:"USDT" extensions:"x-order=03"`                            // 币种
	Amount       decimal.Decimal        `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=04"`           // 数量
	Available    decimal.Decimal        `json:"available" swaggertype:"string" example:"123.123" extensions:"x-order=05"`        // 可用余额
	Balance      decimal.Decimal        `json:"balance" swaggertype:"string" example:"123.123" extensions:"x-order=06"`          // 账户资产
	CreatedTime  alias.UnixTime         `json:"created_time" swaggertype:"integer" example:"**********" extensions:"x-order=07"` // 创建时间
	Type         define.AccountBillType `json:"type" swaggertype:"integer" example:"1" extensions:"x-order=08"`                  // 记录类型 1<<0(1)-交易费用 1<<1(2)-资金费用 1<<2(4)-转入(从资产账户) 1<<3(8)-转出(到资产账户) 1<<4(16)-平仓盈亏 1<<5(32)-平仓手续费 1<<6(64)-模拟盘补充资产 1<<7(128)-减少保证金 1<<8(256)-预扣佣金 1<<9(512)-佣金退款 1<<10(1024)-佣金收入 1<<11(2048)-交易账户划转到跟单账户 1<<12(4096)-跟单账户划转到交易账户 1<<13(8192)-资产账户划转到跟单账户 1<<14(16384)-跟单账户划转到资产账户 1<<15(32768)-强平退回 1<<16(65536)-模拟盘减少资产 1<<17(131072)-爆仓结算手续费 1<<18(262144)-异常资产扣除 1<<19(524288)-增加保证金 1<<20(1048576)-赠金领取 1<<21(2097152)-赠金失效 1<<22(4194304)-赠金回收
}

type DocUsdtFollowEntrustSoldArg struct {
	ContractCode string             `json:"contract_code" example:"BTCUSDT" extensions:"x-order=01"`                // 合约代码
	Offset       string             `json:"offset" example:"O" enums:"O,C" extensions:"x-order=02"`                 // 方向 O-开仓 C-平仓
	AccountType  define.AccountType `json:"account_type" swaggertype:"integer" example:"0" extensions:"x-order=03"` // 仓位模式 0-全部 3：带单 4：跟单
	LimitDays    int                `json:"limit_days" example:"30" enums:"7,30" extensions:"x-order=04"`           // 数据限制天数 当前仅支持7天或30天
}

type DocFollowSoldTradeReply struct {
	List []DocFollowSoldTrade `json:"list"` // 数据列表
}

type DocFollowSoldTrade struct {
	TradeID        int64              `json:"trade_id" example:"*****************" extensions:"x-order=01"`                               // 交易id
	ContractCode   string             `json:"contract_code" example:"BTCUSDT" extensions:"x-order=02"`                                    // 合约代码
	ContractName   string             `json:"contract_name" example:"BTCUSDT" extensions:"x-order=03"`                                    // 中文合约名
	ContractNameEn string             `json:"contract_name_en" example:"BTCUSDT" extensions:"x-order=04"`                                 // 英文合约名
	AccountType    int                `json:"account_type" example:"1" enums:"3,4" extensions:"x-order=05"`                               // 账户模式 3: 带单 4:跟单
	Side           string             `json:"side" example:"B" enums:"B,S" extensions:"x-order=06"`                                       // 交易方向 B买S卖
	Offset         string             `json:"offset" example:"O" enums:"O,C" extensions:"x-order=07"`                                     // 开平方向 O: 开仓 C: 平仓
	CurrentPrice   decimal.Decimal    `json:"current_price" swaggertype:"string" example:"123.123" extensions:"x-order=08"`               // 当前价
	Price          decimal.Decimal    `json:"price" swaggertype:"string" example:"123.123" extensions:"x-order=09"`                       // 成交价格
	OpenAvgPrice   decimal.Decimal    `json:"open_avg_price" swaggertype:"string" example:"123.123" extensions:"x-order=10"`              // 开仓均价
	TradeValue     decimal.Decimal    `json:"trade_value" swaggertype:"string" example:"123.123" extensions:"x-order=11"`                 // 成交价值
	TradeAmount    decimal.Decimal    `json:"trade_amount" swaggertype:"string" example:"123.123" extensions:"x-order=12"`                // 成交金额
	Profit         decimal.Decimal    `json:"profit" swaggertype:"string" example:"123.123" extensions:"x-order=13"`                      // 已实现盈亏
	CloseProfit    decimal.Decimal    `json:"close_profit" example:"123" extensions:"x-order=14"`                                         // 平仓盈亏
	ParValue       decimal.Decimal    `json:"par_value" swaggertype:"string" example:"123" extensions:"x-order=15"`                       // 合约面值
	Volume         int                `json:"volume" swaggertype:"string" example:"123.123" extensions:"x-order=16"`                      // 成交张数
	EntrustVolume  int                `json:"entrust_volume" example:"123" extensions:"x-order=17"`                                       // 委托量
	Lever          int                `json:"lever" example:"20" extensions:"x-order=18"`                                                 // 杠杆
	OrderType      int                `json:"order_type" example:"0" enums:"0,1,2,4,5" extensions:"x-order=19"`                           // 订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单
	TradeTime      int64              `json:"trade_time" example:"1526223452" extensions:"x-order=20"`                                    // 成交时间
	IsFollow       bool               `json:"is_follow" example:"false" extensions:"x-order=21"`                                          // 是否是跟单系统订单
	IsTrader       bool               `json:"is_trader" example:"true" extensions:"x-order=22"`                                           // 是否是交易员
	OrderClient    define.OsType      `json:"order_client" swaggertype:"integer" example:"1" enums:"1,2,3,4,5,6" extensions:"x-order=23"` // 下单客户端类型（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
	EntrustOrderId int64              `json:"entrust_order_id" example:"12512512912939223" extensions:"x-order=24"`                       // 委托订单id
	EntrustType    int                `json:"entrust_type" example:"0" enums:"0,1,2" extensions:"x-order=25"`                             // 委托类型 0-市价单 1-限价单 2-强平单
	EntrustMode    int                `json:"entrust_mode" example:"3" enums:"1,2,3" extensions:"x-order=26"`                             // 委托模式 1-对手价 2-最优3挡 3-最优5挡
	EntrustStatus  int                `json:"entrust_status" example:"200" enums:"0,100,101,102,200,201" extensions:"x-order=27"`         // 委托状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
	ShareResource  *UsdtShareResource `json:"share_resource" extensions:"x-order=28"`                                                     // 分享数据
}
