package model

import "github.com/shopspring/decimal"

type DocActivityInfoReq struct {
	CurrentInfo DocActivityCurrent `json:"current_info" extensions:"x-order=01"`                             // 当前活动信息
	AcStageIa   DocActivityStageIa `json:"acstageia" extensions:"x-order=02"`                                // 上期交易信息
	WardList    []DocWardList      `json:"wardlist" extensions:"x-order=03"`                                 // 获奖排行
	ServerTime  string             `json:"servertime" example:"2021-01-22 08:00:00" extensions:"x-order=04"` // 服务器时间
}

type DocActivityCurrent struct {
	StartTime    string          `json:"start_time" example:"2021-01-22 08:00:00" extensions:"x-order=01"`             // 活动开始时间
	EndTime      string          `json:"end_time" example:"2021-01-22 08:00:00" extensions:"x-order=02"`               // 活动结束时间
	OpeningTime  string          `json:"opening_time" example:"2021-01-22 08:00:00" extensions:"x-order=03"`           // 开奖时间
	CurrentBonus decimal.Decimal `json:"current_bonus" swaggertype:"string" example:"123.123" extensions:"x-order=04"` // 当前奖金
	StarDay      string          `json:"star_day" example:"2021-01-22 08:00:00" extensions:"x-order=05"`               // 活动开始日期
	EndDay       string          `json:"end_day" example:"2021-01-22 08:00:00" extensions:"x-order=06"`                // 活动结束日期
	MyTraders    int             `json:"my_traders" example:"13" extensions:"x-order=07"`                              // 我的交易次数
}

type DocActivityStageIa struct {
	Hash   string      `json:"hash" example:"0x69276e71Ef93086caF69d8F901F69dbD35382699" extensions:"x-order=01"` // 交易hash
	MyWard []DocMyWard `json:"myward" extensions:"x-order=02"`                                                    // 我的中奖记录
}

type DocMyWard struct {
	Amount    decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 累计获奖金额
	Frequency int             `json:"frequency" example:"12" extensions:"x-order=02"`                        // 获奖次数
	GetType   int             `json:"grade" example:"1" enums:"1,2,3" extensions:"x-order=03"`               // 获奖等级  1一等奖，2二等奖，3三等奖
}

type DocWardList struct {
	Amount   decimal.Decimal `json:"amount" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 获奖金额
	UserName string          `json:"user_name" example:"123***2314" extensions:"x-order=02"`                // 用户名
}

type DocMyWardReply struct {
	Total int             `json:"total" example:"12" extensions:"x-order=01"` // 记录数
	List  []DocMyWardList `json:"list" extensions:"x-order=02"`               // 列表
}

type DocMyWardList struct {
	Amount         decimal.Decimal `json:"bonus" swaggertype:"string" example:"123.123" extensions:"x-order=01"` // 获奖金额
	UserId         int64           `json:"user_id" example:"1231241251251223" extensions:"x-order=02"`           // 用户id
	ID             int             `json:"id" example:"12321412" extensions:"x-order=03"`                        // 数据id
	GetType        int             `json:"grade" example:"2" enums:"1,2,3" extensions:"x-order=04"`              // 获奖等级 1 一等奖，2 二等奖 ，3 三等奖
	CheckStatus    int             `json:"check_status" example:"1" enums:"-1,0,1,2,3" extensions:"x-order=05"`  // 状态 -1待领取，0待审核，1审核通过 2拒绝，3过期
	ActivityListId int             `json:"activity_list_id" example:"123124" extensions:"x-order=06"`            // 活动列表id
	OpenTime       string          `json:"open_time" example:"2021-01-23 10:10:00" extensions:"x-order=07"`      // 开奖时间
	OrderId        string          `json:"order_id" example:"123545123213124" extensions:"x-order=08"`           // 订单id
	ShareImg       string          `json:"share_img" example:"https://www.a.cn/s.png" extensions:"x-order=09"`   // 分享图
}

type DocUpShareArg struct {
	Id     int    `json:"id" example:"123124" extensions:"x-order=01"`                     // 数据id
	ImgUrl string `json:"imgurl" example:"https://www.a.cn/s.png" extensions:"x-order=02"` // 图片地址，用上传图片file服务上传图片
}
