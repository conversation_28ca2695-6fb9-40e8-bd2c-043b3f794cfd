package server

import (
	"bytes"
	"context"
	rawJson "encoding/json"
	"io"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"bc/libs/cache"
	"bc/libs/commonsrv"
	"bc/libs/conf"
	"bc/libs/convert"
	"bc/libs/database"
	"bc/libs/define"
	"bc/libs/json"
	"bc/libs/log"
	"bc/libs/sign"
	"bc/libs/xplugins"
	"bc/libs/zip"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/json-iterator/go"
	"go.uber.org/zap"
)

// Options 添加返回头,并处理OPTIONS请求
func Options() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("StatusCode", http.StatusOK)
		c.<PERSON><PERSON>("Content-Type", "application/json")
		c.<PERSON>("Access-Control-Allow-Headers", "*")
		c<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c<PERSON><PERSON><PERSON>("Access-Control-Allow-Methods", "PUT,POST,GET,DELETE,OPTIONS")
		if c.Request.Method == http.MethodOptions {
			c.Abort()
			return
		}
		c.Set(define.ResponseCodeKey, http.StatusOK)
		c.Next()
	}
}

// TimeoutWarp 请求超时控制
func TimeoutWarp(ctx context.Context, timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(ctx, timeout)
		defer cancel()

		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// UnLoginRequest 非登录验证接口处理
func UnLoginRequest(f HandlerFunc) gin.HandlerFunc {
	return NormalWarp(tryAuth(f))
}

// LoginRequest 需登录验证接口处理
func LoginRequest(f HandlerFunc) gin.HandlerFunc {
	return NormalWarp(auth(f))
}

// VerifyRequest // 根据国家进行实名认证等级校验
func VerifyRequest(f HandlerFunc) gin.HandlerFunc {
	return NormalWarp(auth(verify(f)))
}

// VerifyLv1Request 需登录并通过实名认证Lv1的接口处理
func VerifyLv1Request(f HandlerFunc) gin.HandlerFunc {
	return NormalWarp(auth(verifyLv1(f)))
}

// VerifyLv2Request 需登录并通过实名认证Lv2的接口处理
func VerifyLv2Request(f HandlerFunc) gin.HandlerFunc {
	return NormalWarp(auth(verifyLv2(f)))
}

// CheckIP 检查ip是否在白名单中
func CheckIP() gin.HandlerFunc {
	return func(c *gin.Context) {
		reqIP := c.ClientIP()
		if len(conf.IPWhiteListForCWS()) > 0 {
			for _, confIP := range conf.IPWhiteListForCWS() {
				if confIP == reqIP {
					goto ok
				}
			}
			c.Set(define.ResponseCodeKey, http.StatusForbidden)
		}
	ok:
		log.Info("checkCallbackIP", zap.String("requestURI", c.Request.RequestURI),
			zap.String("requestIP", reqIP), zap.Any("confIPs", conf.IPWhiteListForCWS()))
		c.Next()
	}
}

// NormalWarp 常规接口请求,从url或body中获取请求参数,并返回reply结构
func NormalWarp(f HandlerFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		reqID := database.NextID()
		c.Set(define.RequestIDKey, reqID)

		arg, err := readArg(c.Request)
		if err != nil {
			log.Error("process readArg error", zap.Int64("reqID", reqID), zap.String("URI", c.Request.RequestURI), zap.Error(err))
			replyWrite(c.Writer, c.Request.RequestURI, &arg.TokenPayload, &define.ReqArg{ReqID: reqID, ReqOs: 0}, &define.Reply{Ret: define.ErrCodeParam, Msg: define.ErrMsgParam.ErrMsg(define.ReqLangCN)})
			return
		}
		arg.ReqID = reqID
		arg.ReqIP = c.ClientIP()
		arg.Token = c.GetHeader(define.RequestTokenKey)
		if arg.ReqLang < define.ReqLangCN || arg.ReqLang >= define.ReqLangNonSupport {
			arg.ReqLang = define.ReqLangEN
		}
		span := xplugins.NewSpan().SetRequestID(arg.ReqID)

		reply := new(define.Reply)
		defer func() {
			processLog(c.Request.RequestURI, &arg.ReqArg, arg.UserID, arg.Data, reply, span)
			replyWrite(c.Writer, c.Request.RequestURI, &arg.TokenPayload, &arg.ReqArg, reply)
		}()

		// 之前的处理出现错误
		reply.Ret = c.GetInt(define.ResponseCodeKey)
		if reply.Ret != http.StatusOK {
			reply.Msg = define.NewReplyErrorByCode(reply.Ret).ErrMsg(arg.ReqLang)
			return
		}

		result, err := f(c, arg, span)
		if err != nil {
			switch e := err.(type) {
			case *define.ReplyError:
				if e != nil {
					reply.Ret = e.Code
					reply.Msg = e.ErrMsg(arg.ReqLang)
				} else {
					reply.Ret = define.ErrCodeBusy
					reply.Msg = define.ErrMsgBusy.ErrMsg(arg.ReqLang)
				}
			default:
				reply.Ret = define.ErrCodeBusy
				reply.Msg = define.ErrMsgBusy.ErrMsg(arg.ReqLang)
			}
		} else if result != nil {
			reply.Ret = result.Ret
			reply.Msg = result.Msg
			reply.Data = result.Data
		}
	}
}

// ValidateSign 进行签名验证
func ValidateSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		{
			c.Next()
			token := c.GetHeader(define.RequestTokenKey)
			params := make(map[string]interface{})
			if c.Request.Method == http.MethodGet {
				for key, val := range c.Request.Form {
					params[key] = val[0]
				}
			} else if c.Request.Method == http.MethodPost {
				bodyBytes, err := ioutil.ReadAll(c.Request.Body)
				if err != nil {
					log.Error("读取请求body失败", zap.String("err", err.Error()))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag
				}
				dec := jsoniter.NewDecoder(bytes.NewReader(bodyBytes))
				dec.UseNumber()
				err = dec.Decode(&params)
				if err != nil {
					log.Error("解档请求body失败",
						zap.ByteString("arg", bodyBytes),
						zap.String("err", err.Error()))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag

				}
				c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
			}

			if len(token) != 0 {
				params["token"] = token
			}
			sigp, ok := params["sig"]
			if !ok {
				log.Error("缺少sig请求参数", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeParam)
				goto flag
			}
			sig, ok := sigp.(string)
			if !ok || len(sig) == 0 {
				log.Error("sig请求参数类型错误或长度为0", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeParam)
				goto flag
			}
			delete(params, "sig")

			ts, ok := params["ts"]
			if !ok {
				log.Error("缺少ts请求参数", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeParam)
				goto flag
			}

			var rt float64
			var err error
			tsJN, ok := ts.(rawJson.Number)
			if !ok {
				tsStr, ok := ts.(string)
				if !ok {
					log.Error("ts请求参数类型错误", zap.Any("params", params))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag
				}
				rt, err = strconv.ParseFloat(tsStr, 64)
				if err != nil {
					log.Error("ts string类型转为float错误",
						zap.Any("params", params),
						zap.String("err", err.Error()))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag
				}
			} else {
				rt, err = tsJN.Float64()
				if err != nil {
					log.Error("ts json.Number类型转为float错误",
						zap.Any("params", params),
						zap.String("err", err.Error()))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag
				}
			}
			if math.Abs(rt-float64(time.Now().Unix())) > define.RequestTimeout {
				log.Error("请求时间超出有效期",
					zap.Any("params", params),
					zap.Float64("abs", math.Abs(rt-float64(time.Now().Unix()))))
				c.Set(define.ResponseCodeKey, define.ErrCodeRequestTimeException)
				goto flag
			}
			var secretKey string

			osStr, ok := params["req_os"]
			if !ok {
				log.Error("缺少req_os参数", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeParam)
				goto flag
			}
			var os string
			osJn, ok := osStr.(rawJson.Number)
			if !ok {
				os, ok = osStr.(string)
				if !ok {
					log.Error("req_os参数类型错误", zap.Any("params", params))
					c.Set(define.ResponseCodeKey, define.ErrCodeParam)
					goto flag
				}
			} else {
				os = osJn.String()
			}
			switch os {
			case define.OsStrWeb, define.OsStrH5:
				secretKey = define.SecretKeyWeb
			case define.OsStrIos:
				secretKey = define.SecretKeyIos
			case define.OsStrAndroid:
				secretKey = define.SecretKeyAndroid
			default:
				log.Error("appId参数错误", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeParam)
				goto flag
			}
			paramsStr := sign.GetParasStr(params)

			origin := paramsStr + secretKey
			log.Info("validateSignRequest", zap.String("origin params", origin))
			mySig := sign.NormalEncode(origin)

			if sig != strings.ToLower(mySig) {
				log.Error("request请求签名验证失败", zap.Any("params", params))
				c.Set(define.ResponseCodeKey, define.ErrCodeSignature)
			}
		}
	flag:
		c.Next()
	}
}

// 不进行传入参数打印
func noPrintIn(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		reply, err := f(c, arg, span)
		if reply != nil {
			reply.NotPrintIn = true
		}
		return reply, err
	}
}

// 不进行返回数据打印
func noPrintOut(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		reply, err := f(c, arg, span)
		if reply != nil {
			reply.NotPrintOut = true
		}
		return reply, err
	}
}

// 进行登录状态验证
func auth(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		var err error
		arg.TokenPayload, err = parseToken(arg.ReqID, arg.Token)
		if err != nil {
			return nil, err
		}

		// 判断是不是用一个设备
		if arg.LastDevice != arg.DeviceID {
			log.Error("auth not the same device", zap.Int64("reqID", arg.ReqID), zap.String("online", arg.LastDevice), zap.String("current", arg.DeviceID))
			return nil, define.ErrMsgLoginElseWhere
		}
		arg.ReqPID = arg.PlatformID

		// 更新过期时间
		var exp time.Duration
		switch arg.ReqOs {
		case define.OsAndroid, define.OsIos:
			exp = define.PhoneLoginExpireDate
		default:
			exp = define.WebLoginExpireDate
		}
		cache.ExpireKey(define.CacheKeyLoginInfo+arg.CacheID, exp)

		return f(c, arg, span)
	}
}

// 尝试登录状态验证
func tryAuth(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		var err error
		arg.TokenPayload, err = parseToken(arg.ReqID, arg.Token)
		if err == nil {
			arg.ReqPID = arg.PlatformID
		}

		return f(c, arg, span)
	}
}

// 进行身份认证状态验证
func verify(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		// 判断用户是否已经通过实名认证
		if (arg.CountryCode == define.ChineseCode && !arg.Verify.Level2()) || (arg.CountryCode != define.ChineseCode && !arg.Verify.Level1OrHigh()) {
			log.Error("user verify state error", zap.Int64("reqID", arg.ReqID), zap.Int64("userID", arg.UserID), zap.Uint8("verify", uint8(arg.Verify)))
			return nil, define.ErrMsgNeedVerifyIdentity
		}
		// 认证验证通过
		return f(c, arg, span)
	}
}

// 进行身份认证状态验证
func verifyLv1(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		// 判断用户是否已经通过实名认证
		if !arg.Verify.Level1OrHigh() {
			log.Error("user verify state error", zap.Int64("reqID", arg.ReqID), zap.Int64("userID", arg.UserID), zap.Uint8("verify", uint8(arg.Verify)))
			return nil, define.ErrMsgNeedVerifyIdentity
		}
		// lv2认证验证通过
		return f(c, arg, span)
	}
}

// 进行身份认证状态验证
func verifyLv2(f HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		// 判断用户是否已经通过实名认证
		if !arg.Verify.Level2() {
			log.Error("user verify state error", zap.Int64("reqID", arg.ReqID), zap.Int64("userID", arg.UserID), zap.Uint8("verify", uint8(arg.Verify)))
			return nil, define.ErrMsgNeedVerifyIdentity
		}
		// lv2认证验证通过
		return f(c, arg, span)
	}
}

func parseToken(reqID int64, token string) (define.TokenPayload, error) {
	var payload define.TokenPayload
	if len(token) == 0 {
		return payload, define.ErrMsgShouldLogin
	}
	t, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return convert.Str2Bytes(define.TokenKey), nil
	})
	if err != nil {
		log.Error("parseToken failed", zap.Int64("reqID", reqID), zap.String("err", err.Error()))
		return payload, define.ErrMsgShouldLogin
	}
	if !t.Valid {
		log.Error("parseToken failed", zap.Int64("reqID", reqID))
		return payload, define.ErrMsgShouldLogin
	}
	// 解析token载荷
	claim, ok := t.Claims.(jwt.MapClaims)
	if !ok {
		log.Error("parseToken failed", zap.Int64("reqID", reqID))
		return payload, define.ErrMsgShouldLogin
	}
	ptr, ok := claim["jti"]
	if !ok {
		log.Error("parseToken jti is not exists", zap.Int64("reqID", reqID), zap.Any("claim", claim))
		return payload, define.ErrMsgShouldLogin
	}
	jti, ok := ptr.(string)
	if !ok {
		log.Error("parseToken jti type not support", zap.Int64("reqID", reqID), zap.Any("ptr", ptr))
		return payload, define.ErrMsgShouldLogin
	}

	// 根据jti到redis获取用户信息
	payload, err = cache.GetTokenPayload(jti)
	if err != nil {
		log.Error("parseToken GetTokenPayload error", zap.Int64("reqID", reqID), zap.String("jti", jti), zap.Error(err))
		return payload, define.ErrMsgShouldLogin
	}
	if payload.AreaCode == "" {
		payload.AreaCode = define.ChineseAreaCode
	}
	if payload.CountryCode == "" {
		country, err := database.GetCountryInfoByCode(payload.AreaCode)
		if err != nil {
			payload.CountryCode = define.ChineseCode
		} else {
			payload.CountryCode = country.CountryEncrypt
		}
	}

	return payload, nil
}

func readArg(r *http.Request) (*define.Arg, error) {
	defer func() {
		if r.Body != nil {
			_ = r.Body.Close()
		}
	}()
	var (
		err error
		arg define.Arg
	)

	if r.Method == http.MethodGet {
		_ = r.ParseForm()
		err = getArgFromParam(r.Form, &arg)
	} else if r.Method == http.MethodPost {
		err = getArgFromBody(r.Body, &arg)
	}
	return &arg, err
}

func getArgFromBody(r io.Reader, arg *define.Arg) (err error) {
	encode, err := ioutil.ReadAll(r)
	if err == nil {
		if conf.ZipHttp() {
			// 压缩处理方式
			unzipData, err := zip.UnzipByte(encode)
			if err != nil {
				log.Error("getArgFromBody UnzipByte failed",
					zap.Int("length", len(encode)),
					zap.ByteString("data", encode),
					zap.String("err", err.Error()))
			}
			err = json.Unmarshal(unzipData, arg)
		} else {
			// 非压缩处理方式
			err = json.Unmarshal(encode, arg)
		}
		if err != nil {
			log.Error("getArgFromBody unmarshal failed",
				zap.Int("length", len(encode)),
				zap.ByteString("data", encode),
				zap.String("err", err.Error()))
		}
	}
	return
}

func getArgFromParam(r url.Values, arg *define.Arg) error {
	if len(r) == 0 {
		return nil
	}
	val := make(map[string]string)
	for k, v := range r {
		val[k] = v[0]
	}

	encode, err := json.Marshal(val)
	if err != nil {
		log.Error("getArgFromParam marshal failed",
			zap.Any("params", val),
			zap.String("err", err.Error()))
		return err
	}
	if language, ok := val["language"]; ok {
		lang, _ := strconv.Atoi(language)
		arg.ReqLang = define.ReqLang(lang)
	}
	if platform, ok := val["platform_id"]; ok {
		arg.ReqPID, _ = strconv.Atoi(platform)
	}
	arg.Data = encode
	return nil
}

// FormWarp 标准http请求处理,从form和url中获取请求参数,并直接返回http错误码
func FormWarp(f HandlerFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		reqID := database.NextID()
		c.Set(define.RequestIDKey, reqID)

		arg, err := paramForm(c)
		if err != nil {
			log.Error("NormalWarp paramForm error", zap.Int64("reqID", reqID), zap.String("URI", c.Request.RequestURI), zap.Error(err))
			_ = c.AbortWithError(http.StatusBadRequest, err)
			return
		}
		arg.ReqID = reqID
		arg.ReqIP = c.ClientIP()
		if arg.ReqLang < define.ReqLangCN || arg.ReqLang >= define.ReqLangNonSupport {
			arg.ReqLang = define.ReqLangEN
		}
		span := xplugins.NewSpan().SetRequestID(arg.ReqID)

		reply := new(define.Reply)
		defer func() {
			processLog(c.Request.RequestURI, &arg.ReqArg, arg.UserID, arg.Data, reply, span)
			c.Status(reply.Ret)
		}()

		// 之前的处理出现错误
		reply.Ret = c.GetInt(define.ResponseCodeKey)
		if reply.Ret != http.StatusOK {
			return
		}

		result, err := f(c, arg, span)
		if err != nil {
			reply.Ret = http.StatusInternalServerError
		} else if result != nil {
			reply.Ret = result.Ret
		} else {
			reply.Ret = http.StatusOK
		}

		c.Next()
	}
}

func BodyWarp(f HandlerFunc) gin.HandlerFunc {
	return func(c *gin.Context) {
		reqID := database.NextID()
		c.Set(define.RequestIDKey, reqID)

		arg, err := paramBody(c)
		if err != nil {
			log.Error("NormalWarp paramBody error", zap.Int64("reqID", reqID), zap.String("URI", c.Request.RequestURI), zap.Error(err))
			_ = c.AbortWithError(http.StatusBadRequest, err)
			return
		}
		arg.ReqID = reqID
		arg.ReqIP = c.ClientIP()
		if arg.ReqLang < define.ReqLangCN || arg.ReqLang >= define.ReqLangNonSupport {
			arg.ReqLang = define.ReqLangEN
		}
		span := xplugins.NewSpan().SetRequestID(arg.ReqID)

		reply := new(define.Reply)
		defer func() {
			processLog(c.Request.RequestURI, &arg.ReqArg, arg.UserID, arg.Data, reply, span)
			c.Status(reply.Ret)
		}()

		// 之前的处理出现错误
		reply.Ret = c.GetInt(define.ResponseCodeKey)
		if reply.Ret != http.StatusOK {
			return
		}

		result, err := f(c, arg, span)
		if err != nil {
			reply.Ret = http.StatusInternalServerError
		} else if result != nil {
			reply.Ret = result.Ret
		} else {
			reply.Ret = http.StatusOK
		}

		c.Next()
	}
}

func paramForm(c *gin.Context) (*define.Arg, error) {
	params := make(map[string]string)
	_ = c.Request.ParseForm()
	for key, val := range c.Request.Form {
		params[key] = val[0]
	}
	//log.Info("TEST LOG", zap.Any("from", c.Request.Form))

	data, err := json.Marshal(params)
	if err != nil {
		log.Error("readCallbackArg marshal failed",
			zap.Any("params", params),
			zap.String("err", err.Error()))
		return nil, define.ErrMsgParam
	}
	return &define.Arg{Data: data}, err
}

func paramBody(c *gin.Context) (*define.Arg, error) {
	body, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		log.Error("paramBody ReadAll failed",
			zap.Error(err))
		return nil, define.ErrMsgParam
	}

	return &define.Arg{Data: body}, err
}

func replyWrite(w http.ResponseWriter, reqURI string, user *define.TokenPayload, reqArg *define.ReqArg, reply *define.Reply) {
	defer commonsrv.SaveApiErrorRecord(reqArg.ReqID, user.UserID, reqArg.ReqOs, reply.Ret, reply.Msg, reqURI, reqArg.ReqIP)
	defer commonsrv.CollectErrorReply(reqArg, user, reply.Ret, reply.Msg, reqURI)
	defer cache.SaveOnlineUserStatistic(reqArg)
	defer cache.SaveOnlineUserStatisticWithUser(user)

	var (
		encode []byte
		err    error
	)
	if reply.Ret == define.ErrCodeConfirmationIsRequired || reply.Ret == define.ErrCodeSafeNeedVerify || reply.Ret == define.ErrCodeSafeFundPassword {
		reply.Msg, err = json.UnescapeHTMLMarshal2String(reply.Data)
		if err != nil {
			log.Error("replyWrite marshal msg fail", zap.Error(err))
		}
	}

	reply.Identifier = conf.Identifier()
	encode, err = json.UnescapeHTMLMarshal(reply)
	if err != nil {
		log.Error("replyWrite marshal failed", zap.String("err", err.Error()))
		reply.Ret = http.StatusBadRequest
		reply.Msg = err.Error()
		http.Error(w, reply.Msg, reply.Ret)
		return
	}
	w.Header().Set("Content-type", "application/json")
	if conf.ZipHttp() {
		// 压缩处理方式
		zipData, err := zip.ZipByte(encode)
		if err != nil {
			log.Error("replyWrite ZipByte failed", zap.String("err", err.Error()))
			reply.Ret = http.StatusBadRequest
			reply.Msg = err.Error()
			http.Error(w, reply.Msg, reply.Ret)
			return
		}
		w.Header().Set("Content-Encoding", "deflate")
		_, _ = w.Write(zipData)
	} else {
		// 非压缩处理方式
		_, _ = w.Write(encode)
	}
}

func processLog(action string, arg *define.ReqArg, uid int64, param []byte, reply *define.Reply, span *xplugins.SpanValues) {
	if !reply.NotPrintIn && !reply.NotPrintOut {
		log.Info("process out:",
			zap.String("action", action),
			zap.Any("reqArg", arg),
			zap.Int64("userID", uid),
			zap.ByteString("params", param),
			zap.Int("ret", reply.Ret),
			zap.String("msg", reply.Msg),
			zap.String("curNode", span.GetRequestNode()),
			zap.String("nextNode", span.GetResponseNode()),
			zap.Any("data", reply.Data))
	} else if !reply.NotPrintIn {
		log.Info("process out:",
			zap.String("action", action),
			zap.Any("reqArg", arg),
			zap.Int64("userID", uid),
			zap.ByteString("params", param),
			zap.Int("ret", reply.Ret),
			zap.String("msg", reply.Msg),
			zap.String("curNode", span.GetRequestNode()),
			zap.String("nextNode", span.GetResponseNode()))
	} else if !reply.NotPrintOut {
		log.Info("process out:",
			zap.String("action", action),
			zap.Any("reqArg", arg),
			zap.Int64("userID", uid),
			zap.Int("ret", reply.Ret),
			zap.String("msg", reply.Msg),
			zap.String("curNode", span.GetRequestNode()),
			zap.String("nextNode", span.GetResponseNode()),
			zap.Any("data", reply.Data))
	} else {
		log.Info("process out:",
			zap.String("action", action),
			zap.Any("reqArg", arg),
			zap.Int64("userID", uid),
			zap.Int("ret", reply.Ret),
			zap.String("msg", reply.Msg),
			zap.String("curNode", span.GetRequestNode()),
			zap.String("nextNode", span.GetResponseNode()))
	}
}
