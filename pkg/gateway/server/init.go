package server

import (
	"context"

	"bc/libs/conf"
	"bc/libs/xsignal"
	"bc/pkg/gateway/service/core"
	"bc/pkg/gateway/service/reverse_core"
	"bc/pkg/gateway/service/spot"
	"bc/pkg/gateway/service/user"
	"bc/pkg/gateway/service/wallet"
	"github.com/gin-gonic/gin"
)

func Init() {
	s := xsignal.NewSignal()
	ctx, cancel := context.WithCancel(context.Background())

	// 初始化依赖rpc客户端
	initDependRPCClient(ctx, s)

	// 初始化本地http服务
	if !conf.DebugEnv() {
		gin.SetMode(gin.ReleaseMode)
	}
	InitHTTP(ctx)

	// 初始化本地回调http服务
	InitCallbackServer(ctx)

	// 关闭http服务
	s.AppendCloseFunc(ShutdownServer)

	// 关闭时先使用上下文通知组件退出
	s.AppendCloseFunc(cancel)

	// 监听系统信号
	s.SignalMonitor()
}

func initDependRPCClient(ctx context.Context, s *xsignal.Signal) {
	// 初始化合约core rpc客户端
	core.InitClient(ctx)
	s.AppendCloseFunc(core.Close)

	// 初始化反向合约core rpc客户端
	reverse_core.InitClient(ctx)
	s.AppendCloseFunc(reverse_core.Close)

	// 初始化现货core rpc客户端
	spot.InitClient(ctx)
	s.AppendCloseFunc(spot.Close)

	// 初始化user rpc客户端
	user.InitClient(ctx)
	s.AppendCloseFunc(user.Close)

	// 初始化wallet rpc客户端
	wallet.InitClient(ctx)
	s.AppendCloseFunc(wallet.Close)
}
