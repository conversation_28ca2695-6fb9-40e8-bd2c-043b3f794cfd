package server

import (
	"context"
	"sync"
	"time"

	"bc/libs/define"
	"bc/libs/xplugins"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

const (
	_expiredTime     = 60  // key超时删除延迟 单位秒
	_cleanChannelCap = 128 // 清理key通道容量
)

type RateLimiter struct {
	limiter   *rate.Limiter
	expiredTs int64
}

type IPRateLimiter struct {
	ips    map[string]*RateLimiter
	clean  map[int64]map[string]struct{}
	mu     *sync.RWMutex
	limit  rate.Limit
	bursts int
}

func NewIPRateLimiter(ctx context.Context, cfg map[string]define.ApiLimitRate) map[string]*IPRateLimiter {
	mp := make(map[string]*IPRateLimiter, len(cfg))
	for key, val := range cfg {
		mp[key] = newIPRateLimiter(ctx, rate.Every(time.Millisecond*time.Duration(val.Limit)), val.Bursts)
	}

	return mp
}

func newIPRateLimiter(ctx context.Context, limit rate.Limit, bursts int) *IPRateLimiter {
	i := &IPRateLimiter{
		ips:    make(map[string]*RateLimiter),
		clean:  make(map[int64]map[string]struct{}),
		mu:     new(sync.RWMutex),
		limit:  limit,
		bursts: bursts,
	}

	// 启动清理程序,清理长时间不使用的key
	go i.cleanIP(ctx, time.Now().Unix())

	return i
}

func (i *IPRateLimiter) AddIP(ip string) *rate.Limiter {
	i.mu.Lock()
	defer i.mu.Unlock()

	et := time.Now().Unix() + _expiredTime
	limiter := rate.NewLimiter(i.limit, i.bursts)

	clean, found := i.clean[et]
	if !found {
		clean = make(map[string]struct{})
		i.clean[et] = clean
	}
	clean[ip] = struct{}{}

	i.ips[ip] = &RateLimiter{
		limiter:   limiter,
		expiredTs: et,
	}

	return limiter
}

func (i *IPRateLimiter) cleanIP(ctx context.Context, now int64) {
	delCh := make(chan int64, _cleanChannelCap)
	tk := time.NewTimer(time.Second)
	defer tk.Stop()

	go func() {
		for key := range delCh {
			i.mu.Lock()
			delete(i.clean, key)
			for k := range i.clean[key] {
				delete(i.ips, k)
			}
			i.mu.Unlock()
		}
	}()

	var found bool
	for {
		select {
		case <-tk.C:
			now++
			i.mu.RLock()
			_, found = i.clean[now]
			i.mu.RUnlock()
			if found {
				delCh <- now
			}
		case <-ctx.Done():
			close(delCh)
			return
		}
	}
}

func (i *IPRateLimiter) GetLimiter(ip string) *rate.Limiter {
	i.mu.Lock()
	rl, exists := i.ips[ip]
	if !exists {
		i.mu.Unlock()
		return i.AddIP(ip)
	}

	delete(i.clean[rl.expiredTs], ip)
	rl.expiredTs = time.Now().Unix() + _expiredTime
	clean, found := i.clean[rl.expiredTs]
	if !found {
		clean = make(map[string]struct{})
		i.clean[rl.expiredTs] = clean
	}
	clean[ip] = struct{}{}

	i.mu.Unlock()

	return rl.limiter
}

func ipLimitRate(next HandlerFunc) HandlerFunc {
	return func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error) {
		limiter := srv.GetRouteLimitRate(c.Request.RequestURI).GetLimiter(arg.ReqIP)
		if limiter != nil && !limiter.Allow() {
			return nil, define.ErrMsgAPIFrequent
		}

		return next(c, arg, span)
	}
}
