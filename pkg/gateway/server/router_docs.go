package server

import (
	"bc/libs/conf"
	"bc/pkg/gateway/docs"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func docsRouter(eng *gin.Engine) {
	docConf := conf.DocConf()
	docs.SwaggerInfo.Version = docConf.Version
	docs.SwaggerInfo.Host = docConf.Host
	docs.SwaggerInfo.BasePath = docConf.BasePath
	docs.SwaggerInfo.Schemes = docConf.Schemes
	docs.SwaggerInfo.Title = docConf.Title
	docs.SwaggerInfo.Description = docConf.Description

	eng.GET("/v2/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler)) // api文档
}
