package server

import (
	"bc/libs/conf"
	"bc/libs/define"
	"bc/libs/xplugins"
	"bc/pkg/gateway/service/core"
	"bc/pkg/gateway/service/reverse_core"
	"bc/pkg/gateway/service/spot"
	"bc/pkg/gateway/service/user"
	"bc/pkg/gateway/service/wallet"
	"github.com/gin-gonic/gin"
)

type HandlerFunc func(c *gin.Context, arg *define.Arg, span *xplugins.SpanValues) (*define.Reply, error)

type Router struct {
	mux         *gin.RouterGroup
	wrap        func(h HandlerFunc) gin.HandlerFunc
	middlewares []gin.HandlerFunc
}

func (r *Router) BindHandler(path string, h HandlerFunc) {
	if r.wrap != nil {
		ms := append(r.middlewares, r.wrap(h))
		bindHandler(r.mux, path, ms...)
	}
}

func NewRouter(mux *gin.RouterGroup, wrap func(h HandlerFunc) gin.HandlerFunc, middlewares ...gin.HandlerFunc) *Router {
	return &Router{mux: mux, wrap: wrap, middlewares: middlewares}
}

func bindHandler(m *gin.RouterGroup, relativePath string, handlers ...gin.HandlerFunc) {
	m.GET(relativePath, handlers...)
	m.POST(relativePath, handlers...)
	m.OPTIONS(relativePath, handlers...)
}

func (s *Server) SetRouter() {
	// 主站接口
	baseRouter(s.engine)
	// usdt合约接口
	usdtRouter(s.engine)
	// usd合约接口
	usdRouter(s.engine)
	// 现货接口
	spotRouter(s.engine)

	// 文档接口
	docsRouter(s.engine)
}

func baseRouter(eng *gin.Engine) {
	v2 := eng.Group("/v2/base")
	{
		// 走原生请求的接口
		formR := NewRouter(v2, FormWarp)
		{
			formR.BindHandler("/legal/update", core.UpdateLegalOrderState) // 法币订单状态更新回调
		}

		// 不需要走参数验证的接口
		noSignR := NewRouter(v2, NormalWarp)
		{
			noSignR.BindHandler("/common/version", core.CheckUpgrade)    // 版本更新
			noSignR.BindHandler("/common/download", core.GetAppDownload) // 获取app下载链接
			noSignR.BindHandler("/common/hotfix", core.GetHotfixPatch)   // 获取热升级补丁列表
		}

		// 不需要登录的验签路由
		unLoginR := NewRouter(v2, UnLoginRequest, ValidateSign())
		// 需要登录的验签路由
		loginR := NewRouter(v2, LoginRequest, ValidateSign())
		// 认证路由
		verifyR := NewRouter(v2, VerifyRequest, ValidateSign())

		// 公共接口
		{
			unLoginR.BindHandler("/ping", core.Ping)                                          // ping接口
			unLoginR.BindHandler("/common/authcode", user.GetAuthCode)                        // 获取验证码
			unLoginR.BindHandler("/common/authcode/safe", user.GetAuthCodeSafe)               // 获取验证码(易盾)
			unLoginR.BindHandler("/common/authcode/verify", user.CheckAuthCode)               // 校验验证码
			unLoginR.BindHandler("/common/coin/list", core.GetCoinList)                       // 获取币种列表
			unLoginR.BindHandler("/common/banner/list", core.HomeBanner)                      // 获取轮播图列表
			unLoginR.BindHandler("/common/notice/list", core.GetNoticeList)                   // 获取公告列表
			unLoginR.BindHandler("/common/notice/detail", core.GetNoticeInfo)                 // 获取公告详情
			unLoginR.BindHandler("/common/notice/important/new", core.GetImportantNoticeV2)   // 获取重要通知
			unLoginR.BindHandler("/common/countryareacode", noPrintOut(user.GetAreaCodeList)) // 获取区域列表
			unLoginR.BindHandler("/common/contactus", user.ContactUS)                         // 联系我们
			unLoginR.BindHandler("/common/about", core.About)                                 // 关于我们
			unLoginR.BindHandler("/common/serverConfig", core.ServerConfig)                   // 服务器配置列表(目前仅有域名列表)
			unLoginR.BindHandler("/common/rate", core.GetCoinLegalRate)                       // 获取币种法币汇率
			unLoginR.BindHandler("/common/app/status", core.AppStatus)                        // 获取app状态
			unLoginR.BindHandler("/common/provisions-of-risk", core.GetRiskProvisions)        // 获取平台风险准备金
		}

		// 数据统计
		{
			unLoginR.BindHandler("/collect/error/submit", noPrintIn(core.ErrorSubmit)) // 客户端错误日志提交
			unLoginR.BindHandler("/collect/download/submit", core.AppDownloadSubmit)   // APP下载统计
			unLoginR.BindHandler("/collect/domain/delay", core.CollectDomainDelay)     // 服务域名速度测试结果提交
		}

		// 注册登录
		{
			unLoginR.BindHandler("/account/check", user.CheckAccount)    // 检查账号是否存在
			unLoginR.BindHandler("/account/register", user.Register)     // 注册
			unLoginR.BindHandler("/account/login", user.Login)           // 登录(api_version为v2时要检查安全验证)
			unLoginR.BindHandler("/account/forget", user.ForgetPassword) // 找回密码(api_version为v2时要检查安全验证)
			loginR.BindHandler("/account/logout", user.Logout)           // 登出(用户非登录状态调用不进行拦截)
			loginR.BindHandler("/account/delete", user.DeleteAccount)    // 账户注销
		}

		// 用户信息
		{
			loginR.BindHandler("/user/info", user.UserInfo)                                      // 用户信息
			loginR.BindHandler("/user/info/modify", user.ModifyUserInfo)                         // 用户信息更新(昵称/介绍/头像)
			loginR.BindHandler("/user/switch/modify", user.UserSwitchModify)                     // 用户开关控制
			loginR.BindHandler("/user/option/modify", user.UserOptionModify)                     // 用户选项控制
			loginR.BindHandler("/user/invite", user.UserInviteInfo)                              // 邀请信息
			loginR.BindHandler("/user/invite/list", noPrintOut(user.InviteList))                 // 邀请记录
			loginR.BindHandler("/user/verify/state", user.GetUserVerifyState)                    // 获取用户认证状态
			loginR.BindHandler("/user/verify/idnumber", user.IdNumberVerify)                     // 提交身份信息审核
			loginR.BindHandler("/user/verify/other", user.ForeignVerify)                         // 提交身份信息审核(非大陆)
			loginR.BindHandler("/user/verify/manual", user.ManualVerify)                         // 提交活体身份信息审核(人工)
			loginR.BindHandler("/user/verify/face", noPrintIn(user.FaceVerify))                  // 提交面部认证信息
			loginR.BindHandler("/user/verify/progress", user.GetVerifyProgress)                  // 获取用户认证进度
			loginR.BindHandler("/user/verify/face/h5", noPrintIn(user.FaceVerifyForH5))          // 提交面部认证信息(h5)
			verifyR.BindHandler("/user/verify/withdraw", noPrintIn(user.WithdrawVerify))         // 提交提币面部认证信息
			verifyR.BindHandler("/user/verify/withdraw/h5", noPrintIn(user.WithdrawVerifyForH5)) // 提交面部认证信息(h5)
			loginR.BindHandler("/user/quota", core.UserQuota)                                    // 用户配额
			loginR.BindHandler("/trade/license/agree", user.TradeLicenseAgree)                   // 标记接受交易协议
			loginR.BindHandler("/follow/license/agree", user.FollowLicenseAgree)                 // 标记接收跟单协议
		}

		// api管理
		{
			loginR.BindHandler("/user/api/list", user.GetUserApiList)     // 查询用户api列表
			loginR.BindHandler("/user/api/detail", user.GetUserApiDetail) // 查询用户api信息
			verifyR.BindHandler("/user/api/create", user.UserApiCreate)   // 用户api创建
			loginR.BindHandler("/user/api/modify", user.UserApiModify)    // 用户api修改配置
			loginR.BindHandler("/user/api/delete", user.UserApiDelete)    // 用户api删除
		}

		// 安全中心相关
		{
			loginR.BindHandler("/user/safe/account/modify", user.SafeModifyAccount)              // 绑定手机/邮箱
			loginR.BindHandler("/user/safe/spare/email/modify", user.SafeModifySpareEmail)       // 绑定/修改备用邮箱
			loginR.BindHandler("/user/safe/password/login/modify", user.SafeModifyLoginPassword) // 设置或修改登录密码
			loginR.BindHandler("/user/safe/password/fund/modify", user.SafeModifyFundPassword)   // 设置或修改资金密码
			loginR.BindHandler("/user/safe/password/fund/timeliness", user.SafeTradeVerifyMode)  // 修改资金密码验证时效
			loginR.BindHandler("/user/safe/password/login/verify", user.SafeVerifyLoginPassword) // 校验登录密码
			loginR.BindHandler("/user/safe/password/fund/verify", user.SafeVerifyFundPassword)   // 校验资金密码
			loginR.BindHandler("/user/safe/totp/secret/get", user.SafeGetNewTotpSecret)          // 获取验证器私钥
			loginR.BindHandler("/user/safe/totp/modify", user.SafeModifyTotpSecret)              // 绑定验证器私钥
			loginR.BindHandler("/user/safe/login/verify/switch", user.SafeModifyLoginVerifyMode) // 修改登录验证开关
			loginR.BindHandler("/user/safe/twostep/verify", user.SafeTwoStepVerify)              // 校验安全验证
			loginR.BindHandler("/user/safe/logs", user.SafeUserLogs)                             // 用户操作日志获取
		}

		// 资产相关
		{
			loginR.BindHandler("/asset/list", wallet.MultipleAssetList)                             // 多资产账户列表获取
			loginR.BindHandler("/asset/detail", wallet.MultipleAssetDetail)                         // 多资产账户详情获取
			loginR.BindHandler("/asset/transfer", wallet.AssetTransfer)                             // 资产划转
			loginR.BindHandler("/asset/deposit/address", wallet.DepositAddress)                     // 充币地址
			loginR.BindHandler("/asset/withdraw/address/default", wallet.GetWithdrawDefaultAddress) // 获取默认提币地址
			loginR.BindHandler("/asset/withdraw/address/list", wallet.WithdrawAddressList)          // 提币地址列表
			loginR.BindHandler("/asset/withdraw/address/add", wallet.WithdrawAddressAdd)            // 提币地址设置
			loginR.BindHandler("/asset/withdraw/address/del", wallet.WithdrawAddressDel)            // 提币地址删除
			loginR.BindHandler("/asset/withdraw/address/update", wallet.WithdrawAddressUpdate)      // 提币地址更新
			loginR.BindHandler("/asset/withdraw", wallet.Withdraw)                                  // 提币申请
			loginR.BindHandler("/asset/bill", noPrintOut(wallet.WalletBill))                        // 资产账户流水记录
			loginR.BindHandler("/asset/bill/withdraw", noPrintOut(wallet.WalletWithdrawBill))       // 资产账户提币记录
			loginR.BindHandler("/asset/bill/recharge", noPrintOut(wallet.WalletRechargeBill))       // 资产账户充币记录
			loginR.BindHandler("/asset/bill/transfer", noPrintOut(wallet.WalletTransferBill))       // 资产账户划转记录
			loginR.BindHandler("/asset/bill/brokerage", noPrintOut(wallet.WalletBrokerageBill))     // 资产账户佣金记录
			loginR.BindHandler("/asset/bill/legal", noPrintOut(wallet.WalletLegalBill))             // 资产账户法币记录
		}

		// 法币交易
		{
			verifyR.BindHandler("/legal/login", core.C2CLogin)                        // 登录获取c2c专用token
			verifyR.BindHandler("/legal/exchange/rate", core.LegalExchangeRate)       // 下单获取汇率
			verifyR.BindHandler("/legal/place", core.LegalPlaceOrder)                 // 下单获取订单号和token
			verifyR.BindHandler("/legal/order/list", noPrintOut(core.LegalOrderList)) // 获取订单列表
			loginR.BindHandler("/legal/limit", core.LegalPlaceLimit)                  // 获取下单限制

			loginR.BindHandler("/legal/third/config", core.ThirdLegalConf)          // 获取三方买币配置信息
			loginR.BindHandler("/legal/third/merchants", core.ThirdLegalMerchants)  // 获取三方买币商家列表
			loginR.BindHandler("/legal/third/place", core.ThirdLegalOrderPlace)     // 三方买币下单
			loginR.BindHandler("/legal/third/order/list", core.ThirdLegalOrderList) // 获取三方买币订单记录

			//loginR.BindHandler("/legal/order/pay/confirm", core.LegalOrderPayConfirm) // 下单标记收到款项
			verifyR.BindHandler("/user/payment/list", core.ListUserPayments)   // 用户支付方式列表
			verifyR.BindHandler("/user/payment/add", core.AddUserPayments)     // 用户支付方式添加
			verifyR.BindHandler("/user/payment/edit", core.UpdateUserPayments) // 用户支付方式修改
			verifyR.BindHandler("/user/payment/del", core.DelUserPayments)     // 用户支付方式移除
		}

		//活动
		{
			loginR.BindHandler("/activity/trade/info", core.ActivityInfo)        // 本期活动信息
			loginR.BindHandler("/activity/trade/myward", core.ActivityMyWard)    // 我的获奖记录
			loginR.BindHandler("/activity/trade/upshare", core.UpShare)          // 更新分享
			unLoginR.BindHandler("/activity/trade/unlogin", core.ActivityUnInfo) // 活动信息
		}

		// 币种兑换
		{
			loginR.BindHandler("/exchange/order/pre", wallet.ExchangePreOrder)         // 预兑换,锁定价格,返回订单摘要
			loginR.BindHandler("/exchange/order/actual", wallet.ExchangeActualOrder)   // 实际兑换
			loginR.BindHandler("/exchange/order/history", wallet.ExchangeOrderHistory) // 兑币订单历史记录
		}

		// 赠金接口
		{
			loginR.BindHandler("/gift-cash/activity/list", wallet.GiftCashActivityList) // 赠金活动列表
			loginR.BindHandler("/gift-cash/activity/receive", wallet.GiftCashReceive)   // 赠金活动领取
			loginR.BindHandler("/gift-cash/my/head", wallet.GiftCashHead)               // 我的赠金头部数据
			loginR.BindHandler("/gift-cash/my/record", wallet.GiftCashRecordList)       // 赠金记录
		}

		//测试接口
		if !conf.IsProduct() {
			noSignR.BindHandler("/legal/rpcAudit/test", core.TestLegalAudit)         //法币卖出订单确认
			noSignR.BindHandler("/legal/orderCallback/test", core.TestOrderCallBack) //法币卖出订单确认
		}
	}
}

func usdtRouter(eng *gin.Engine) {
	v2 := eng.Group("/v2/usdt")
	{
		// 不需要走参数验证的接口
		noSignR := NewRouter(v2, NormalWarp)
		// 不需要登录的验签路由
		unLoginR := NewRouter(v2, UnLoginRequest, ValidateSign())
		// 需要登录的验签路由
		loginR := NewRouter(v2, LoginRequest, ValidateSign())

		// 公共接口
		{
			unLoginR.BindHandler("/common/summary", core.GetSummary) // 获取摘要信息
		}

		// 合约信息
		{
			unLoginR.BindHandler("/contract/list", core.ContractList)                                // 合约列表
			unLoginR.BindHandler("/contract/market", core.ContractMarket)                            // 指定合约行情
			unLoginR.BindHandler("/contract/detail", core.ContractDetail)                            // 合约详情
			unLoginR.BindHandler("/contract/recommend", core.ContractRecommend)                      // 合约推荐
			unLoginR.BindHandler("/contract/kline", noPrintOut(core.ContractKline))                  // 合约k线
			unLoginR.BindHandler("/contract/fundingrate", core.GetFundingRate)                       // 资金费率
			unLoginR.BindHandler("/contract/markets", noPrintOut(ipLimitRate(core.ContractMarkets))) // 获取全部合约行情
		}

		// 榜单
		{
			unLoginR.BindHandler("/rank/increase", core.IncreaseRank)   // 当日涨幅榜
			unLoginR.BindHandler("/rank/buy", core.BuyRank)             // 看涨人数榜
			unLoginR.BindHandler("/rank/sell", core.SellRank)           // 看跌人数榜
			unLoginR.BindHandler("/rank/indicator", core.Indicator)     // 买卖指标
			unLoginR.BindHandler("/rank/dragSupport", core.DragSupport) // 阻力支撑位
		}

		// 用户信息
		{
			loginR.BindHandler("/user/trade/lever", core.TradeLever)         // 获取用户当前交易杠杆倍数
			loginR.BindHandler("/user/trade/lever/switch", core.SwitchLever) // 切换用户当前交易杠杆倍数
		}

		// 订单相关
		{
			loginR.BindHandler("/order/hold", noPrintOut(core.PositionCurrent))                             // 当前持仓
			loginR.BindHandler("/order/sold", noPrintOut(core.EntrustSold))                                 // 成交记录
			loginR.BindHandler("/all/order/sold", noPrintOut(core.AllEntrustSold))                          // 成交记录(包含带单跟单以及普通开仓)
			loginR.BindHandler("/order/plan", noPrintOut(core.PlanOrder))                                   // 计划单
			loginR.BindHandler("/order/entrust/current", noPrintOut(core.EntrustOrderCurrentList))          // 当前委托
			loginR.BindHandler("/order/entrust/current/plan", noPrintOut(core.EntrustOrderCurrentPlanList)) // 当前委托(计划单)
			loginR.BindHandler("/order/entrust/history", noPrintOut(core.EntrustOrderHistoryList))          // 历史委托
			loginR.BindHandler("/order/entrust/history/plan", noPrintOut(core.EntrustOrderHistoryPlanList)) // 历史委托(计划单)
			loginR.BindHandler("/order/entrust/history/share", core.EntrustOrderHistoryShareResource)       // 历史委托分享资源
			loginR.BindHandler("/position/set/list", core.StopPriceList)                                    // 止盈止损列表
			loginR.BindHandler("/position/stop/list", core.ContractStopPriceList)                           // 合约止盈止损列表
		}

		// 资产相关
		{
			loginR.BindHandler("/asset/trade/bill/transfer", noPrintOut(wallet.TradeWalletTransferBill)) // 交易账户划转记录
			loginR.BindHandler("/asset/trade/bill/profit", noPrintOut(wallet.TradeWalletProfitBill))     // 交易账户盈亏记录
			loginR.BindHandler("/asset/trade/bill", noPrintOut(wallet.UsdtTradeAssetBill))               // 交易账户流水记录
		}

		// 交易相关
		{
			loginR.BindHandler("/order/place", core.OrderOpenPosition)              // 开仓
			loginR.BindHandler("/order/cancel", core.EntrustCancel)                 // 撤销委托
			loginR.BindHandler("/order/batch/cancel", core.EntrustOrderBatchCancel) //批量委托        // 开仓
			loginR.BindHandler("/position/closeout", core.CloseOut)                 // 平仓
			loginR.BindHandler("/position/closeout/all", core.AllOrderLiquidate)    // 一键全平
			loginR.BindHandler("/order/plan/place", core.PlanPlace)                 // 计划单下单
			loginR.BindHandler("/order/plan/cancel", core.PlanCancel)               // 计划单撤销
			loginR.BindHandler("/position/set/stop", core.SetStop)                  // 设置止盈止损
			loginR.BindHandler("/position/cancel/stop", core.CancelStop)            // 删除止盈止损
			loginR.BindHandler("/position/margin/modify", core.MarginModify)        // 调整保证金
		}

		// 跟单相关接口
		{
			loginR.BindHandler("/follow/dealer/apply", core.DealerApply)                                   // 交易员申请
			loginR.BindHandler("/follow/asset/bill", noPrintOut(wallet.UsdtFollowTradeAssetBill))          // 跟单账户流水记录
			loginR.BindHandler("/follow/asset/bill/transfer", noPrintOut(wallet.FollowWalletTransferBill)) // 跟单账户划转记录
			loginR.BindHandler("/follow/asset/bill/profit", noPrintOut(wallet.FollowWalletProfitBill))     // 跟单账户盈亏记录
			loginR.BindHandler("/follow/dealer/lever", core.GetDealerLever)                                // 获取交易员当前交易杠杆倍数
			loginR.BindHandler("/follow/dealer/lever/switch", core.DealerLeverSwitch)                      // 切换交易员当前交易杠杆倍数
			loginR.BindHandler("/follow/order/hold", noPrintOut(core.FollowPositionCurrent))               // 当前带单跟单持仓
			loginR.BindHandler("/follow/order/sold", noPrintOut(core.FollowEntrustSold))                   // 历史成交数据
			loginR.BindHandler("/follow/my/dealer/list", noPrintOut(core.MyDealerList))                    // 我的交易员列表
			loginR.BindHandler("/follow/my/profit", core.MyFollowProfitLoss)                               // 我的跟单账户收益情况
			loginR.BindHandler("/follow/current", noPrintOut(core.FollowOrderCurrent))                     // 当前带单跟单列表
			loginR.BindHandler("/follow/history", noPrintOut(core.FollowOrderHistory))                     // 历史带单跟单列表
			loginR.BindHandler("/follow/head", core.FollowHeadData)                                        // 带单跟单头部数据
			loginR.BindHandler("/follow/dealer/list", noPrintOut(core.GetLeaderList))                      // 交易员列表
			loginR.BindHandler("/follow/dealer/detail", core.GetLeaderDetail)                              // 交易员详情
			loginR.BindHandler("/follow/dealer/order/history", noPrintOut(core.DealerOrderHistory))        // 交易员历史带单
			loginR.BindHandler("/follow/dealer/order/current", noPrintOut(core.DealerOrderCurrent))        // 交易员当前带单
			loginR.BindHandler("/follow/dealer/followers", noPrintOut(core.DealerFollowers))               // 交易员当前跟随者列表
			loginR.BindHandler("/follow/setting/current", core.GetFollowSetting)                           // 获取当前跟单设置
			loginR.BindHandler("/follow/setting", core.SetFollow)                                          // 跟单设置
			loginR.BindHandler("/follow/exit", core.ExitFollow)                                            // 退出跟单
			loginR.BindHandler("/follow/remove", core.RemoveFollow)                                        // 移除跟随
			loginR.BindHandler("/follow/my/followers", noPrintOut(core.MyFollowers))                       // 我的跟随者
			loginR.BindHandler("/follow/lead/profit", noPrintOut(core.GetDealerLeadProfit))                // 获取带单分润统计(日数据)
			loginR.BindHandler("/follow/lead/profit/detail", noPrintOut(core.GetDealerLeadProfitDetail))   // 获取带单分润详情
			loginR.BindHandler("/follow/message", noPrintOut(core.GetFollowMessage))                       // 获取带单跟单消息
			loginR.BindHandler("/follow/mark/message/readed", core.MarkReadMessage)                        // 标记消息已读
			loginR.BindHandler("/follow/order/place", core.LeaderPlace)                                    // 带单开仓
			loginR.BindHandler("/follow/position/closeout", core.FollowCloseOut)                           // 带单跟单平仓
			loginR.BindHandler("/follow/position/set/stop", core.FollowSetStop)                            // 带单跟单设置止盈止损
			loginR.BindHandler("/follow/dealer/setting/current", core.FollowDealerCurrentSetting)          // 带单员当前带单设置
			loginR.BindHandler("/follow/dealer/setting", core.FollowDealerSetting)                         // 带单员带单设置
			loginR.BindHandler("/follow/position/margin/modify", core.FollowMarginModify)                  // 调整跟单逐仓保证金
		}

		//测试接口
		if !conf.IsProduct() {
			loginR.BindHandler("/position/closeout/test", core.CloseOutCustomTest) // 平仓
			loginR.BindHandler("/funding/rate/set", core.FundingRateSet)
			loginR.BindHandler("/trigger/test", core.SpyIndexPrice)
			noSignR.BindHandler("/trigger/test/dev", core.SpyIndexPrice)
			noSignR.BindHandler("/follow/brokerage/test/dev", core.TestFollowBrokerage)      //测试跟单返佣
			loginR.BindHandler("/position/closeout/orderType", core.CloseOutCustomOrderType) // 平仓
			loginR.BindHandler("/test/market/warn", core.TestMarketWarn)
			noSignR.BindHandler("/test/market/warn/index", core.TestIndexPriceWarn)
			noSignR.BindHandler("/test/market/warn/wsclose", core.TestCloseWsWarn)
		}
	}
}

func usdRouter(eng *gin.Engine) {
	v2 := eng.Group("/v2/usd")
	{
		// 不需要走参数验证的接口
		noSignR := NewRouter(v2, NormalWarp)
		// 不需要登录的验签路由
		unLoginR := NewRouter(v2, UnLoginRequest, ValidateSign())
		// 需要登录的验签路由
		loginR := NewRouter(v2, LoginRequest, ValidateSign())

		// 公共接口
		{
			unLoginR.BindHandler("/common/summary", reverse_core.GetSummary) // 获取摘要信息
		}

		// 合约信息
		{
			unLoginR.BindHandler("/contract/list", reverse_core.ContractList)                                // 合约列表
			unLoginR.BindHandler("/contract/market", reverse_core.ContractMarket)                            // 指定合约行情
			unLoginR.BindHandler("/contract/detail", reverse_core.ContractDetail)                            // 合约详情
			unLoginR.BindHandler("/contract/recommend", reverse_core.ContractRecommend)                      // 合约推荐
			unLoginR.BindHandler("/contract/kline", noPrintOut(reverse_core.ContractKline))                  // 合约k线
			unLoginR.BindHandler("/contract/fundingrate", reverse_core.GetFundingRate)                       // 资金费率
			unLoginR.BindHandler("/contract/markets", noPrintOut(ipLimitRate(reverse_core.ContractMarkets))) // 全部合约行情
		}

		// 榜单
		{
			unLoginR.BindHandler("/rank/increase", reverse_core.IncreaseRank)   // 当日涨幅榜
			unLoginR.BindHandler("/rank/buy", reverse_core.BuyRank)             // 看涨人数榜
			unLoginR.BindHandler("/rank/sell", reverse_core.SellRank)           // 看跌人数榜
			unLoginR.BindHandler("/rank/indicator", reverse_core.Indicator)     // 买卖指标
			unLoginR.BindHandler("/rank/dragSupport", reverse_core.DragSupport) // 阻力支撑位
		}

		// 订单相关
		{
			loginR.BindHandler("/order/hold", noPrintOut(reverse_core.PositionCurrent))                             // 当前持仓
			loginR.BindHandler("/order/sold", noPrintOut(reverse_core.EntrustSold))                                 // 成交记录
			loginR.BindHandler("/all/order/sold", noPrintOut(reverse_core.FollowEntrustSold))                       // 成交记录(包含带单跟单以及普通开仓)
			loginR.BindHandler("/order/plan", noPrintOut(reverse_core.PlanOrder))                                   // 计划单
			loginR.BindHandler("/order/entrust/current", noPrintOut(reverse_core.EntrustOrderCurrentList))          // 当前委托
			loginR.BindHandler("/order/entrust/current/plan", noPrintOut(reverse_core.EntrustOrderCurrentPlanList)) // 当前委托(计划单)
			loginR.BindHandler("/order/entrust/history", noPrintOut(reverse_core.EntrustOrderHistoryList))          // 历史委托
			loginR.BindHandler("/order/entrust/history/plan", noPrintOut(reverse_core.EntrustOrderHistoryPlanList)) // 历史委托(计划单)
			loginR.BindHandler("/order/entrust/history/share", reverse_core.EntrustOrderHistoryShareResource)       // 历史委托分享资源
			loginR.BindHandler("/position/set/list", reverse_core.StopPriceList)                                    // 止盈止损列表
			loginR.BindHandler("/position/stop/list", reverse_core.ContractStopPriceList)                           // 合约止盈止损列表
		}

		// 资产相关
		{
			loginR.BindHandler("/asset/trade/bill/transfer", noPrintOut(wallet.ReverseTradeWalletTransferBill)) // 交易账户划转记录
			loginR.BindHandler("/asset/trade/bill/profit", noPrintOut(wallet.ReverseTradeWalletProfitBill))     // 交易账户盈亏记录
			loginR.BindHandler("/asset/trade/bill", noPrintOut(wallet.UsdTradeAssetBill))                       // 交易账户流水记录
		}

		// 交易相关
		{
			loginR.BindHandler("/user/trade/lever", reverse_core.TradeLever)                // 获取用户当前交易杠杆倍数
			loginR.BindHandler("/user/trade/lever/switch", reverse_core.SwitchLever)        // 切换用户当前交易杠杆倍数
			loginR.BindHandler("/order/place", reverse_core.OrderOpenPosition)              // 开仓
			loginR.BindHandler("/order/cancel", reverse_core.EntrustCancel)                 // 撤销委托
			loginR.BindHandler("/order/batch/cancel", reverse_core.EntrustOrderBatchCancel) // 批量撤单
			loginR.BindHandler("/position/closeout", reverse_core.CloseOut)                 // 平仓
			loginR.BindHandler("/position/closeout/all", reverse_core.AllOrderLiquidate)    // 一键全平
			loginR.BindHandler("/order/plan/place", reverse_core.PlanPlace)                 // 计划单下单
			loginR.BindHandler("/order/plan/cancel", reverse_core.PlanCancel)               // 计划单撤销
			loginR.BindHandler("/position/set/stop", reverse_core.SetStop)                  // 设置止盈止损
			loginR.BindHandler("/position/cancel/stop", reverse_core.CancelStop)            // 删除止盈止损
			loginR.BindHandler("/position/margin/modify", reverse_core.MarginModify)        // 调整保证金
		}

		// 跟单相关接口
		//{
		//	loginR.BindHandler("/follow/dealer/apply", reverse_core.DealerApply)                                   // 交易员申请
		//	loginR.BindHandler("/follow/asset/bill/transfer", noPrintOut(wallet.FollowWalletTransferBill)) // 跟单账户划转记录
		//	loginR.BindHandler("/follow/asset/bill/profit", noPrintOut(wallet.FollowWalletProfitBill))     // 跟单账户盈亏记录
		//	loginR.BindHandler("/follow/dealer/lever", reverse_core.GetDealerLever)                                // 获取交易员当前交易杠杆倍数
		//	loginR.BindHandler("/follow/dealer/lever/switch", reverse_core.DealerLeverSwitch)                      // 切换交易员当前交易杠杆倍数
		//	loginR.BindHandler("/follow/order/hold", noPrintOut(reverse_core.FollowPositionCurrent))               // 当前带单跟单持仓
		//	loginR.BindHandler("/follow/my/dealer/list", noPrintOut(reverse_core.MyDealerList))                    // 我的交易员列表
		//	loginR.BindHandler("/follow/my/profit", reverse_core.MyFollowProfitLoss)                               // 我的跟单账户收益情况
		//	loginR.BindHandler("/follow/current", noPrintOut(reverse_core.FollowOrderCurrent))                     // 当前带单跟单列表
		//	loginR.BindHandler("/follow/history", noPrintOut(reverse_core.FollowOrderHistory))                     // 历史带单跟单列表
		//	loginR.BindHandler("/follow/head", reverse_core.FollowHeadData)                                        // 带单跟单头部数据
		//	loginR.BindHandler("/follow/dealer/list", noPrintOut(reverse_core.GetLeaderList))                      // 交易员列表
		//	loginR.BindHandler("/follow/dealer/detail", reverse_core.GetLeaderDetail)                              // 交易员详情
		//	loginR.BindHandler("/follow/dealer/order/history", noPrintOut(reverse_core.DealerOrderHistory))        // 交易员历史带单
		//	loginR.BindHandler("/follow/dealer/order/current", noPrintOut(reverse_core.DealerOrderCurrent))        // 交易员当前带单
		//	loginR.BindHandler("/follow/dealer/followers", noPrintOut(reverse_core.DealerFollowers))               // 交易员当前跟随者列表
		//	loginR.BindHandler("/follow/setting/current", reverse_core.GetFollowSetting)                           // 获取当前跟单设置
		//	loginR.BindHandler("/follow/setting", reverse_core.SetFollow)                                          // 跟单设置
		//	loginR.BindHandler("/follow/exit", reverse_core.ExitFollow)                                            // 退出跟单
		//	loginR.BindHandler("/follow/remove", reverse_core.RemoveFollow)                                        // 移除跟随
		//	loginR.BindHandler("/follow/my/followers", noPrintOut(reverse_core.MyFollowers))                       // 我的跟随者
		//	loginR.BindHandler("/follow/lead/profit", noPrintOut(reverse_core.GetDealerLeadProfit))                // 获取带单分润统计(日数据)
		//	loginR.BindHandler("/follow/lead/profit/detail", noPrintOut(reverse_core.GetDealerLeadProfitDetail))   // 获取带单分润详情
		//	loginR.BindHandler("/follow/message", noPrintOut(reverse_core.GetFollowMessage))                       // 获取带单跟单消息
		//	loginR.BindHandler("/follow/mark/message/readed", reverse_core.MarkReadMessage)                        // 标记消息已读
		//	loginR.BindHandler("/follow/order/place", reverse_core.LeaderPlace)                                    // 带单开仓
		//	loginR.BindHandler("/follow/position/closeout", reverse_core.FollowCloseOut)                           // 带单跟单平仓
		//	loginR.BindHandler("/follow/position/set/stop", reverse_core.FollowSetStop)                            // 带单跟单设置止盈止损
		//	loginR.BindHandler("/follow/dealer/setting/current", reverse_core.FollowDealerCurrentSetting)          // 带单员当前带单设置
		//	loginR.BindHandler("/follow/dealer/setting", reverse_core.FollowDealerSetting)                         // 带单员带单设置
		//}

		//测试接口
		if !conf.IsProduct() {
			loginR.BindHandler("/position/closeout/test", reverse_core.CloseOutCustomTest) // 平仓
			loginR.BindHandler("/funding/rate/set", reverse_core.FundingRateSet)
			loginR.BindHandler("/trigger/test", reverse_core.SpyIndexPrice)
			noSignR.BindHandler("/trigger/test/dev", reverse_core.SpyIndexPrice)
			noSignR.BindHandler("/follow/brokerage/test/dev", reverse_core.TestFollowBrokerage)      //测试跟单返佣
			loginR.BindHandler("/position/closeout/orderType", reverse_core.CloseOutCustomOrderType) // 平仓
			loginR.BindHandler("/test/market/warn", reverse_core.TestMarketWarn)
			noSignR.BindHandler("/test/market/warn/index", reverse_core.TestIndexPriceWarn)
			noSignR.BindHandler("/test/market/warn/wsclose", reverse_core.TestCloseWsWarn)
		}
	}
}

func spotRouter(eng *gin.Engine) {
	v2 := eng.Group("/v2/spot")
	{
		// 不需要登录的验签路由
		unLoginR := NewRouter(v2, UnLoginRequest, ValidateSign())
		// 需要登录的验签路由
		loginR := NewRouter(v2, LoginRequest, ValidateSign())

		// 公共接口
		{
			unLoginR.BindHandler("/common/summary", spot.GetSummary) // 获取摘要信息
		}

		// 交易对信息
		{
			unLoginR.BindHandler("/symbol/list", spot.SymbolList)                                // 交易对列表
			unLoginR.BindHandler("/symbol/detail", spot.SymbolDetail)                            // 交易对详情
			unLoginR.BindHandler("/symbol/kline", noPrintOut(spot.SymbolKline))                  // 交易对k线
			unLoginR.BindHandler("/symbol/markets", noPrintOut(ipLimitRate(spot.SymbolMarkets))) // 全部交易对行情
		}

		// 交易相关
		{
			loginR.BindHandler("/order/place", spot.Place)                          // 开仓
			loginR.BindHandler("/order/cancel", spot.EntrustCancel)                 // 撤销委托
			loginR.BindHandler("/order/batch/cancel", spot.EntrustOrderBatchCancel) // 批量撤销
			loginR.BindHandler("/order/plan/place", spot.PlanPlace)                 // 计划单下单
			loginR.BindHandler("/order/plan/cancel", spot.PlanCancel)               // 计划单撤销
		}

		// 订单相关
		{
			loginR.BindHandler("/order/entrust/current", noPrintOut(spot.EntrustOrderCurrentList))          // 当前委托
			loginR.BindHandler("/order/entrust/current/plan", noPrintOut(spot.EntrustOrderCurrentPlanList)) // 当前委托(计划单)
			loginR.BindHandler("/order/entrust/history", noPrintOut(spot.EntrustOrderHistoryList))          // 历史委托
			loginR.BindHandler("/order/entrust/history/plan", noPrintOut(spot.EntrustOrderHistoryPlanList)) // 历史委托(计划单)
			loginR.BindHandler("/order/entrust/trade/detail", noPrintOut(spot.EntrustOrderTradeDetail))     // 委托成交明细
		}

		// 资产相关
		{
			loginR.BindHandler("/asset/trade/bill/transfer", noPrintOut(wallet.SpotTradeWalletTransferBill)) // 交易账户划转记录
			loginR.BindHandler("/asset/trade/bill/profit", noPrintOut(wallet.SpotTradeWalletProfitBill))     // 交易账户盈亏记录
			loginR.BindHandler("/asset/trade/bill", noPrintOut(wallet.SpotTradeWalletBill))                  // 交易账户流水记录
		}
	}
}

func (s *Server) SetCallbackRouter() {
	// 路由前缀
	v2 := s.engine.Group("/v2/callback")
	{
		// 大钱包回调走原生请求的接口
		formR := NewRouter(v2, FormWarp, CheckIP())
		{
			formR.BindHandler("/trycharge", wallet.TryChargeCallback) // 预充币回调
			formR.BindHandler("/recharge", wallet.RechargeCallback)   // 充币完成回调
			formR.BindHandler("/withdraw", wallet.WithdrawCallback)   // 提币完成回调
		}

		// 其它
		bodyR := NewRouter(v2, BodyWarp)
		{
			bodyR.BindHandler("/mercuryo/back", wallet.MercuryoCallBack) // mercuryo回调
		}
	}
}
