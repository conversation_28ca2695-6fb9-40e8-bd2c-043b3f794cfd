package server

import (
	"context"
	"net"
	"time"

	"bc/libs/conf"
	"bc/libs/log"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type Server struct {
	valid    bool
	engine   *gin.Engine
	listener net.Listener
	limiter  map[string]*IPRateLimiter
}

func (s *Server) GetRouteLimitRate(url string) *IPRateLimiter {
	return s.limiter[url]
}

func (s *Server) Run(addr string) {
	_ = s.engine.Run(addr)
}

func (s *Server) Close() {
	if s.valid {
		_ = s.listener.Close()
	}
}

var srv Server

func InitHTTP(ctx context.Context) {
	srv.valid = true
	srv.engine = gin.New()
	_ = srv.engine.SetTrustedProxies(conf.GinTrustedProxies())

	// 注册全局处理中间件
	srv.engine.Use(
		Options(),
		log.GinLogger(ctx),
		log.GinRecovery(true),
		TimeoutWarp(ctx, time.Duration(conf.ReadTimeout())*time.Second))

	srv.limiter = NewIPRateLimiter(ctx, conf.ApiRate())

	// 注册路由
	srv.SetRouter()

	log.Infof("start http listen, addr[%s]", conf.ListenAddr())
	go srv.Run(conf.ListenAddr())
	return
}

var callbackServer Server

func InitCallbackServer(ctx context.Context) {
	if conf.IsSimulate() {
		return
	}

	callbackServer.valid = true
	callbackServer.engine = gin.New()
	_ = callbackServer.engine.SetTrustedProxies(conf.GinTrustedProxies())

	// 注册全局处理中间件
	callbackServer.engine.Use(
		Options(),
		log.GinLogger(ctx),
		log.GinRecovery(true),
		TimeoutWarp(ctx, time.Duration(conf.ReadTimeout())*time.Second))

	// 注册路由
	callbackServer.SetCallbackRouter()

	log.Infof("start callback http listen", zap.String("addr", conf.ListenAddrForCWS()))
	go callbackServer.Run(conf.ListenAddrForCWS())
	return
}

func ShutdownServer() {
	srv.Close()
	callbackServer.Close()
}
