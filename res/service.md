### 服务分布

#### 节点1[外网:13.208.65.6 内网:172.31.41.98]:
- gateway
  端口:[pprof: 10001, http: 10002, cws: 10003]
- core
  端口:[pprof: 10011, rpc: 10012, rpcx: 10013]
- push
  端口:[pprof: 10021, http: 10022]
- kline
  端口:[pprof: 10031, rpc: 10032]
- file
  端口:[pprof: 10041, http: 10042]
- third
  端口:[pprof: 10051, rpcx: 10052]
- wallet
  端口:[pprof: 10061, rpcx: 10062]
- follow
  端口:[pprof: 10071, rpc: 10072]
- hedge
  端口:[pprof: 10081]
- task
  端口:[pprof: 10091, http: 10092, rpc: 10093]
- user
  端口:[pprof: 10101, rpcx: 10102]

#### 节点2[外网:13.208.73.250 内网:172.31.35.227]:
- limit
  端口:[pprof: 10111]
- bucketing
  端口:[pprof: 10121, rpcx: 10122]
- price
  端口:[pprof: 10131, http: 10132]
- market
  端口:[pprof: 10141, rpc: 10142]
- kline(服务2)
  端口:[pprof: 10031, rpc: 10032]

#### 节点3[外网:15.152.88.91 内网:172.31.36.103]:
- force
  端口:[pprof: 10151]
- forcefollow
  端口:[pprof: 10161]
- forcedealing
  端口:[pprof: 10171]
- bucketing(服务2)
  端口:[pprof: 10121, rpcx: 10122]

#### 节点4[外网:13.208.126.249 内网:172.31.36.202]:
- trigger
  端口:[pprof: 10181]
- planorder
  端口:[pprof: 10191]
- closing
  端口:[pprof: 10201, rpcx: 10202]