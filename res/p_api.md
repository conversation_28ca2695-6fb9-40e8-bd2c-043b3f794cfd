合约相关接口文档

## 更新记录
### 2022-04-20
```text
    24 设置止盈止损
        "entrust_limit":"0" // 止盈委托价 最优五档传0,限价传对应委托价格
        "entrust_stop":"123.123" // 止损委托价 最优五档传0,限价传对应委托价格
    108 获取止盈止损列表 164 合约止盈止损记录
        "entrust_limit":"0" // 止盈委托价 0为最优五档 大于0为限价
        "entrust_stop":"123.123" // 止损委托价 0为最优五档 大于0为限价
```
### 2022-04-18
```text
    9.[注册](#9-注册)
        "quickly_register": 1 // 是否快速注册 0-否 1-是(不强制设置密码)
```
### 2022-03-24
```text
新增字段
   21.[开仓*](#21-开仓)
        "position_id": "2412515125123213" // 持仓id,加仓用
```
### 2022-03-23
```text
新增字段
    9.[注册](#9-注册)
    10.[登录](#10-登录)
    13.[用户信息](#13-用户信息)
        "is_split_position": false, // 是否开启分仓模式 false-不开启 true-开启
    103.[用户开关控制](#103-用户开关控制)
        | is_split_position | 是否开启分仓模式 | 0-关闭分仓模式 1-开启分仓模式 |
```
### 2022-03-09
```text
新增字段
  159 新资产查询
    | >gift_lock_amount | decimal | 赠金委托冻结 |
    | >margin_gift      | decimal | 赠金保证金 |
```
### 2022-03-02
```text
新增字段
  37-交易账户划转记录 42-交易账户盈亏记录
    "gift_available": "123.123" // 赠金可用
```
### 2022-03-01
```text
新增接口
  171 获取赠金活动列表
  172 赠金活动领取
  173 我的赠金头部数据
  174 赠金记录
新增字段
  159 新资产查询
    | >total_gift_receive | decimal | 累计入账赠金  |
    | >gift_available     | decimal | 赠金可用      |
    | >gift_balance       | decimal | 赠金         |
    | >gift_used          | decimal | 累计使用赠金  |
```

### 2022-02-15

```text
新增接口
    170-调整跟单逐仓保证金
新增字段
    76-获取带单跟单持仓列表
        "available": "123.123", // 可用
        "init_margin": "0", // 起始保证金
        "adjust_margin": "123.123", // 调整保证金,
        "commission": "0", // 手续费
    80-当前带单跟单列表
        "available": "123.123", // 可用
        "init_margin": "0", // 起始保证金
        "adjust_margin": "123.123", // 调整保证金,
        "commission": "0", // 手续费
```

### 2022-01-07

```text
新增接口
    166-获取三方买币配置
    167-获取三方买币商户列表
    168-三方买币获取下单地址
    169-获取三方买币订单记录
```

### 2022-01-05

新增参数
    30-获取充币地址
    32-提币申请
        |tag|否|string|地址标签|
    143-获取默认提币地址
    144-获取提币地址列表
    145-增加提币地址
    147-更新提币地址
        |coin_tag|否|string|币种标签|
新增接口
    165-服务域名速度测试结果提交

```
### 2022-01-04
```text
新增字段
    159-新资产查询
        |total_legal_v2|decimal|总折合法币价值(仅在mode传0时有效,对应客户端语言)|
```

### 2021-12-23

```text
新增接口
    164 合约止盈止损记录
```

### 2021-12-09

```text
重写接口
    43 用户当前合约杠杆倍数
    44 切换用户合约默认杠杆
```

### 2021-12-06

```text
新增接口
    157 预兑换
    158 实际兑换
    159 新资产查询
    160 兑币记录
    161 资金账户记录
    162 币种法币汇率获取
    163 平台摘要获取
新增字段
    16 合约列表
        "hold_deposit_ratio": "0.05", // 维持保证金率 0.05%
        "full_lever": [
            1, 2, 3, 5, 10, 20, 50
        ], // 全仓杠杆倍率
        "leverages": [
            1, 2, 3, 5, 10, 20, 50
        ], // 逐仓杠杆列表
        "follow_lever": [
            1, 2, 3, 5, 10, 20, 50
        ] // 跟单杠杆列表
    38 币种列表
        "is_exchange": false, // 是否支持兑换
          "min_exchange": "1", // 最小交易量
          "max_exchange": "10000", // 最大交易量
          "max_exchange_volume": "0", // 每日最大交易量
          "min_exchange_money": "0", // 最小交易额
          "daily_exchange_money": "0", // 每日最大交易额
          "exchange_coin_point": "0", // 币本位加点
          "exchange_usdt_point": "0", // 兑换美元加点
          "exchange_fee_rate": "0", // 兑换费率
          "exchange_price_range": "0.0005", // 兑换价格范围
          "is_hedge": false, // 是否支持对冲
          "hedge_warn_threshold": "0", // 对冲账户余额不足报警阈值
    39-当前持仓 40-成交记录 41-计划单 76-获取带单跟单持仓列表 77-新成交记录(包含带单跟单以及普通开仓) 81-历史带单跟单
    150-获取当前普通委托 151-获取当前计划委托 152-获取历史普通委托 153-获取历史计划委托 155-历史平仓分享数据
        "par_value": "100", // 合约面值
    135-服务器配置信息
        |blowing_rate|decimal|爆仓率|
    144 获取提币地址列表
        |coin_name|是|string|币种名称|
    32 提币申请 && 143 获取默认提币地址
        |coin_name|是|string|币种名称(大写)|
```

### 2021-11-02

```text
新增请求字段
    16合约列表、17合约明细
     "redundancy_factor": "100",//市价冗余系数
    "lock_float_factor": "0.005" //上浮系数
```

### 2021-11-01

```text
新增请求字段
    152 获取历史普通委托
        |offset|否|string|开平仓方向 O-开仓 C-平仓 不传返回全部|
        |filter|否|integer|筛选方式 0-不筛选 1-只返回已成交数据|
    153 获取历史计划委托
        |filter|否|integer|筛选方式 0-不筛选 1-计划单 2-止盈止损|
```

### 2021-10-27

```text
新增字段
    39 当前持仓
        "volume_lock": 2, // 持仓冻结张数
两步验证新增逻辑
    返回增加:
        "spare_email": "<EMAIL>" // 需要验证的备用邮箱,如果不为空表示需要进行验证码校验
    验证参数增加:
        "spare_email_code": "241521" // 备用邮箱验证码
```

### 2021-10-19

```text
新增字段
    13 用户信息
        "spare_email": "", // 备用邮箱
新增接口
    156 绑定备用邮箱
```

### 2021-10-18

```text
新增字段
    31 获取资产详情
        "balance": "123.123", // 资产
        "lock_amount": "213.2312" // 委托冻结
```

### 2021-10-15

```text
新增接口
    154 撤销委托
    155 历史平仓分享数据
新增字段
    23 平仓
        "entrust_type": 1, // 委托类型 0-市价 1-限价
        "entrust_price": "123.123" // 委托价格;限价单传递价格
```

### 2021-10-14

```text
新增字段
    151 获取当前计划委托
        |condition|integer|触发条件 1 >= 2 <=|
新增接口
    152 获取历史普通委托
    153 获取历史计划委托
```

### 2021-10-13

```text
新增字段
    21 开仓
        "entrust_type": 1, // 委托类型 0-市价 1-限价
        "entrust_price": "123,123" // 委托价格;限价单传递价格
    97 带单开仓
        "limit": "100000.123", // 止盈价
        "stop": "1000.123", // 止损价
        "trigger_type": 0, // 止盈止损触发标的 0-成交价,1-标记价格
        "entrust_type": 0, // 委托类型 0-市价 1-限价
        "price": "123.123" // 委托价格;限价单传递价格
新增接口
    150 获取当前普通委托
    151 获取当前计划委托
```

### 2021-09-26

```text
新增字段
    135 服务器配置信息
        |china_mobile_checking|boolean|是否开启国内手机号限制 true-限制+86手机号注册绑定|
```

### 2021-09-22

```text
新增接口
    149 新版获取重要通知
```

### 2021-09-13

```text
更新接口
    9 注册
        新增请求参数:
            "channel_id": "21f08867-4944-4abf-88f1-1f4b9f2a927d", // 渠道id(注册奖励活动)
        新增返回参数:
            "reward": "0.6", // 注册获得奖励(USDT)
```

### 2021-08-18

```text
新增接口
    143.[获取默认提币地址](#143-获取默认提币地址)
    144.[获取提币地址列表](#144-获取提币地址列表)
    145.[增加提币地址](#145-增加提币地址)
    146.[删除提币地址](#146-删除提币地址)
    147.[更新提币地址](#147-更新提币地址)
    148.[验证两步验证](#148-验证两步验证)
更新接口
    32.[提币申请](#32-提币申请)
    123.[资金密码校验](#123-资金密码校验)
```

### 2021-08-11

```text
新增接口字段
    118 用户收款账户列表 && 119 用户收款账户添加 && 120 用户收款账户修改
      "invalid": false //是否置为无效状态 true为无效账户
```

### 2021-08-02

```text
新增接口
    142 重要通知
新增返回字段
    6 公告列表
        "is_important": true, // 是否是重要通知
```

### 2021-07-28

```text
新增接口
    141 校验登录密码
新增返回字段
    135 服务器配置信息
        |is_third_kyc|boolean|是否启用三方kyc|
        |gesture_interval|integer|手势密码验证间隔(分钟)|
```

### 2021-07-27

```text
新增返回字段
    13 用户信息
        "is_show_dealer_setting": true // 是否显示交易员设置
    88 获取当前跟单设置
        "limit_follow_contracts": ["BTCUSDT", "ETHUSDT"], // 当前交易员跟随限制合约
        "limit_follow_mode": 3, // 当前交易员限制跟单方式 1-固定张数 2-固定比例 多种相加
        "limit_follow_threshold": "123.123" // 当前交易员限制跟单最低本金 0为不限制
```

is_show_dealer_setting

### 2021-07-26

```text
新增请求字段
    31 获取资产详情
         "mode": 1 // 账户类型 0-全部账户 1-资金账户 2-交易账户 3-跟单账户
新增返回字段
    83 交易员列表
        "limit_follow_contracts": ["BTCUSDT", "ETHUSDT"], // 跟随限制合约
        "limit_follow_threshold": "0", // 跟单最低本金 0为不限制
    84 交易员详情
        "limit_follow_contracts": ["BTCUSDT", "ETHUSDT"], // 跟随限制合约
        "limit_follow_mode": 3, // 跟单方式 1-固定张数 2-固定比例 多种相加
        "limit_follow_threshold": "0", // 跟单最低本金 0为不限制
新增接口
    139 当前带单设置
    140 带单设置
```

### 2021-07-19

```text
新增字段
    135 服务器配置信息
        |server_hosts|array|服务器域名列表(新)|
        |-recommend|boolean|是否是推荐域名|
        |-host_address|string|域名地址|
        |is_slider|boolean|是否启用滑块|
```

### 2021-07-13

```text
新增字段：
    40 成交记录 && 77 新成交记录
        "order_client": 1, // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
    135 服务器配置信息
        |trader_lately_position|integer|交易员持仓数据显示|
        |trader_lately_history|integer|交易员历史数据条数|
        |show_follow_users|integer|跟随着盈利从大到小人数|
```

### 2021-07-05

```text
新增字段：
    16 17 合约数据增加返回
    "is_maintenance": true,//是否维护中，true-维护中
```

### 2021-06-30

```text
新增字段：
    16 17 合约数据增加返回
    "ioc_limit":"10",//ioc委托限制
    "ioc_buy_limit":"2",//ioc买方价格限制
    "ioc_sell_limit"："2",//ioc卖方价格限制
```

### 2021-06-29

```text
新增字段：
    22 条件单下单（ioc下单，选限价，策略传2）
    44 条件单记录(返回数据）
    "entrust_type": 0,//委托类型 0-市价 1-限价
    "entrust_price": "10020.02",//委托执行价格
    "entrust_strategy": 2,//委托策略 0-默认 1-fok 2-ioc

    16 17 数据增加返回
    "ioc_limit":"10",//ioc委托限制
    "ioc_buy_limit":"2",//ioc买方价格限制
    "ioc_sell_limit"："2",//ioc卖方价格限制
```

### 2021-06-25

```text
新增接口:
    138 APP下载统计
```

### 2021-06-10

```text
新增接口:
    137 客户端错误提交
```

### 2021-06-07

```text
新增字段:
    135 服务器配置信息
        "public_url": "pandafe.com", // 公共域名
        "private_url": "pandafe.cloud", // 私有域名
```

### 2021-05-14

```text
新增字段:
    13 用户信息
        "is_show_dealer": true // 是否显示交易员申请入口
    16 合约列表
        "is_follow": true, // 是否支持带单
    17 合约明细
        "max_trader_volume": 10, // 交易员最大下单张数, 0不限制
        "is_follow": true // 是否支持带单
    24 设置止盈止损
        "amount": 12, // 张数
        "plan_close_order_id": 123124124124123223, // 需要更新的止盈止损id
    31 获取资产详情
        "blowing_rate": "12.123", // 平仓风险率
        "risk_rate": "12.12" // 账户分享率
    40 成交记录
        "entrust_type": 0, // 委托类型，0-市价单 1-限价单 2-强平单
    59 获取客户端最新下载链接
        "version": "1.2.3" // 版本号
    77 新成交记录(包含带单跟单以及普通开仓)
        "entrust_type": 0, // 委托类型，0-市价单 1-限价单 2-强平单
        "close_profit": "-1.31", // 平仓盈亏
    78 获取我的交易员列表        
        "rebate": "10" // 分佣比例
    81 历史带单跟单
        "entrust_type": 0, // 委托类型，0-市价单 1-限价单 2-强平单
    84 交易员详情
        "rebate": "10", // 分佣比例
        "history_follow_number": 0, // 历史跟随人数
新增接口:
    136 获取热更新补丁列表
```

## 接口

- 带*号的为模拟盘有的接口

1.[检查版本更新*](#1-检查版本更新)
2.[获取验证码](#2-获取验证码)
3.[获取验证码(易盾)](#3-获取验证码-易盾)
4.[校验验证码](#4-校验验证码)
5.[轮播图列表](#5-轮播图列表)
6.[公告列表](#6-公告列表)
7.[公告正文](#7-公告正文)
8.[检查账号是否可以注册](#8-检查账号是否可以注册)
9.[注册](#9-注册)
10.[登录](#10-登录)
11.[登出](#11-登出)
12.[忘记密码](#12-忘记密码)
13.[用户信息](#13-用户信息)
14.[改绑账号](#14-改绑账号)
15.[重置密码](#15-重置密码)
16.[合约列表*](#16-合约列表)
17.[合约明细*](#17-合约明细)
18.[合约推荐](#18-合约推荐)
19.[合约k线*](#19-合约k线)
20.[条件单撤销*](#20-条件单撤销)
21.[开仓*](#21-开仓)
22.[条件单下单*](#22-条件单下单)
23.[平仓*](#23-平仓)
24.[设置止盈止损*](#24-设置止盈止损)
25.[用户邀请列表](#25-用户邀请列表)
26.[涨幅榜](#26-涨幅榜)
27.[看涨人数榜](#27-看涨人数榜)
28.[看跌人数榜](#28-看跌人数榜)
29.[合约行情](#29-合约行情)
30.[获取充币地址](#30-获取充币地址)
31.[获取资产详情*](#31-获取资产详情)
32.[提币申请](#32-提币申请)
33.[资金划转](#33-资金划转)
34.[资产账户提币记录](#34-资产账户提币记录)
35.[资产账户充币记录](#35-资产账户充币记录)
36.[资产账户划转记录](#36-资产账户划转记录)
37.[交易账户划转记录](#37-交易账户划转记录)
38.[币种列表](#38-币种列表)
39.[当前持仓*](#39-当前持仓)
40.[成交记录*](#40-成交记录)
41.[计划单*](#41-计划单)
42.[交易账户盈亏记录*](#42-交易账户盈亏记录)
43.[用户当前合约杠杆倍数*](#43-用户当前合约杠杆倍数)
44.[切换用户合约默认杠杆*](#44-切换用户合约默认杠杆)
45.[资产账户佣金记录](#45-资产账户佣金记录)
46.[模拟盘账户初始化*](#46-模拟盘账户初始化)
47.[分享资源](#47-分享资源)
48.[获取用户认证状态](#48-获取用户认证状态)
49.[提交身份信息审核](#49-提交身份信息审核)
50.[提交面部认证信息](#50-提交面部认证信息)
51.[获取用户认证进度](#51-获取用户认证进度)
52.[法币下单](#52-法币下单)
53.[法币订单记录](#53-法币订单记录)
54.[资产账户法币记录](#54-资产账户法币记录)
55.[获取法币系统登录token](#55-获取法币系统登录token)
56.[获取法币系统限制](#56-获取法币系统限制)
57.[邀请信息统计](#57-邀请信息统计)
58.[指定合约行情](#58-指定合约行情)
59.[获取客户端最新下载链接*](#59-获取客户端最新下载链接)
60.[合约指标](#60-合约指标)
61.[合约阻力支撑位](#61-合约阻力支撑位)
62.[模拟盘资产补领*](#62-模拟盘资产补领)
63.[h5提交面部认证信息](#63-h5提交面部认证信息)
64.[调整保证金*](#64-调整保证金)
65.[获取国家区域列表](#65-获取国家区域列表)
66.[资金费率*](#66-资金费率)
67.[图片上传](#67-图片上传)
68.[非大陆地区认证](#68-非大陆地区认证)
69.[修改用户信息](#69-修改用户信息)
70.[同意跟单协议](#70-同意跟单协议)
71.[交易员申请](#71-交易员申请)
72.[跟单账户盈亏记录](#72-跟单账户盈亏记录)
73.[跟单账户划转记录](#73-跟单账户划转记录)
74.[获取当前带单杠杆](#74-获取当前带单杠杆)
75.[切换新带单杠杆](#75-切换新带单杠杆)
76.[获取带单跟单持仓列表](#76-获取带单跟单持仓列表)
77.[新成交记录](#77-新成交记录(包含带单跟单以及普通开仓))
78.[获取我的交易员列表](#78-获取我的交易员列表)
79.[获取我的跟单头部数据](#79-获取我的跟单头部数据)
80.[当前带单跟单列表](#80-当前带单跟单列表)
81.[历史带单跟单](#81-历史带单跟单)
82.[获取带单跟单头部数据](#82-获取带单跟单头部数据)
83.[交易员列表](#83-交易员列表)
84.[交易员详情](#84-交易员详情)
85.[交易员历史带单](#85-交易员历史带单)
86.[交易员当前带单](#86-交易员当前带单)
87.[交易员跟随者列表](#87-交易员跟随者列表)
88.[获取当前跟单设置](#88-获取当前跟单设置)
89.[跟单设置](#89-跟单设置)
90.[取消跟单](#90-取消跟单)
91.[移除跟随](#91-移除跟随)
92.[我的跟随者](#92-我的跟随者)
93.[获取带单分润统计](#93-获取带单分润统计(日数据))
94.[获取带单分润详情](#94-获取带单分润详情)
95.[获取带单跟单消息](#95-获取带单跟单消息)
96.[标记消息已读](#96-标记消息已读)
97.[带单开仓](#97-带单开仓)
98.[带单跟单平仓](#98-带单跟单平仓)
99.[带单跟单设置止盈止损](#99-带单跟单设置止盈止损)
100.[同意交易协议](#100-同意交易协议)
101.[提交提币认证信息](#101-提交提币认证信息)
102.[h5提交提币认证信息](#102-h5提交提币认证信息)
103.[用户开关控制](#103-用户开关控制)
104.[全部平仓*](#104-全部平仓)
105.[获取活动信息](#105-获取活动信息)
106.[获取我的中奖记录](#106-获取我的中奖记录)
107.[确定分享图片](#107-确定分享图片)
108.[获取止盈止损列表*](#108-获取止盈止损列表)
109.[删除止盈止损*](#109-删除止盈止损)
110.[联系我们](#110-联系我们)
111.[安全中心绑定或更换手机邮箱账号](#111-安全中心绑定或更换手机邮箱账号)
112.[安全中心修改登录密码](#112-安全中心修改登录密码)
113.[设置或修改资金密码](#113-设置或修改资金密码)
114.[修改资金密码验证时效](#114-修改资金密码验证时效)
115.[获取验证器私钥](#115-获取验证器私钥)
116.[安全中心绑定或更换验证器](#116-安全中心绑定或更换验证器)
117.[修改登录验证开关](#117-修改登录验证开关)
118.[用户收款账户列表](#118-用户收款账户列表)
119.[用户收款账户添加](#119-用户收款账户添加)
120.[用户收款账户修改](#120-用户收款账户修改)
121.[用户收款账户移除](#121-用户收款账户移除)
122.[法币获取下单汇率](#122-法币获取下单汇率)
123.[资金密码校验](#123-资金密码校验)
124.[获取用户配额](#124-获取用户配额)
125.[提交人工信息认证照片](#125-提交人工信息认证照片)
126.[模拟账户资产减少*](#126-模拟账户资产减少)
127.[关于社群](#127-关于社群)
128.[测试接口服务器*](#128-测试接口服务器)
129.[用户选项控制](#129-用户选项控制)
130.[用户API列表获取](#130-用户API列表获取)
131.[用户API详情获取](#131-用户API详情获取)
132.[用户API创建](#132-用户API创建)
133.[用户API更新](#133-用户API更新)
134.[用户API删除](#134-用户API删除)
135.[服务器配置信息](#135-服务器配置信息)
136.[获取热更新补丁列表](#136-获取热更新补丁列表)
137.[客户端错误提交](#137-客户端错误提交)
138.[APP下载统计](#138-APP下载统计)
139.[当前带单设置](#139-当前带单设置)
140.[带单设置](#140-带单设置)
141.[校验登录密码](#141-校验登录密码)
142.[重要通知](#142-重要通知)
143.[获取默认提币地址](#143-获取默认提币地址)
144.[获取提币地址列表](#144-获取提币地址列表)
145.[增加提币地址](#145-增加提币地址)
146.[删除提币地址](#146-删除提币地址)
147.[更新提币地址](#147-更新提币地址)
148.[验证两步验证](#148-验证两步验证)
149.[新版获取重要通知](#149-新版获取重要通知)
150.[获取当前普通委托](#150-获取当前普通委托)
151.[获取当前计划委托](#151-获取当前计划委托)
152.[获取历史普通委托](#152-获取历史普通委托)
153.[获取历史计划委托](#153-获取历史计划委托)
154.[撤销委托](#154-撤销委托)
155.[历史平仓分享数据](#155-历史平仓分享数据)
156.[绑定备用邮箱](#156-绑定备用邮箱)
157.[预兑换](#157-预兑换)
158.[实际兑换](#158-实际兑换)
159.[新资产查询](#159-新资产查询)
160.[兑币记录](#160-兑币记录)
161.[资金账户记录](#161-资金账户记录)
162.[币种法币汇率获取](#162-币种法币汇率获取)
163.[平台摘要获取](#163-平台摘要获取)
164.[合约止盈止损记录](#164-合约止盈止损记录)
165.[服务域名速度测试结果提交](#165-服务域名速度测试结果提交)
166.[获取三方买币配置](#166-获取三方买币配置)
167.[获取三方买币商户列表](#167-获取三方买币商户列表)
168.[三方买币获取下单地址](#168-三方买币获取下单地址)
169.[获取三方买币订单记录](#169-获取三方买币订单记录)
170.[调整跟单逐仓保证金](#170-调整跟单逐仓保证金)
171.[获取赠金活动列表](#171-获取赠金活动列表)
172.[赠金活动领取](#172-赠金活动领取)
173.[我的赠金头部数据](#173-我的赠金头部数据)
174.[赠金记录](#174-赠金记录)

### 1 检查版本更新

- URL: http://{host_addr}/v1/common/version
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "platform_id": 1, // 平台id
  "channel_id": 0, // 渠道id 安卓{0-极速下载 1-本地下载} IOS{0-testflight 1-超级签 2-企业签}
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "needUpdate": false, // 是否有更新
    "version": "********", // 当前最新版本号 
    "content": "1-更新...\n2-更新...", // 更新文案(适配中英文)
    "force": false, // 是否需要强升
    "link": "http://xxx.xx.com/download", // 下载页面
    "url": "http://xxx.xx.com/download/test.apk", // 下载文件链接
    "extra_url": "https://www.officeqb.com/res/android/pandafe.apk", // 额外下载文件链接
    "created_time": ********** // 最后一次发布更新时间戳
  }
}
```

### 2 获取验证码

- URL: http://{host_addr}/v1/common/authcode
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********", // 账号
    "mode": 2, // 验证码类型 0-常规请求 1-忘记密码 2-用户注册 3-修改手机号 4-新绑定手机号 5-修改邮箱 6-新绑定邮箱 7-重置登录密码 8-重置资金密码 9-安全验证 10-绑定备用邮箱
    "areacode": "86" // 在未注册的手机号获取验证码时传,用户注册/新绑定手机号
},
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 3 获取验证码 易盾

- URL: http://{host_addr}/v1/common/authcode/safe
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********", // 账号
    "mode": 2, // 验证码类型 0-常规请求 1-忘记密码 2-用户注册 3-修改手机号 4-新绑定手机号 5-修改邮箱 6-新绑定邮箱 7-重置登录密码 8-重置资金密码 9-安全验证 10-绑定备用邮箱
    "captcha_id": "dfieirjiesdfad", // 易盾captchaID
    "validate": "dsfdsafjkjkcvijsdi...", // 易盾返回的二次校验数据
    "areacode": "86" // 在未注册的手机号获取验证码时传,用户注册/新绑定手机号
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 4 校验验证码

- URL: http://{host_addr}/v1/common/authcode/verify
- 只用于验证码校验和数据提交不在一个页面上完成的接口
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********", // 账号
    "code": "152842", // 验证码
    "mode": 1 // 验证码类型 0-常规请求 1-忘记密码 2-用户注册 3-修改手机号 4-新绑定手机号 5-修改邮箱 6-新绑定邮箱 7-重置登录密码 8-重置资金密码 9-安全验证 10-绑定备用邮箱
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 5 轮播图列表

- URL: http://{host_addr}/v1/common/banner/list
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
      {
          "image": "http://www.baidu.com/cn.png", // 图片
          "link": "http://www.baidu.com/cn" // 跳转路径
      },
      {
          "image": "http://www.baidu.com/cn2.png", // 图片
          "link": "http://www.baidu.com/cn2" // 跳转路径
      }
  ]
}
```

### 6 公告列表

- URL: http://{host_addr}/v1/common/notice/list
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
        "id": 10000030, // 公告id
        "is_top": true, // 是否是置顶状态
        "is_important": true, // 是否是重要通知
        "created_at": 1566957412, // 创建时间
        "title": "乔碧萝殿下赛高", // 标题
        "link": "http://**************:8101/notice/?id=10000030&language=0" // 跳转链接
    },
    {
        "id": ********,
        "is_top": false,
        "is_important": false, // 是否是重要通知
        "created_at": **********,
        "title": "淡黄的长裙,蓬松的头发",
        "link": "http://**************:8101/notice/?id=********&language=0"
    }
  ]
}
```

### 7 公告正文

- URL: http://{host_addr}/v1/common/notice/detail
- Method: POST
- Request Body:

```json
{
  "data": {
    "id": ******** // 公告id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": ********, // 公告id
    "createdAt": **********, // 公告创建时间
    "title": "淡黄的长裙,蓬松的头发", // 公告标题
    "content": "淡黄的长裙,蓬松的头发" // 公告正文
  }
}
```

### 8 检查账号是否可以注册

- URL: http://{host_addr}/v1/account/check
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********" // 账号
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "status": false // 是否可注册 true-可以注册 false-已存在
  }
}
```

### 9 注册

- URL: http://{host_addr}/v1/account/register
- Method: POST
- Request Body:

```json
{
  "data": {
      "channel_id": "21f08867-4944-4abf-88f1-1f4b9f2a927d", // 渠道id(注册奖励活动)
      "account": "***********", // 账号
      "invite_code": "Xis2n3", // 邀请码
      "code": "823751", // 验证码
      "areacode": "86", // 区号
      "country_code": "CN", // 国家代码
      "password": "sdfiefnvdkfjawiwejfijdfij==", // 密码(加密后)
      "quickly_register": 1 // 是否快速注册 0-否 1-是(不强制设置密码)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "reward": "0.6", // 注册获得奖励(USDT)
    "token": "token...", // token
    "User_id": 7129472, // 用户id
    "user_name": "***********", // 用户账号
    "invite_code": "C2d9fN", // 邀请码
    "enable_login": true, // 是否可登录
    "enable_withdraw": true, // 是否可提币
    "enable_trade": true, // 是否可交易
    "phone": "***********", // 手机号
    "email": "", // 邮箱
    "verify": 0, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
    "has_password": false, // 是否已设置登录密码
    "has_fund_password": false, // 是否已设置资金密码
    "has_totp_bind": false, // 是否已绑定验证器
    "login_verify_phone": false, // 是否已开启登录两步验证 手机号
    "login_verify_email": false, // 是否已开启登录两步验证 邮箱
    "login_verify_totp": false, // 是否已开启登录两步验证 验证器
    "trade_verify_fund": 0, // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
    "area_code": "86", // 国家区号
    "country_code": "CN", // 国家代码
    "nickname": "小伙计", // 昵称
    "nickname_en": "little boy", // 英文昵称
    "introduce": "棒棒棒", // 介绍
    "introduce_en": "good", // 英文介绍
    "avatar": "https://api.officeqb.com/res/image/7144588_e0737f24-aa47-4e75-931d-a2a971b25c75_1603957667.jpg", // 头像
    "dealer_state": -2, // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
    "rebate": "0", // 分佣比例
    "platform_id": 1, // 平台id
    "content": "张三", // 备注
    "is_agent": true, // 是否是代理
    "withdraw_verify": 1, // 提币活体验证状态 0-未认证 1-认证审核中 2-认证审核失败 3-认证审核通过
    "trade_approved": true, // 是否已同意交易协议
    "enable_simulator": true, // 是否开启模拟盘功能
    "trade_confirm": true, // 是否开启交易二次确认
    "legal_withdraw_limit": false, // 是否因为24小时内有法币入金操作导致限制提币
    "change_style": 1, // 涨跌样式 0-绿涨红跌 1-红涨绿跌
    "profit_style": 1, // 浮动盈亏计算方式 0-标记价格 1-最新成交价
    "is_split_position": false, // 是否开启分仓模式 false-不开启 true-开启
    "is_open_api": true, // 是否有权限开通api,false时本字段不返回
    "show_agent_ratio": false // 当用户是代理用户时,是否在邀请页显示返佣比例
  }
}
```

### 10 登录

- URL: http://{host_addr}/v1/account/login
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********", // 账号
    "password": "fdfeigid...jaijeijd==", // 加密后的登录密码,为空字符串时使用验证码方式登录
    "code": "823751", // 验证码
    "phone_code": "123456", // 两步验证 手机验证码
    "email_code": "123456", // 两步验证 邮箱验证码
    "totp_code": "123456" // 两步验证 令牌验证码
  },
  "api_version": "V2", // api版本(V2增加两步验证)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "token": "token...", // token
    "User_id": 7129472, // 用户id
    "user_name": "***********", // 用户账号
    "invite_code": "C2d9fN", // 邀请码
    "enable_login": true, // 是否可登录
    "enable_withdraw": true, // 是否可提币
    "enable_trade": true, // 是否可交易
    "phone": "***********", // 手机号
    "email": "", // 邮箱
    "verify": 0, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
    "has_password": false, // 是否已设置登录密码
    "has_fund_password": false, // 是否已设置资金密码
    "has_totp_bind": false, // 是否已绑定验证器
    "login_verify_phone": false, // 是否已开启登录两步验证 手机号
    "login_verify_email": false, // 是否已开启登录两步验证 邮箱
    "login_verify_totp": false, // 是否已开启登录两步验证 验证器
    "trade_verify_fund": 0, // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
    "area_code": "86", // 国家区号
    "country_code": "CN", // 国家代码
    "nickname": "小伙计", // 昵称
    "nickname_en": "little boy", // 英文昵称
    "introduce": "棒棒棒", // 介绍
    "introduce_en": "good", // 英文介绍
    "avatar": "https://api.officeqb.com/res/image/7144588_e0737f24-aa47-4e75-931d-a2a971b25c75_1603957667.jpg", // 头像
    "dealer_state": -2, // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
    "rebate": "0", // 分佣比例
    "platform_id": 1, // 平台id
    "content": "张三", // 备注
    "is_agent": true, // 是否是代理
    "withdraw_verify": 1, // 提币活体验证状态 0-未认证 1-认证审核中 2-认证审核失败 3-认证审核通过
    "trade_approved": true, // 是否已同意交易协议
    "enable_simulator": true, // 是否开启模拟盘功能
    "trade_confirm": true, // 是否开启交易二次确认
    "legal_withdraw_limit": false, // 是否因为24小时内有法币入金操作导致限制提币
    "change_style": 1, // 涨跌样式 0-绿涨红跌 1-红涨绿跌
    "profit_style": 1, // 浮动盈亏计算方式 0-标记价格 1-最新成交价
    "is_split_position": false, // 是否开启分仓模式 false-不开启 true-开启
    "is_open_api": true, // 是否有权限开通api,false时本字段不返回
    "show_agent_ratio": false // 当用户是代理用户时,是否在邀请页显示返佣比例
  }
}
```

### 11 登出

- URL: http://{host_addr}/v1/account/logout
- 尽量调用,用于清理数据,可不理会返回值
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 12 忘记密码

- URL: http://{host_addr}/v1/account/forget
- Method: POST
- Request Body:

```json
{
  "data": {
    "account": "***********", // 账号
    "new_password": "jviie323ik...divcnaCJFDRns==", // 新加密后的密码
    "phone_code": "123456", // 两步验证 手机验证码
    "email_code": "123456", // 两步验证 邮箱验证码
    "totp_code": "123456" // 两步验证 令牌验证码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 13 用户信息

- URL: http://{host_addr}/v1/user/info
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "User_id": 7129472, // 用户id
    "user_name": "***********", // 用户账号
    "invite_code": "C2d9fN", // 邀请码
    "enable_login": true, // 是否可登录
    "enable_withdraw": true, // 是否可提币
    "enable_trade": true, // 是否可交易
    "phone": "***********", // 手机号
    "email": "", // 邮箱
    "spare_email": "", // 备用邮箱
    "verify": 0, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
    "has_password": true, // 是否已设置登录密码
    "has_fund_password": false, // 是否已设置资金密码
    "has_totp_bind": false, // 是否已绑定验证器
    "login_verify_phone": false, // 是否已开启登录两步验证 手机号
    "login_verify_email": false, // 是否已开启登录两步验证 邮箱
    "login_verify_totp": false, // 是否已开启登录两步验证 验证器
    "trade_verify_fund": 0, // 交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证
    "area_code": "86", // 国家区号
    "country_code": "CN", // 国家代码
    "nickname": "小伙计", // 昵称
    "nickname_en": "little boy", // 英文昵称
    "introduce": "棒棒棒", // 介绍
    "introduce_en": "good", // 英文介绍
    "avatar": "https://api.officeqb.com/res/image/7144588_e0737f24-aa47-4e75-931d-a2a971b25c75_1603957667.jpg", // 头像
    "follow_approved": true, // 是否已经接受了跟单协议
    "dealer_state": -2, // 交易员状态 -2:未申请 -1:注销 0:暂停 1:是交易员 2:提交申请待审核
    "rebate": "0", // 分佣比例
    "platform_id": 1, // 平台id
    "content": "张三", // 备注
    "is_agent": true, // 是否是代理
    "withdraw_verify": 1, // 提币活体验证状态 0-未认证 1-认证审核中 2-认证审核失败 3-认证审核通过
    "trade_approved": true, // 是否已同意交易协议
    "enable_simulator": true, // 是否开启模拟盘功能
    "trade_confirm": true, // 是否开启交易二次确认
    "legal_withdraw_limit": false, // 是否因为24小时内有法币入金操作导致限制提币
    "change_style": 1, // 涨跌样式 0-绿涨红跌 1-红涨绿跌
    "profit_style": 1, // 浮动盈亏计算方式 0-标记价格 1-最新成交价
    "is_open_api": true, // 是否有权限开通api,false时本字段不返回
    "is_split_position": false, // 是否开启分仓模式 false-不开启 true-开启
    "show_agent_ratio": false, // 当用户是代理用户时,是否在邀请页显示返佣比例
    "is_show_dealer": true, // 是否显示交易员申请入口
    "is_show_dealer_setting": true // 是否显示交易员设置
  }
}
```

### 14 改绑账号

- URL: http://{host_addr}/v1/user/account/modify
- Method: POST
- Request Body:

```json
{
  "data": {
    "new_account": "***********", // 新账号
    "code": "852337" // 新账号验证码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 15 重置密码

- URL: http://{host_addr}/v1/user/password/modify
- Method: POST
- Request Body:

```json
{
  "data": {
    "new_password": "jviie323ik...divcnaCJFDRns==" // 新加密后的密码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 16 合约列表

- URL: http://{host_addr}/v1/contract/list
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
        "contract_code": "BTCUSDT", // 合约code
        "contract_name": "BTC永续合约", // 合约名称
        "contract_name_en": "BTC Contract", // 合约名称(英文)
      "contract_icon": "https://www.officeqb.com/res/btc.png",
      "contract_type": 0, // 合约类型 0：币本位 1：金本位
      "index_precision": 1, // 指数精度
      "digit": 4, // 价格精度
        "price": "10234.2323", // 成交价格
      "price_cny": "242699.4", // 成交价格(cny)
      "index_price": "0.0", // 指数价格
        "change_ratio": "13.42", // 涨跌幅 +13.42%
      "change": "2809.6", // 涨跌
      "buy_count": 133302, // 看涨人数
      "sell_count": 133302, // 看跌人数
      "delisted": false, // 是否已下架
      "is_follow": true, // 是否支持带单
      "price_coin_name": "BTC", // 计价币种
      "par_value": "0.01", // 面值
      "trade_volume": "12345", // 成交量(张数)
      "ioc_limit":"10",//ioc委托限制
      "ioc_buy_limit":"2",//ioc买方价格限制
      "ioc_sell_limit": "2",//ioc卖方价格限制
      "is_maintenance": true, //是否维护中，true-维护中
      "redundancy_factor": "100",//市价冗余系数
      "lock_float_factor": "0.005", //上浮系数
      "hold_deposit_ratio": "0.05", // 维持保证金率 0.05%
      "full_lever": [
        1, 2, 3, 5, 10, 20, 50
      ], // 全仓杠杆倍率
      "leverages": [
        1, 2, 3, 5, 10, 20, 50
      ], // 逐仓杠杆列表
      "follow_lever": [
        1, 2, 3, 5, 10, 20, 50
      ] // 跟单杠杆列表
    }
  ]
}
```

### 17 合约明细

- URL: http://{host_addr}/v1/contract/detail
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT" // 合约代码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "contract_code": "BTCUSD", // 合约code
    "contract_name": "BTC永续合约", // 合约名称
    "contract_name_en": "BTC Contract", // 合约名称(英文)
    "contract_type": 0, // 合约类型 0：币本位 1：金本位
    "coin_id": 1, // 结算币种id
    "coin_name": "BTC", // 结算币种
    "market_name": "USD", // 计价币种
    "par_value": "100", // 合约面值 1张=100USD
    "min_order_volume": "0.02", // 单笔最小下单量 张
    "max_order_volume": "0.02", // 单笔最大下单量 张
    "taker_fee": "0.075", // taker手续费 0.075%
    "maker_fee": "-0.025", // market手续费 -0.025%
    "hold_deposit_ratio": "0.05", // 维持保证金率 0.05%
    "fund_fee": "-0.1234", // 资金费率 -0.1234%
    "estimate_fund_fee": "0.1234", // 预测资金费率 0.1234%
    "digit": 4, // 合约精度
    "index_precision": 5, // 合约指数价格精度
    "mark_precision": 6, // 标记价格精度
    "step": "0.0005", // 下单价格步长
    "delisted": false, // 是否已经下架 true-已下架 false-上架中
    "full_lever": [
      1, 2, 3, 5, 10, 20, 50
    ], // 全仓杠杆倍率
    "leverages": [
      1, 2, 3, 5, 10, 20, 50
    ], // 逐仓杠杆列表
    "follow_lever": [
      1, 2, 3, 5, 10, 20, 50
    ], // 跟单杠杆列表
    "min_trader_volume": 10, // 交易员最小下单张数, 0不限制
    "max_trader_volume": 10, // 交易员最大下单张数, 0不限制
    "index_price": "10000.00", // 指数价格
    "index_price_cny": "70000.00", // 指数折合人民币价格
    "buy_price": "70000.00", // 指数买入价格
    "sell_price": "70000.00", // 指数卖出价格
    "change_daily": "12.23", // 当日涨跌幅 12.23%
    "change_24h": "8.21", // 24小时涨跌幅 8.21%
    "change_8h": "8.21", // 8小时涨跌幅 8.21%
    "change_4h": "8.21", // 4小时涨跌幅 8.21%
    "change_2h": "8.21", // 2小时涨跌幅 8.21%
    "change_1h": "8.21", // 1小时涨跌幅 8.21%
    "change_30m": "8.21", // 30分钟涨跌幅 8.21%
    "change_10m": "8.21", // 10分钟涨跌幅 8.21%
    "change_volume": "238.21", // 当日涨跌 +238.21
    "high_price": "10020.02", // 当日最高价 
    "low_price": "99829.82", // 当日最低价
    "trade_24h": "12222", //24小时成交量
    "is_follow": true, // 是否支持带单
    "ioc_limit":"10",//ioc委托限制
    "ioc_buy_limit":"2",//ioc买方价格限制
    "ioc_sell_limit": "2",//ioc卖方价格限制
    "is_maintenance": true, //是否维护中，true-维护中
    "redundancy_factor": "100",//市价冗余系数
    "lock_float_factor": "0.005" //上浮系数

  }
}
```

### 18 合约推荐

- URL: http://{host_addr}/v1/contract/recommend
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约名称
      "contract_name_en": "BTCUSDT", // 合约英文名称
      "index": 6833.1, // 合约指数
      "index_str": "6833.1", // 合约指数(字符串)
      "quote_change": "32.43" // 涨跌幅
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "contract_name": "ETH合约", // 合约名称
      "contract_name_en": "ETHUSDT", // 合约英文名称
      "index": 233.13, // 合约指数
      "index_str": "233.1300", // 合约指数(字符串)
      "quote_change": "12.43" // 涨跌幅
    }
  ]
}
```

### 19 合约k线

- URL: http://{host_addr}/v1/contract/kline
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSD", // 合约code
    "duration": "1m", // 间隔 1m/5m/15m/30m/1h/2h/4h/6h/12h/1d/1w
    "start_time": 0, // 开始时间(10位时间戳)
    "end_time": 1583143200 // 结束时间(10位时间戳)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "contract_code": "BTCUSD", // 合约code
    "contract_name": "BTC永续合约", // 合约名称
    "contract_name_en": "BTC Contract",
    // 合约名称(英文)
    "server_time": 1523232213,
    // 服务器时间(秒时间戳)
    "list": [
      {
        "start_time": 1523204222,
        // 开始时间(10位时间戳)
        "end_time": 1523204622,
        // 结束时间(10位时间戳)
        "open_price": 99972.23,
        // 开盘价
        "close_price": 10002.23,
        // 收盘价
        "high_price": 10020.02,
        // 最高价
        "low_price": 99829.82,
        // 最低价
        "volume": 3234,
        // 成交量
        "per_value": 100
        //历史合约面值
      },
      {
        "start_time": 1523204622,
        // 开始时间(10位时间戳)
        "end_time": 1523205022,
        // 结束时间(10位时间戳)
        "open_price": 99972.23,
        // 开盘价
        "close_price": 10002.23,
        // 收盘价
        "high_price": 10020.02,
        // 最高价
        "low_price": 99829.82,
        // 最低价
        "volume": 3234,
        // 成交量
        "per_value": 100
        //历史合约面值
      }
    ]
  }
}
```

### 20 条件单撤销

- URL: http://{host_addr}/v1/order/plan/cancel
- Method: POST
- Request Body:

```json
{
  "data": {
    "order_id": 1234213213213, // 订单id
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf=" // 资金密码(加密后)
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 21 开仓

- URL: http://{host_addr}/v1/order/place
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSD", // 合约code
    "hold_type": 1, // 持仓类型 1:全仓 2:逐仓
    "side": "B", // 方向 B买 S卖
    "future_price": "10000.01", //预估成交价
    "amount": 10,//张数
    "leverage": 10, // 杠杠倍数
    "ncr": "random string", //随机字符串
    "limit": 0.0, // 止盈价
    "stop": 0.0,
    // 止损价
    "mode": 3,
    // 下单模式，1-对手价 2-最优3挡 3-最优5挡
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf=",
    // 资金密码(加密后)
    "entrust_type": 1, // 委托类型 0-市价 1-限价
    "entrust_price": "123,123", // 委托价格;限价单传递价格
    "position_id": "2412515125123213" // 持仓id,加仓用
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
      "available": "39.20" //可用余额
  }
}
```

### 22 条件单下单

- URL: http://{host_addr}/v1/order/plan/place
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSD", // 合约code
    "hold_type": 1, // 持仓类型 1:全仓 2:逐仓 3:带单
    "side": "B", // 方向 B买 S卖
    "price": "10000.01", // 价格
    "amount": 10,//下单张数
    "lever": 10,
    "entrust_type": 0,//委托类型 0-市价 1-限价
    "entrust_price": "10020.02",//委托执行价格
    "entrust_strategy": 2,//委托策略 0-默认 1-fok 2-ioc 
    // 杠杠倍数
    "mode": 3,
    // 下单模式，1-对手价 2-最优3挡 3-最优5挡
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf="
    // 资金密码(加密后)
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 23 平仓

- URL: http://{host_addr}/v1/position/closeout
- Method: POST
- Request Body:

```json
{
  "data": {
        "position_id": 123456, // 持仓id
        "close_type":1,//平仓类型，1-部分平仓 2-全部平仓(忽略amount)
        "amount": 1,//平仓张数
        "ncr": "random string", //随机字符串
        "mode": 3, // 下单模式，1-对手价 2-最优3挡 3-最优5挡
        "entrust_type": 1, // 委托类型 0-市价 1-限价
        "entrust_price": "123.123" // 委托价格;限价单传递价格
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
  }
}
```

### 24 设置止盈止损

- 请根据指数价格校验参数是否有效，服务端只校验数据是否合法
- URL: http://{host_addr}/v1/position/set/stop
- Method: POST
- Request Body:

```json
{
  "data": {
        "plan_close_order_id": 12412991294231294, // 需要更新的止盈止损记录id
        "position_id": 123456, // 持仓id
        "limit":"39.29", //止盈价
        "stop":"39.29", //止损价
        "entrust_limit":"0" // 止盈委托价 最优五档传0,限价传对应委托价格
        "entrust_stop":"123.123" // 止损委托价 最优五档传0,限价传对应委托价格
        "amount": 12, // 张数
        "fund_password": "dfieIdUUDKe732DH2eoj88Udf=" // 资金密码(加密后)
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
  }
}
```

### 25 用户邀请列表

- URL: http://{host_addr}/v1/user/invite/list
- Method: POST
- Request Body:

```json
{
  "data": {
    "extra": "ZvsBTS", // 子用户邀请码,用于查询二级用户邀请列表
    "page": 0, // 页码,从0开始
    "page_size": 10 // 每页数据量
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "has_invite": true, // 是否有下级
      "user_id": 4319273, // 用户id
      "code": "ZvsBTS", // 该用户的邀请码,用于查询该用户的邀请列表
      "created_time": 1577782401 // 注册时间
    },
    {
      "has_invite": true,
      "user_id": 41505152,
      "code": "ZvsBS", 
      "created_time": 1577782565
    },
    {
      "has_invite": false,
      "user_id": 4259712,
      "code": "vs3BS", 
      "created_time": 1578036742
    },
    {
      "has_invite": false,
      "user_id": 403577548,
      "code": "ZvBaS",
      "created_time": 1578052386
    }
  ]
}
```

### 26 涨幅榜

- URL: http://{host_addr}/v1/rank/increase
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约名称
      "contract_name_en": "BTCUSDT", // 合约英文名称
      "index": 6833.1, // 合约指数
      "index_str": "6833.1", // 合约指数(字符串)
      "increase_ratio": 32.43 // 涨幅
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "contract_name": "ETH合约", // 合约名称
      "contract_name_en": "ETHUSDT", // 合约英文名称
      "index": 233.12, // 合约指数
      "index_str": "233.1200", // 合约指数(字符串)
      "increase_ratio": 12.43 // 涨幅
    }
  ]
}
```

### 27 看涨人数榜

- URL: http://{host_addr}/v1/rank/buy
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约名称
      "contract_name_en": "BTCUSDT", // 合约英文名称
      "index": 6833.1, // 合约指数
      "index_str": "6833.1", // 合约指数(字符串)
      "amount": 10003 // 人数
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "contract_name": "ETH合约", // 合约名称
      "contract_name_en": "ETHUSDT", // 合约英文名称
      "index": 233.12, // 合约指数
      "index_str": "233.1200", // 合约指数(字符串)
      "amount": 363 // 人数
    }
  ]
}
```

### 28 看跌人数榜

- URL: http://{host_addr}/v1/rank/sell
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约名称
      "contract_name_en": "BTCUSDT", // 合约英文名称
      "index": 6833.1, // 合约指数
      "index_str": "6833.1", // 合约指数(字符串)
      "amount": 10003 // 人数
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "contract_name": "ETH合约", // 合约名称
      "contract_name_en": "ETHUSDT", // 合约英文名称
      "index": 233.12, // 合约指数
      "index_str": "233.1200", // 合约指数(字符串)
      "amount": 363 // 人数
    }
  ]
}
```

### 29 合约行情

- URL: http://{host_addr}/v1/contract/tick
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约名称
      "contract_name_en": "BTCUSDT", // 合约英文名称
      "index": 6833.1, // 合约指数
      "index_str": "6833.1", // 合约指数(字符串)
      "quote_change": "32.43" // 涨跌幅
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "contract_name": "ETH合约", // 合约名称
      "contract_name_en": "ETHUSDT", // 合约英文名称
      "index": 233.12, // 合约指数
      "index_str": "233.1200", // 合约指数(字符串)
      "quote_change": "12.43" // 涨跌幅
    }
  ]
}
```

### 30 获取充币地址

- URL: http://{host_addr}/v1/asset/deposit/address
- Method: POST
- Request Body:

```json
{
  "data": {
    "coin_id": 2, // 币种id
    "protocol": "ERC20" // 协议(没有传空字符串)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "coin_id": 36, // 币种id
    "verify_number": 30, // 链上验证数
    "address": "******************************************", // 地址
    "protocol": "ERC20", // 协议
    "tag": "12312512" // 地址标签
  }
}
```

### 31 获取资产详情

- URL: http://{host_addr}/v1/asset/detail
- Method: POST
- Request Body:

```json
{
  "data": {
    "coin_id": 2, // 币种id
    "mode": 1 // 账户类型 0-全部账户 1-资金账户 2-交易账户 3-跟单账户
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "coin_id": 2, // 币种id
    "coin_name": "USDT", // 币种名称
    "total_balance": 213.2312, // 总资产
    "total_balance_cny": "23.21", // 总资产(折合cny),如有精度需求需进行处理
    "trade": { // 交易账户
      "total": 213.2312, // 总资产
      "total_cny": "213.23", // 总资产(折合人民币)
      "rights": 213.2312, // 总权益
      "float": 213.2312, // 总浮动盈亏
      "margin": 213.2312, // 起始保证金
      "total_margin": "213.2312", // 总保证金 初始保证金+手续费
      "balance": "123.123", // 资产
      "available": 213.2312, // 可用余额
      "lock_amount": "213.2312", // 委托冻结
      "diff": "213.2312", // 待结算差值
      "can_transfer": 213.2312, // 可划转余额
      "blowing_rate": "12.123", // 平仓风险率
      "risk_rate": "12.12" // 账户分享率
    },
    "wallet": { // 资产账户
      "total": 213.2312, // 总资产
      "total_cny": "213", // 总资产(折合人民币)
      "available": 213.2312, // 可用余额
      "frozen": 213.2312, // 冻结数量
      "can_withdraw": "23.223343" // 可提现数量
    },
    "follow": { // 跟单账户
      "total": "0", // 总资产
      "total_cny": "213.2", // 总资产(折合人民币)
      "available": "0", // 可用余额
      "margin": "0", // 保证金
      "float": "0", // 总浮动盈亏
      "can_transfer": "0", // 可划转余额
      "lock_amount": "213.2312" // 委托冻结
    }
  }
}
```

### 32 提币申请

- URL: http://{host_addr}/v1/asset/withdraw
- Method: POST
- Request Body:

| 字段            | 必选  | 类型      | 备注             |
|:------------- |:--- |:------- |:-------------- |
| verify_id     | 是   | integer | 资金密码验证id       |
| coin_id       | 是   | integer | 币种id 当前固定传36   |
| coin_name     | 是   | string  | 币种名称(大写)       |
| amount        | 是   | decimal | 提现金额           |
| address       | 是   | string  | 到账地址           |
| tag           | 否   | string  | 地址标签           |
| protocol      | 是   | string  | 地址协议,非多协议传空字符串 |
| code          | 否   | string  | 短信验证码          |
| phone_code    | 否   | string  | 两步验证 手机验证码     |
| email_code    | 否   | string  | 两步验证 邮箱验证码     |
| totp_code     | 否   | string  | 两步验证 令牌验证码     |
| fund_password | 否   | string  | 资金密码(加密后)      |

```json
{
  "data": {
    "verify_id": 124124124125353532,
    "coin_id": 2,
    "coin_name": "USDT",
    "amount": "231.123",
    "address": "0x32fj293ufjidjij",
    "tag": "192412512",
    "protocol": "ERC20",
    "code": "341212",
    "phone_code": "123456",
    "email_code": "123456",
    "totp_code": "",
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf="
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

- 超额返回体,错误码1211需要特殊处理

| 字段     | 类型      | 备注                                                             |
|:------ |:------- |:-------------------------------------------------------------- |
| remain | decimal | 剩余额度,提币时,如果返回的数量小于系统配置的单次提币额度,说明该用户当日剩余的额度为返回值,否则应返回的是单次最大提币额度 |

```json
{
  "ret": 1211,
  "msg": "大于最大提币限额",
  "data": {
    "remain": "100"
  }
}
```

### 33 资金划转

- URL: http://{host_addr}/v1/asset/transfer
- Method: POST
- Request Body:

```json
{
  "data": {
    "coin_id": 2, // 币种id
    "transfer_type": 1, // 划转类型 0-资金账户->交易账户 1-交易账户->资金账户 2-资金账户->跟单账户 3-跟单账户->资金账户, 4-交易账户->跟单账户 5-跟单账户->交易账户
    "amount": "123.1223", // 数量
    "captcha_id": "dfieirjiesdfad", // 易盾captchaID
    "validate": "dsfdsafjkjkcvijsdi..." // 易盾返回的二次校验数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 34 资产账户提币记录

- URL: http://{host_addr}/v1/asset/bill/withdraw
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212, // 用户id
      "type": 2, // 类型 1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16: 邀请佣金奖励 32: 代理佣金奖励
      "status": 2, // 状态 充值 1：未到账 2：已到账\n提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝\n其它为 2：已到账
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "amount": 232.2342, // 数量
      "tx": "0xd23423fd232", // 交易哈希
      "balance": 123.1242, // 可用余额
      "lock_amount": 223.12, // 冻结数量
      "created_time": **********, // 创建时间
      "approved_time": ********** // 提现审核时间
    }
  ]
}
```

### 35 资产账户充币记录

- URL: http://{host_addr}/v1/asset/bill/recharge
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212, // 用户id
      "type": 2, // 类型 1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16: 邀请佣金奖励 32: 代理佣金奖励
      "status": 2, // 状态 充值 1：未到账 2：已到账\n提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝\n其它为 2：已到账
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "amount": 232.2342, // 数量
      "tx": "0xd23423fd232", // 交易哈希
      "balance": 123.1242, // 可用余额
      "lock_amount": 223.12, // 冻结数量
      "created_time": **********, // 创建时间
      "approved_time": ********** // 提现审核时间
    }
  ]
}
```

### 36 资产账户划转记录

- URL: http://{host_addr}/v1/asset/bill/transfer
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212, // 用户id
      "type": 2, // 类型 1:充值 2:提现 4:划转到交易账户 8:从交易账户转入 16:邀请佣金奖励 32:代理佣金奖励 64:法币订单收币 128:空投奖励 256:资产账户划转到跟单账户 512:跟单账户划转到资产账户
      "status": 2, // 状态 充值 1：未到账 2：已到账\n提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝\n其它为 2：已到账
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "amount": 232.2342, // 数量
      "tx": "0xd23423fd232", // 交易哈希
      "balance": 123.1242, // 可用余额
      "lock_amount": 223.12, // 冻结数量
      "created_time": **********, // 创建时间
      "approved_time": ********** // 提现审核时间
    }
  ]
}
```

### 37 交易账户划转记录

- URL: http://{host_addr}/v1/asset/trade/bill/transfer
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": **************, // 流水id
      "user_id": 7201223, // 用户id
      "amount": 232.2342, // 数量
      "available": 2342.342, // 可用余额
      "balance": 314212.1231, // 账户资产
      "created_time": **********, // 创建时间
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "type": 4, // 类型 1:开仓手续费(1<<0) 2:资金费用(1<<1) 4:从资产账户转入(1<<2) 8:划转到资产账户(1<<3) 16:平仓盈亏(1<<4) 32:平仓手续费(1<<5) 64:模拟盘补充资产(1<<6) 128:减少保证金(1<<7) 256:预扣佣金(1<<8) 512:佣金退款(1<<9) 1024:佣金收入(1<<10) 2048:交易账户划转到跟单账户(1<<11) 4096:跟单账户划转到交易账户(1<<12) 8192:资产账户划转到跟单账户(1<<13) 16384:跟单账户划转到资产账户(1<<14) 32768-强平退回(1<<15) 65536-模拟盘减少资产(1<<16) 131072-爆仓结算手续费(1<<17) 262144-异常资产扣除(1<<18) 524288-增加保证金(1<<19) 1048576-赠金领取(1<<20) 2097152-赠金失效(1<<21) 4194304-赠金回收(1<<22)
      "margin": 2312.123, // 仓位保证金
      "account_rights": 312.234, // 用户权益
      "gift_available": "123.123" // 赠金可用
    }
  ]
}
```

### 38 币种列表

- URL: http://{host_addr}/v1/common/coin/list
- 当前版本其他接口使用的币种为列表中支持充币的币种id
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "can_transfer": true, // 是否支持划转
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "full_name": "USDT...", // 币全称
      "icon": "xxx.ico", // 币图标
      "max_transfer": 10000.0, // 资产划转单笔最大数量
      "max_withdraw": 10000.0, // 单笔最大提币
      "min_transfer": 0.001, // 资产划转单笔最小数量
      "min_withdraw": 0.001, // 单笔最小提币
      "miner_fee": 0.003, // 单笔提币手续费率
      "pay_confirms":20, // 入账需要确认数
      "precision": 6, // 精度（小数位）
      "recharge_enable": true, // 是否支持充值
      "status": 1, // 状态 0：下架 1：正常
      "tag": "ERC20", // 币种标识 如usdt ERC2
      "wallet_name": "usdt2", // 钱包名称
      "withdraw_enable": true, // 是否支持提现
      "withdraw_review": 500, // 单次提现审核数量
      "withdraw_time": 3, // 单日提币次数
      "is_exchange": false, // 是否支持兑换
      "min_exchange": "1", // 最小交易量
      "max_exchange": "10000", // 最大交易量
      "max_exchange_volume": "0", // 每日最大交易量
      "min_exchange_money": "0", // 最小交易额
      "daily_exchange_money": "0", // 每日最大交易额
      "exchange_coin_point": "0", // 币本位加点
      "exchange_usdt_point": "0", // 兑换美元加点
      "exchange_fee_rate": "0", // 兑换费率
      "exchange_price_range": "0.0005", // 兑换价格范围
      "is_hedge": false, // 是否支持对冲
      "hedge_warn_threshold": "0", // 对冲账户余额不足报警阈值
      "multiple": true, // 是否多协议币种
      "protocols": [ // 协议列表
        {
          "wallet_name": "usdt2", // 钱包名称
          "coin_id": 36, // 币种id
          "coin_name": "USDT", // 币种名
          "protocol": "ERC20", // 协议名
          "can_deposit": true, // 是否支持充币
          "can_withdraw": true, // 是否支持提币
          "min_withdraw": "10", // 最小提币数量
          "max_withdraw": "2000", // 单笔最大提币数量
          "withdraw_fee": "2", // 提币手续费
          "withdraw_review": "500", // 提币审核阈值
          "withdraw_time": 3, // 单日最大提币次数
          "withdraw_precision": 6, // 币种精度
          "confirm_num": 30, // 链上确认数
          "weight": 10.123 // 排序权重 从大到小
        }
      ]
    }
  ]
}
```

### 39 当前持仓

- URL: http://{host_addr}/v1/order/hold
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "total_rights": 123.12313, // 总权益
    "total_float": 2.2323, // 总浮动盈亏
    "list": [ // 持仓列表
      {
        "position_id": 123123124151521323, // 持仓id
        "contract_code": "BTCUSDT", // 合约代码
        "contract_name": "BTCUSDT合约", // 合约中文名
        "contract_name_en": "BTCUSDT...", // 合约英文名
        "par_value": "100", // 合约面值
        "side": "B", // 合约方向 B买/多 S卖/空
        "price": 234.1231, // 持仓均价
        "price_str": "234.1231", // 持仓均价
        "volume": 34, // 持仓张数
        "volume_lock": 2, // 持仓冻结张数
        "force_price": 1231.2341, // 强平价格
        "limit": 1231.2332, // 止盈价
        "stop": 1100.23123, // 止损价
        "account_type": 1, // 持仓类型 1全仓 2逐仓
        "lever": 100, // 杠杆倍数
        "margin": 1213.1231, // 起始保证金 注:仓位保证金=起始保证金+手续费++浮动盈亏
        "float_profit": 123.14154, // 浮动盈亏
        "available": 21.1231, // 可用余额
        "profit_ratio": 2.63, // 盈亏率
        "margin_ratio": 1.23, // 保证金率
        "contract_index": 231.23123, // 合约指数
        "buy_price": 1213.12312, // 买入价
        "sell_price": 1412.2312, // 卖出价
        "user_id": 7312312, // 用户id
        "commission": 3.412, // 手续费
        "share_resource": {
          "image_background": "https://baidu.com/bg.png", // 背景图
          "title_color": "#FFAAFF", // 标题字色
          "title_vertical": 560, // 标题垂直位置
          "content_color": "#FFAAFF", // 文本区字色
          "title_text": "大吉大利,落地成盒", // 分享标题
          "title_size": 12 // 标题字号
        }
      }
    ]
  }
}
```

### 40 成交记录

- URL: http://{host_addr}/v1/order/sold
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT", // 合约代码
    "offset": "O" // 类型 O-开仓 C-平仓 传空字符串不进行筛选
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "order_client": 1, // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
      "trade_id": 233423423423423434, // 成交编号
      "account_type": 1, // 账户模式 1：全仓 2：逐仓
      "available": 212.3123, // 可用余额
      "balance": 2344.2323, // 账户资产
      "commission": 12.123, // 手续费
      "contract_code": "BTCUSDT", // 合约代码
      "contract_name": "BTCUSDT合约", // 合约中文名
      "contract_name_en": "BTCUSDT...", // 合约英文名
      "par_value": "100", // 合约面值
      "lever": 50, // 杠杆
      "limit": 3123.231, // 止盈价（触发止盈时持仓带来的）
      "offset": "O", // O: 开仓 C: 平仓
      "order_id": *****************, // 报单编号
      "order_type": 0, // 0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单
      "price": 234.23425, // 成交价格
      "profit": 23.234235, // 实现盈亏
      "profit_str": "23.234235",
      // 实现盈亏
      "close_profit": 20.234235,
      //平仓盈亏
      "close_profit_str": "20.234235",
      //平仓盈亏
      "side": "B",
      // B买S卖
      "stop": 2343.23452,
      // 止损价（触发止损时持仓带来的）
      "total_profit": 231.23141,
      // 累计已实现盈亏
      "trade_amount": 2343.2352,
      // 成交金额
      "trade_time": 1582342342,
      // 成交时间
      "trade_value": 234.234234,
      // 成交价值
      "user_id": 7293929,
      // 用户id
      "entrust_order_id": 4,
      //委托订单id
      "entrust_mode": 3,
      // 委托模式，1-对手价 2-最优3挡 3-最优5挡
      "entrust_type": 0,
      // 委托类型，0-市价单 1-限价单 2-强平单
      "entrust_volume": 4,
      // 委托数量
      "volume": 4,
      // 成交张数
      "entrust_status": 200,
      // 委托状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
      "share_resource": {
        "image_background": "https://baidu.com/bg.png",
        // 背景图
        "title_color": "#FFAAFF",
        // 标题字色
        "title_vertical": 560,
        // 标题垂直位置
        "content_color": "#FFAAFF",
        // 文本区字色
        "title_text": "大吉大利,落地成盒",
        // 分享标题
        "title_size": 12
        // 标题字号
      }
    }
  ]
}
```

### 41 计划单

- URL: http://{host_addr}/v1/order/plan
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT" // 合约代码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSD", // 合约code
      "contract_name": "BTC永续合约", // 合约名称
      "contract_name_en": "BTC Contract",
      // 合约名称(英文)
      "par_value": "100", // 合约面值
      "plan_order_id": ***************,
      // 条件单id
      "status": 2,
      // 条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败)
      "side": "B",
      // 方向 B买 S卖
      "create_time": **********,
      // 提交时间
      "volume": 12,
      // 下单张数
      "condition": 1,
      // 条件 1-大于等于 2-小于等于
      "trigger_price": "123.123",
      // 触发价格
      "order_time": **********,
      // 触发时间(10位时间戳) 未触发时为0
      "account_type": 2,
      // 账户模式 1-全仓 2-逐仓
      "lever": 10,
      // 杠杠倍数
      "mode": 1,
      //委托模式，1-对手价 2-最优3挡 3-最优5挡
      "entrust_type": 0,//委托类型 0-市价 1-限价
      "entrust_price": "10020.02",//委托执行价格
      "entrust_strategy": 2,//委托策略 0-默认 1-fok 2-ioc
    }
  ]
}
```

### 42 交易账户盈亏记录

- URL: http://{host_addr}/v1/asset/trade/bill/profit
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": **************, // 流水id
      "user_id": 7201223, // 用户id
      "amount": 232.2342, // 数量
      "available": 2342.342, // 可用余额
      "balance": 314212.1231, // 账户资产
      "created_time": **********, // 创建时间
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "type": 1, // 类型 1:开仓手续费(1<<0) 2:资金费用(1<<1) 4:从资产账户转入(1<<2) 8:划转到资产账户(1<<3) 16:平仓盈亏(1<<4) 32:平仓手续费(1<<5) 64:模拟盘补充资产(1<<6) 128:减少保证金(1<<7) 256:预扣佣金(1<<8) 512:佣金退款(1<<9) 1024:佣金收入(1<<10) 2048:交易账户划转到跟单账户(1<<11) 4096:跟单账户划转到交易账户(1<<12) 8192:资产账户划转到跟单账户(1<<13) 16384:跟单账户划转到资产账户(1<<14) 32768-强平退回(1<<15) 65536-模拟盘减少资产(1<<16) 131072-爆仓结算手续费(1<<17) 262144-异常资产扣除(1<<18) 524288-增加保证金(1<<19) 1048576-赠金领取(1<<20) 2097152-赠金失效(1<<21) 4194304-赠金回收(1<<22)
      "margin": 2312.123, // 仓位保证金
      "account_rights": 312.234, // 用户权益
      "gift_available": "123.123" // 赠金可用
    }
  ]
}
```

### 43 用户当前合约杠杆倍数

- URL: http://{host_addr}/v1/user/trade/lever
- Method: POST
- Request Body:

| 参数名           | 必选  | 类型     | 描述     |
|:------------- |:--- |:------ |:------ |
| contract_code | 是   | string | 合约code |

```json
{
  "data": {
    "contract_code": "BTCUSDT"
  }
}
```

- Response Body:

| 参数名           | 类型      | 描述                        |
|:------------- |:------- |:------------------------- |
| account_type  | integer | 当前账户模式 1-全仓 2-逐仓          |
| max_lever     | integer | 当前账户最大可设置杠杆数(0时不限制,按合约定义) |
| cross_lever   | integer | 全仓杠杆                      |
| long_lever    | integer | 逐仓多方向杠杆                   |
| short_lever   | integer | 逐仓空方向杠杆                   |
| holding_long  | boolean | 是否持有多方向订单                 |
| holding_short | boolean | 是否持有空方向订单                 |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "account_type": 1,
    "max_lever": 0,
    "cross_lever": 150,
    "long_lever": 100,
    "short_lever": 100,
    "holding_long": false,
    "holding_short": false
  }
}
```

### 44 切换用户合约默认杠杆

- URL: http://{host_addr}/v1/user/trade/lever/switch
- Method: POST
- Request Body:

| 参数名           | 必选  | 类型      | 描述                  |
|:------------- |:--- |:------- |:------------------- |
| contract_code | 是   | string  | 合约code              |
| account_type  | 是   | integer | 账户模式 1-全仓 2-逐仓      |
| cross_lever   | 否   | integer | 全仓杠杆(账户模式为全仓时必选)    |
| long_lever    | 否   | integer | 逐仓多方向杠杆(账户模式为逐仓时必选) |
| short_lever   | 否   | integer | 逐仓空方向杠杆(账户模式为逐仓时必选) |

```json
{
  "data": {
    "contract_code": "BTCUSDT",
    "account_type": 2,
    "cross_lever": 0,
    "long_lever": 50,
    "short_lever": 100
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 45 资产账户佣金记录

- URL: http://{host_addr}/v1/asset/bill/brokerage
- Method: POST
- Request Body:

```json
{
  "data": {
    "mode": 0 // 记录类型 0-所有类型 16: 邀请佣金奖励 32: 代理佣金奖励 1024: 佣金收入 2048:活动奖励 获取多种时类型相加
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212, // 用户id
      "type": 2, // 类型 1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16: 邀请佣金奖励 32: 代理佣金奖励 1024: 佣金收入
      "status": 2, // 状态 充值 1：未到账 2：已到账\n提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝\n其它为 2：已到账
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "amount": 232.2342, // 数量
      "tx": "0xd23423fd232", // 交易哈希
      "balance": 123.1242, // 可用余额
      "lock_amount": 223.12, // 冻结数量
      "created_time": **********, // 创建时间
      "approved_time": ********** // 提现审核时间
    }
  ]
}
```

### 46 模拟盘账户初始化

- URL: http://{host_addr}/v1/account/init
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 47 分享资源

- URL: http://{host_addr}/v1/common/share/resource
- Method: POST
- Request Body:

```json
{
  "data": {
    "level": 1 // 盈利等级 ... -3 -2 -1 1 2 3 ... 分别对应百分比转换成的档位
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "image_background": "https://xxx.com/bg.png", // 背景图
    "title_color": "#FFAAFF", // 标题字色
    "title_vertical": 560, // 标题垂直位置
    "content_color": "#FFAAFF", // 文本区字色
    "title_text": "大吉大利,落地成盒", // 分享标题
    "title_size": 12 // 标题字号
  }
}
```

### 48 获取用户认证状态

- URL: http://{host_addr}/v1/user/verify/state
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": 10000001, // 认证记录id 无用
    "state": 3, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
    "err_code": 0, // 认证错误码 0-无错误 1001-证件号码已经存在 1002-证件号码与姓名不匹配 1003-身份信息与活体检测不匹配
    "err_msg": "", // 失败原因 通过返回空字符串,失败根据客户端语言进行返回
    "name": "张三", // 认证姓名
    "surname": "张", // 姓
    "forename": "三", // 名
    "number": "130721199303258888",
    // 认证证件号
    "card_type": 0,
    // 证件类型 0-大陆证件 1-非大陆证件
    "static_identity": false
    // 是否被管理后台固定的身份信息, true的时候失败直接再次进行人脸认证 false则从身份信息提交重新走一遍
  }
}
```

### 49 提交身份信息审核

- URL: http://{host_addr}/v1/user/verify/idnumber
- Method: POST
- Request Body:

```json
{
  "data": {
    "name": "张三", // 姓名
    "number": "130721199303258888", // 身份证号
    "captcha_id": "dfieirjiesdfad", // 易盾captchaID
    "validate": "dsfdsafjkjkcvijsdi..." // 易盾返回的二次校验数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 50 提交面部认证信息

- URL: http://{host_addr}/v1/user/verify/face
- Method: POST
- Request Body:

```json
{
  "data": {
    "proto_buf": [253, 13, 48, 23, 55, 23], // protoBuf数据
    "proto_buf_hex": "cc02b3f2c201..." // protoBuf数据(ios使用该字段传输16进制字符串)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 51 获取用户认证进度

- URL: http://{host_addr}/v1/user/verify/progress
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": 10000001, // 认证记录id 无用
    "state": 3, // 认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过
    "err_code": 0, // 认证错误码 0-无错误 1001-证件号码已经存在 1002-证件号码与姓名不匹配 1003-身份信息与活体检测不匹配
    "err_msg": "", // 失败原因 通过返回空字符串,失败根据客户端语言进行返回
    "name": "张三", // 认证姓名
    "surname": "张", // 姓
    "forename": "三", // 名
    "number": "130721199303258888", // 认证证件号
    "card_type": 0 // 证件类型 0-大陆证件 1-非大陆证件
  }
}
```

### 52 法币下单

- URL: http://{host_addr}/v1/legal/place

- Method: POST

- Momo: 下单获取汇率后，卖出时建议以数字币模式进行下单

- Request Body:
  
  ```json
  {
  "data": {
    "side": 2,
    // 下单方向 1-卖 2买
    "mode": 0,
    // 下单输入类型 0-输入法币下单 1-输入数字币下单
    "coin_name": "USDT",
    // 币种名称
    "amount": "100.23",
    // 下单数量/下单金额
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf=",
    // 资金密码(加密后)
    "payment_id": 23,
    //收款账户id(卖单必须）
    "business_id": 5,
    //接单商户Id(从下单获取汇率接口获取）
    "exchange_rate": "6.72",
    //实时汇率(从下单获取汇率接口获取）
    "rate_token": "abcdefghijklmnop"
    //下单时获取汇率token(从下单获取汇率接口获取）
  },
  "api_version": "V2",
  // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
  }
  ```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "token": "dfMeiDNdfEiSjSEnGnS...", // token
    "platform_id": "sdfsadifjiajeijr" // 平台id
  }
}
```

- 超额返回体,错误码1211需要特殊处理
  
  ```json
  {
  "ret": 1211,
  "msg": "大于最大提币限额",
  "data": {
    "remain": "100" // 剩余额度
  }
  }
  ```

### 53 法币订单记录

- URL: http://{host_addr}/v1/legal/order/list
- Method: POST
- Request Body:

```json
{
  "data": {
    "state": 1 // 订单状态 1-进行中 2-已完成 3-已取消
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "order_id": 7192324, // 本地订单号
      "coin_id": 36, // 币种id
      "coin_name": "USDT", // 币种名称
      "order_type": 2,
      // 订单类型 1-出售 2-购买
      "state": 1,
      // 订单状态 0,1 等待买家付款; 2 卖家确认收款/买家确认付款; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8-后台取消(交易失败）
      "amount": "123.4543",
      // 订单数量
      "legal_amount": "1.63", // 法币数量
      "create_time": 1592323232, // 创建时间
      "close_time": 0 // 完成时间
    }
  ]
}
```

### 54 资产账户法币记录

- URL: http://{host_addr}/v1/asset/bill/legal
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212,
      // 用户id
      "type": 64,
      // 类型 1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16: 邀请佣金奖励 32: 代理佣金奖励 64: 法币订单入账 4096-法币订单卖出
      "status": 2,
      // 状态 充值 1：未到账 2：已到账\n提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝\n其它为 2：已到账
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "amount": 232.2342, // 数量
      "tx": "", // 交易哈希
      "balance": 123.1242, // 可用余额
      "lock_amount": 223.12, // 冻结数量
      "created_time": **********, // 创建时间
      "approved_time": ********** // 提现审核时间
    }
  ]
}
```

### 55 获取法币系统登录token

- URL: http://{host_addr}/v1/legal/login
- Method: POST
- Request Body:

```json
{
  "data": {
    "order_id": 13284828123882838284, // 本地系统订单编号
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "token": "dfMeiDNdfEiSjSEnGnS...", // token
    "platform_id": "sdfsadifjiajeijr" // 平台id
  }
}
```

### 56 获取法币系统限制

- URL: http://{host_addr}/v1/legal/limit
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "value_min": "10.00", // 最小下单金额
    "value_max": "50,000.00" // 最大下单金额
  }
}
```

### 57 邀请信息统计

- URL: http://{host_addr}/v1/user/invite
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "last_reward": "0.0000",  // 昨日返佣
    "total_reward": "223.2423", // 累计返佣
    "last_invited": 3, // 今日邀请
    "total_invited": 66 // 累计邀请
  }
}
```

### 58 指定合约行情

- URL: http://{host_addr}/v1/contract/market
- Method: POST
- Request Body:

```json
{
  "data": {
        "contract_code": "ETHUSDT"
    },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
           "contract_code": "ETHUDT", //合约代码
           "contract_index": 238.41, 
           "index_price": "238.41", //指数价格
           "indexCny": 1699.73,
    "index_price_cny": "1699.73",
    //指数人民币价格
    "trade_price": "1699.73",
    //成交价
    "trade_price_cn": "2300.20",
    //成交价人民币价格
           "buyPrice": 238.47, 
           "buy_price": "238.47", //买入价
           "sellPrice": 238.32,
           "sell_price": "238.32", //卖出价
           "buy_count": 522487, //看涨人数
           "sell_count": 340797,//看跌人数
           "amount": 362, //成交量
           "side": "S", //成交方向
           "create_by": "2020-06-01T17:25:14.67323649+08:00"
  }
}
```

### 59 获取客户端最新下载链接

- URL: http://{host_addr}/v1/common/download?os=1&platform_id=1  // os 1-安卓 2-ios(ios暂无效) // platform_id 平台id

- Method: GET

- Response Body:

| 参数名       | 类型      | 描述                                                                                                      |
|:--------- |:------- |:------------------------------------------------------------------------------------------------------- |
| os        | integer | 客户端类型 1-安卓 2-IOS                                                                                        |
| list      | object  | 下载渠道列表                                                                                                  |
| >key      | string  | 渠道名 fast-极速下载 fast_2-极速下载2 local-本地下载 spare-备用下载 app_store-苹果商店 test_flight-Testflight google_play-谷歌商店 |
| >>version | string  | 版本号                                                                                                     |
| >>url     | string  | 下载链接                                                                                                    |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "os": 1,
    "list": {
      "fast": {
        "version": "1.0.4",
        "url": "https://www.officeqb.com/res/pkg/141.apk",
      }
    }
  }
}
```

### 60 合约指标

- URL: http://{host_addr}/v1/rank/indicator
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "index": "6833.1", // 合约指数(字符串)
      "index_cny": "6833.12", // 合约指数(cny字符串)
      "side": "B" // 方向，B-偏多 S-偏空
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "index": "6833.1", // 合约指数(字符串)
      "index_cny": "6833.12", // 合约指数(cny字符串)
      "side": "B" // 方向，B-偏多 S-偏空
    }
  ]
}
```

### 61 合约阻力支撑位

- URL: http://{host_addr}/v1/rank/dragSupport
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "contract_code": "BTCUSDT", // 合约代码
      "drag": "6833.1", // 阻力位
      "drag_cny": "6833.12", // 阻力位(cny)
      "support": "6833.1", // 支撑位
      "support_cny": "6833.12", // 支撑位(cny)
      "trade_volume": "2821661" // 24小时成交量
    },
    {
      "contract_code": "ETHUSDT", // 合约代码
      "drag": "6833.1", // 阻力位
      "drag_cny": "6833.12", // 阻力位(cny)
      "support": "6833.1", // 支撑位
      "support_cny": "6833.12", // 支撑位(cny)
      "trade_volume": "2821661" // 24小时成交量
    }
  ]
}
```

### 62 模拟盘资产补领

- URL: https://sim-api.{host_addr}/v1/asset/increase
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 63 h5提交面部认证信息

- URL: http://{host_addr}/v1/user/verify/face/h5
- Method: POST
- Request Body:

```json
{
  "data": {
    "base64_image": "data:image/png;base64,iVBORw0..." // base64_image数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 64 调整保证金

- URL: http://{host_addr}/v1/position/margin/modify
- Method: POST
- Request Body:

```json
{
  "data": {
    "position_id": 23124124123123123, // 持仓id
    "amount": "123.234", // 调整数量
    "side": "add" // 方向 add-增加 sub-减少
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 65 获取国家区域列表

- URL: http://{host_addr}/v1/common/countryareacode
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
        "id": 10, // 数据id
        "country_en": "Armenia", //英文名
        "country_cn": "亚美尼亚", // 中文
        "country_tw": "亞美尼亞", // 繁体
        "country_code": "374", // 区号
        "country_encrypt": "AM" // 国家代码
    },
    {
        "id": 24, // 数据id
        "country_en": "Benin", //英文名
        "country_cn": "贝宁", // 中文
        "country_tw": "貝南", // 繁体
        "country_code": "229", // 区号
        "country_encrypt": "BJ" // 国家代码
    }
  ]
}
```

### 66 资金费率

- URL: http://{host_addr}/v1/contract/fundingrate
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
        "BCHUSDT": {
            "contract_code": "BCHUSDT", // 合约code
            "funding_rate": "0.000243", // 当前费率
            "current_time": 1594281600, // 当期时间
            "estimated_rate": "0.00028", // 预测费率
            "next_time": 1594310400 // 下期时间
        },
        "BSVUSDT": {
            "contract_code": "BSVUSDT",
            "funding_rate": "0.000163",
            "current_time": 1594281600,
            "estimated_rate": "0.00012",
            "next_time": 1594310400
        }
  }
}
```

### 67 图片上传

- URL: http://{host_addr}/res/upload/image
- Method: POST
- Content-Type: form-data
- Request Body:

```
ts: 当前时间戳
nonce: 随机数
req_os: app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
file: 要上传的文件
sig: 签名


签名方式:
加入token并排序后按照nonce=xxx&req_os=x&token=xxx&ts=xxx生成待签名串
进行两次md5再做一次sha256得到最终的签名
```

| key    | value                                                             |
|:------ |:----------------------------------------------------------------- |
| ts     | **********                                                        |
| nonce  | 1234552123                                                        |
| req_os | 1                                                                 |
| file   | {file}                                                            |
| sig    | a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591 |

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
       "file_name": "https://api.officeqb.com/res/image/7165310_7_1594719395030165464.jpg" // 最终图片链接
  }
}
```

### 68 非大陆地区认证

- URL: http://{host_addr}/v1/user/verify/other
- Method: POST
- Request Body:

```json
{
  "data": {
    "surname": "老", // 姓
    "forename": "干妈", // 名
    "number": "QQ20206666", // 证件号
    "country_code": "TW", // 国家代码
    "photo": "https://image.baidu.com/xxx.jpg" // 证件照片
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 69 修改用户信息

- URL: http://{host_addr}/v1/user/info/modify
- Method: POST
- Request Body:

```json
{
  "data": {
        "avatar": "https://api.officeqb.com/res/image/7144588_e0737f24-aa47-4e75-931d-a2a971b25c75_1603957667.jpg", // 头像
        "nickname": "小男孩", // 昵称
        "nickname_en": "Little boy", // 英文昵称
        "introduce": "棒棒棒", // 介绍
        "introduce_en": "good good good" // 英文介绍
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 70 同意跟单协议

- URL: http://{host_addr}/v1/follow/license/agree
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 71 交易员申请

- URL: http://{host_addr}/v1/follow/dealer/apply
- Method: POST
- Request Body:

```json
{
  "data": {
     "contact": "13800138000" // 联系方式
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 72 跟单账户盈亏记录

- URL: http://{host_addr}/v1/follow/asset/bill/profit
- Method: POST
- Request Body:

```json
{
  "data": {
    "position_id": 0 // 持仓id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": **************, // 流水id
      "user_id": 7201223, // 用户id
      "position_id": 2391299419299192939, // 持仓id
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "balance": "314212.1231", // 账户资产
      "available": "2342.342", // 可用余额
      "type": 1, // 类型 1:开仓手续费(1<<0) 2:资金费用(1<<1) 4:从资产账户转入(1<<2) 8:划转到资产账户(1<<3) 16:平仓盈亏(1<<4) 32:平仓手续费(1<<5) 64:模拟盘补充资产(1<<6) 128:减少保证金(1<<7) 256:预扣佣金(1<<8) 512:佣金退款(1<<9) 1024:佣金收入(1<<10) 2048:交易账户划转到跟单账户(1<<11) 4096:跟单账户划转到交易账户(1<<12) 8192:资产账户划转到跟单账户(1<<13) 16384:跟单账户划转到资产账户(1<<14)  32768-强平退回(1<<15) 65536-模拟盘减少资产(1<<16) 131072-爆仓结算手续费(1<<17) 262144-异常资产扣除(1<<18) 524288-增加保证金(1<<19) 
      "amount": "232.2342", // 数量
      "create_time": **********, // 创建时间
      "margin": "2312.123", // 仓位保证金
      "account_rights": "312.234" // 用户权益
    }
  ]
}
```

### 73 跟单账户划转记录

- URL: http://{host_addr}/v1/follow/asset/bill/transfer
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": **************, // 流水id
      "user_id": 7201223, // 用户id
      "position_id": 2391299419299192939, // 持仓id
      "currency_id": 2, // 币种id
      "currency_name": "USDT", // 币种名称
      "balance": "314212.1231", // 账户资产
      "available": "2342.342", // 可用余额
      "type": 16384, // 类型 1:开仓手续费(1<<0) 2:资金费用(1<<1) 4:从资产账户转入(1<<2) 8:划转到资产账户(1<<3) 16:平仓盈亏(1<<4) 32:平仓手续费(1<<5) 64:模拟盘补充资产(1<<6) 128:减少保证金(1<<7) 256:预扣佣金(1<<8) 512:佣金退款(1<<9) 1024:佣金收入(1<<10) 2048:交易账户划转到跟单账户(1<<11) 4096:跟单账户划转到交易账户(1<<12) 8192:资产账户划转到跟单账户(1<<13) 16384:跟单账户划转到资产账户(1<<14) 32768-强平退回(1<<15) 65536-模拟盘减少资产(1<<16) 131072-爆仓结算手续费(1<<17) 262144-异常资产扣除(1<<18) 524288-增加保证金(1<<19)
      "amount": "232.2342", // 数量
      "create_time": **********, // 创建时间
      "margin": "2312.123", // 仓位保证金
      "account_rights": "312.234" // 用户权益
    }
  ]
}
```

### 74 获取当前带单杠杆

- URL: http://{host_addr}/v1/follow/dealer/lever
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT" // 合约代码
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "buy_lever": 10, // 当前合约买方向默认杠杆
    "sell_lever": 10 // 当前合约卖方向默认杠杆
  }
}
```

### 75 切换新带单杠杆

- URL: http://{host_addr}/v1/follow/dealer/lever/switch
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT", // 合约代码
    "side": "B", // 买卖方向 B-买(做多) S-卖(做空)
    "lever": 10 // 新杠杆
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 76 获取带单跟单持仓列表

- URL: http://{host_addr}/v1/follow/order/hold
- Method: POST
- Request Body:

```json
{
  "data": {
        "page": 0, // 页码 从0开始
        "count": 20 // 单页数据量
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        "total_rights": "133.24", // 总权益(页码为0时返回)
        "total_float": "256.24", // 总未实现盈亏(页码为0时返回)
        "available": "256.24", // 资产可用
        "list": [ // list返回空列表时表示后面没有数据了
            {
                "id": 10021001, // 持仓id
                "contract_code": "BSVUSDT", // 合约code
                "contract_name": "BSVUSDT永续", // 合约中文名
                "contract_name_en": "BSVUSDT", // 合约英文名
                "par_value": "100", // 合约面值
                "side": "S", // 方向 B买S卖
                "volume": 124, // 下单张数
                "contract_index": 123, // 合约指数
                "lever": 10, // 杠杆
                "limit": "234", // 止盈价
                "stop": "23", // 止损价
                "trader_uid": 12334, // 交易员用户id(带单为0)
                "follow_position_id": 0, // 跟单对应持仓id(带单为0)
                "trader_nickname": "小伙子", // 交易员昵称
                "price": "123", // 持仓均价
                "margin": "0", // （起始保证金+手续费+调整）
                "init_margin": "0", // 起始保证金
                "adjust_margin": "123.123", // 调整保证金,
                "commission": "0", // 手续费
                "profit_ratio": "12.3", // 盈亏率
                "force_price": "123", // 强平价
                "float_profit": "256.24", // 未实现盈亏
                "funding_amount": "0", // 资金费用
                "index_precision": 5, // 合约指数价格精度
                "open_position_time": 1602342341, // 开仓时间
                "share_resource": {
                    "image_background": "https://baidu.com/bg.png", // 背景图
                    "title_color": "#FFAAFF", // 标题字色
                    "title_vertical": 560, // 标题垂直位置
                    "content_color": "#FFAAFF", // 文本区字色
                    "title_text": "大吉大利,落地成盒", // 分享标题
                    "title_size": 12 // 标题字号
                }
            }
        ]
    }
}
```

### 77 新成交记录(包含带单跟单以及普通开仓)

- URL: http://{host_addr}/v1/all/order/sold
- Method: POST
- Request Body:

```json
{
  "data": {
    "contract_code": "BTCUSDT", // 合约代码
    "offset": "O" // 类型 O-开仓 C-平仓 传空字符串不进行筛选
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
        "order_client": 1, // 委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)
        "trade_id": 187215848400756737, // 交易id
        "contract_code": "EOSUSDT", // 合约代码
        "contract_name": "EOSUSDT永续", // 合约中文名
        "contract_name_en": "EOSUSDT", // 合约英文名
        "par_value": "100", // 合约面值
        "account_type": 3, // 账户模式 1：全仓 2：逐仓 3: 带单跟单
        "side": "B", // 交易方向 B买S卖
        "offset": "C", // O: 开仓 C: 平仓
      "price": "2.512",
      // 成交价格
      "volume": 1,
      // 成交张数
      "trade_value": "251.2",
      // 成交价值
      "lever": 50,
      // 杠杆
      "trade_amount": "5.024",
      // 成交金额
      "profit": "-1.27584",
      // 已实现盈亏
      "close_profit": "-1.31",
      // 平仓盈亏
      "index_precision": 5,
      // 合约指数价格精度
      "order_type": 0,
      // 订单类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单 6：条件平仓 7-带单平仓(跟单者被动平仓 8-带单止盈 9-带单止损 101:自主平仓 102:带单平仓 103:止盈平仓 104:止损平仓 105:强制平仓 106:带单止盈 107:带单止损 108:带单开仓
      "trade_time": **********,
      // 成交时间
      "is_trader": false,
      // 是否是交易员
      "entrust_mode": 3,
      // 委托模式，1-对手价 2-最优3挡 3-最优5挡
      "entrust_type": 0,
      // 委托类型，0-市价单 1-限价单 2-强平单
      "entrust_volume": 4,
      // 委托数量
      "entrust_status": 200,
      // 委托状态 0-未成交 100-用户撤销 101-市价未成已撤 102-后台撤销 200-全部成交 201-部分成交已撤
      "share_resource": {
        "image_background": "https://www.officeqb.com/res/share_background_20200521172816.png",
        // 背景图
        "title_color": "#111111",
        // 标题字色
        "title_vertical": 560,
        // 标题垂直位置
        "content_color": "#333333",
        // 文本区字色
        "title_text": "距离翻盘，只有一步之遥",
        // 分享标题
        "title_size": 18
        // 标题字号
      }
    }
  ]
}
```

### 78 获取我的交易员列表

- URL: http://{host_addr}/v1/follow/my/dealer/list
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "trader_uid": 180, // 交易员用户id
            "avatar": "https://baidu.com/bg.png", // 交易员头像(空字符串为用户还未设置头像,使用默认)
            "nickname": "小伙子", // 交易员昵称
            "max_amount": "10000", // 最大授权使用本金(0不限制)
            "total_amount": "100000", // 累计使用本金
            "profit_loss": "-2.90182", // 净收益
            "rebate": "10" // 分佣比例
        }
    ]  
}
```

### 79 获取我的跟单头部数据

- URL: http://{host_addr}/v1/follow/my/profit
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        "total_proceeds": "0", // 累计净收益
        "total_principal": "0" // 累计使用本金
    } 
}
```

### 80 当前带单跟单列表

- URL: http://{host_addr}/v1/follow/current
- Method: POST
- Request Body:

```json
{
  "data": {
      "contract_code": "BSVUSDT", // 合约,传空字符串为不过滤
      "page": 0, // 页码(从0开始)
      "count": 20 // 单页数据量(最大100)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
      "total": 1, // 当前条件下总条数(只有page是0且用户是交易员时返回,更换合约过滤条件时需要重新获取)
      "available": "123.123", // 可用余额
      "list": [
        {
            "id": 10000001, // 持仓id
            "contract_code": "BSVUSDT", // 合约代码
            "contract_name": "BSVUSDT永续", // 合约中文名
            "contract_name_en": "BSVUSDT", // 合约英文名
            "side": "S", // 持仓方向
            "contract_index": 153.24, // 合约指数
            "volume": 124, // 持仓张数
            "lever": 10, // 杠杆
            "trader_uid": 180, // 交易员uid
            "trader_nickname": "小伙子", // 交易员名字
            "follow_position_id": 0, // 对应带单订单id
            "limit": "0", // 止盈价
            "stop": "0", // 止损价
            "price": "123", // 成交价格
            "margin": "0", // 保证金
            "profit_ratio": "123", // 收益率
            "force_price": "123", // 强平价
            "float_profit": "-3804.32", // 收益
            "funding_amount": "0", // 资金费用
            "index_precision": 5, // 合约指数价格精度
            "open_position_time": 1602342341, // 开仓时间
            "share_resource": {
                "image_background": "https://www.officeqb.com/res/share_background_20200521172816.png", // 背景图
                "title_color": "#111111", // 标题字色
                "title_vertical": 560, // 标题垂直位置
                "content_color": "#333333", // 文本区字色
                "title_text": "本王，已经赢得了天下!", // 文案
                "title_size": 18 // 文案字号
            }
        }
    ]
  }
}
```

### 81 历史带单跟单

- URL: http://{host_addr}/v1/follow/history
- Method: POST
- Request Body:

```json
{
  "data": {
      "contract_code": "BTCUSDT" // 合约,传空字符串为不过滤
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "id": 100004, // 订单id
            "contract_code": "BTCUSDT", // 合约代码
            "contract_name": "BTCUSDT永续", // 合约中文名
            "contract_name_en": "BTCUSDTSwap", // 合约英文名
            "par_value": "100", // 合约面值
            "side": "B", // 合约方向
            "close_order_type": 6, // 类型 0: 市价单 1：计划单 2：止盈单 4：止损单 5：强平单 6：条件平仓 7-带单平仓(跟单者被动平仓 8-带单止盈 9-带单止损 101:自主平仓 102:带单平仓 103:止盈平仓 104:止损平仓 105:强制平仓 106:带单止盈 107:带单止损 108:带单开仓
            "entrust_type": 0, // 委托类型，0-市价单 1-限价单 2-强平单
            "volume": 123, // 张数
            "trader_uid": 180, // 交易员uid
            "follow_position_id": 88888, // 对应带单订单id
            "trader_nickname": "小伙子", // 交易员昵称
            "lever": 20, // 杠杆
            "open_price": "123.321", // 开仓价
            "close_price": "234.432", // 平仓价
            "init_margin": "124", // 保证金
            "close_profit": "901.9", // 收益
            "close_profit_ratio": "7.2734", // 收益率
            "commission": "1.1", // 手续费
            "funding_amount": "3", // 资金费用
            "withholding": "100", // 预扣分佣
            "index_precision": 5, // 合约指数价格精度
            "open_time": 1604541775, // 开仓时间
            "close_time": 1604542318, // 平仓时间
            "share_resource": {
                "image_background": "https://www.officeqb.com/res/share_background_20200521172816.png", // 背景图
                "title_color": "#111111", // 标题字色
                "title_vertical": 560, // 标题垂直位置
                "content_color": "#333333", // 文本区字色
                "title_text": "你已经是那最牛逼的万分之一!", // 文案
                "title_size": 18 // 文案字号
            }
        }
    ]
}
```

### 82 获取带单跟单头部数据

- URL: http://{host_addr}/v1/follow/head
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "is_trader": false, // 是否是交易员
    "withholding": "0", // 预计今日分润
    "total_brokerage": "0", // 累计分润
    "day_profit": "123.2412", // 今日盈亏
    "day_profit_ratio": "5.1232" // 今日收益率
  }
}
```

### 83 交易员列表

- URL: http://{host_addr}/v1/follow/dealer/list
- Method: POST
- Request Body:

```json
{
  "data": {
      "nickname": "子", // 昵称关键字
      "sort_type": 0, // 排序规则 0-综合排名 1-近期收益率 2-累计收益 3-近期胜率 4-交易量 5-总收益率
      "page": 0, // 页码,从0开始
      "count": 100 // 单页数据量
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "trader_id": 180, // 交易员id
            "avatar": "https://baidu.com/bg.png", // 头像
            "nickname": "小伙子", // 昵称
            "introduce": "中文简介", // 简介
            "profit_ratio": "-0.6916", // 近期收益率
            "total_profit": "-149.0876", // 累计收益
            "win_rate": "0", // 近期胜率
            "follow_number": 0, // 跟单人数
            "follow_limit": 10, // 跟单限制人数
            "following": true // 当前是否跟随了该交易员
        }
    ]
}
```

### 84 交易员详情

- URL: http://{host_addr}/v1/follow/dealer/detail
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180 // 交易员id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
        "user_id": 180, // 用户id
        "avatar": "https://baidu.com/bg.png", // 头像
        "nickname": "小伙子", // 昵称
        "introduce": "中文简介", // 介绍
        "follow_number": 0, // 跟随人数
        "history_follow_number": 0, // 历史跟随人数
        "follow_limit": 10, // 跟随限制人数
        "limit_follow_contracts": ["BTCUSDT", "ETHUSDT"], // 跟随限制合约
        "limit_follow_mode": 3, // 跟单方式 1-固定张数 2-固定比例 多种相加
        "limit_follow_threshold": "0", // 跟单最低本金 0为不限制
        "total_win": 0, // 总盈利笔数
        "total_loss": 17, // 总亏损笔数
        "stay_days": 3, // 入驻天数
        "rebate": "10", // 分佣比例
        "recent_profit_rate": "-0.6916", // 近期收益率
        "total_profit": "-149.0876", // 总收益
        "recent_win_rate": "0", // 近期胜率
        "total_profit_rate": "-0.797", // 总盈利率
        "adept_contracts": "BTCUSDT,ETHUSDT,XRPUSDT", // 擅长合约
        "following": false, // 当前是否跟随中
        "min_multiple": "0.5", // 最小跟单比例
        "max_multiple": "200", // 最大跟单比例
        "min_number": 1, // 最小跟单固定张数
        "max_number": 10000 // 最大跟单固定张数
    }
}
```

### 85 交易员历史带单

- URL: http://{host_addr}/v1/follow/dealer/order/history
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180, // 交易员id
      "count": 20 // 请求数据量(最大100)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "id": 187891723744059392, // 订单id
            "contract_code": "EOSUSDT", // 合约代码
            "contract_name": "EOSUSDT永续", // 合约中文名
            "contract_name_en": "EOSUSDT", // 合约英文名
            "side": "B", // 方向
            "lever": 50, // 杠杆
            "open_price": "2.512", // 开仓价
            "close_price": "2.465", // 平仓价
            "close_profit_ratio": "-1.07", // 收益率
            "index_precision": 5, // 合约指数价格精度
            "open_time": 1604542859, // 开仓时间
            "close_time": 1604576566 // 平仓时间
        }
    ]
}
```

### 86 交易员当前带单

- URL: http://{host_addr}/v1/follow/dealer/order/current
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180, // 交易员id
      "page": 0, // 页码(从0开始)
      "count": 100 // 单页数据量(最大100)
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "id": 187867399993696256, // 订单id
            "contract_code": "EOSUSDT", // 合约代码
            "contract_name": "EOSUSDT永续", // 合约中文名
            "contract_name_en": "EOSUSDT", // 合约英文名
            "side": "B", // 方向
            "lever": 50, // 杠杆
            "open_price": "2.501", // 开仓价
            "profit_ratio": "0.12", // 收益率
            "contract_index": "2.485", // 当前合约指数(价格)
            "index_precision": 5, // 合约指数价格精度
            "open_time": 1604570767 // 开仓时间
        }
    ]
}
```

### 87 交易员跟随者列表

- URL: http://{host_addr}/v1/follow/dealer/followers
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180 // 交易员id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "avatar": "http://baidu.com/bg.png", // 头像
            "nickname": "gendanzhe", // 昵称
            "total_cost": "0", // 累计使用本金
            "total_profit": "-4.35273" // 累计收益
        },
        {
            "avatar": "",
            "nickname": "176****6033",
            "total_cost": "100000",
            "total_profit": "-740.90182"
        }
    ]
}
```

### 88 获取当前跟单设置

- URL: http://{host_addr}/v1/follow/setting/current
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180 // 交易员id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
      "trader_id": 180, // 交易员id
      "quantity": 5, // 下单跟随数量
      "contracts": "BTCUSDT,ETHUSDT", // 跟单合约,逗号分隔
      "proportion": "0.3", // 下单跟随比例
      "stop_profit": "0.8", // 止盈比例
      "stop_loss": "0.5", // 止损比例
      "max_amount": "1000.123", // 最大授权资金
      "limit_follow_contracts": ["BTCUSDT", "ETHUSDT"], // 当前交易员跟随限制合约
      "limit_follow_mode": 3, // 当前交易员限制跟单方式 1-固定张数 2-固定比例 多种相加
      "limit_follow_threshold": "123.123" // 当前交易员限制跟单最低本金 0为不限制
    }
}
```

### 89 跟单设置

- URL: http://{host_addr}/v1/follow/setting
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180, // 交易员id
      "quantity": 5, // 下单跟随数量
      "contracts": "BTCUSDT,ETHUSDT", // 跟单合约,逗号分隔
      "proportion": "0.3", // 下单跟随比例
      "stop_profit": "0.8", // 止盈比例
      "stop_loss": "0.5", // 止损比例
      "max_amount": "1000.123" // 最大授权资金
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": null
}
```

### 90 取消跟单

- URL: http://{host_addr}/v1/follow/exit
- Method: POST
- Request Body:

```json
{
  "data": {
      "trader_id": 180 // 交易员id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": null
}
```

### 91 移除跟随

- URL: http://{host_addr}/v1/follow/remove
- Method: POST
- Request Body:

```json
{
  "data": {
      "user_id": 180 // 跟随者id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": null
}
```

### 92 我的跟随者

- URL: http://{host_addr}/v1/follow/my/followers
- Method: POST
- Request Body:

```json
{
  "data": {
      "page": 0, // 页码 从0开始
      "count": 100 // 单页请求数据量 最大100
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": {
      "follow_number": 100, // 当前带单人数
      "follow_limit": 200, // 总可带单人数
      "next_level": "234.123", // 下个级别需要的总资金数(可用+所有持仓初始保证金), 0为不能继续升级
      "list": [ // 跟随列表
        {
            "user_id": 123451, // 跟随者用户id
            "avatar": "http://baidu.com/bg.png", // 头像,未设置时使用客户端默认
            "nickname": "gendanzhe", // 昵称
            "total_principal": "124.1231", // 跟单总本金
            "follow_time": 1601232342 // 跟随时间
        },
        {
            "user_id": 123412,
            "avatar": "",
            "nickname": "176****6033",
            "total_principal": "1000",
            "follow_time": 1600232342
      }
    ]
  }
}
```

### 93 获取带单分润统计(日数据)

- URL: http://{host_addr}/v1/follow/lead/profit
- Method: POST
- Request Body:

```json
{
  "data": {
      "count": 90 // 请求天数
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "total_profit": "111.11", // 实际分润
            "win_profit": "234.56", // 盈利分润
            "loss_profit": "123.45", // 亏损退回
            "time": ********** // 时间
        }
    ]
}
```

### 94 获取带单分润详情

- URL: http://{host_addr}/v1/follow/lead/profit/detail
- Method: POST
- Request Body:

```json
{
  "data": {
      "own_day": **********, // 归属日期秒时间戳
      "page": 0, // 页码 从0开始
      "count": 100 // 单页请求数据量 最大100
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "nickname": "gendanzhe", // 昵称
            "profit": "4.35273" // 分润
        }
    ]
}
```

### 95 获取带单跟单消息

- URL: http://{host_addr}/v1/follow/message
- Method: POST
- Request Body:

```json
{
  "data": {
      "page": 0, // 页码 从0开始
      "count": 100 // 单页请求数据量 最大100
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": [
        {
            "id": 123124124125123, // 消息id
            "nickname": "gendanzhe", // 昵称
            "category": 1, // 类型 1-开仓成功 2-开仓失败 3-平仓成功 4-平仓失败 5-爆仓 6-停止跟随 7-获取返佣 8-审核成功 9-审核失败 10-身份停用 11-身份撤销
            "title": "xxx", // 标题
            "content": "xxxxxxx", // 内容
            "is_readed": false, // 是否已读
            "create_time": ********** // 创建时间
        }
    ]
}
```

### 96 标记消息已读

- URL: http://{host_addr}/v1/follow/mark/message/readed
- Method: POST
- Request Body:

```json
{
  "data": {
      "id_list": [ // 如果传空列表会将所有当前用户下的消息标记为已读
        341342533252351235,
        341342533252351236,
        341342533252351237
      ]
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
    "ret": 0,
    "msg": "",
    "data": null
}
```

### 97 带单开仓

- URL: http://{host_addr}/v1/follow/order/place
- Method: POST
- Request Body:

```json
{
    "data": {
        "contract_code": "BTCUSD", // 合约code
        "side": "B", // 方向 B买 S卖
        "future_price": "10000.01", //预估成交价
        "amount": 10,//张数
        "leverage": 10, // 杠杠倍数
        "ncr": "random string", //随机字符串
        "mode": 3, // 下单模式，1-对手价 2-最优3挡 3-最优5挡
        "limit": "100000.123", // 止盈价
        "stop": "1000.123", // 止损价
        "trigger_type": 0, // 止盈止损触发标的 0-成交价,1-标记价格
    "entrust_type": 0, // 委托类型 0-市价 1-限价
    "price": "123.123" // 委托价格;限价单传递价格
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
      "available": "39.20" //可用余额
  }
}
```

### 98 带单跟单平仓

- URL: http://{host_addr}/v1/follow/position/closeout
- Method: POST
- Request Body:

```json
{
  "data": {
        "position_id": 123456, // 持仓id
    "ncr": "random string",
    //随机字符串
    "mode": 3,
    // 下单模式，1-对手价 2-最优3挡 3-最优5挡
    "fund_password": "dfieIdUUDKe732DH2eoj88Udf="
    // 资金密码(加密后)
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 99 带单跟单设置止盈止损

- 请根据指数价格校验参数是否有效，服务端只校验数据是否合法
- URL: http://{host_addr}/v1/follow/position/set/stop
- Method: POST
- Request Body:

```json
{
  "data": {
        "position_id": 123456, // 持仓id
        "limit":"39.29", //止盈价 "0"为取消
        "stop":"39.29", //止损价 "0"为取消
        "fund_password": "dfieIdUUDKe732DH2eoj88Udf=" // 资金密码(加密后)
  },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 100 同意交易协议

- URL: http://{host_addr}/v1/trade/license/agree
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 101 提交提币认证信息

- URL: http://{host_addr}/v1/user/verify/withdraw
- Method: POST
- Request Body:

```json
{
  "data": {
    "proto_buf": [253, 13, 48, 23, 55, 23] // protoBuf数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 102 h5提交提币认证信息

- URL: http://{host_addr}/v1/user/verify/withdraw/h5
- Method: POST
- Request Body:

```json
{
  "data": {
    "base64_image": "data:image/png;base64,iVBORw0..." // base64_image数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 103 用户开关控制

- URL: http://{host_addr}/v1/user/switch/modify
- Method: POST
- Request Body:

| 选项           | 备注     | 值                 |
|:------------ |:------ |:----------------- |
| trade_confirm | 交易二次确认开关 | 0-关闭二次确认 1-开启二次确认 |
| is_split_position | 是否开启分仓模式 | 0-关闭分仓模式 1-开启分仓模式 |

```json
{
  "data": {
    "action": "trade_confirm", // 开关项 当前仅支持交易二次确认开关
    "open_fix": 1 // 是否开启, 0-关闭 1-开启
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 104 全部平仓

- URL: http://{host_addr}/v1/position/closeout/all
- Method: POST
- Request Body:

```json
{
  "data": {
        "contract_code": "BTCUSDT" // 传值则只平指定合约下的持仓,否则平所有合约
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
  }
}
```

### 105 获取活动信息

- URL: http://{host_addr}/v1/activity/trade/info
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
       "current_info": { //当前活动信息
            "start_time": "2021-01-22 08:00:00", //活动开始时间
            "end_time": "2021-01-23 08:00:00",//活动结束时间
            "opening_time" : "2021-01-23 10:00:00",//开奖时间
            "current_bonus" : 13831.01,//当前奖金
            "my_traders" : 13,//我的交易次数
            "start_day": "2021-01-22 08:00:00", //活动开始日期
            "end_day": "2021-01-24 09:00:00",//活动结束日期
         },
          "acstageia": { //上期交易信息
                   "hash": "0x69276e71Ef93086caF69d8F901F69dbD35382699", //交易hash
                   "myward":[//我的中奖记录
                           { 
                             "amount": 272, //累计获奖金额
                             "frequency": 11,//获奖次数
                             "grade" : 3  //获奖等级  1一等奖，2二等奖，3三等奖
                            },
                            { 
                             "amount": 212, //累计获奖金额
                             "frequency": 10,//获奖次数
                             "grade" : 2  //获奖等级  1一等奖，2二等奖，3三等奖
                            }
                          ]                       
               },
       "wardlist":[//获奖排行
                    { 
                        "amount": 272, //获奖金额
                        "user_name": "134233****22"//用户名
                      },
                      { 
                         "amount": 272, //获奖金额
                         "user_name": "134233****22"//用户名
                      }
                   ] 
  }
}
```

### 106 获取我的中奖记录

- URL: http://{host_addr}/v1/activity/trade/myward
- Method: POST
- Request Body:

```json
{
 "data": {
        "pageNo": 1, //页数 
        "pagesize": 10 //数量
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
       "total": 20, //总数量
       "list":[ //列表
               { 
                  "bonus": 272, //获奖金额
                  "id": 112, //数据id
                  "grade": 1, //获奖等级 1 一等奖，2 二等奖 ，3 三等奖
                  "check_status": 1, //状态   -1待领取，0待审核，1审核通过 2拒绝，3过期
                  "open_time": "2021-01-23 10:10:00", //开奖时间
                  "order_id": "18391040111345" //订单id
                },
                 { 
                   "bonus": 272, //获奖金额
                   "id": 112, //数据id
                   "grade": 1, //获奖等级 1 一等奖，2 二等奖 ，3 三等奖
                   "check_status": 1, //状态   -1待领取，0待审核，1审核通过 2拒绝，3过期
                   "open_time": "2021-01-23 10:10:00", //开奖时间
                   "order_id": "18391040111345" //订单id
                 }
        ] 
  }
}
```

### 107 确定分享图片

- URL: http://{host_addr}/v1/activity/trade/upshare
- Method: POST
- Request Body:

```json
{
 "data": {
        "id": 10, //数据id
        "imgurl": "https://5h9tdj.pickupjade.com/static/hot.png" //图片地址， 用上传图片file服务上传图片，
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
 }
}
```

### 108 获取止盈止损列表

- URL: http://{host_addr}/v1/position/set/list
- Method: POST
- Request Body:

```json
{
 "data": {
        "plan_order_id": 123456789012345678 // 持仓id
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "plan_close_order_id": 123456789012345678, // 止盈止损记录id
      "amount": 12, // 张数
      "limit": "1234.23", // 止盈价
      "stop": "1234.33", // 止损价
      "entrust_limit":"0" // 止盈委托价 0为最优五档 大于0为限价
      "entrust_stop":"123.123" // 止损委托价 0为最优五档 大于0为限价
      "create_time": 1234567890 // 创建时间(秒级10位时间戳)
    }
  ]
}
```

### 109 删除止盈止损

- URL: http://{host_addr}/v1/position/cancel/stop
- Method: POST
- Request Body:

```json
{
 "data": {
   "plan_close_order_id": 123456789012345678, // 止盈止损记录id
   "fund_password": "dfieIdUUDKe732DH2eoj88Udf=" // 资金密码(加密后)
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 110 联系我们

- URL: http://{host_addr}/v1/common/contactus
- Method: POST
- Request Body:

```json
{
  "data": {
    "real_name": "老王", // 姓名
    "mobile": "***********", // 手机号
    "email": "<EMAIL>", // 邮箱
    "content": "高价回收旧家电", // 备注
    "captcha_id": "dfieirjiesdfad", // 易盾captchaID
    "validate": "dsfdsafjkjkcvijsdi...", // 易盾返回的二次校验数据
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 111 安全中心绑定或更换手机邮箱账号

- URL: http://{host_addr}/v1/user/safe/account/modify
- Method: POST
- Request Body:

```json
{
 "data": {
   "new_account": "***********", // 新绑定账号(手机号/邮箱)
   "area_code": "86", // 新手机区号,邮箱不需要
   "country_code": "CN", // 新手机国家代码,邮箱不需要
   "code": "123456", // 新绑定账号验证码
   "login_verify": 1, // 是否开启登录二次验证 0-关 1-开
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 112 安全中心修改登录密码

- URL: http://{host_addr}/v1/user/safe/password/login/modify
- Method: POST
- Request Body:

```json
{
 "data": {
   "new_password": "Iddeg823kdfWEEf832Doe=", // 新密码(加密后)
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 113 设置或修改资金密码

- URL: http://{host_addr}/v1/user/safe/password/fund/modify
- Method: POST
- Request Body:

```json
{
 "data": {
   "new_password": "Iddeg823kdfWEEf832Doe=", // 新密码(加密后)
   "verify_type": 0, // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 114 修改资金密码验证时效

- URL: http://{host_addr}/v1/user/safe/password/fund/timeliness
- Method: POST
- Request Body:

```json
{
 "data": {
   "verify_type": 0, // 验证类型(修改资金密码使用),0-永不验证 1-24小时内验证一次 2-永远验证
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 115 获取验证器私钥

- URL: http://{host_addr}/v1/user/safe/totp/secret/get
- Method: POST
- Request Body:

```json
{
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "secret": "IDH73NWMIDH73NWM" // 验证器私钥
  }
}
```

### 116 安全中心绑定或更换验证器

- URL: http://{host_addr}/v1/user/safe/totp/modify
- Method: POST
- Request Body:

```json
{
 "data": {
   "code": "123456", // 新绑定令牌验证码
   "login_verify": 1, // 是否开启登录二次验证 0-关 1-开
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 117 修改登录验证开关

- URL: http://{host_addr}/v1/user/safe/login/verify/switch
- Method: POST
- Request Body:

```json
{
 "data": {
   "verify_phone": 1, // 是否开启登录二次验证(手机) 0-关 1-开
   "verify_email": 1, // 是否开启登录二次验证(邮箱) 0-关 1-开
   "verify_totp": 1, // 是否开启登录二次验证(令牌) 0-关 1-开
   "phone_code": "123456" // 两步验证 手机验证码(旧账号)
   "email_code": "123456" // 两步验证 邮箱验证码(旧账号)
   "totp_code": "123456" // 两步验证 令牌验证码
 },
  "api_version": "V2", // api版本(V2增加资金密码)
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 118 用户收款账户列表

- URL: http://{host_addr}/v1/user/payment/list
- Method: POST
- Request Body:

```json
{
  "data": {
  },
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": 1,
      //收款账户id标识
      "type": 1,
      // 收款方式，1-银行卡 2-支付宝
      "bank_name": "中国银行",
      // 开户行
      "bank_branch_name": "中关村支行",
      // 开户行支行
      "bank_numb": "988888888888888888",
      //收款账户（银行卡号、支付宝账户）
      "account_holder": "赵云",
      //收款人
      "ocr_address": "/img/url",
      //二维码地址
      "invalid": false
      //是否置为无效状态 true为无效账户
    }
  ]
}
```

### 119 用户收款账户添加

- URL: http://{host_addr}/v1/user/payment/add
- Method: POST
- Request Body:

```json
{
  "data": {
    "type": 1,
    // 收款方式，1-银行卡 2-支付宝 3-微信
    "bank_name": "中国银行",
    // 开户行
    "bank_branch_name": "中关村支行",
    // 开户行支行
    "bank_numb": "988888888888888888",
    //收款账户（银行卡号、支付宝账户）
    "account_holder": "赵云",
    //收款人
    "ocr_address": "/img/url",
    //二维码地址
    "invalid": false
    //是否置为无效状态 true为无效账户
  },
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": 1,
    //收款账户id标识
    "type": 1,
    // 收款方式，1-银行卡 2-支付宝 3-微信
    "bank_name": "中国银行",
    // 开户行
    "bank_branch_name": "中关村支行",
    // 开户行支行
    "bank_numb": "988888888888888888",
    //收款账户（银行卡号、支付宝账户）
    "account_holder": "赵云",
    //收款人
    "ocr_address": "/img/url",
    //二维码地址
    "invalid": false
    //是否置为无效状态 true为无效账户
  }
}
```

### 120 用户收款账户修改

- URL: http://{host_addr}/v1/user/payment/edit
- Method: POST
- Request Body:

```json
{
  "data": {
    "id": 1,
    //收款账户id标识
    "type": 1,
    // 收款方式，1-银行卡 2-支付宝 3-微信
    "bank_name": "中国银行",
    // 开户行
    "bank_branch_name": "中关村支行",
    // 开户行支行
    "bank_numb": "988888888888888888",
    //收款账户（银行卡号、支付宝账户）
    "account_holder": "赵云",
    //收款人
    "ocr_address": "/img/url",
    //二维码地址
    "invalid": false
    //是否置为无效状态 true为无效账户
  },
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
  }
}
```

### 121 用户收款账户移除

- URL: http://{host_addr}/v1/user/payment/del
- Method: POST
- Request Body:

```json
{
  "data": {
    "id": 1
    //收款账户id标识
  },
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
  }
}
```

### 122 法币获取下单汇率

- URL: http://{host_addr}/v1/legal/exchange/rate
- Method: POST
- Request Body:

```json
{
  "data": {
    "side": 2,
    // 下单方向 1-卖 2买
    "mode": 0,
    // 下单输入类型 0-输入法币下单 1-输入数字币下单
    "coin_name": "USDT",
    // 币种名称
    "amount": "100.23"
    // 下单数量/下单金额
  },
  "device": "XIAOMI MIX 2S",
  // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  // 设备id
  "version": "10",
  // 数字版本号
  "req_os": 1,
  // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0,
  // 语言类型 0-中文 1-英文
  "ts": **********,
  // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591"
  // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "business_id": 5,
    //接单商户Id
    "exchange_rate": "6.72",
    //实时汇率
    "rate_token": "abcdefghijklmnopqrstuvwxy"
    //汇率下单token,
    "amount": "1",
    //下单币的数量
    "amount_cn": "20.90"
    //人民币数量
  }
}
```

### 123 资金密码校验

- URL: http://{host_addr}/v1/user/safe/password/fund/verify
- Method: POST
- Request Body:

| 字段       | 必选  | 类型     | 备注        |
|:-------- |:--- |:------ |:--------- |
| password | 是   | string | 资金密码(加密后) |

```json
{
  "data": {
    "password": "IDMVIejDFE53KGEidn5912k7d2kdfRd="
  }
}
```

- Response Body:

| 字段        | 类型     | 备注         |
|:--------- |:------ |:---------- |
| verify_id | string | 资金密码校验标记id |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "verify_id": 123124125125125
  }
}
```

### 124 获取用户配额

- URL: http://{host_addr}/v1/user/quota
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "withdraw_max_quota": "600", // 本次最大提币配额(需要和资产详情中的可提余额做对比,如果可提余额小于该值那么限制最大输入为可提余额, 否则限制为最大输入该值)
    "withdraw_once": "10000", // 提币单笔限额
    "withdraw_daily": "20000", // 提币每日限额
    "legal_quota_min_cny": "100", // 法币交易单笔最小限额(CNY)
    "legal_quota_min_usdt": "20", // 法币交易单笔最小限额(USDT)
    "legal_quota_cny": "50000", // 法币交易单笔限额(CNY)
    "legal_quota_usdt": "7000" // 法币交易单笔限额(USDT)
  }
}
```

### 125 提交人工信息认证照片

- URL: http://{host_addr}/v1/user/verify/manual
- Method: POST
- Request Body:

```json
{
  "data": {
    "avatar_photo": "https://..../***.jpg", // 证件照片(人像面)--应对图片进行压缩,保证图片在1.5m以下
    "emblem_proto": "https://..../***.jpg", // 证件照片(国徽面)--应对图片进行压缩,保证图片在1.5m以下
    "hold_proto": "https://..../***.jpg" // 手持证件照--应对图片进行压缩,保证图片在1.5m以下
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 126 模拟账户资产减少

- URL: http://{host_addr}/v1/asset/decrease
- Method: POST
- Request Body:

```json
{
  "data": {
    "amount": "1002.2324" // 数量
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 127 关于社群

- URL: http://{host_addr}/v1/common/about
- Method: POST
- Request Body:

```json
{
  "platform_id": 1, // 平台id
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "support_email": "<EMAIL>", // 客服邮箱
    "communities": [ // 社群列表
      {
        "name": "Wechat", // 社群名称
        "icon": "https://www.officeqb.com/res/weixin.png", // 社群图标
        "account": "pandafe21", // 社群对应账号
        "link": "https://www.officeqb.com/res/wechat_pandafe21_qrcode.png", // 社群链接,微信为二维码需特殊处理
        "is_wechat": true // 是否是微信
      },
      {
        "name": "Weibo",
        "icon": "https://www.officeqb.com/res/weibo.png", // 社群图标
        "account": "Pandafe",
        "link": "https://weibo.com/Pandafe",
        "is_wechat": false
      },
      {
        "name": "Twitte",
        "icon": "https://www.officeqb.com/res/twitter.png", // 社群图标
        "account": "PandaFeOthcia",
        "link": "https://twitter.com/PandaFeOhcial",
        "is_wechat": false
      },
      {
        "name": "Facebook",
        "icon": "https://www.officeqb.com/res/facebook.png", // 社群图标
        "account": "pandafe2013",
        "link": "https://www.facebook.com/pandafe2013",
        "is_wechat": false
      },
      {
        "name": "Reddit",
        "icon": "https://www.officeqb.com/res/reddit.png", // 社群图标
        "account": "pandafe",
        "link": "https://www.reddit.com/settings/prohle",
        "is_wechat": false
      }
    ]
  }
}
```

### 128 测试接口服务器

- URL: http://{host_addr}/v1/ping
- Method: POST
- Request Body:

```json
{
  "platform_id": 1, // 平台id
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 129 用户选项控制

- URL: http://{host_addr}/v1/user/option/modify
- Method: POST
- Request Body:

| 选项           | 备注     | 值                 |
|:------------ |:------ |:----------------- |
| change_style | 涨跌样式   | 0-绿涨红跌 1-红涨绿跌     |
| profit_style | 盈亏计算方式 | 0-使用标记价格 1-使用成交价格 |

```json
{
  "data": {
    "action": "change_style", // 选项
    "option": 1 // 选项值
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 130 用户API列表获取

- URL: http://{host_addr}/v1/user/api/list
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

| 字段          | 类型      | 备注         |
|:----------- |:------- |:---------- |
| id          | integer | 记录id       |
| app_name    | string  | 备注信息       |
| app_key     | string  | 公钥         |
| auth_ips    | string  | 绑定ip,逗号分隔  |
| create_time | integer | 创建时间,秒级时间戳 |
| state       | integer | 0-正常 2-过期  |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": 2,
      "app_name": "test",
      "app_key": "B9Aa2******",
      "auth_ips": "***************,2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
      "create_time": 1618911566,
      "state": 0
    }
  ]
}
```

### 131 用户API详情获取

- URL: http://{host_addr}/v1/user/api/detail
- Method: POST
- Request Body:

| 字段         | 类型      | 备注         |
|:---------- |:------- |:---------- |
| id         | integer | 记录id       |
| phone_code | string  | 两步验证 手机验证码 |
| email_code | string  | 两步验证 邮箱验证码 |
| totp_code  | string  | 两步验证 令牌验证码 |

```json
{
  "data": {
    "id": 3,
    "phone_code": "123456",
    "email_code": "123456",
    "totp_code": "123456"
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

| 字段          | 类型      | 备注         |
|:----------- |:------- |:---------- |
| id          | integer | 记录id       |
| app_name    | string  | 备注信息       |
| app_key     | string  | 公钥         |
| app_secret  | string  | 私钥         |
| auth_ips    | string  | 绑定ip,逗号分隔  |
| create_time | integer | 创建时间,秒级时间戳 |
| state       | integer | 0-正常 2-过期  |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": 3,
    "app_name": "test",
    "app_key": "827deB530B8AaC4d37E77D1444Af979C",
    "app_secret": "6Fa9954B56Ca19818FB639B1D0c69Ffd762D8d97e46A27B4a8f318c2487c4593",
    "auth_ips": "***************,2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
    "create_time": 1618914427,
    "state": 0
  }
}
```

### 132 用户API创建

- URL: http://{host_addr}/v1/user/api/create
- Method: POST
- Request Body:

| 字段         | 类型     | 备注                                |
|:---------- |:------ |:--------------------------------- |
| remark     | string | 备注信息                              |
| ip_list    | string | 绑定ip列表,英文逗号分隔,最多支持10个IP,且IPv6最多2个 |
| phone_code | string | 两步验证 手机验证码                        |
| email_code | string | 两步验证 邮箱验证码                        |
| totp_code  | string | 两步验证 令牌验证码                        |

```json
{
  "data": {
    "remark": "测试",
    "ip_list": "***************,2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
    "phone_code": "123456",
    "email_code": "123456",
    "totp_code": "123456"
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 133 用户API更新

- URL: http://{host_addr}/v1/user/api/modify
- Method: POST
- Request Body:

| 字段         | 类型      | 备注                                |
|:---------- |:------- |:--------------------------------- |
| id         | integer | 记录id                              |
| remark     | string  | 备注信息                              |
| ip_list    | string  | 绑定ip列表,英文逗号分隔,最多支持10个IP,且IPv6最多2个 |
| phone_code | string  | 两步验证 手机验证码                        |
| email_code | string  | 两步验证 邮箱验证码                        |
| totp_code  | string  | 两步验证 令牌验证码                        |

```json
{
  "data": {
    "id": 3,
    "remark": "测试",
    "ip_list": "***************,2001:0db8:3c4d:0015:0000:0000:1a2f:1a2b",
    "phone_code": "123456",
    "email_code": "123456",
    "totp_code": "123456"
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 134 用户API删除

- URL: http://{host_addr}/v1/user/api/delete
- Method: POST
- Request Body:

| 字段         | 类型      | 备注         |
|:---------- |:------- |:---------- |
| id         | integer | 记录id       |
| phone_code | string  | 两步验证 手机验证码 |
| email_code | string  | 两步验证 邮箱验证码 |
| totp_code  | string  | 两步验证 令牌验证码 |

```json
{
  "data": {
    "id": 3,
    "phone_code": "123456",
    "email_code": "123456",
    "totp_code": "123456"
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 135 服务器配置信息

- URL: http://{host_addr}/v1/common/serverConfig
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

| 字段                     | 类型      | 备注                            |
|:---------------------- |:------- |:----------------------------- |
| host_list              | array   | 服务器域名列表(后期将废弃该字段)             |
| server_hosts           | array   | 服务器域名列表(新)                    |
| -recommend             | boolean | 是否是推荐域名                       |
| -host_address          | string  | 域名地址                          |
| is_slider              | boolean | 是否启用滑块                        |
| is_third_kyc           | boolean | 是否启用三方kyc                     |
| china_mobile_checking  | boolean | 是否开启国内手机号限制 true-限制+86手机号注册绑定 |
| gesture_interval       | integer | 手势密码验证间隔(分钟)                  |
| public_url             | array   | 公共域名                          |
| private_url            | array   | 私有域名                          |
| max_api_count          | integer | 最大可用api个数                     |
| trader_lately_position | integer | 交易员持仓数据显示                     |
| trader_lately_history  | integer | 交易员历史数据条数                     |
| show_follow_users      | integer | 跟随着盈利从大到小人数                   |
| blowing_rate           | decimal | 爆仓率                           |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "host_list": [
      "api.officeqb.com",
      "api.pandafe.club"
    ],
    "server_hosts": [
      {
        "recommend": true,
        "host_address": "api.officeqb.com"
      }
    ],
    "public_url": "pandafe.com",
    "private_url": "pandafe.cloud",
    "is_slider": true,
    "is_third_kyc": true,
    "china_mobile_checking": false,
    "gesture_interval": 480,
    "max_api_count": 1,
    "trader_lately_position": 50,
    "trader_lately_history": 50,
    "show_follow_users": 50,
    "blowing_rate": "0.5"
  }
}
```

### 136 获取热更新补丁列表

- URL: http://{host_addr}/v1/common/hotfix
- Method: POST
- Request Body:

```json
{
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

| 字段          | 类型      | 备注              |
|:----------- |:------- |:--------------- |
| os_type     | integer | 客户端类型 1-安卓 2-安卓 |
| version     | integer | 客户端版本           |
| patch_name  | string  | 补丁名称            |
| patch_url   | string  | 补丁下载地址          |
| create_time | integer | 创建时间            |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "os_type": 2,
      "version": 1230,
      "patch_name": "PAHomeMyView_1230",
      "patch_url": "https://www.officeqb.com/res/PAHomeMyView_1230_1621482128.js",
      "create_time": 1621482128
    },
    {
      "os_type": 2,
      "version": 1230,
      "patch_name": "PAHomeOtherView_1230",
      "patch_url": "https://www.officeqb.com/res/PAHomeOtherView_1230_1621482130.js",
      "create_time": 1621482130
    }
  ]
}
```

### 137 客户端错误提交

- URL: http://{host_addr}/v1/common/error/submit
- Method: POST
- Request Body:

```json
{
  "data": {
    "list": [
      {
        "error_type": 1, // 错误类型 1-本地异常 2-api response 3-Ws response ... 其它http错误码
        "error_msg": "errdsfasidjgisadjgi", // 报错内容
        "error_time": 1623192234334 // 出错时间(毫秒秒时间戳)
      }
    ]
  },
  "system_os": "IOS 15", // 系统信息
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 138 APP下载统计

- URL: http://{host_addr}/v1/common/download/submit
- Method: POST
- Request Body:

```json
{
  "data": {
    "os": 1, // app客户端 1-安卓 2-ios
    "download_mode": 1, // 下载模式 1-极速下载 2-本地下载 3-本地更新
    "version": "1.2.3" // 下载版本号 (文字版本号: 1.2.3)
  },
  "system_os": "IOS 15", // 系统信息
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 139 当前带单设置

- URL: http://{host_addr}/v1/follow/dealer/setting/current
- Method: POST
- Request Body:

```json
{
  "system_os": "IOS 15", // 系统信息
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "follow_contract": ["BTCUSDT", "ETHUSDT"], // 可跟单合约
    "follow_mode": 1, // 跟单方式 1-固定张数 2-固定比例 多种相加
    "follow_threshold": "123.123122" // 最小跟随本金 0为不限制
  }
}
```

### 140 带单设置

- URL: http://{host_addr}/v1/follow/dealer/setting
- Method: POST
- Request Body:

```json
{
  "data": {
    "follow_contract": ["BTCUSDT", "ETHUSDT"], // 可跟单合约
    "follow_mode": 1, // 跟单方式 1-固定张数 2-固定比例 多种相加
    "follow_threshold": "123.123122" // 最小跟随本金 0为不限制
  },
  "system_os": "IOS 15", // 系统信息
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 141 校验登录密码

- URL: http://{host_addr}/v1/user/safe/password/login/verify
- Method: POST
- Request Body:

```json
{
  "data": {
    "password": "Kd9fn2dsjSD0dfew8cvjs=" // 密码(加密后)
  },
  "system_os": "IOS 15", // 系统信息
  "device": "iPhone XX", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "1230", // 数字版本号
  "req_os": 2, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 142 重要通知

- URL: http://{host_addr}/v1/common/notice/important
- Method: POST
- Request Body:

```json
{
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": ********, // 公告id
      "createdAt": **********, // 公告创建时间
      "title": "淡黄的长裙,蓬松的头发", // 公告标题
      "content": "淡黄的长裙,蓬松的头发" // 公告正文
    }
  ]
}
```

### 143 获取默认提币地址

- URL: http://{host_addr}/v1/asset/withdraw/address/default
- Method: POST
- Request Body:

| 参数名       | 必选  | 类型     | 描述   |
|:--------- |:--- |:------ |:---- |
| coin_name | 是   | string | 币种名称 |

```json
{
  "data": {
    "coin_name": "USDT"
  }
}
```

- Response Body:

| 参数名           | 类型      | 描述      |
|:------------- |:------- |:------- |
| id            | integer | 记录id    |
| user_id       | integer | 用户id    |
| coin_name     | 是       | string  |
| coin_protocol | string  | 币种协议    |
| coin_tag      | string  | 币种标签    |
| addr          | string  | 提币地址    |
| addr_alias    | string  | 地址别名    |
| is_default    | boolean | 是否是默认地址 |
| is_trust      | boolean | 是否是信任地址 |
| create_time   | integer | 创建时间戳   |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "id": 20,
    "user_id": 12312412214,
    "coin_name": "USDT",
    "coin_protocol": "ERC20",
    "coin_tag": "12314521412",
    "addr": "0x2efisjfi32ewifs32383hsdfdaf",
    "addr_alias": "黑洞",
    "is_default": true,
    "is_trust": false,
    "create_time": 1623523521
  }
}
```

### 144 获取提币地址列表

- URL: http://{host_addr}/v1/asset/withdraw/address/list
- Method: POST
- Request Body:

| 参数名       | 必选  | 类型     | 描述                  |
|:--------- |:--- |:------ |:------------------- |
| coin_name | 是   | string | 币种名称(大写)            |
| protocol  | 是   | string | 币种协议 ERC20/TRC20... |

```json
{
  "data": {
    "protocol": "TRC20"
  }
}
```

- Response Body:

| 参数名            | 类型      | 描述             |
|:-------------- |:------- |:-------------- |
| has_default    | boolean | 当前币种是否包含默认提币地址 |
| list           | array   | 数据列表           |
| .id            | integer | 记录id           |
| .user_id       | integer | 用户id           |
| .coin_name     | string  | 币种名称           |
| .coin_protocol | string  | 币种协议           |
| .coin_tag      | string  | 币种标签           |
| .addr          | string  | 提币地址           |
| .addr_alias    | string  | 地址别名           |
| .is_default    | boolean | 是否是默认地址        |
| .is_trust      | boolean | 是否是信任地址        |
| .create_time   | integer | 创建时间戳          |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "has_default": true,
    "list": [
      {
        "id": 20,
        "user_id": 12312412214,
        "coin_name": "USDT",
        "coin_protocol": "ERC20",
        "coin_tag": "12314521412",
        "addr": "0x2efisjfi32ewifs32383hsdfdaf",
        "addr_alias": "黑洞",
        "is_default": true,
        "is_trust": false,
        "create_time": 1623523521
      }
    ]
  }
}
```

### 145 增加提币地址

- URL: http://{host_addr}/v1/asset/withdraw/address/add
- Method: POST
- Request Body:

| 参数名            | 必选  | 类型      | 描述                                      |
|:-------------- |:--- |:------- |:--------------------------------------- |
| verify_id      | 否   | integer | 验证id,如果is_trust为true,那么将校验两步验证返回的id传到这里 |
| coin_name      | 是   | string  | 币种名称                                    |
| coin_protocol  | 是   | string  | 币种协议                                    |
| coin_tag       | 否   | string  | 币种标签                                    |
| addr           | 是   | string  | 提币地址                                    |
| addr_alias     | 是   | string  | 地址别名                                    |
| is_default_num | 是   | integer | 是否是默认地址 0-否 1-是                         |
| is_trust_num   | 是   | integer | 是否是信任地址 0-否 1-是                         |

```json
{
  "data": {
    "verify_id": 3412314324312413241,
    "coin_name": "USDT",
    "coin_protocol": "ERC20",
    "coin_tag": "12314521412",
    "addr": "0x2efisjfi32ewifs32383hsdfdaf",
    "addr_alias": "黑洞",
    "is_default_num": 1,
    "is_trust_num": 0
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 146 删除提币地址

- URL: http://{host_addr}/v1/asset/withdraw/address/del
- Method: POST
- Request Body:

| 参数名 | 必选  | 类型      | 描述   |
|:--- |:--- |:------- |:---- |
| id  | 是   | integer | 记录id |

```json
{
  "data": {
    "id": 32413241
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 147 更新提币地址

- URL: http://{host_addr}/v1/asset/withdraw/address/update
- Method: POST
- Request Body:

| 参数名            | 必选  | 类型      | 描述                                     |
|:-------------- |:--- |:------- |:-------------------------------------- |
| id             | 是   | integer | 记录id                                   |
| verify_id      | 否   | integer | 验证id,如果改动了是否是新人地址开关,那么将校验两步验证返回的id传到这里 |
| coin_tag       | 否   | string  | 币种标签                                   |
| addr           | 是   | string  | 提币地址                                   |
| addr_alias     | 是   | string  | 地址别名                                   |
| is_default_num | 是   | integer | 是否是默认地址 0-否 1-是                        |
| is_trust_num   | 是   | integer | 是否是信任地址 0-否 1-是                        |

```json
{
  "data": {
    "id": 32413241,
    "verify_id": 3412314324312413241,
    "coin_tag": "12314521412",
    "addr": "0x2efisjfi32ewifs32383hsdfdaf",
    "addr_alias": "黑洞",
    "is_default_num": 1,
    "is_trust_num": 0
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 148 验证两步验证

- URL: http://{host_addr}/v1/user/safe/twostep/verify
- Method: POST
- Request Body:

| 参数名        | 必选  | 类型     | 描述         |
|:---------- |:--- |:------ |:---------- |
| phone_code | 否   | string | 两步验证 短信验证码 |
| email_code | 否   | string | 两步验证 邮箱验证码 |
| totp_code  | 否   | string | 两步验证 令牌验证码 |

```json
{
  "data": {
    "phone_code": "242134",
    "email_code": "123123",
    "totp_code": ""
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "verify_id": 1232141223124124
  }
}
```

### 149 新版获取重要通知

- URL: http://{host_addr}/v1/common/notice/important/new
- Method: POST
- Response Body:

| 参数名         | 类型      | 描述                      |
|:----------- |:------- |:----------------------- |
| id          | integer | 通知id                    |
| title       | string  | 标题                      |
| content     | string  | 内容(文字通知时为正文,图片通知时为图片地址) |
| link        | string  | 跳转链接                    |
| notice_type | integer | 通知类型 1-文本通知 2-图片通知      |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "id": 10000001,
      "title": "测试通知",
      "content": "https://x.cn/example.png",
      "link": "https://www.example.com",
      "notice_type": 2
    }
  ]
}
```

### 150 获取当前普通委托

- URL: http://{host_addr}/v1/order/entrust/current
- Method: POST
- Response Body:

| 参数名            | 类型      | 描述                         |
|:-------------- |:------- |:-------------------------- |
| order_id       | string  | 订单id                       |
| contract_code  | string  | 合约代码                       |
| contract_name  | string  | 合约名称                       |
| par_value      | decimal | 合约面值                       |
| side           | string  | 方向(B-买 S-卖)                |
| offset         | string  | 开平方向(O-开 C-平)              |
| account_type   | integer | 账户模式 1：全仓 2：逐仓 3: 带单 4: 跟单 |
| lever          | integer | 杠杆                         |
| state          | integer | 状态 (0-待成交 202-部分成交)        |
| mode           | integer | 委托模式 (1-对手价 2-最优3挡 3-最优5挡) |
| entrust_volume | integer | 委托张数                       |
| trade_volume   | integer | 成交张数                       |
| entrust_time   | integer | 委托时间                       |
| entrust_price  | string  | 委托价格                       |
| limit          | string  | 止盈价格                       |
| stop           | string  | 止损价格                       |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
            "order_id": "311779077889458176",
            "contract_code": "BTCUSDT",
            "contract_name": "BTCUSDT永续",
            "par_value": "100",
            "side": "B",
            "offset": "O",
            "account_type": 1,
            "lever": 20,
            "state": 202,
            "mode": 3,
            "entrust_volume": 10,
            "trade_volume": 3,
            "entrust_time": **********,
            "entrust_price": "123.001",
            "limit": "0",
            "stop": "123.123"
        }
  ]
}
```

### 151 获取当前计划委托

- URL: http://{host_addr}/v1/order/entrust/current/plan
- Method: POST
- Response Body:

| 参数名            | 类型      | 描述                         |
|:-------------- |:------- |:-------------------------- |
| order_id       | string  | 订单id                       |
| contract_code  | string  | 合约代码                       |
| contract_name  | string  | 合约名称                       |
| par_value      | decimal | 合约面值                       |
| side           | string  | 方向(B-买 S-卖)                |
| account_type   | integer | 账户模式 1：全仓 2：逐仓 3: 带单       |
| lever          | integer | 杠杆                         |
| condition      | integer | 触发条件 1 >= 2 <=             |
| entrust_mode   | integer | 委托模式 (1-对手价 2-最优3挡 3-最优5挡) |
| entrust_time   | integer | 委托时间                       |
| trigger_price  | string  | 触发价格                       |
| entrust_price  | string  | 执行价格                       |
| entrust_volume | integer | 委托张数                       |
| limit          | string  | 止盈价格                       |
| stop           | string  | 止损价格                       |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
            "order_id": "311779077889458176",
            "contract_code": "BTCUSDT",
            "contract_name": "BTCUSDT永续",
            "par_value": "100",
            "side": "B",
            "account_type": 1,
            "lever": 20,
            "condition": 1,
            "entrust_volume": 10,
            "entrust_mode": 3,
            "entrust_time": **********,
            "entrust_price": "0",
            "trigger_price": "50123",
            "limit": "0",
            "stop": "0"
        }
  ]
}
```

### 152 获取历史普通委托

- URL: http://{host_addr}/v1/order/entrust/history
- Method: POST
- Request Body:

| 参数名    | 必选  | 类型      | 描述                     |
|:------ |:--- |:------- |:---------------------- |
| offset | 否   | string  | 开平仓方向 O-开仓 C-平仓 不传返回全部 |
| filter | 否   | integer | 筛选方式 0-不筛选 1-只返回已成交数据  |

```json
{
  "data": {
    "offset": "O",
    "filter": 1
  }
}
```

- Response Body:

| 参数名            | 类型      | 描述                                                            |
|:-------------- |:------- |:------------------------------------------------------------- |
| order_id       | string  | 订单id                                                          |
| contract_code  | string  | 合约代码                                                          |
| contract_name  | string  | 合约名称                                                          |
| par_value      | decimal | 合约面值                                                          |
| side           | string  | 方向(B-买 S-卖)                                                   |
| offset         | string  | 开平方向(O-开仓 C-平仓)                                               |
| account_type   | integer | 账户模式 1：全仓 2：逐仓 3: 带单 4: 跟单                                    |
| entrust_type   | integer | 委托类型 0-市价 1-限价                                                |
| mode           | integer | 委托模式 (1-对手价 2-最优3挡 3-最优5挡)                                    |
| order_client   | integer | 委托客户端(1 android端 2 ios端 3 web 4 h5端 5 openApi 6 系统)           |
| lever          | integer | 杠杆                                                            |
| state          | integer | 成交状态 101-未成交已撤 200-全部成交 201-部分成交已撤                            |
| source         | integer | 来源 0-自主下单 1-计划单 2-止盈单 4-止损单 5-强平单 6-条件平仓 7-带单平仓 8-带单止盈 9-带单止损 |
| trade_volume   | integer | 成交张数                                                          |
| entrust_volume | integer | 委托张数                                                          |
| entrust_time   | integer | 委托时间                                                          |
| update_time    | integer | 更新时间                                                          |
| entrust_price  | string  | 委托价格                                                          |
| trade_price    | string  | 成交均价                                                          |
| limit          | string  | 止盈价格                                                          |
| stop           | string  | 止损价格                                                          |
| close_profit   | string  | 平仓收益                                                          |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
            "order_id": "296252701979705344",
            "contract_code": "FILUSDT",
            "contract_name": "FILUSDT永续",
            "par_value": "100",
            "side": "S",
            "offset": "C",
            "account_type": 1,
            "entrust_type": 0,
            "mode": 3,
            "order_client": 1,
            "lever": 20,
            "state": 200,
            "source": 0,
            "trade_volume": 94,
            "entrust_volume": 94,
            "entrust_time": **********,
            "update_time": **********,
            "entrust_price": "0",
            "trade_price": "74.39",
            "limit": "123.123",
            "stop": "0",
            "close_profit": "0"
        }
  ]
}
```

### 153 获取历史计划委托

- URL: http://{host_addr}/v1/order/entrust/history/plan
- Method: POST
- Request Body:

| 参数名    | 必选  | 类型      | 描述                      |
|:------ |:--- |:------- |:----------------------- |
| filter | 否   | integer | 筛选方式 0-不筛选 1-计划单 2-止盈止损 |

```json
{
  "data": {
    "filter": 1
  }
}
```

- Response Body:

| 参数名            | 类型      | 描述                         |
|:-------------- |:------- |:-------------------------- |
| order_id       | string  | 订单id                       |
| contract_code  | string  | 合约代码                       |
| contract_name  | string  | 合约名称                       |
| par_value      | decimal | 合约面值                       |
| side           | string  | 方向(B-买 S-卖)                |
| account_type   | integer | 账户模式 1：全仓 2：逐仓 3: 带单       |
| lever          | integer | 杠杆                         |
| state          | integer | 状态 2-触发成功 3-触发成功,委托失败      |
| entrust_type   | integer | 委托类型 0-市价 1-限价             |
| trigger_index  | integer | 触发标的 0-成交价 1-标记价格          |
| trigger_type   | integer | 类型 1-计划单 2-止盈 4-止损         |
| entrust_mode   | integer | 委托模式 (1-对手价 2-最优3挡 3-最优5挡) |
| entrust_volume | integer | 委托张数                       |
| entrust_time   | integer | 委托时间                       |
| update_time    | integer | 触发时间                       |
| trigger_price  | string  | 触发价格                       |
| entrust_price  | string  | 执行价格                       |
| limit          | string  | 止盈价格                       |
| stop           | string  | 止损价格                       |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
            "order_id": "312135344155787264",
            "contract_code": "BTCUSDT",
            "contract_name": "BTCUSDT永续",
            "par_value": "100",
            "side": "B",
            "account_type": 1,
            "lever": 20,
            "state": 2,
            "entrust_type": 0,
            "trigger_index": 0,
            "trigger_type": 1,
            "entrust_mode": 3,
            "entrust_volume": 10,
            "entrust_time": **********,
            "update_time": **********,
            "trigger_price": "57500",
            "entrust_price": "0",
            "limit": "123.123",
            "stop": "0"
        }
  ]
}
```

### 154 撤销委托

- URL: http://{host_addr}/v1/order/cancel
- Method: POST
- Request Body:

| 参数名 | 必选  | 类型      | 描述     |
|:--- |:--- |:------- |:------ |
| id  | 是   | integer | 委托记录id |

```json
{
  "date": {
    "id": 1241295412959129232
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 155 历史平仓分享数据

- URL: http://{host_addr}/v1/order/entrust/history/share
- Method: POST
- Request Body:

| 参数名 | 必选  | 类型      | 描述     |
|:--- |:--- |:------- |:------ |
| id  | 是   | integer | 委托记录id |

```json
{
  "date": {
    "id": 1241295412959129232
  }
}
```

- Response Body:

| 参数名               | 类型      | 描述                        |
|:----------------- |:------- |:------------------------- |
| contract_code     | string  | 合约代码                      |
| par_value         | decimal | 合约面值                      |
| side              | string  | 多空方向 B-平空 S-平多            |
| open_avg_price    | string  | 开仓均价                      |
| trade_price       | string  | 平仓均价                      |
| close_profit      | string  | 平仓收益                      |
| profit_ratio      | string  | 平仓收益率                     |
| current_price     | string  | 当前价格                      |
| share_resource    | object  | 分享资源（仅在平仓成功数据里返回，其它为NULL） |
| .image_background | string  | 背景图                       |
| .title_color      | string  | 标题颜色                      |
| .title_vertical   | integer | 标题位置                      |
| .content_color    | string  | 内容颜色                      |
| .title_text       | string  | 标题文案                      |
| .title_size       | integer | 标题字号                      |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "contract_code": "BTCUSDT",
    "par_value": "100",
    "side": "B",
    "open_avg_price": "123.123",
    "trade_price": "123.123",
    "close_profit": "12.123",
    "profit_ratio": "10.12",
    "current_price": "123.234",
    "share_resource": {
      "image_background": "https://www.officeqb.com/res/share_background_1631514863_sc.jpeg",
      "title_color": "#FFCE00",
      "title_vertical": 560,
      "content_color": "#333333",
      "title_text": "君子问凶不问吉，高手看盘先看势",
      "title_size": 15
    }
  }
}
```

### 156 绑定备用邮箱

- URL: http://{host_addr}/v1/user/safe/spare/email/modify
- Method: POST
- Request Body:

| 参数名              | 必选  | 类型     | 描述            |
|:---------------- |:--- |:------ |:------------- |
| new_email        | 是   | stirng | 新备用邮箱         |
| code             | 是   | stirng | 新备用邮箱验证码      |
| phone_code       | 否   | stirng | 安全验证-手机验证码    |
| email_code       | 否   | stirng | 安全验证-邮箱验证码    |
| spare_email_code | 否   | stirng | 安全验证-旧备用邮箱验证码 |
| totp_code        | 否   | stirng | 安全验证-验证器验证码   |

```json
{
  "date": {
    "new_email": "<EMAIL>",
    "code": "123941",
    "phone_code": "123941",
    "email_code": "123941",
    "spare_email_code": "123941",
    "totp_code": "123941"
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 157 预兑换

- URL: http://{host_addr}/v1/exchange/order/pre
- Method: POST
- Request Body:

| 参数名    | 必选  | 类型      | 描述       |
|:------ |:--- |:------- |:-------- |
| base   | 是   | string  | 兑出币种     |
| target | 是   | string  | 兑入币种     |
| amount | 是   | decimal | 兑出币种使用数量 |

```json
{
  "data": {
    "base": "USDT",
    "target": "BTC",
    "amount": "123.123"
  }
}
```

- Response Body:

| 参数名            | 类型      | 描述     |
|:-------------- |:------- |:------ |
| pre_order_id   | string  | 预兑换id  |
| base           | string  | 兑出币种   |
| target         | string  | 兑入币种   |
| base_amount    | decimal | 兑出币种数量 |
| target_amount  | decimal | 兑入币种数量 |
| price_rate     | decimal | 单价     |
| fee            | decimal | 手续费    |
| actual_arrival | decimal | 实际到账数量 |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "pre_order_id": "1251257387582783",
    "base": "USDT",
    "target": "BTC",
    "base_amount": "224.1224",
    "target_amount": "0.0412",
    "price_rate": "63312.1",
    "fee": "2.231224",
    "actual_arrival": "0.0411"
  }
}
```

### 158 实际兑换

- URL: http://{host_addr}/v1/exchange/order/actual
- Method: POST
- Request Body:

| 参数名          | 必选  | 类型     | 描述    |
|:------------ |:--- |:------ |:----- |
| pre_order_id | 是   | string | 预兑换id |

```json
{
  "data": {
    "pre_order_id": "1251257387582783"
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 159 新资产查询

- URL: http://{host_addr}/v1/asset/detail/multiple
- Method: POST
- Request Body:

| 参数名  | 必选  | 类型      | 描述                             |
|:---- |:--- |:------- |:------------------------------ |
| mode | 否   | integer | 资产类型 0-全部 1-资产账户 2-交易账户 3=跟单账户 |

```json
{
  "data": {
    "mode": 0
  }
}
```

- Response Body:

| 参数名             | 类型      | 描述                           |
|:--------------- |:------- |:---------------------------- |
| total_usdt      | decimal | 总折合USDT(仅在mode传0时有效)         |
| total_legal     | decimal | 总折合法币价值(仅在mode传0时有效,固定cny)   |
| total_legal_v2  | decimal | 总折合法币价值(仅在mode传0时有效,对应客户端语言) |
| wallet          | object  | 资产账户                         |
| >total_usdt     | decimal | 资产账户总折合USDT                  |
| >total_legal    | decimal | 资产账户总折合客户端语言对应法币(固定cny)      |
| >total_legal_v2 | decimal | 总折合法币价值(仅在mode传0时有效,对应客户端语言) |
| >list           | array   | 资产账户列表                       |
| >>coin_id       | integer | 资产账户币种id                     |
| >>coin_name     | string  | 资产账户币种名称                     |
| >>balance       | decimal | 资产账户可用                       |
| >>frozen        | decimal | 资产账户冻结                       |
| >>can_withdraw  | decimal | 资产账户可提币数量                    |
| trade           | object  | 交易账户                         |
| >total          | decimal | 总资产                          |
| >total_legal    | decimal | 资产账户总折合客户端语言对应法币(固定cny)      |
| >total_legal_v2 | decimal | 总折合法币价值(仅在mode传0时有效,对应客户端语言) |
| >rights         | decimal | 总权益                          |
| >float          | decimal | 总浮动盈亏                        |
| >margin         | decimal | 起始保证金                        |
| >total_margin   | decimal | 总保证金 初始保证金+手续费               |
| >balance        | decimal | 资产                           |
| >available      | decimal | 可用余额                         |
| >lock_amount    | decimal | 委托冻结                         |
| >diff           | decimal | 待结算差值                        |
| >can_transfer   | decimal | 可划转余额                        |
| >blowing_rate   | decimal | 平仓风险率                        |
| >risk_rate      | decimal | 账户分享率                        |
| >total_gift_receive | decimal | 累计入账赠金                        |
| >gift_available   | decimal | 赠金可用                        |
| >gift_balance   | decimal | 赠金                        |
| >gift_used      | decimal | 累计使用赠金 |
| >gift_lock_amount | decimal | 赠金委托冻结 |
| >margin_gift      | decimal | 赠金保证金 |
| follow          | object  | 跟单账户                         |
| >total          | decimal | 总资产                          |
| >total_legal    | decimal | 资产账户总折合客户端语言对应法币(固定cny)      |
| >total_legal_v2 | decimal | 总折合法币价值(仅在mode传0时有效,对应客户端语言) |
| >available      | decimal | 可用余额                         |
| >margin         | decimal | 保证金                          |
| >float          | decimal | 总浮动盈亏                        |
| >can_transfer   | decimal | 可划转余额                        |
| >lock_amount    | decimal | 委托冻结                         |

```json
{
    "ret": 0,
    "msg": "",
    "identifier": "",
    "data": {
        "total_usdt": "16230992.62147",
        "total_legal": "103878352.77",
        "total_legal_v2": "103878352.77",
        "wallet": {
            "total_usdt": "16230992.62147",
            "total_legal": "103878352.77",
            "total_legal_v2": "103878352.77",
            "list": [
                {
                    "coin_id": 1,
                    "coin_name": "BTC",
                    "balance": "101.0001",
                    "frozen": "0",
                    "can_withdraw": "101.0001"
                },
                {
                    "coin_id": 2,
                    "coin_name": "ETH",
                    "balance": "2001.06",
                    "frozen": "0",
                    "can_withdraw": "2001.06"
                },
                {
                    "coin_id": 36,
                    "coin_name": "USDT",
                    "balance": "10001",
                    "frozen": "20",
                    "can_withdraw": "10001"
                }
            ]
        },
        "trade": {
            "total": "0",
            "total_legal": "0",
            "total_legal_v2": "0",
            "rights": "0",
            "float": "0",
            "margin": "0",
            "total_margin": "0",
            "balance": "0",
            "available": "0",
            "can_transfer": "0",
            "lock_amount": "0",
            "diff": "0",
            "blowing_rate": "0.5",
            "risk_rate": "0",
            "total_gift_receive": "123.123",
            "gift_available": "123.123",
            "gift_balance": "123.123",
            "gift_used": "123.123",
            "gift_lock_amount": "123.123",
            "margin_gift": "123.123",
        },
        "follow": {
            "rights": "0",
            "total": "0",
            "total_legal": "0",
            "total_legal_v2": "0",
            "available": "0",
            "margin": "0",
            "float": "0",
            "can_transfer": "0",
            "lock_amount": "0"
        }
    }
}
```

### 160 兑币记录

- URL: http://{host_addr}/v1/exchange/order/history
- Method: POST
- Request Body:

| 参数名   | 必选  | 类型      | 描述                                              |
|:----- |:--- |:------- |:----------------------------------------------- |
| state | 是   | integer | 状态 -1-已结束(包含已取消和已完成) 0-全部 1-未完成 100-已取消 200-已完成 |

```json
{
  "data": {
    "state": 0
  }
}
```

- Response Body:

| 参数名            | 类型      | 描述                          |
|:-------------- |:------- |:--------------------------- |
| from_coin_name | string  | 来源币种名称                      |
| to_coin_name   | string  | 目标币种名称                      |
| from_amount    | decimal | 来源数量                        |
| to_amount      | decimal | 目标数量                        |
| price          | decimal | 单价                          |
| real_amount    | decimal | 到账数量                        |
| refund_amount  | decimal | 退回数量                        |
| fee            | decimal | 手续费                         |
| update_time    | integer | 时间(秒级时间戳)                   |
| id             | string  | 订单号                         |
| state          | integer | 订单状态 1-等待确认 100-已取消 200-已完成 |

```json
{
    "ret": 0,
    "msg": "",
    "identifier": "",
    "data": [
      {
        "from_coin_name": "USDT",
        "to_coin_name": "BTC",
        "from_amount": "10000",
        "to_amount": "0.15",
        "price": "0.000015",
        "real_amount": "0.14923",
        "refund_amount": "0",
        "fee": "50",
        "update_time": "1636613232",
        "id": "163661323223421342",
        "state": 200
      }
    ]
}
```

### 161 资金账户记录

- URL: http://{host_addr}/v1/asset/bill
- Method: POST
- Request Body:

| 参数名       | 必选  | 类型      | 描述                                                                                                                                                                                                                       |
|:--------- |:--- |:------- |:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| bill_type | 是   | integer | 资产类型(需要多项直接相加进行传参) 0-全部 1-充币 2-提现 4-划转到交易账户 8-划转到资产账户 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单收币 128-空投奖励 256-资产账户划转到跟单账户 512-跟单账户划转到资产账户 1024-佣金收入 2048-活动奖励 4096-法币订单卖出币 8192-异常资产扣除 16384-币币兑换(兑出) 32768-币币兑换(兑入) 65536-币币兑换(兑出退回) |

```json
{
  "data": {
    "bill_type": 0
  }
}
```

- Response Body:

| 参数名           | 类型      | 描述                                                                                                                                                                                               |
|:------------- |:------- |:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| user_id       | integer | 用户id                                                                                                                                                                                             |
| type          | integer | 1-充币 2-提现 4-划转到交易账户 8-划转到资产账户 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单收币 128-空投奖励 256-资产账户划转到跟单账户 512-跟单账户划转到资产账户 1024-佣金收入 2048-活动奖励 4096-法币订单卖出币 8192-异常资产扣除 16384-币币兑换(兑出) 32768-币币兑换(兑入) 65536-币币兑换(兑出退回) |
| status        | integer | 状态 充值 1：未到账 2：已到账 提现 1：申请已提交 2：已到账 3：审核通过 4：拒绝 其它为 2：已到账                                                                                                                                         |
| currency_id   | integer | 币种id                                                                                                                                                                                             |
| currency_name | string  | 币种名称                                                                                                                                                                                             |
| amount        | decimal | 数量                                                                                                                                                                                               |
| tx            | string  | 交易哈希                                                                                                                                                                                             |
| balance       | decimal | 可用余额                                                                                                                                                                                             |
| lock_amount   | decimal | 冻结数量                                                                                                                                                                                             |
| created_time  | integer | 创建时间                                                                                                                                                                                             |
| approved_time | integer | 提现审核时间(更新时间)                                                                                                                                                                                     |
| bill_id       | string  | 流水记录id                                                                                                                                                                                           |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "user_id": 7231212,
      "type": 2,
      "status": 2,
      "currency_id": 2,
      "currency_name": "USDT",
      "tx": "0xd23423fd232",
      "amount": "232.2342",
      "balance": "123.1242",
      "lock_amount": "223.12",
      "created_time": **********,
      "approved_time": **********,
      "bill_id": "141251253125354354"
    }
  ]
}
```

### 162 币种法币汇率获取

- URL: http://{host_addr}/v1/common/rate
- Method: POST
- Response Body:

| 参数名          | 类型      | 描述    |
|:------------ |:------- |:----- |
| {{CoinName}} | object  | 币种汇率  |
| >usd         | decimal | 美元汇率  |
| >cny         | decimal | 人民币汇率 |
| >krw         | decimal | 韩元汇率  |
| >update_time | integer | 更新时间  |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "USDT": {
      "usd": "0.99574",
      "cny": "6.36",
      "krw": "1183.5",
      "update_time": 1637652724
    }
  }
}
```

### 163 平台摘要获取

- URL: http://{host_addr}/v1/common/summary
- Method: POST
- Response Body:

| 参数名             | 类型      | 描述      |
|:--------------- |:------- |:------- |
| trade_24h_value | decimal | 24小时成交额 |

```json
{
    "ret": 0,
    "msg": "",
    "identifier": "",
    "data": {
        "trade_24h_value": "6760767288.2212"
    }
}
```

### 164 合约止盈止损记录

- URL: http://{host_addr}/v1/position/stop/list
- Method: POST
- Request Body:

| 参数名           | 必选  | 类型     | 描述     |
|:------------- |:--- |:------ |:------ |
| contract_code | 否   | string | 合约code |

```json
{
  "data": {
    "contract_code": "BTCUSDT"
  }
}
```

- Response Body:

| 参数名           | 类型      | 描述                  |
|:------------- |:------- |:------------------- |
| contract_code | string  | 合约代码                |
| side          | string  | 方向 B-平多 S-平空        |
| amount        | integer | 张数                  |
| limit         | decimal | 止盈价(为0时代表该条记录未设置止盈) |
| stop          | decimal | 止损价(为0时代表该条记录未设置止损) |
| entrust_limit          | decimal | 止盈委托价 0为最优五档 大于0为限价 |
| entrust_stop          | decimal | 止损委托价 0为最优五档 大于0为限价 |

```json
{
  "ret": 0,
  "msg": "",
  "identifier": "",
  "data": [
    {
      "contract_code": "BTCUSDT",
      "side": "B",
      "amount": 6,
      "limit": "60000",
      "stop": "45000",
      "entrust_limit":"0"
      "entrust_stop":"123.123"
    },
    {
      "contract_code": "BTCUSDT",
      "side": "B",
      "amount": 3,
      "limit": "50000",
      "stop": "40000",
      "entrust_limit":"0"
      "entrust_stop":"123.123"
    }
  ]
}
```

### 165 服务域名速度测试结果提交

- URL: http://{host_addr}/v1/collect/domain/delay
- Method: POST
- Request Body:

| 参数名     | 必选  | 类型      | 描述                  |
|:------- |:--- |:------- |:------------------- |
| list    | 是   | array   | 数据列表                |
| >domain | 是   | string  | 域名                  |
| >delay  | 是   | integer | 耗时(毫秒数,如果超时传999999) |

```json
{
  "data": {
    "list": [
      {
        "domain": "baidu.com",
        "delay": 77
      },
      {
        "domain": "taobao.com",
        "delay": 88
      },
      {
        "domain": "qq.com",
        "delay": 66
      }
    ]
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 166 获取三方买币配置

- URL: http://{host_addr}/v1/legal/third/config
- Method: POST
- Response Body:

| 参数名                  | 类型      | 描述                                                 |
|:-------------------- |:------- |:-------------------------------------------------- |
| default_pay_coin     | string  | 默认支付币种                                             |
| default_digital_coin | string  | 默认数字币种                                             |
| pay_coins            | array   | 支持的支付法币币种列表                                        |
| >name                | string  | 名称                                                 |
| >icon                | string  | 图标                                                 |
| digital_coins        | array   | 支持的购买币种列表                                          |
| >name                | string  | 名称                                                 |
| >icon                | string  | 图标                                                 |
| pay_mode             | array   | 付款方式列表                                             |
| >pay_id              | int     | 支付方式id 1-VISA 2-MASTRCARD 4-APPLE PAY 8-GOOGLE PAY |
| >pay_name            | string  | 支付方式名称                                             |
| >logo                | string  | 支付方式图标链接                                           |
| order_conf           | object  | 订单配置                                               |
| >{数字币种名称}            | object  | 数字币种名称                                             |
| >>{支付法币币种名称}         | object  | 支付法币币种名称                                           |
| >>>price_rate        | decimal | 汇率                                                 |
| >>>min_pay           | decimal | 最小支付金额,0不限制                                        |
| >>>max_pay           | decimal | 最大支付金额,0不限制                                        |
| >>>min_coin          | decimal | 最小购买币数量,0不限制                                       |
| >>>max_coin          | decimal | 最大购买币数量,0不限制                                       |

```json
{
  "ret": 0,
  "msg": "",
  "identifier": "",
  "data": {
    "default_pay_coin": "USD",
    "default_digital_coin": "USDT",
    "pay_coins": [
      {
        "name": "KRW",
        "icon": "https://www.x.com/krw.png"
      },
      {
        "name": "USD",
        "icon": "https://www.x.com/usd.png"
      }
    ],
    "digital_coins": [
      {
        "name": "USDT",
        "icon": "https://www.x.com/usdt.png"
      },
      {
        "name": "BTC",
        "icon": "https://www.x.com/btc.png"
      }
    ],
    "pay_mode": [
      {
        "pay_id": 1,
        "pay_name": "VISA",
        "logo": ""
      },
      {
        "pay_id": 2,
        "pay_name": "MASTRCARD",
        "logo": ""
      },
      {
        "pay_id": 4,
        "pay_name": "APPLE PAY",
        "logo": ""
      },
      {
        "pay_id": 8,
        "pay_name": "GOOGLE PAY",
        "logo": ""
      }
    ],
    "order_conf": {
      "BTC": {
        "KRW": {
          "price_rate": "0",
          "min_pay": "0",
          "max_pay": "0",
          "min_coin": "0",
          "max_coin": "0"
        },
        "USD": {
          "price_rate": "0",
          "min_pay": "0",
          "max_pay": "0",
          "min_coin": "0",
          "max_coin": "0"
        }
      },
      "USDT": {
        "KRW": {
          "price_rate": "0",
          "min_pay": "0",
          "max_pay": "0",
          "min_coin": "0",
          "max_coin": "0"
        },
        "USD": {
          "price_rate": "0",
          "min_pay": "0",
          "max_pay": "0",
          "min_coin": "0",
          "max_coin": "0"
        }
      }
    }
  }
}
```

- 安卓返回体

| 参数名                  | 类型      | 描述                                                 |
|:-------------------- |:------- |:-------------------------------------------------- |
| default_pay_coin     | string  | 默认支付币种                                             |
| default_digital_coin | string  | 默认数字币种                                             |
| pay_coins            | array   | 支持的支付法币币种列表                                        |
| >name                | string  | 名称                                                 |
| >icon                | string  | 图标                                                 |
| digital_coins        | array   | 支持的购买币种列表                                          |
| >name                | string  | 名称                                                 |
| >icon                | string  | 图标                                                 |
| pay_mode             | array   | 付款方式列表                                             |
| >pay_id              | int     | 支付方式id 1-VISA 2-MASTRCARD 4-APPLE PAY 8-GOOGLE PAY |
| >pay_name            | string  | 支付方式名称                                             |
| >logo                | string  | 支付方式图标链接                                           |
| order_conf           | array   | 订单配置                                               |
| >digital_coin        | string  | 数字币种名称                                             |
| >sub_conf            | array   | 子配置列表                                              |
| >>pay_coin           | string  | 支付法币币种名称                                           |
| >>price_rate         | decimal | 汇率                                                 |
| >>min_pay            | decimal | 最小支付金额,0不限制                                        |
| >>max_pay            | decimal | 最大支付金额,0不限制                                        |
| >>min_coin           | decimal | 最小购买币数量,0不限制                                       |
| >>max_coin           | decimal | 最大购买币数量,0不限制                                       |

```json
{
  "ret": 0,
  "msg": "",
  "identifier": "",
  "data": {
    "default_pay_coin": "USD",
    "default_digital_coin": "USDT",
    "pay_coins": [
      {
        "name": "KRW",
        "icon": "https://www.x.com/krw.png"
      },
      {
        "name": "USD",
        "icon": "https://www.x.com/usd.png"
      }
    ],
    "digital_coins": [
      {
        "name": "USDT",
        "icon": "https://www.x.com/usdt.png"
      },
      {
        "name": "BTC",
        "icon": "https://www.x.com/btc.png"
      }
    ],
    "pay_mode": [
      {
        "pay_id": 1,
        "pay_name": "VISA",
        "logo": ""
      },
      {
        "pay_id": 2,
        "pay_name": "MASTRCARD",
        "logo": ""
      },
      {
        "pay_id": 4,
        "pay_name": "APPLE PAY",
        "logo": ""
      },
      {
        "pay_id": 8,
        "pay_name": "GOOGLE PAY",
        "logo": ""
      }
    ],
    "order_conf": [
      {
        "digital_coin": "USDT",
        "sub_conf": [
          {
            "pay_coin": "KRW",
            "price_rate": "0",
            "min_pay": "0",
            "max_pay": "0",
            "min_coin": "0",
            "max_coin": "0"
          },
          {
            "pay_coin": "USD",
            "price_rate": "0",
            "min_pay": "0",
            "max_pay": "0",
            "min_coin": "0",
            "max_coin": "0"
          }
        ]
      },
      {
        "digital_coin": "BTC",
        "sub_conf": [
          {
            "pay_coin": "KRW",
            "price_rate": "0",
            "min_pay": "0",
            "max_pay": "0",
            "min_coin": "0",
            "max_coin": "0"
          },
          {
            "pay_coin": "USD",
            "price_rate": "0",
            "min_pay": "0",
            "max_pay": "0",
            "min_coin": "0",
            "max_coin": "0"
          }
        ]
      }
    ]
  }
}
```

### 167 获取三方买币商户列表

- URL: http://{host_addr}/v1/legal/third/merchants
- Method: POST
- Request Body:

| 参数名                 | 必选  | 类型      | 描述                                |
|:------------------- |:--- |:------- |:--------------------------------- |
| pay_coin            | 是   | string  | 支付法币币种名                           |
| pay_coin_amount     | 是   | decimal | 支付法币数量                            |
| digital_coin        | 是   | string  | 买入币种名                             |
| digital_coin_amount | 是   | decimal | 购买数字币数量                           |
| use_mode            | 是   | integer | 用户选择的输入方式 1-输入法币折算数字币 2-输入数字币折算法币 |

```json
{
  "data": {
    "pay_coin": "USD",
    "pay_coin_amount": "200",
    "digital_coin": "USDT",
    "digital_coin_amount": "200",
    "use_mode": 1
  }
}
```

- Response Body:

| 参数名                  | 类型      | 描述                                                          |
|:-------------------- |:------- |:----------------------------------------------------------- |
| bus_id               | integer | 商家id                                                        |
| bus_name             | string  | 商家名字                                                        |
| bus_logo             | string  | 商家图标url                                                     |
| pay_mode             | integer | 支付方式(位运算,和配置接口中的支付方式id进行与运算,pay_mode&id==id则显示该id对应的支付方式图标) |
| market               | string  | 描述                                                          |
| agreement            | string  | 协议信息                                                        |
| agreement_url        | string  | 协议url                                                       |
| expected_arrival_min | integer | 预计最小到账时间（分钟）                                                |
| expected_arrival_max | integer | 预计最大到账时间（分钟）                                                |
| price_rate           | decimal | 预估汇率                                                        |
| pay_coin_amount      | decimal | 支付法币数量                                                      |
| digital_coin_amount  | decimal | 购买数字币数量                                                     |

```json
{
  "data": [
    {
      "bus_id": 1,
      "bus_name": "币商平台1",
      "bus_logo": "https://res.x.cn/icon.png",
      "pay_mode": 15,
      "market": "",
      "agreement": "",
      "agreement_url": "",
      "expected_arrival_min": 5,
      "expected_arrival_max": 10,
      "price_rate": "1.23",
      "pay_coin_amount": "123",
      "digital_coin_amount": "100"
    }
  ]
}
```

### 168 三方买币获取下单地址

- URL: http://{host_addr}/v1/legal/third/place
- Method: POST
- Request Body:

| 参数名                 | 必选  | 类型      | 描述                                |
|:------------------- |:--- |:------- |:--------------------------------- |
| bus_id              | 是   | integer | 商家id                              |
| pay_coin            | 是   | string  | 支付法币币种名                           |
| pay_coin_amount     | 是   | decimal | 支付法币数量                            |
| digital_coin        | 是   | string  | 买入币种名                             |
| digital_coin_amount | 是   | decimal | 购买数字币数量                           |
| use_mode            | 是   | integer | 用户选择的输入方式 1-输入法币折算数字币 2-输入数字币折算法币 |

```json
{
  "data": {
    "bus_id": 1,
    "pay_coin": "USD",
    "pay_coin_amount": "200",
    "digital_coin": "USDT",
    "digital_coin_amount": "200",
    "use_mode": 1
  }
}
```

- Response Body:

| 参数名       | 类型     | 描述         |
|:--------- |:------ |:---------- |
| third_url | string | 跳转三方平台链接地址 |

```json
{
  "data": {
    "third_url": "https://baidu.com"
  }
}
```

### 169 获取三方买币订单记录

- URL: http://{host_addr}/v1/legal/third/order/list
- Method: POST
- Response Body:

| 参数名          | 类型      | 描述                                                          |
|:------------ |:------- |:----------------------------------------------------------- |
| order_id     | integer | 订单ID                                                        |
| bus_name     | string  | 商家名字                                                        |
| coin_name    | string  | 币种名字                                                        |
| legal_name   | string  | 使用法币                                                        |
| coin_amount  | decimal | 购买币种数量                                                      |
| legal_amount | decimal | 支付法币数量                                                      |
| fee          | decimal | 手续费                                                         |
| rates        | decimal | 汇率                                                          |
| status       | integer | 1确认中 2发送中 3订单取消 4订单提交中 5订单失败 6订单成功                          |
| pay_mode     | integer | 支付方式(位运算,和配置接口中的支付方式id进行与运算,pay_mode&id==id则显示该id对应的支付方式图标) |
| create_time  | integer | 创建时间(秒时间戳)                                                  |
| update_time  | integer | 更新时间(秒时间戳)                                                  |

```json
{
  "data": [
    {
      "order_id": 325139394292425442,
      "bus_name": "币商平台1",
      "coin_name": "BTC",
      "legal_name": "USD",
      "coin_amount": "0.23452",
      "legal_amount": "13124.23",
      "fee": "0.0032",
      "rates": "54523.23",
      "status": 1,
      "pay_mode": 0,
      "create_time": 1652523342,
      "update_time": 1652523342
    }
  ]
}
```

### 170 调整跟单逐仓保证金

- URL: http://{host_addr}/v1/follow/position/margin/modify
- Method: POST
- Request Body:

```json
{
  "data": {
    "position_id": 23124124123123123, // 持仓id
    "amount": "123.234", // 调整数量
    "side": "add" // 方向 add-增加 sub-减少
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": **********, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 171 获取赠金活动列表

- URL: http://{host_addr}/v1/gift-cash/activity/list
- Method: POST
- Response Body:

| 参数名 | 类型 | 描述 |
|:---|:--- |:--- |
| id | integer | 活动ID |
| title | string | 活动名称 |
| content | string | 活动说明 |
| time_limit | integer | 限制自注册开始多少小时内可参与 |
| category | integer | 活动类型 1-首充大礼包 |
| partake | integer | 限制参与者 0-全部可参与 1-普通用户可参与 2-代理可参与 |
| join_state | integer | 参与状态 1-未开始 2-进行中 3-待领取 4-已领取 5-已过期 |
| amount_limit | decimal | 需要满足多少数量 |
| gift_amount | decimal | 奖励多少数量 |
| exceed_time | integer | 过期时间 |
| record_id | string | 状态为待领取时,该字段为奖励记录id,用于领取时传值 |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
  	{
    	"id": 1,
      "title": "首充大礼包",
      "content": "冲冲冲",
      "time_limit": 48,
      "category": 1,
      "partake": 0,
      "join_state": 5,
      "amount_limit": "500",
      "gift_amount": "20",
      "exceed_time": 1630583090,
      "record_id": ""
    }
  ]
}
```

### 172 赠金活动领取

- URL: http://{host_addr}/v1/gift-cash/activity/receive
- Method: POST
- Request Body:

| 参数名 | 必选  | 类型 | 描述 |
|:--- |:--- |:---- |:--- |
| id  | 是 | string | 奖励记录id |

```json
{
  "data": {
    "id": "23124124123123123"
  }
}
```

- Response Body:

```json
{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 173 我的赠金头部数据

- URL: http://{host_addr}/v1/gift-cash/my/head
- Method: POST
- Response Body:

| 参数名 | 类型 | 描述 |
|:---|:--- |:--- |
| available | decimal | 当前赠金余额 |
| total_receive | decimal | 累计领取赠金 |
| total_use | decimal | 累计使用赠金 |

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "available":"123.123",
    "total_receive":"123.123",
    "total_use":"123.123"
  }
}
```

### 174 赠金记录

- URL: http://{host_addr}/v1/gift-cash/my/record
- Method: POST
- Request Body:

| 参数名 | 必选  | 类型 | 描述 |
|:--- |:--- |:---- |:--- |
| state  | 是 | integer | 参与状态 4-已领取 5-已过期 |

```json
{
  "data": {
    "state": 4
  }
}
```

- Response Body:


| 参数名 | 类型      | 描述 |
|:---|:--------|:--- |
| order_time | integer | 时间 |
| title | string  | 赠金名称 |
| amount | decimal | 金额 |

```json
{
  "ret": 0,
  "msg": "",
  "data": [
    {
      "order_time": 1652334422,
      "title": "首充大礼包",
      "amount": "123.123"
    }
  ]
}
```