##系统接口参数及响应说明  

|客户端|SecretKey|
|:-|:-|
| android | 826b03d3f108a2663fb1acf2d603eca60d1a51ac8491a98d5bf54cd17762316d |
| ios | 6d6afe0274cd5c331910f8643ecf06a662491a0cea6188a0db13cd64d0238317 |
| web和h5 | blue4e2269ea98876cf5235e4958300f415c5121ef2ee3b46e78d2b5b64de98b |

### 签名步骤:

1.筛选并排序

获取所有请求参数,不包括数组类型和字节类型参数(如文件/字节流),然后添加req_lang字段/req_os字段/device字段/device_id字段/version字段/ts字段/api_version字段,剔除值为空(null或空字符串)的参数,并按照参数第一个字符的键值ASCII码递增排序(字母升序排序),如果遇到相同字符则按照第二个字符的键值ASCII码递增排序,以此类推.
```
req_lang: 语言 0-中文 1-英文
req_os: 实际使用客户端 1-安卓 2-ios 3-web 4-h5 5-openapi
device: 设备名称
device_id 设备ID deviceID或浏览器版本号
version: 客户端数字版本号
ts:当前秒时间戳(10位)
api_version: api版本
```

2.拼接

将排序后的参数与其对应值,组合成"参数=参数值"的格式(参数值需按照RFC3986规范进行url编码),并且把这些参数用&字符连接起来,并在最后拼接各自平台对应的SecretKey,此时生成的字符串为待签名字符串.

例如下面的请求示例,参数值都是示例,开发者参考格式即可:

```
URL: http://127.0.0.1/v1/common/version
METHOD: POST
```

原请求参数:

```json
{
  "data": {
    "verNum": 10,
    "verStr": "1.0.1 哈哈哈"
  }
}
```

则待签名字符串为:

```
api_version=V2&device=XIAOMI+MIX+2S&device_id=A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5&req_lang=0&req_os=1&ts=1585797968&verNum=10&verStr=1.0.1+%E5%93%88%E5%93%88%E5%93%88&version=10826b03d3f108a2663fb1acf2d603eca60d1a51ac8491a98d5bf54cd17762316d
```

3.调用签名函数

使用各自语言对应的对待签名字符串进行签名,并进行两次md5再做一次sha256得到最终的签名.

```
1fc45d9aa0c569fce9ac64fb37865a7fae55b4136519359e3a0e10a20042065e
```

4.把生成的签名赋值给sig参数,拼接到请求参数中.

最终的请求参数:

```json
{
  "data": {
    "verNum": 10,
    "verStr": "1.0.1 哈哈哈"
  },
  "device": "XIAOMI MIX 2S",
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  "version": "10",
  "req_os": 1,
  "req_lang": 0,
  "ts": 1585797968,
  "sig": "1fc45d9aa0c569fce9ac64fb37865a7fae55b4136519359e3a0e10a20042065e"
}
```

### 参数举例

```json
{
  "data": {
    "verNum": 10,
    "verStr": "1.0.1 哈哈哈"
  },
  "device": "XIAOMI MIX 2S",
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5",
  "version": "10",
  "req_os": 1,
  "req_lang": 0,
  "ts": 1585797968,
  "sig": "1fc45d9aa0c569fce9ac64fb37865a7fae55b4136519359e3a0e10a20042065e"
}
```

`data`是业务相关参数，各个接口都不一样

其它的是公共参数，所有接口必传

`req_lange`是语言 0-中文 1-英文

`req_os`是实际使用客户端 1-安卓 2-ios 3-web 4-h5 5-openapi

`ts`是时间戳(10位)

`sig`是签名

`version`是当前app数字版本号转成字符串

`device_id` 设备ID deviceID

`device` 设备名称

### 响应举例

```json
{
  "ret": 0,
  "msg": "",
  "data": {
    "needUpdate": false,
    "version": "1.0.1.21",
    "content": "1-更新...\n2-更新...",
    "force": false,
    "link": "http://xxx.xx.com/download",
    "url": "http://xxx.xx.com/download/test.apk",
    "created_time": 1562152152
  }
}
```

`ret`是错误码

`msg`是错误消息

`data`是业务相关数据，可能是字典也可能是数组

**注意：**部分接口文档，只列出了参数和响应的`data`字段

## 安全中心相关逻辑

增加公共参数api_version，传 “V2”使用安全中心相关逻辑

两步验证相关说明：
登录|找回密码接口：
首次请求登录接口或找回密码接口后，服务端判断用户是否需要进行两步验证，如果需要，则返回 错误码 1135，并在返回体中声明需要验证的类型，如下
{
"data": {
"phone": "19900000001", // 需要进行验证的手机号
"email": "", // 需要进行验证的邮箱，空字符串表示不需要验证
"totp": true, // true为需要进行输入
}
}
另外,找回密码第一步获取验证码后,点下一步调用校验验证码判断验证码是否正确,然后填写新密码再请求找回密码接口,如果返回需要两步验证再进行额外处理,如返回成功则直接找回成功


资金密码相关交易接口：
除法币交易和提币申请每次都需要资金密码外，其它交易接口使用用户信息返回的交易资金密码验证方式进行判断用户是否需要输入资金密码（验证方式为‘每次输入’时主动弹窗，其它则请求时如果返回错误码 1136，则弹窗提示输入资金密码）
资金密码输入错误会返回错误码 1137，并返回如下内容：
{
"data": {
"miscount": 4 // 连续输入错误的次数，达到5次后服务端会清除用户登录信息，将用户踢下线
}
}
资金密码未设置返回错误码 1107

两步验证相关错误码，可用来提醒用户输入错误的验证码位置
1130 无效的手机验证码
1131 无效的邮箱验证码
1132 无效的验证器验证码
1133 手机验证码已过期,请重新获取
1134 邮箱验证码已过期,请重新获取


## 返回码

|code|des|
|:-|:-|
0|成功
1001|系统繁忙,请稍后再试
1002|无效请求
1003|服务暂时不可用
1004|请求参数错误
1005|请求时间异常
1006|签名验证失败
1007|需要升级
1008|验证码错误
1009|验证码无效,请重新获取
1010|图形验证码错误
1011|无效的邀请码
1012|操作超时
1013|cws错误
1014|请勿重复提交
1015|状态错误
1016|请求频繁
1017|文件过大
1018|不支持的文件类型
1019|字符长度超限
1101|请先登录
1102|用户不存在
1103|账号已存在
1104|账号格式错误
1105|账号长度过长
1106|用户在其它地方登录
1107|资金密码未设置
1108|登录密码未设置
1109|登录密码不正确
1110|登录密码不能与资金密码相同
1111|资金密码不能与登录密码相同
1112|禁止登录
1113|当前用户被禁止交易
1114|当前用户被禁止提币
1115|其他用户正在进行对当前账号进行注册或绑定操作,请稍后再试
1116|用户未认证
1117|证件信息已经审核通过,请进行面部认证
1118|证件号已被占用
1119|无效的身份证号
1120|姓名与身份证号不匹配
1121|需要先通过身份信息审核
1122|面部认证已通过
1123|已存在
1124|当前身份无法执行此操作
1125|当前交易员跟随人数已满
1126|交易员不可进行跟单绑定
1127|当前未跟随
1128|不支持修改邮箱
1129|当前操作需要有两种以上安全验证
1130|无效的手机验证码
1131|无效的邮箱验证码
1132|无效的验证器验证码
1133|手机验证码已过期,请重新获取
1134|邮箱验证码已过期,请重新获取
1135|需要进行两步验证
1136|需要进行资金密码校验
1137|资金密码错误
1138|与旧密码相同
1201|小于最小单笔转入限制
1202|超过最大单笔转出限制
1203|币种暂不支持划转
1204|可用余额不足
1205|无效的提币地址
1206|当日有正在审核中的提币申请
1207|当日已经成功提币次数已达上限
1208|保证金不足
1209|当前币种不支持提币
1210|小于最小提币限额
1211|大于最大提币限额
1212|剩余可划转数量不足
1213|近期修改了密码,24小时内不支持提币
1214|模拟账户总权益大于10W不可再领取
1215|需要先完成提币身份认证
1216|为了您的账户安全。法币交易后，24小时内暂不可提币。
1301|合约不存在
1302|合约暂不可交易
1303|暂不支持最优价下单
1304|不支持的价格
1305|剩余持仓数不足
1306|超出最大持仓量
1307|委托请求频繁 (每秒最多49单)
1308|全仓模式不支持调整调整保证金
1309|低于单笔最小开仓量
1310|高于单笔最大开仓量
1311|当前持仓无效或已平仓
1312|与当前持仓或计划单杠杆倍数不同
1313|大于可下单金额
1314|您当前合约未触发的计划单已达到10条
1315|止盈价格需要大于开仓均价(做多)
1316|止损价格需要小于开仓均价(做多)
1317|止盈价格需要小于开仓均价(做空)
1318|止损价格需要大于开仓均价(做空)
1319|止盈价格需要大于触发价格(做多)
1320|止损价格需要小于触发价格(做多)
1321|止盈价格需要小于触发价格(做空)
1322|止损价格需要大于触发价格(做空)
1323|止盈价格不可以等于合约指数
1324|止损价格不可以等于合约指数
1325|止盈止损已经超出6条 请删除后再设置
1326|可用余额不足增加保证金失败
1327|保证金不足无法减少
1328|您当前有全仓模式的持仓请先平仓后再进行切换
1329|您当前有全仓模式的计划单请先撤销后再进行切换
1330|您当前有逐仓模式的持仓请先平仓后再进行切换
1331|您当前有逐仓模式的计划单请先撤销后在进行切换
1332|您当前合约多单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
1333|您当前合约空单有持仓或未触发计划单请先平仓或撤销后再进行杠杆切换
1334|无法以当前账户模式下单
1335|下单失败，请稍后再试 (行情中断)
1336|暂不支持切换到指定账户模式
1337|下单失败，请稍后再试 (超出合约单位时间交易限制)
1338|部分平仓失败,请稍后再试
1401|暂无商户接单
1402|不在商户服务时间
1403|单笔最小限额为 ￥{number}
1404|单笔最大限额为 ￥{number}
1405|有未完成订单
1406|抱歉，该报价已失效，请重新下单
1501|活动已过期
1502|奖励已发放