INSERT INTO new_basecoin.tb_contract (contract_id, contract_name, contract_name_en, contract_code, contract_type, step, price_step, digit, par_value, min_order_volume, max_order_volume, max_posi_volume, open_order_enable, close_order_enable, coin_id, coin_name, price_coin_name, fee_taker, order_by, delisted, maintenance_margin_ratio, full_lever, part_lever, follow_lever, index_precision, recommend, min_trader_volume, slippage, is_show, unit_trade, label_max_order_volume, label_max_posi_volume, label_fee, label_funding, label_slippage, label_lever, label_min_risk_rate, blowing_up_fee) VALUES (11, 'DOTUSDT永续', 'DOTUSDT', 'DOTUSDT', 1, 0.00500000, 0.00500000, 3, 0.10, 1, 1000, 10000, 1, 1, 36, 'USDT', 'DOT', 0.00050000, 3, 0, 0.005000, '5,15,20,30,50', '5,15,20,30,50', '50', 3, 0, 0, 0.000000, 1, 1000000, 3000, 30000, '-0.0003,0.000500', 0.001000, '0.01,2', 30, 0.250000, 0.000500);
#铺单指数表结构增加之后sql
INSERT INTO new_basecoin.tb_contract_depth (contract_id, contract_code, distance, hold_threshold, level_factor, pos_move_factor, max_adjust_factor, max_exe_signal, buy_factor, sell_factor, bucket_base, bucket_pin_factor, bucket_price_update, bucket_threshold, bucket_amount, bucket_max_amount, pre_bucket_base, max_bucket_base, base_factor, min_24h_amount, max_24h_amount, bucket_base_duration, depth_level_start, depth_level_end, depth_spay_money, extreme_switch, extreme_range_limit, extreme_level_inc, extreme_level_limit, large_trade_count, large_trade_factor, extreme_netpos, extreme_price_change, trade_diff_warn, depth_min_rand, depth_max_rand, index_invalid_value, reason_range, departure_factor, index_diff_switch, index_ref_value, index_exception_adjust, depth_copy_source, bait_warn_level, bait_level_pre_value, depth_level_adjust_factor, max_trade_level, bait_price_adjust, bait_amount_adjust, single_base_level, single_max_level, single_adjust, funds_protect, continue_protect_factor, spot_exception_time, spot_exception_threshold, exception_check_factor, depth_switch) VALUES (11, 'DOTUSDT', 0.05500000, 200, 0.10, 10000.00000000, 500.00000000, 10, 1.07000000, 1.07000000, 0.05000000, 0.00100000, 0.02000000, 3, 0.00400000, 4, 2708.00000000, 10895.00000000, 0.50000000, 93703358.10000000, 376551254.90000000, 0, 0, 0, 0.00000000, 0, 0.00500000, 0.01000000, 10.00000000, 5.00000000, 0.85000000, 10.00000000, 0.05000000, 0.10000000, 10.00000000, 100.00000000, 0.25000000, 0.50000000, 0.00250000, 5, 8.00000000, 0.00000000, 1, 5, 1000, 1.00000000, 6, 12.00000000, 1.00000000, 1, 6, 1, 12.00000000, 1.00000000, 10, 0.00000000, 1.00000000, 4);
