####测试api

### 1 触发测试
- URL: http://{host_addr}/v1/trigger/test
- Method: POST
- Request Body:
```json
{
  "data": {
       "contract_code":"BTCUSD",//交易对名称
       "contract_index": 4000, //指数价格（主）
       "index_price":"4000", //指数价格
       "index_price_cny":"2000.20",//折合cny
       "side":"B",//方向 B-买 S-卖
       "amount":100, //成交量,单位张
       "buyPrice":136, //买入价（主）
       "buy_price": "39.02",//买入价
       "sellPrice":133, //卖出价（主）
       "sell_price": "133",//卖出价
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

- 备注：
```
本方法伪装指数价格推送，指数价格信息会写入
```
- Response Body:

```json

{
  "ret": 0,
  "msg": "",
  "data": null
}
```

### 2 资金费用测试
- URL: http://{host_addr}/v1/funding/test
- Method: POST
- Request Body:
```json
{
  "data": {
       "contract_code":"BTCUSD",//交易对名称
       "index_price": "39.20", //指数价格
       "index_price_cny":"2000.20",//折合cny
       "side":"B",//方向 B-买 S-卖
       "amount":100, //成交量,单位张
       "buy_price": "39.02",//买1价格
       "sell_price": "39.02",//卖1价格
  },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

### 3 资金费用测试
- URL: http://{host_addr}/v1/funding/rate/set
- Method: POST
- Request Body:
```json
{
  "data": {
      "ContractCode":"ETHUSDT",//合约code
      "FundingRate":"0.0025", //资金费率
      "ThisFundingTime":"0001-01-01T00:00:00Z",
      "EstimatedRate":"0.0025",
      "SettlementTime":"0001-01-01T00:00:00Z",
      "Time":"0001-01-01T00:00:00Z"},
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

### 4 行情报警测试
- URL: http://{host_addr}/v1/test/market/warn
- Method: POST
- Request Body:
```json
{
  "data": {
      "ContractCode":"ETHUSDT",//合约code
      "gap":0 //时间间隔 传为0合约恢复正常，下次可正常报警，传大于设置的阈值会报警，仅仅报警一次
   },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

### 5 行情api断开测试
- URL: https://api.officeqb.com/v1/test/market/warn/wsclose
- Method: POST
- Request Body:
```json
{
   "data": "测试关闭",
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```

### 6 买卖盘波动率偏离度测试
- URL: http://{host_addr}/v1/test/market/warn/index
- Method: POST
- Request Body:
```json
{
 "data": {
        "contract_code":"BTCUSDT",
        "contract_index": 39.20, //指数价格
        "amount":100, //数量
        "buyPrice": 39.02, //买价格
        "sellPrice": 39.02 //卖价格
   },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
}
```

### 7 跟单佣金计算
- URL: http://{host_addr}/v1/follow/brokerage/test/dev
- Method: POST
- Request Body:
```json
{
 "data": {
       
   },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
}
```
### 7 跟单佣金计算
- URL: http://{host_addr}/v1/follow/brokerage/test/dev
- Method: POST
- Request Body:
```json
{
 "data": {
       
   },
  "device": "XIAOMI MIX 2S", // 设备名称
  "device_id": "A1B2C3-D4E5F6-G7H8I9-J0K1L2-M3N4O5", // 设备id
  "version": "10", // 数字版本号
  "req_os": 1, // app类型 1-安卓 2-ioa 3-web 4-h5 5-openapi
  "req_lang": 0, // 语言类型 0-中文 1-英文
  "ts": 1578034379, // 当前秒时间戳
  "sig": "a425fa8a7555af7cx2dd5a26cf2693bdc1ecd78c329a5c1d3d31b05c892b6e591" // 签名
}
```