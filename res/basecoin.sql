create table help_topic
(
	help_topic_id int unsigned not null
		primary key,
	name char(64) not null,
	help_category_id smallint unsigned not null,
	description text not null,
	example text not null,
	url text not null,
	constraint name
		unique (name)
)
comment 'help topics';

create table tb_about_community
(
	id int auto_increment
		primary key,
	platform_id int(10) default 1 not null comment '平台id',
	community varchar(64) default '' not null comment '社群名称',
	community_icon varchar(255) default '' not null comment '社群图标',
	account varchar(64) default '' not null comment '社群对应账户名',
	link varchar(255) default '' not null comment '链接地址',
	category tinyint default 2 not null comment '类型 1-客服邮箱 2-社群',
	is_wechat tinyint(1) default 0 not null comment '是否是微信 微信需要对链接做特殊处理',
	constraint uk_community
		unique (community)
)
comment '关于信息表';

create table tb_activity
(
	id int auto_increment
		primary key,
	activity_name varchar(200) default '' not null,
	`describe` varchar(255) default '' not null,
	limit_up decimal(36,8) default 0.******** null comment '最高上限',
	ratio decimal(36,8) default 0.******** null comment '手续费比例',
	start_day date null comment '开始日期',
	end_day date null comment '结束日期',
	start_time datetime null comment '开始时间',
	end_time datetime null comment '结束时间',
	opening_time datetime null comment '开奖时间',
	first_prize decimal(36,8) default 0.******** null comment '一等奖比例',
	second_award decimal(36,8) default 0.******** null comment '二等奖',
	third_award decimal(36,8) default 0.******** null comment '三等奖',
	status int(1) default 0 null comment '1开启 0关闭',
	creat_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
	creat_manage varchar(255) default '' null comment '创建人'
);

create table tb_activity_list
(
	id int auto_increment
		primary key,
	activity_pareid int null comment '活动主id',
	limit_up decimal(36,8) null,
	ratio decimal(36,8) null comment '比例',
	start_time datetime null comment '开始时间',
	end_time datetime null comment '结束时间',
	opening_time datetime null comment '开奖时间按',
	first_prize decimal(36,8) null,
	third_award decimal(36,8) null,
	second_award decimal(36,8) default 0.******** null,
	current_bonus decimal(36,8) default 0.******** null comment '当前奖金',
	hash varchar(255) default '' null,
	lssued decimal(36,8) default 0.******** null comment '已发放奖励',
	activity_name varchar(255) null comment '活动名称',
	start_day date null comment '开始日期',
	end_day date null comment '结束日期',
	`describe` varchar(255) null comment '描述',
	own_day datetime default CURRENT_TIMESTAMP null comment '归属日期',
	set_bonus decimal(36,8) default 100.******** null comment '设置金额'
);

create table tb_activity_user_list
(
	id int auto_increment
		primary key,
	user_id bigint null,
	order_id bigint null comment '订单id',
	activity_list_id int null comment '子活动id',
	bonus decimal(36,8) null comment '奖金',
	grade int(1) null comment '1 一等奖 2二等奖 3三等奖',
	ratio decimal(36,8) null comment '比例',
	share_img varchar(255) default '' null comment '分享图片地址',
	check_status int(1) default -1 null comment '设置状态  -1待领取，0待审核，1审核通过 2拒绝，3过期',
	check_time datetime null comment '审核时间',
	check_manage varchar(255) null comment '审核人',
	open_time datetime null comment '开奖时间'
);

create table tb_activity_ward
(
	user_id bigint not null,
	totalward decimal(36,8) null,
	is_real int(1) default 1 not null,
	constraint indexUid
		unique (user_id)
);

alter table tb_activity_ward
	add primary key (user_id);

create table tb_admin_manage
(
	manage_id int auto_increment
		primary key,
	name varchar(50) not null comment '登录名称',
	password varchar(50) not null,
	salt char(4) not null,
	real_name varchar(50) null comment '名字',
	mobile varchar(20) null,
	reg_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '注册时间',
	online int(4) default 2 null comment '1在线，2离线',
	last_login datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '最后登录时间',
	status tinyint(1) default 1 null comment '状态：1解禁，2禁止，3注销',
	last_ip varchar(20) null comment '登录ip',
	creat_name varchar(50) null comment '创建者名字',
	model_id int null comment '用户级别',
	vkey varchar(100) default '' null comment '谷歌验证码',
	constraint name
		unique (name),
	constraint name_2
		unique (name)
)
comment '管理员表';

create table tb_admin_menu
(
	id int auto_increment
		primary key,
	name char(50) null,
	class char(50) null,
	fuction char(50) null,
	status int(1) null,
	parent_id int(5) null,
	type int(1) null,
	top int(2) null
)
comment '菜单栏';

create table tb_admin_model
(
	model_id int auto_increment
		primary key,
	model_name char(50) default '' not null,
	status int(1) null,
	creat_time datetime default CURRENT_TIMESTAMP null,
	last_time datetime default CURRENT_TIMESTAMP null,
	data blob null,
	constraint name
		unique (model_name)
)
comment '权限表';

create table tb_agent_capital_snapshot
(
	id int auto_increment
		primary key,
	user_id bigint null comment '用户uid',
	in_cash decimal(36,8) default 0.******** null comment '入金',
	out_cash decimal(36,8) default 0.******** null comment '出金',
	net_income decimal(36,8) default 0.******** null,
	account_equity decimal(36,8) default 0.******** null comment '账户权益',
	deposit decimal(36,8) null comment '冻结保证金',
	pnl decimal(36,8) null comment 'pnl',
	own_time date null comment '归属日',
	constraint uk_day_uid
		unique (own_time, user_id)
);

create table tb_airdrop
(
	id int auto_increment
		primary key,
	coin_id int null,
	coin_name varchar(30) default '' null,
	stats int(1) default 0 null comment '0待审核，1已审核,2拒绝',
	numbers int default 0 null comment '空头人数',
	fail_numbers int default 0 null comment '失败人数',
	amount decimal(32,8) default 0.******** null comment '数量',
	apply_name varchar(100) collate utf8_bin default '' null comment '申请人',
	apply_time datetime default CURRENT_TIMESTAMP null comment '申请时间',
	examine_name varchar(100) default '' null comment '审核人',
	examine_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '审核时间',
	mark varchar(255) collate utf8_bin default '' null comment '备注',
	suburl varchar(200) collate utf8_bin default '' null comment '对应文件名字'
)
collate=utf8mb4_bin;

create index idx_apply_time
	on tb_airdrop (apply_time);

create index idx_examine_time
	on tb_airdrop (examine_time);

create table tb_airdrop_list
(
	id int auto_increment
		primary key,
	parent_id int default 0 null comment '对应空投id',
	user_id bigint default 0 null comment '用户id',
	amount decimal(32,8) default 0.******** null,
	coin_id int null,
	coin_name varchar(100) default '' null,
	created_time datetime default CURRENT_TIMESTAMP null,
	stats int(1) default 0 null,
	update_time datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
)
collate=utf8mb4_bin;

create index idx_create_time
	on tb_airdrop_list (created_time);

create index idx_update_time
	on tb_airdrop_list (update_time);

create table tb_banner
(
	id int auto_increment comment '轮播图id'
		primary key,
	platform_id int(10) default 1 not null comment '平台id',
	title varchar(50) charset utf8 not null comment '标题',
	image varchar(255) charset utf8 default 'none' not null comment '中文图',
	link varchar(255) charset utf8 default 'none' not null comment '中文跳转地址',
	start datetime default CURRENT_TIMESTAMP not null comment '开始时间',
	end datetime default CURRENT_TIMESTAMP not null comment '结束时间',
	valid tinyint(1) default 1 not null comment '是否有效',
	`limit` tinyint default 0 not null comment '客户端限定 0 不限制, 1 android 2 ios ，3 web',
	weight double(16,8) default 0.******** not null comment '显示权重',
	lang_type tinyint(1) default 0 not null comment '0中文，1英文，2繁体，3韩文，4日文'
)
collate=utf8_bin;

create table tb_bug_loss
(
	id int not null
		primary key,
	loss decimal(36,6) default 0.000000 null comment '损失'
)
collate=utf8mb4_bin;

create table tb_chain_wallet
(
	id bigint(18) auto_increment
		primary key,
	coin_name varchar(20) default '' not null comment '币种名称',
	balance decimal(36,8) not null comment '账户资产',
	own_day date not null comment '归属日期',
	constraint tb_chain_wallet_date_coin
		unique (own_day, coin_name)
)
comment '大钱包资产记录表';

create table tb_coin_wallet_config
(
	wallet_name varchar(150) charset utf8 not null comment '大钱包对应币种名'
		primary key,
	coin_id int(10) not null comment '币种id',
	coin_name varchar(150) charset utf8 not null comment '币种名称',
	protocol varchar(20) charset utf8 default '' not null comment '币种协议',
	can_deposit tinyint(1) default 1 not null comment '可充币',
	can_withdraw tinyint(1) default 1 not null comment '可提币',
	min_withdraw decimal(36,8) default 0.******** not null comment '最小提币数量',
	max_withdraw decimal(36,8) default 0.******** not null comment '单次最大提币数量',
	withdraw_fee decimal(36,8) not null comment '提币手续费',
	withdraw_manage_review decimal(36,8) default 500.******** not null comment '提现总管理员需审核数',
	withdraw_review decimal(36,8) default 0.******** not null comment '提现需要审核数',
	withdraw_time int default 0 not null comment '单日提币次数',
	withdraw_precision smallint default 8 not null comment '提现最大精度',
	confirm_num smallint default 0 not null comment '充提币需要确认数',
	weight double(16,8) default 0.******** not null comment '排序权重 从大到小排序',
	update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
comment '钱包币种配置表' collate=utf8_bin;

create index idx_coin_id
	on tb_coin_wallet_config (coin_id);

create index idx_coin_name
	on tb_coin_wallet_config (coin_name);

create table tb_contact_us
(
	id int auto_increment
		primary key,
	mobile varchar(20) default '' null comment '手机号',
	relname varchar(50) default '' null comment '名字',
	email varchar(100) default '' null comment '邮箱',
	content varchar(255) default '' null comment '备注',
	ip_address varchar(255) default '' null comment '用户ip',
	creat_time datetime default CURRENT_TIMESTAMP null
);

create table tb_contract
(
	contract_id int auto_increment
		primary key,
	contract_name varchar(32) not null comment '合约名称中文',
	contract_name_en varchar(32) not null comment '合约名称英文',
	contract_code varchar(16) not null comment '合约代码',
	contract_type smallint default 1 not null comment '合约类型 0：币本位 1：金本位',
	step decimal(11,8) default 0.******** not null comment '下单及止盈止损步长',
	price_step decimal(11,8) default 0.01000000 not null comment '价格波动值',
	digit int default 0 not null comment '交易对精度',
	par_value decimal(6,2) default 0.00 not null comment '每张面值',
	min_order_volume int default 0 not null comment '单笔最小下单量(张)',
	max_order_volume int default 0 not null comment '单笔最大下单量(张)',
	max_posi_volume int default 0 not null comment '最大持仓张数',
	open_order_enable tinyint(1) default 0 not null comment '可开仓（开关）',
	close_order_enable tinyint(1) default 0 not null comment '可平仓（开关）',
	coin_id int default 0 not null comment '结算币种Id',
	coin_name varchar(8) default '' not null comment '保证金币种 ',
	price_coin_name varchar(8) default '' not null comment '计价币种',
	fee_taker decimal(11,8) default 0.******** not null comment 'taker手续费',
	order_by float(11,6) default 0.000000 not null comment '排序',
	delisted tinyint(1) default 1 not null comment '0：上架  1：下架',
	maintenance_margin_ratio decimal(7,6) default 0.000000 not null comment '维持保证金率',
	full_lever varchar(256) not null comment '全仓杠杆倍数（逗号分割）',
	part_lever varchar(256) not null comment '逐仓杠杆倍数（逗号分割）',
	follow_lever varchar(256) default '100' not null comment '带单杠杆倍数（逗号分割）',
	index_precision int default 2 not null comment '合约指数价格精度',
	recommend tinyint(1) default 0 not null comment '是否为推荐合约',
	min_trader_volume int default 0 not null comment '带单者最小购买张数',
	slippage decimal(36,6) default 0.000000 not null comment '滑点',
	is_show tinyint(1) default 0 not null comment '0:隐藏  1:显示',
	unit_trade bigint default 1000000 not null comment '单位成交限制',
	label_max_order_volume int default 0 not null comment '标签可设置 单笔最大下单量',
	label_max_posi_volume int default 0 null comment '标签可设置 最大持仓',
	label_fee varchar(255) default '' null comment '标签 可设置的手续费',
	label_funding decimal(36,6) default 0.000000 null comment '标签 可设置资金费率',
	label_slippage varchar(255) default '' null comment '标签 滑点范围 以逗号分隔',
	label_lever int default 150 not null comment '标签 最大杠杆',
	label_min_risk_rate decimal(36,6) default 0.250000 not null comment '标签 最底风险率',
	blowing_up_fee decimal(36,6) default 0.000500 null,
	constraint UK_Contract_Code
		unique (contract_code)
)
comment '永续合约定义参数' collate=utf8_bin;

create index idx_order_by
	on tb_contract (order_by);

create table tb_contract_depth
(
	contract_id int not null comment '合约id',
	contract_code varchar(16) not null comment '合约code'
		primary key,
	distance decimal(16,8) default 4.******** not null comment '盘口常规间距',
	hold_threshold int default 100 null comment '净持仓阈值',
	level_factor decimal(10,2) default 0.10 not null comment '规模等级敏感系数',
	pos_move_factor decimal(36,8) default 1.******** not null comment '仓位偏移容忍基数',
	max_adjust_factor decimal(36,8) default 20.******** null comment '最大调整系数',
	max_exe_signal int default 10 not null comment '最大执行修正信号',
	buy_factor decimal(36,8) default 1.59000000 not null comment '买方深度伪装系数',
	sell_factor decimal(36,8) default 1.59000000 not null comment '卖方深度伪装系数',
	bucket_base decimal(36,8) default 8.******** not null comment '对敲盘口基准',
	bucket_pin_factor decimal(36,8) default 0.01000000 null comment '插针比例系数',
	bucket_price_update decimal(36,8) default 9.******** null comment '价格修正限制值',
	bucket_threshold int default 3 null comment '对敲累加阈值',
	bucket_amount decimal(36,8) default 0.00350000 null comment '对敲放量振幅限制',
	bucket_max_amount int default 8 null comment '放量上限系数',
	pre_bucket_base decimal(36,8) default 523.******** not null comment '预设对敲基准',
	max_bucket_base decimal(36,8) default 1153.******** not null comment '最大对敲基准',
	base_factor decimal(36,8) default 1.******** not null comment '对敲基准倍数',
	min_24h_amount decimal(36,8) default 0.******** null comment '24小时最小成交量',
	max_24h_amount decimal(36,8) default 0.******** null comment '24小时最大成交量',
	bucket_base_duration int default 0 not null comment '对敲基础放量时间周期 >=0',
	depth_level_start int default 0 not null comment '深度伪装起始档位',
	depth_level_end int default 0 null comment '深度伪装结束档位',
	depth_spay_money decimal(36,8) default 0.******** not null comment '特殊档位伪装币数量',
	extreme_switch tinyint default 0 not null comment '极端行情干预开关 0-关闭 1-开启',
	extreme_range_limit decimal(36,8) default 10.******** not null comment '异常幅度限制',
	extreme_level_inc decimal(36,8) default 1.******** not null comment '异常行情等级递增系数 >0',
	extreme_level_limit decimal(36,8) default 10.******** not null comment '极限等级限制',
	large_trade_count decimal(36,8) default 5.******** not null comment '连续大额成交次数',
	large_trade_factor decimal(36,8) default 0.85000000 not null comment '大额异常成交系数',
	extreme_netpos decimal(36,8) default 10.******** not null comment '净持仓异常规模系数',
	extreme_price_change decimal(36,8) default 5.******** not null comment '极端报价跟踪变动',
	trade_diff_warn decimal(36,8) default 15.******** not null comment '成交价余额指数价格差预警值',
	depth_min_rand decimal(36,8) default 10.******** not null comment '最小铺单随机数',
	depth_max_rand decimal(36,8) default 100.******** not null comment '铺单最大随机数',
	index_invalid_value decimal(36,8) default 0.25000000 not null comment '无效剔除边界参数',
	reason_range decimal(36,8) default 0.50000000 not null comment '合理标记价格调整幅度',
	departure_factor decimal(36,8) default 0.00250000 not null comment '偏离系数',
	index_diff_switch tinyint default 0 not null comment '合约基差算法开关 0-5',
	index_ref_value decimal(36,8) default 10.******** not null comment '合约参考值',
	index_exception_adjust decimal(36,8) default 0.******** not null comment '数据异常调整参数，如果为0，则自动为参考值/2',
	depth_copy_source int default 1 not null comment '深度来源 1-okex 2-huobi 3-biance',
	bait_warn_level int default 5 not null comment '鱼饵警戒档位',
	bait_level_pre_value int default 1000 not null comment '鱼饵档位铺单预设值',
	depth_level_adjust_factor decimal(36,8) default 1.******** not null comment '深度档位调整系数',
	max_trade_level int default 6 not null comment '最大可成交档位',
	bait_price_adjust decimal(36,8) default 12.******** not null comment '中鱼价格修正参数',
	bait_amount_adjust decimal(36,8) default 1.******** not null comment '中鱼配料调整系数',
	single_base_level int default 1 not null comment '单边市基础跳数',
	single_max_level int default 6 not null comment '单边市最大跳数',
	single_adjust int default 1 not null comment '单边市调节',
	funds_protect decimal(36,8) default 12.******** not null comment '反套利系数',
	continue_protect_factor decimal(36,8) default 1.******** not null comment '价差抗冲击系数',
	spot_exception_time int default 10 null comment '现货异常持续时间',
	spot_exception_threshold decimal(36,8) default 0.******** null comment '现货指数异常检测阈值',
	exception_check_factor decimal(36,8) default 1.******** null comment '异常检测比例系数',
	depth_switch int default 4 null comment '铺单开关 0-3家均值 1-火币 2-币安 3-okex 4-3家合约最新成交'
)
comment '合约深度配置表';

create table tb_contract_depth_back
(
	contract_id int not null comment '合约id',
	contract_code varchar(16) not null comment '合约code',
	distance decimal(16,8) default 4.******** not null comment '盘口常规间距',
	hold_threshold int default 100 null comment '净持仓阈值',
	level_factor decimal(10,2) default 0.10 not null comment '规模等级系数',
	max_adjust_factor decimal(36,8) default 20.******** null comment '最大调整系数',
	max_exe_signal int default 10 not null comment '最大执行修正信号',
	buy_factor decimal(36,8) default 1.59000000 not null comment '买方深度伪装系数',
	sell_factor decimal(36,8) default 1.59000000 not null comment '卖方深度伪装系数',
	bucket_base decimal(36,8) default 8.******** not null comment '对敲盘口基准',
	bucket_pin_factor decimal(36,8) default 0.01000000 null comment '插针比例系数',
	bucket_price_update decimal(36,8) default 9.******** null comment '价格修正限制值',
	bucket_threshold int default 3 null comment '对敲累加阈值',
	bucket_amount decimal(36,8) default 0.00350000 null comment '对敲放量振幅限制',
	bucket_max_amount int default 8 null comment '放量上限系数',
	pre_bucket_base decimal(36,8) default 523.******** not null comment '预设对敲基准',
	max_bucket_base decimal(36,8) default 1153.******** not null comment '最大对敲基准',
	base_factor decimal(36,8) default 1.******** not null comment '对敲基准倍数',
	min_24h_amount decimal(36,8) default 0.******** null comment '24小时最小成交量',
	max_24h_amount decimal(36,8) default 0.******** null comment '24小时最大成交量',
	bucket_base_duration int default 0 not null comment '对敲基础放量时间周期 >=0',
	depth_level_start int default 0 not null comment '深度伪装起始档位',
	depth_level_end int default 0 not null comment '深度伪装结束档位',
	depth_spay_money decimal(36,8) default 0.******** not null comment '特殊档位伪装币数量',
	extreme_switch tinyint default 0 not null comment '极端行情干预开关 0-关闭 1-开启',
	extreme_range_limit decimal(36,8) default 10.******** not null comment '异常幅度限制',
	extreme_level_inc decimal(36,8) default 1.******** not null comment '异常行情等级递增系数 >0',
	extreme_level_limit decimal(36,8) default 10.******** not null comment '极限等级限制',
	large_trade_count decimal(36,8) default 5.******** not null comment '连续大额成交次数',
	large_trade_factor decimal(36,8) default 0.85000000 not null comment '大额异常成交系数',
	extreme_netpos decimal(36,8) default 10.******** not null comment '净持仓异常规模系数',
	extreme_price_change decimal(36,8) default 5.******** not null comment '极端报价跟踪变动',
	trade_diff_warn decimal(36,8) default 15.******** not null comment '成交价余额指数价格差预警值',
	depth_min_rand decimal(36,8) default 10.******** not null comment '最小铺单随机数',
	depth_max_rand decimal(36,8) default 100.******** not null comment '铺单最大随机数',
	index_invalid_value decimal(36,8) default 0.25000000 not null comment '无效剔除边界参数',
	reason_range decimal(36,8) default 0.50000000 not null comment '合理标记价格调整幅度',
	departure_factor decimal(36,8) default 0.00250000 not null comment '偏离系数',
	index_diff_switch tinyint default 0 not null comment '合约基差算法开关 0-5',
	index_ref_value decimal(36,8) default 10.******** not null comment '合约参考值',
	index_exception_adjust decimal(36,8) default 0.******** not null comment '数据异常调整参数，如果为0，则自动为参考值/2',
	depth_copy_source int default 1 not null comment '深度来源 1-okex 2-huobi 3-biance',
	bait_warn_level int default 5 not null comment '鱼饵警戒档位',
	depth_level_adjust_factor decimal(36,8) default 1.******** not null comment '深度档位调整系数',
	max_trade_level int default 6 not null comment '最大可成交档位',
	bait_price_adjust decimal(36,8) default 12.******** not null comment '中鱼价格修正参数',
	bait_amount_adjust decimal(36,8) default 1.******** not null comment '中鱼配料调整系数',
	single_base_level int default 1 not null comment '单边市基础跳数',
	single_max_level int default 6 not null comment '单边市最大跳数',
	single_adjust int default 1 not null comment '单边市调节',
	funds_protect decimal(36,8) default 12.******** not null comment '反套利系数',
	continue_protect_factor decimal(36,8) default 1.******** not null comment '价差抗冲击系数'
);

create table tb_contract_index
(
	index_id bigint auto_increment
		primary key,
	contract_code varchar(16) not null,
	contract_index decimal(36,8) not null comment '合约指数',
	buy_price decimal(36,8) not null comment '买入价',
	sell_price decimal(36,8) not null comment '卖出价',
	created_time datetime not null,
	trade_price decimal(36,8) default 0.******** not null comment '成交价'
)
comment '指数信息' collate=utf8_bin;

create table tb_contract_index_detail
(
	detail_id bigint auto_increment
		primary key,
	index_id bigint not null,
	level int not null comment '档位',
	buy_price decimal(36,8) not null,
	buy_volume int not null,
	sell_price decimal(36,8) not null,
	sell_volume int not null
)
comment '指数信息明细' collate=utf8_bin;

create index IDX_Index_Level
	on tb_contract_index_detail (index_id, level);

create table tb_contract_indicator
(
	id int auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	side char null comment '方向',
	timestamp bigint not null comment '时间戳',
	create_time datetime not null comment '入库时间',
	price decimal(36,8) null,
	constraint idx_time
		unique (contract_code, timestamp)
)
comment '合约指标' collate=utf8mb4_bin;

create table tb_contract_lever_risk
(
	id int auto_increment
		primary key,
	contract_id int not null comment '合约id',
	level int null comment '档位',
	min_lever int null comment '最低杠杆 >=',
	max_lever int not null comment '最高杠杆 <=',
	risk_rate decimal(4,2) default 0.95 not null comment '档位风险度',
	create_time timestamp default CURRENT_TIMESTAMP null comment '添加时间',
	constraint uk_id_level
		unique (contract_id, level)
)
comment '合约杠杆风险率档位表';

create table tb_contract_support
(
	id int auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	timestamp bigint not null comment '时间戳',
	create_time datetime null comment '入库时间',
	drag decimal(36,8) default 0.******** null comment '阻力',
	support decimal(36,8) default 0.******** null comment '支撑',
	constraint tb_contract_support_contract_code_timestamp_uindex
		unique (contract_code, timestamp)
)
comment '合约压力支撑信息表' collate=utf8mb4_bin;

create table tb_contract_tick
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) not null comment '合约名称',
	trade_price decimal(36,8) not null comment '成交价',
	buy_price decimal(36,8) default 0.******** not null comment '买入价',
	sell_price decimal(36,8) default 0.******** not null comment '卖出价',
	index_price decimal(36,8) default 0.******** not null comment '指数价格',
	trade_amount bigint default 0 null comment '成交张数',
	create_time datetime not null comment '时间'
)
comment '合约tick表' collate=utf8mb4_bin;

create index idx_create_time
	on tb_contract_tick (create_time);

create index in_contrccode
	on tb_contract_tick (contract_code, id);

create table tb_country_areacode
(
	id int auto_increment
		primary key,
	country_en varchar(255) collate utf8_bin null,
	country_cn varchar(255) collate utf8_bin null,
	country_tw varchar(255) collate utf8_bin null,
	country_code varchar(255) collate utf8_bin null,
	country_encrypt varchar(10) collate utf8_bin null,
	constraint uq_encrypt
		unique (country_encrypt)
)
collate=utf8mb4_bin;

create table tb_currency
(
	currency_id int not null,
	currency_name varchar(16) not null comment '币种名称',
	wallet_name varchar(16) null comment '钱包名称',
	full_name varchar(32) null comment '币全称',
	icon varchar(255) null comment '币图标',
	`precision` int default 0 not null comment '精度（小数位）',
	recharge_enable tinyint(1) default 1 not null comment '充值 1：开启 0：禁止',
	withdraw_enable tinyint(1) default 1 not null comment '提现 1：开启 0：禁止',
	pay_confirms int not null comment '入账需要确认数',
	miner_fee decimal(36,8) not null comment '单笔提币手续费',
	min_withdraw decimal(36,8) not null comment '单笔最小提币',
	max_withdraw decimal(36,8) not null comment '单笔最大提币',
	withdraw_review decimal(36,8) not null comment '单次提现审核数量',
	tag varchar(20) default '' not null comment '币种标识 如usdt ERC20',
	min_transfer decimal(36,8) default 0.01000000 not null comment '资产划转单笔最小数量',
	max_transfer decimal(36,8) default 10000.******** not null comment '资产划转单笔最大数量',
	status tinyint(1) default 0 not null comment '0：下架 1：正常 ',
	can_transfer tinyint(1) default 1 not null comment '0: 不支持划转 1:支持划转',
	multiple tinyint(1) default 0 not null comment '是否是多协议币种 0-不是 1-是',
	withdraw_time int not null comment '单日提币次数',
	constraint IDX_CurrencyName
		unique (currency_name),
	constraint currency_id_UNIQUE
		unique (currency_id)
)
comment '币种信息表' collate=utf8_bin;

alter table tb_currency
	add primary key (currency_id);

create table tb_data_audit
(
	id int auto_increment
		primary key,
	audit_cycle varchar(255) null comment '清算周期',
	ttype int(1) default 1 null comment '类型',
	cycle_amount decimal(36,6) default 0.000000 null comment '周期内数量',
	cycle_number int default 0 null comment '周期内人数',
	total_amount decimal(36,6) default 0.000000 null comment '累计数量',
	total_number int default 0 null comment '累计人数',
	total_average decimal(36,6) default 0.000000 null comment '累计平均值',
	difference decimal(36,6) default 0.000000 null comment '平均差',
	created_at datetime default CURRENT_TIMESTAMP null
)
collate=utf8_bin;

create index indexType
	on tb_data_audit (ttype, created_at);

create table tb_dealer_apply
(
	id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null comment '用户id',
	realname varchar(255) not null comment '用户姓名',
	contact varchar(255) not null comment '联系方式',
	state tinyint(1) not null comment '审核状态 0-待审核 1-通过 2-拒绝',
	create_time datetime not null comment '创建时间',
	audit_time datetime not null comment '审核时间',
	manager varchar(255) default '' not null comment '审核员'
)
comment '交易员申请表' collate=utf8mb4_bin;

create index idx_uid
	on tb_dealer_apply (user_id);

create table tb_dealer_audit
(
	id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	trader_uid bigint not null comment '交易员uid',
	follow_uid bigint not null comment '跟随者uid',
	side_type tinyint(1) not null comment '0退回跟随者，1 给交易员结算',
	wait_profit decimal(36,6) default 0.000000 not null comment '待结算盈利金额',
	wait_loss decimal(36,6) default 0.000000 not null comment '待结算亏损金额',
	withholding decimal(10,3) default 0.000 not null comment '预扣佣金',
	rebate decimal(36,8) default 0.******** not null comment '结算返佣比列',
	start_audit_time datetime not null comment '结算开始时间（对应关系表时间）',
	actual_amount decimal(36,6) not null comment '实际结算金额（发放金额）',
	own_at date not null comment '归属时间',
	create_time datetime default CURRENT_TIMESTAMP not null comment '结算时间'
)
comment '交易员与跟单者清算表' collate=utf8mb4_bin;

create table tb_dealer_day_summary
(
	trader_uid bigint(11) not null,
	profit decimal(36,6) default 0.000000 not null comment '当天带单盈利资金',
	loss decimal(36,6) default 0.000000 not null comment '当天带单亏损资金',
	get_profit decimal(36,6) default 0.000000 not null comment '当天累计所得佣金',
	number int default 0 not null comment '当天带单张数',
	total_profit decimal(36,6) default 0.000000 not null comment '当天平仓盈亏',
	total_capital decimal(36,6) default 0.000000 not null comment '当天平仓保证金',
	follow_count int(5) default 0 not null comment '跟单人数',
	profit_count int(10) default 0 not null comment '当天盈利次数',
	loss_count int(10) default 0 not null comment '当天亏损次数',
	trading_volume decimal(36,6) default 0.000000 not null comment '交易量',
	platform_id int default 0 not null comment '平台id',
	own_at date not null comment '归属日期',
	create_time datetime default CURRENT_TIMESTAMP not null,
	constraint uk_user_own
		unique (trader_uid, own_at)
)
comment '带单者每天结算表' collate=utf8mb4_bin;

create table tb_dealer_follow
(
	id int auto_increment
		primary key,
	trader_uid bigint not null comment '交易员uid',
	follow_uid bigint not null comment '跟随者id',
	currency_id int not null comment '保证金币种id',
	contract varchar(255) not null comment '跟随合约 用逗号隔开',
	follow_status tinyint(1) default 1 not null comment '0无效 ，1有效',
	quantity int default 0 not null comment '下单跟随数量',
	proportion decimal(11,3) default 0.000 not null comment '下单跟随比例',
	stop_loss decimal(11,3) default 0.000 not null comment '止损比例',
	stop_profit decimal(11,3) default 0.000 not null comment '止盈比例',
	max_amount decimal(36,6) default 0.000000 not null comment '最大授权资金',
	already_amount decimal(36,6) default 0.000000 not null comment '已使用资金',
	total_amount decimal(36,6) default 0.000000 not null comment '累计已使用资金',
	wait_profit decimal(36,6) default 0.000000 not null comment '待结算盈利资金',
	wait_loss decimal(36,6) unsigned default 0.000000 not null comment '盈利待赎回资金',
	total_actual_amount decimal(36,6) default 0.000000 not null comment '累计分佣资金',
	already_profit decimal(36,6) default 0.000000 not null comment '累计盈利资金',
	already_loss decimal(36,6) default 0.000000 not null comment '累计亏损资金',
	withholding decimal(36,8) default 0.******** not null comment '预扣返佣金额',
	start_audit_time datetime default CURRENT_TIMESTAMP not null comment '周期结算开始时间',
	follow_time datetime default CURRENT_TIMESTAMP not null comment '跟随时间',
	platform_id int default 0 not null comment '平台id',
	constraint uk_fid_did
		unique (follow_uid, trader_uid)
)
comment '交易员和跟随者关系表' collate=utf8mb4_bin;

create index idx_dealer_id
	on tb_dealer_follow (trader_uid);

create table tb_dealer_lever
(
	user_id bigint not null comment '用户id',
	contract_code varchar(16) not null comment '合约代码',
	side char default 'B' not null comment '方向',
	lever int default 10 not null comment '合约杠杆',
	update_time bigint(14) default 0 not null comment '更新时间戳',
	constraint user_contract_side_atype
		unique (user_id, contract_code, side)
)
comment '交易员带单杠杆';

create index idx_update_time
	on tb_dealer_lever (update_time);

create table tb_dealer_people_config
(
	id int auto_increment
		primary key,
	min_amount decimal(36,6) default 0.000000 not null comment '最小资金',
	max_amount decimal(36,6) default 0.000000 not null comment '资金数量',
	number int not null comment '所带人数'
)
collate=utf8mb4_bin;

create table tb_dealer_trade_count
(
	trader_id bigint not null comment '交易员id',
	contract_code varchar(20) not null comment '合约代码',
	trade_count bigint default 0 not null comment '交易次数',
	primary key (trader_id, contract_code)
)
comment '交易员交易次数表' collate=utf8mb4_bin;

create table tb_dealer_user
(
	user_id bigint not null
		primary key,
	platform_id int default 0 not null,
	rebate decimal(10,3) default 0.100 not null comment '分佣比例',
	is_status tinyint(1) default 2 not null comment '-1注销，0停用 1正常，2待审核',
	phone varchar(20) default '' not null comment '手机号',
	follow_number int(5) default 0 not null comment '跟单人数',
	limit_number int(5) default 10 not null comment '跟单人数限制',
	create_time datetime default CURRENT_TIMESTAMP not null comment '申请时间',
	audit_time datetime default '1970-01-01 00:00:00' not null comment '审核时间',
	create_manage varchar(100) default '' not null comment '创建人',
	total_amount decimal(36,6) default 0.000000 not null comment '累计分佣',
	total_profit decimal(36,6) default 0.000000 not null comment '累计盈利',
	total_loss decimal(36,6) default 0.000000 not null comment '累计亏损',
	total_profit_count int(10) default 0 not null comment '累计盈利次数',
	total_loss_count int(10) default 0 not null comment '累计亏损次数',
	total_capital decimal(36,6) default 0.000000 not null comment '累计平仓保证金'
)
collate=utf8mb4_bin;

create table tb_dealer_weight_config
(
	id int not null
		primary key,
	day_number int not null comment '评分日期',
	day_profit decimal(10,3) not null comment '近日收益率权重',
	total_profit decimal(10,3) not null comment '累计收益权重',
	winning decimal(10,3) not null comment '胜率权重',
	traders decimal(10,3) not null comment '交易量权重',
	total_profit_rate decimal(10,3) not null comment '累计收益率权重'
)
collate=utf8mb4_bin;

create table tb_error_api
(
	id bigint auto_increment
		primary key,
	own_day date not null comment '所属时间',
	req_id bigint not null comment '请求id',
	req_uri varchar(255) not null comment '请求地址',
	err_code int(10) default 0 not null comment '错误代码',
	err_msg varchar(255) not null comment '错误内容',
	user_id bigint default 0 not null comment '用户id',
	client_os tinyint not null comment '设备类型 （1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	create_time datetime not null comment '创建时间'
)
comment 'API接口错误记录' collate=utf8mb4_bin;

create index idx_err_code
	on tb_error_api (err_code);

create index idx_own_day_code
	on tb_error_api (own_day, err_code);

create table tb_error_external
(
	id bigint auto_increment
		primary key,
	own_day date not null comment '所属时间',
	sys_type tinyint not null comment '系统类型 1-商汤 2-非小号 3-币虎',
	req_id bigint not null comment '请求id',
	req_uri varchar(255) not null comment '请求地址',
	err_code int(10) default 0 not null comment '错误代码',
	err_msg varchar(255) default '' not null comment '错误内容',
	user_id bigint default 0 not null comment '用户id',
	create_time datetime not null comment '创建时间'
)
comment '外部接口错误记录' collate=utf8mb4_bin;

create index idx_err_code
	on tb_error_external (err_code);

create index idx_own_day_sys_code
	on tb_error_external (own_day, sys_type, err_code);

create index idx_sys_type
	on tb_error_external (sys_type);

create table tb_error_sys
(
	id bigint auto_increment
		primary key,
	own_day date not null comment '所属时间',
	err_type tinyint default 0 not null comment '错误类型 1-行情断开',
	err_msg varchar(255) not null comment '错误内容',
	contract_code varchar(20) not null comment '合约代码',
	user_id bigint default 0 not null comment '用户id',
	create_time datetime not null comment '创建时间'
)
comment '系统错误记录' collate=utf8mb4_bin;

create index idx_code
	on tb_error_sys (contract_code);

create index idx_err_type
	on tb_error_sys (err_type);

create index idx_own_day_type_code
	on tb_error_sys (own_day, err_type, contract_code);

create table tb_finance_asset
(
	coin_id int default 0 not null,
	platform_id int not null comment '平台id',
	coin_name varchar(200) charset utf8 default '' not null comment '币种名称',
	type tinyint default 1 not null comment '1资产账户，2交易账户，3-法币账户',
	amount decimal(36,8) not null comment '余额',
	mix_charge decimal(36,8) default 0.******** not null comment '最小充币',
	mix_with decimal(36,8) default 0.******** not null comment '最小提币',
	fee decimal(36,8) default 0.******** not null comment '手续费',
	amount_lock decimal(36,8) not null comment '锁定',
	address varchar(200) charset utf8 default '' not null comment '充币提币地址',
	asset_award decimal(36,8) default 0.******** not null comment 'type为2时代表已实现盈亏'
)
comment '平台资产表' collate=utf8_bin;

create table tb_finance_asset_history
(
	id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null comment '用户Id',
	coin_id int default 0 null comment '币种id',
	coin_name varchar(200) charset utf8 default '' null comment '币种名称',
	amount decimal(36,8) null comment '金额',
	amount_after decimal(36,8) null comment '平台钱余额',
	op_type int default 1 null comment '操作类型id 1-交易手续费 4-提币手续费 8-盈亏收入 9-资金费用 100-平台账户充币 200-平台账户提币 300-转入 301-转入到交易账户 302-转入到资产账户 310-转出 311-转出到交易账户 312-转出到资产账户 320-平台提币手续费 330-邀请佣金奖励 331-代理佣金奖励 332-空投 333-C2C结算，334-C2C其他用途 340-模拟盘补领资产,341 活动奖励',
	remark varchar(255) charset utf8 default '' null comment '操作类型标记',
	op_user_name varchar(255) charset utf8 default '' null comment '操作者用户名',
	source_id bigint default 0 null comment '来源订单id',
	create_at timestamp default CURRENT_TIMESTAMP null,
	mark varchar(255) charset utf8 default '' null comment '备注'
)
comment '平台账户操作记录' collate=utf8_bin;

create table tb_finance_asset_snapshot
(
	coin_id int default 0 not null,
	platform_id int default 0 not null comment '平台id',
	coin_name varchar(200) charset utf8 default '' not null comment '币种名称',
	type tinyint default 1 not null comment '1资产账户，2交易账户',
	amount decimal(36,8) not null comment '余额',
	mix_charge decimal(36,8) null comment '最小充币',
	mix_with decimal(36,8) null comment '最小提币',
	fee decimal(36,8) null comment '手续费',
	amount_lock decimal(36,8) not null comment '锁定',
	address varchar(200) charset utf8 default '' null comment '充币提币地址',
	own_at date null,
	asset_award decimal(36,8) default 0.******** not null
)
comment '平台资产表' collate=utf8_bin;

create table tb_finance_c2c_history
(
	id int auto_increment
		primary key,
	user_id bigint not null comment '用户Id',
	coin_id int default 0 null comment '币种id',
	coin_name varchar(200) charset utf8 default '' null comment '币种名称',
	amount decimal(36,8) null comment '金额',
	amount_after decimal(36,8) null comment '平台钱余额',
	op_type int default 1 null comment '操作类型id,1盈利结算  333-C2C结算，334-C2C其他用途',
	remark varchar(255) charset utf8 default '' null comment '标记内容',
	op_user_name varchar(255) charset utf8 default '' null comment '操作者用户名',
	source_id bigint default 0 null comment '来源订单id',
	mark varchar(255) charset utf8 default '' null comment '备注',
	create_at timestamp default CURRENT_TIMESTAMP null,
	platform_id int default 0 null
)
comment 'c2c法币账户资金明细' collate=utf8_bin;

create index idx_create_time
	on tb_finance_c2c_history (create_at);

create index idx_user_id
	on tb_finance_c2c_history (user_id);

create table tb_finance_capital_history
(
	id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台地',
	user_id bigint not null comment '用户Id',
	coin_id int default 0 null comment '币种id',
	coin_name varchar(200) charset utf8 default '' null comment '币种名称',
	amount decimal(36,8) null comment '金额',
	amount_after decimal(36,8) null comment '平台钱余额',
	op_type int default 1 null comment '操作类型id,1盈利结算',
	remark varchar(255) charset utf8 default '' null comment '标记内容',
	op_user_name varchar(255) charset utf8 default '' null comment '操作者用户名',
	source_id bigint default 0 null comment '来源订单id',
	mark varchar(255) charset utf8 default '' null comment '备注',
	create_at timestamp default CURRENT_TIMESTAMP null
)
comment '平台交易账户历史记录' collate=utf8_bin;

create table tb_follow_account
(
	account_id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null,
	currency_id int not null,
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	lock_amount decimal(36,6) default 0.000000 not null comment '锁定金额',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	total_proceeds decimal(36,6) default 0.000000 not null comment '累计净收益',
	total_principal decimal(36,6) default 0.000000 not null comment '累计开仓使用本金',
	total_close_principal decimal(36,6) default 0.000000 not null comment '累计平仓使用本金',
	day_profit decimal(36,6) default 0.000000 not null comment '当日盈亏',
	day_principal decimal(36,6) default 0.000000 not null comment '当日使用本金',
	constraint Uk_uid_cid
		unique (user_id, currency_id)
)
comment '用户交易账户' collate=utf8_bin;

create table tb_follow_account_history
(
	id bigint not null comment '流水编号'
		primary key,
	user_id bigint(11) not null comment '用户id',
	platform_id int(10) default 0 not null comment '平台id',
	position_id bigint default 0 not null comment '持仓id',
	currency_id int not null comment '币种id',
	currency_name varchar(16) not null comment '币种',
	balance decimal(18,6) not null comment '账户资产',
	available decimal(36,8) not null comment '可用资产',
	type int not null comment '1：开仓手续费 2：资金费用 4：从资产账户转入 8：划转到资产账户  16：平仓盈亏  32：平仓手续费 64:模拟补充资金 128：调整保证金 256:预扣佣金 512：佣金退款 1024：跟单佣金收入',
	amount decimal(36,6) not null comment '数量',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	ip_address varchar(64) default '' not null comment '客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	margin decimal(36,6) not null comment '仓位保证金',
	account_rights decimal(36,8) default 0.******** not null
)
comment '用户跟单账户财务记录' collate=utf8_bin;

create index idx_created_time
	on tb_follow_account_history (create_time);

create index idx_position_id
	on tb_follow_account_history (position_id);

create index idx_type
	on tb_follow_account_history (type);

create table tb_follow_config
(
	id int not null
		primary key,
	rebate decimal(10,3) default 0.000 not null comment '默认分佣比例',
	min_multiple decimal(10,3) default 0.000 not null comment '最小下单倍数',
	max_multiple decimal(10,3) default 0.000 not null comment '最大下单倍数',
	min_number int default 0 not null comment '最小下单量',
	max_number int default 0 not null comment '最大下单量'
)
comment '跟单者全局配置表' collate=utf8mb4_bin;

create table tb_follow_position
(
	id bigint auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null,
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	price decimal(36,8) not null comment '持仓均价',
	volume int not null comment '持仓张数',
	force_price decimal(36,8) not null comment '强平价',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价
',
	stop decimal(36,8) default 0.******** not null comment '止损价
',
	trader_uid bigint default 0 not null comment '交易员uid',
	follow_position_id bigint default 0 not null comment '跟随上级订单id',
	lever int not null comment '杠杆',
	init_margin decimal(36,6) default 0.000000 not null comment '初始保证金',
	margin decimal(36,6) not null comment '保证金余额（起始保证金+手续费+调整）',
	adjust_margin decimal(36,6) default 0.000000 not null comment '调整保证金',
	float_profit decimal(36,6) default 0.000000 not null comment '未实现盈亏',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	profit_ratio decimal(11,4) not null comment '盈亏率',
	margin_ratio decimal(11,6) not null comment '保证金率',
	contract_index decimal(36,8) not null comment '合约指数',
	buy_price decimal(36,8) not null comment '买入价',
	sell_price decimal(36,8) not null comment '卖出价',
	commission decimal(36,6) not null comment '手续费',
	funding_amount decimal(36,6) default 0.000000 not null comment '累计资金费用',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
comment '持仓数据' collate=utf8_bin;

create index IDX_USR_CODE
	on tb_follow_position (user_id, contract_code, side);

create table tb_follow_position_history
(
	id bigint not null
		primary key,
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null,
	contract_code varchar(16) not null comment '合约代码',
	close_order_type tinyint not null comment '平仓类型',
	side char not null comment 'B买S卖',
	open_price decimal(36,8) not null comment '开仓价',
	volume int not null comment '张数',
	trader_uid bigint default 0 not null comment '交易员uid',
	close_price decimal(36,8) not null comment '平仓价',
	follow_position_id bigint default 0 not null comment '跟随上级订单id',
	lever int not null comment '杠杆',
	init_margin decimal(36,6) default 0.000000 not null comment '初始保证金',
	close_profit decimal(36,8) not null comment '平仓盈亏',
	open_commission decimal(36,6) not null comment '开仓手续费',
	funding_amount decimal(36,6) default 0.000000 not null comment '资金费用',
	withholding decimal(36,6) not null comment '预扣分佣',
	open_time datetime not null comment '开仓时间',
	close_time datetime not null comment '开仓时间',
	close_commission decimal(36,6) not null comment '平仓手续费'
)
comment '持仓数据' collate=utf8_bin;

create index IDX_USR_CODE
	on tb_follow_position_history (user_id, contract_code, side);

create index idx_close_time
	on tb_follow_position_history (close_time);

create table tb_follow_trade
(
	deal_id bigint not null comment '成交编号',
	platform_id int default 0 not null comment '平台id',
	user_id bigint not null,
	trader_uid bigint default 0 not null comment '交易员uid',
	follow_position_id bigint default 0 not null comment '跟随持仓id',
	order_id bigint not null comment '报单编号',
	position_id bigint not null comment '持仓id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	offset char not null comment 'O: 开仓 C: 平仓',
	price decimal(36,8) not null comment '成交价格',
	volume int not null comment '成交张数',
	trade_value decimal(36,6) not null comment '成交价值',
	commission decimal(36,6) not null comment '手续费',
	back_profit decimal(36,6) not null comment '交易员分佣金额',
	lever int not null comment '杠杆',
	trade_amount decimal(36,6) default 0.000000 not null comment '成交金额',
	profit decimal(36,6) default 0.000000 not null comment '实现盈亏',
	close_profit decimal(36,6) default 0.000000 not null comment '平仓盈亏',
	order_type int default 0 not null comment '0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价（触发止盈时持仓带来的）',
	stop decimal(36,8) default 0.******** not null comment '止损价（触发止损时持仓带来的）',
	trade_time datetime not null comment '成交时间',
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	broke_price decimal(36,6) default 0.000000 not null comment '破产价格',
	buy_price decimal(36,6) default 0.000000 not null comment '买入价',
	sell_price decimal(36,6) default 0.000000 not null comment '卖出价',
	is_trader tinyint(1) not null comment '是否是交易员',
	entrust_order_id bigint default 0 not null comment '委托订单',
	ip_address varchar(64) default '' not null comment '委托客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint default 6 not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	raw_blowing_fee decimal(36,6) default 0.000000 null comment '原始爆仓手续费',
	blowing_fee decimal(36,6) default 0.000000 null comment '爆仓手续费',
	trade_remark int null comment '成交备注 1-穿仓',
	constraint UK_Trade_ID
		unique (deal_id)
)
comment '成交记录' collate=utf8_bin;

create index IDX_Contract_ID
	on tb_follow_trade (contract_code);

create index IDX_Order_ID
	on tb_follow_trade (order_id);

create index IDX_Side
	on tb_follow_trade (side);

create index IDX_Trade_Time
	on tb_follow_trade (trade_time);

create index IDX_User_ID
	on tb_follow_trade (user_id);

alter table tb_follow_trade
	add primary key (deal_id);

create table tb_funding_fee
(
	contract_code varchar(16) not null comment '合约代码',
	coin_id int not null,
	coin_name varchar(8) not null comment '保证金币种',
	funding_rate decimal(11,6) not null comment '资金费率',
	funding_type tinyint(1) default 0 not null comment '1带单 0 非带单',
	`interval` int default 8 not null comment '时间间隔',
	buy_to_sell decimal(36,6) not null comment '多向空支付费用',
	sell_to_buy decimal(36,6) not null comment '空向多支付费用',
	total_amount decimal(36,6) not null comment '账户总资产',
	created_by datetime not null comment '触发时间',
	fee_id int auto_increment
		primary key,
	position_value_diff decimal(36,6) not null comment '持仓价值差值'
)
comment '资金费用扣除' collate=utf8_bin;

create index idx_contract_code
	on tb_funding_fee (contract_code);

create index idx_create_by
	on tb_funding_fee (created_by);

create table tb_funding_fee_detail
(
	user_id bigint default 0 not null,
	contract_code varchar(16) not null comment '合约代码',
	coin_id int not null,
	coin_name varchar(8) not null comment '结算币种',
	funding_rate decimal(11,6) not null comment '资金费率',
	amount decimal(36,6) not null comment '扣除或奖励费用',
	before_balance decimal(36,6) not null comment '扣除前 账户资产',
	before_close_profit decimal(36,6) not null comment '扣除前 已实现盈亏',
	after_balance decimal(36,6) not null comment '扣除后 账户资产',
	after_close_profit decimal(36,6) not null comment '扣除后 已实现盈亏',
	position_value_diff decimal(36,6) default 0.000000 null comment '持仓价值差值',
	created_by datetime not null comment '触发时间',
	detail_id int auto_increment
		primary key,
	funding_type tinyint not null comment '0-非带单账户 1-带单'
)
comment '资金费用明细' collate=utf8_bin;

create index IDX_Coin_ID
	on tb_funding_fee_detail (coin_id);

create index IDX_Contract_Code
	on tb_funding_fee_detail (contract_code);

create index IDX_User_ID
	on tb_funding_fee_detail (user_id);

create index idx_order_by
	on tb_funding_fee_detail (created_by);

create table tb_funding_rate_history
(
	contract_code varchar(16) not null comment '合约代码',
	funding_rate decimal(11,6) not null comment '当期资金费率',
	estimated_rate decimal(11,6) not null comment '预测资金费率',
	created_by datetime not null comment '时间',
	constraint idx_code_date
		unique (contract_code, created_by)
)
comment '资金费率数据' collate=utf8_bin;

create table tb_hedge_asset
(
	history_id int auto_increment
		primary key,
	group_id int not null comment '对冲组id',
	hedge_account varchar(16) not null comment '对冲子账号',
	contract_account varchar(16) not null comment '合约账户',
	available decimal(36,8) not null comment '可用数量',
	used_amount decimal(36,8) not null comment '已用数量',
	balance decimal(36,8) not null comment '账户权益',
	close_profit decimal(36,8) not null comment '已实现盈亏',
	unclose_profit decimal(36,8) not null comment '未实现盈亏',
	lock_amount decimal(36,8) not null comment '冻结数量',
	margin_ratio decimal(11,4) not null comment '保证金率',
	maintenance_margin_ratio decimal(11,4) not null comment '维持保证金率',
	created_at datetime not null
)
comment '对冲账号资产' collate=utf8_bin;

create index IDX_DT
	on tb_hedge_asset (created_at, contract_account, hedge_account);

create table tb_hedge_caution_receiver
(
	id int auto_increment
		primary key,
	receiver varchar(64) not null comment '预警通知接收地址(邮箱或国内手机)'
)
comment ' 对冲预警邮件设置' collate=utf8_bin;

create table tb_hedge_config
(
	config_id int auto_increment
		primary key,
	group_id int not null comment '对冲组id',
	hedge_account varchar(16) not null comment '对冲子账号',
	created_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	updated_at datetime default CURRENT_TIMESTAMP not null comment '修改时间',
	api_status int default 1 not null comment 'api状态,0-失败，1-成功',
	public_key varchar(1024) null comment '公钥',
	private_key varchar(1024) null comment '私钥',
	hint_phrase varchar(1024) null comment '短语',
	order_by int default 1 not null comment '排序',
	constraint IDX_ACCOUNT
		unique (hedge_account)
)
comment '对冲账号配置' collate=utf8_bin;

create table tb_hedge_contract_config
(
	config_id int auto_increment
		primary key,
	group_id int not null comment '对冲组id',
	hedge_account varchar(16) not null comment '对冲账户',
	lever int not null comment '杠杆',
	margin_direction int not null comment '保证金方向0：正向 1：反向',
	contract_type int not null comment '0: 永续合约  1: 交割合约',
	account_type int not null comment '账户模式 0: 全仓 1：逐仓',
	contract_code varchar(16) not null comment '对冲的合约代码',
	created_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	hedge_status int default 0 null comment '对冲状态，0-辅助对冲，1-主对冲',
	max_position_vol int default 4200 not null comment '最大持仓张数',
	min_available decimal(36,8) default 200.******** not null comment '最少可用数量阈值USDT',
	margin_ratio decimal(11,4) default 40.0000 not null comment '保证金率预警值',
	notice_switch int default 1 not null comment '1: 开 0:关',
	risk_exposure decimal(36,8) default 0.******** not null comment '风险敞口币值',
	min_exposure_ratio decimal(16,8) default 0.******** not null comment '最小敞口保留比例',
	hedge_ratio decimal(16,8) default 0.******** not null comment '对冲比例',
	hedging_adjust decimal(16,8) default 0.******** not null comment '对冲下单价格调整比例'
)
comment '对冲合约配置' collate=utf8_bin;

create index IDX_ACCOUNT
	on tb_hedge_contract_config (hedge_account);

create table tb_hedge_group
(
	group_id int auto_increment comment '组id'
		primary key,
	group_name varchar(255) not null comment '组名',
	group_info varchar(255) not null comment '说明信息',
	platform_id int default 1 not null comment '对冲平台id 1-okex'
)
comment '对冲组配置表';

create table tb_hedge_group_user
(
	user_id bigint not null comment '用户id'
		primary key,
	group_id int not null comment '对冲组id'
);

create table tb_hedge_mail
(
	mail_id int auto_increment
		primary key,
	email varchar(64) not null comment '预警邮件',
	ctype int(1) default 1 not null comment '1对冲，2行情'
)
comment ' 对冲预警邮件设置' collate=utf8_bin;

create table tb_hedge_notice
(
	notice_id int auto_increment
		primary key,
	notice_type int null comment '1：资产类 2：交易类 3：API状态类 4:行情类',
	notice_content varchar(256) null comment '预警内容',
	email varchar(64) null comment '预警email',
	notice_status int null comment '1: 成功 0:失败',
	created_by datetime not null,
	hedge_account varchar(16) not null,
	contract_code varchar(16) not null comment '对冲的合约代码'
)
comment '预警记录' collate=utf8_bin;

create index IDX_DT
	on tb_hedge_notice (created_by);

create index IDX_STATUS
	on tb_hedge_notice (notice_type);

create table tb_hedge_order
(
	hedge_id bigint not null
		primary key,
	contract_code varchar(16) not null comment '合约',
	order_id varchar(50) default '' not null comment '对冲平台订单号',
	contract_type int default 0 not null comment '合约类型 0-永续合约 1-交割合约',
	place_type int not null comment '类型 1.开多，2.开空，3.平多，4.平空',
	order_type int not null comment '委托类型，0-普通委托，1-只做maker,2-全部成交或立即取消
3-立即成交并取消剩余 4-市价单',
	state int default 0 not null comment '状态，1.下单成功 3.委托失败',
	amount decimal(16,8) not null comment '下单量',
	created_time datetime not null comment '创建时间'
)
comment '对冲订单表' collate=utf8mb4_bin;

create index idx_order_id
	on tb_hedge_order (order_id);

create table tb_hedge_position
(
	position_id int auto_increment
		primary key,
	hedge_account varchar(16) not null comment '对冲子账户',
	contract_account varchar(16) not null comment '合约账户',
	volume decimal(16,8) not null comment '持仓张数',
	force_price decimal(36,8) not null comment '强平价格',
	long_value decimal(36,8) not null comment '持多价值',
	short_value decimal(36,8) not null comment '持空价值',
	long_price decimal(36,8) not null comment '持多开仓均价',
	short_price decimal(36,8) not null comment '持空开仓均价',
	unclose_profit decimal(36,8) not null comment '未实现盈亏'
)
comment '合约持仓' collate=utf8_bin;

create index idx_contract_code
	on tb_hedge_position (contract_account);

create index idx_hedge_account
	on tb_hedge_position (hedge_account);

create table tb_hedge_position_history
(
	position_id int auto_increment
		primary key,
	hedge_account varchar(16) not null comment '对冲子账号',
	contract_account varchar(16) not null comment '合约账户',
	force_price decimal(36,8) not null comment '强平价格',
	long_value decimal(36,8) not null comment '持多价值',
	short_value decimal(36,8) not null comment '持空价值',
	long_price decimal(36,8) not null comment '持多开仓均价',
	short_price decimal(36,8) not null comment '持空开仓均价',
	unclose_profit decimal(36,8) not null comment '未实现盈亏',
	created_at datetime not null comment '记录时间',
	volume decimal(16,8) default 0.******** not null comment '持仓张数'
)
comment '对冲账号持仓' collate=utf8_bin;

create index IDX_DT
	on tb_hedge_position_history (created_at, hedge_account, contract_account);

create table tb_id_suffix
(
	id mediumint unsigned auto_increment
		primary key,
	id_suffix char(5) not null comment 'id后缀',
	state tinyint unsigned not null comment '状态'
)
comment 'ID后缀表' collate=utf8_bin;

create index IDX_State
	on tb_id_suffix (state);

create table tb_kline_12h
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_12h (contract_code, start_time, end_time);

create table tb_kline_15m
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_15m (contract_code, start_time, end_time);

create table tb_kline_1d
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_1d (contract_code, start_time, end_time);

create table tb_kline_1h
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_1h (contract_code, start_time, end_time);

create table tb_kline_1m
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_1m (contract_code, start_time, end_time);

create table tb_kline_1w
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_1w (contract_code, start_time, end_time);

create table tb_kline_2h
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_2h (contract_code, start_time, end_time);

create table tb_kline_30m
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_30m (contract_code, start_time, end_time);

create table tb_kline_4h
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_4h (contract_code, start_time, end_time);

create table tb_kline_5m
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_5m (contract_code, start_time, end_time);

create table tb_kline_6h
(
	id bigint auto_increment
		primary key,
	contract_code varchar(16) null comment '合约代码',
	start_time bigint default 0 null comment '节点起始时间',
	end_time bigint default 0 null comment '节点结束时间',
	open_price decimal(36,8) default 0.******** null comment '开盘价',
	close_price decimal(36,8) default 0.******** null comment '收盘价',
	high_price decimal(36,8) default 0.******** null comment '最高价',
	low_price decimal(36,8) default 0.******** null comment '最低价',
	volume bigint default 0 null comment '交易量',
	per_value decimal(36,8) default 0.******** not null comment '历史面值'
)
comment 'K线' collate=utf8_bin;

create index IDX_Code_Time
	on tb_kline_6h (contract_code, start_time, end_time);

create table tb_label
(
	id int auto_increment
		primary key,
	label_name varchar(100) default '' null comment '标签名字',
	platform_id int default 0 null comment '平台id',
	status_type tinyint(1) default 1 null comment '1 正常 0无效',
	creat_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
	creat_manage varchar(100) default '' null comment '创建管理员'
)
collate=utf8_bin;

create table tb_label_info
(
	id int auto_increment
		primary key,
	platform_id int null,
	label_id int null comment '标签id',
	contract_code varchar(100) default '' null comment '合约代码',
	max_lever int(4) default 0 null comment '最大杠杆',
	max_order_volume int default 0 null comment '单笔最大下单量',
	min_order_volume int default 0 null comment '单笔最小下单量',
	max_posi_volume int default 0 null comment '最大持仓张数',
	fee decimal(36,8) default 0.******** null comment '手续费',
	funding decimal(36,8) default 0.******** null comment '资金费用',
	slippage decimal(36,8) default 0.******** null comment '滑点价差',
	creat_time datetime default CURRENT_TIMESTAMP null,
	status_stype tinyint(1) default 1 null comment '1开启 0关闭',
	risk_rate decimal(4,2) default 0.00 null comment '风险率加点',
	constraint lablinfoindex
		unique (label_id, contract_code)
)
collate=utf8_bin;

create table tb_legal_order
(
	order_id bigint not null
		primary key,
	coin_id int not null comment '币种id',
	coin_name varchar(20) not null comment '币种名称',
	user_id bigint not null comment '用户id',
	order_type tinyint(1) not null comment '类型 2购买1出售',
	state int(2) not null comment '状态 0待审核(卖单） 1 等待买家付款; 2 卖家确认收款／等待卖家发货; 3用户取消; 4 商家取消; 5 超时取消; 6交易完成(已放币）; 7：补充放款; 8：后台审核拒绝',
	amount decimal(32,8) not null comment '数量',
	legal_amount decimal(32,2) not null comment '法币数量',
	platform_price decimal(32,8) not null comment '汇率',
	balance decimal(32,8) default 0.******** not null comment '剩余账户资产',
	lock_amount decimal(32,8) not null comment '冻结资产',
	avail_amount decimal(32,8) not null comment '可用资产',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	close_time datetime default '1970-01-01 00:00:00' not null comment '完成时间',
	pay_order_id bigint not null comment '第三方交易id',
	ip_address varchar(64) not null comment 'ip地址',
	imei varchar(64) not null comment '设备码',
	order_client tinyint not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	payment_id int default 0 not null comment '用户收款支付方式id',
	payment_type int default 0 not null comment '收款方式，1-银行卡 2-支付宝 3-微信',
	bank_name varchar(20) default '' not null comment '银行开户行',
	bank_branch_name varchar(100) default '' not null comment '银行支行',
	bank_numb varchar(200) default '' not null comment '银行账户/支付宝账户',
	account_holder varchar(50) default '' not null comment '收款人',
	ocr_address varchar(200) default '' null comment '收款二维码',
	audit_manager varchar(200) default '' not null comment '审核员',
	audit_time datetime default '1970-01-01 00:00:00' not null comment '审核时间'
)
collate=utf8mb4_bin;

create index idx_state
	on tb_legal_order (state);

create index idx_user_id
	on tb_legal_order (user_id);

create table tb_legal_stat
(
	stat_id bigint auto_increment
		primary key,
	coin_name varchar(20) not null comment '币种名字',
	day_succ_purchase decimal(64,8) default 0.******** not null comment '当日成功购买数量',
	day_succ_cny decimal(64,2) default 0.00 not null comment '当日成功购买折合cny',
	total_succ_purchase decimal(64,8) default 0.******** not null comment '累计成功购买数量',
	total_succ_cny decimal(64,2) default 0.00 not null comment '累计成功购买折合cny',
	day_succ_order int default 0 not null comment '当天完成订单数量',
	total_succ_order int default 0 not null comment '累计完成订单数量',
	day_bus_cancel int default 0 not null comment '当天商家取消订单数量',
	day_user_cancel int default 0 not null comment '当天用户取消订单数量',
	own_day date null comment '归属日期',
	constraint TBPLATFORMSTATid
		unique (coin_name, own_day)
)
comment '渠道统计表' collate=utf8_bin;

create table tb_margin_currency
(
	currency_id int not null,
	currency_name varchar(16) not null comment '币种名称',
	`precision` int default 0 not null comment '精度（小数位）',
	min_transfer decimal(36,6) default 0.010000 not null comment '资产划转单笔最小数量',
	max_transfer decimal(36,6) default 10000.000000 not null comment '资产划转单笔最大数量',
	status tinyint(1) default 0 not null comment '0：下架 1：正常 ',
	can_transfer tinyint(1) default 1 not null comment '0: 禁止划转  1:支持划转',
	constraint IDX_CurrencyName
		unique (currency_name),
	constraint currency_id_UNIQUE
		unique (currency_id)
)
comment '保证金币种' collate=utf8_bin;

alter table tb_margin_currency
	add primary key (currency_id);

create table tb_market_config
(
	id int auto_increment
		primary key,
	api_name varchar(50) collate utf8_bin null comment '上手名字',
	api_url varchar(100) collate utf8_bin default '' null comment '上手url',
	api_account varchar(100) collate utf8_bin default '' null comment '账号',
	api_pass varchar(100) collate utf8_bin default '' null comment '密码',
	api_status int(1) default 0 null comment '1正常，0不正常',
	contract_codes varchar(255) collate utf8_bin default '' null comment '订阅合约列表,隔开',
	notice_switch int(1) default 1 null comment '订阅开关 1-开，0-关',
	warning_threshold int(2) default 3 null comment '预警阈值（分钟单位）'
)
collate=utf8mb4_bin;

create table tb_market_notice
(
	notice_id int auto_increment
		primary key,
	api_name varchar(50) default 'OKEX' null comment '上手名字',
	notice_type int null comment '1：资产类 2：交易类 3：API状态类 4:行情类',
	notice_content varchar(256) null comment '预警内容',
	email varchar(64) null comment '预警email',
	notice_status int null comment '1: 成功 0:失败',
	created_by datetime not null,
	hedge_account varchar(16) not null,
	contract_code varchar(16) not null comment '合约代码'
)
comment '预警记录' collate=utf8_bin;

create index IDX_DT
	on tb_market_notice (created_by);

create index IDX_STATUS
	on tb_market_notice (notice_type);

create table tb_message
(
	id bigint not null,
	sender_id bigint not null comment '发送者id',
	sender_nickname varchar(255) not null comment '发送者昵称',
	receiver_id bigint not null comment '接收者id',
	category tinyint(4) unsigned default 0 not null comment '类型 1-开仓成功 2-开仓失败 3-平仓成功 4-平仓失败 5-爆仓 6-停止跟随 7-获取返佣 8-审核成功 9-审核失败 10-身份停用 10-身份撤销',
	title varchar(255) default '' not null comment '标题',
	content varchar(1024) not null comment '内容',
	language_type tinyint(4) unsigned not null comment '语言类型 0-中文, 1-英文 2-繁体 3-韩语',
	is_readed tinyint(1) default 0 not null comment '是否已读',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	primary key (id, language_type)
)
comment '消息表' collate=utf8mb4_bin;

create index idx_reveiver_id
	on tb_message (receiver_id);

create table tb_notice
(
	id int auto_increment comment '公告id'
		primary key,
	platform_id int(10) default 1 not null comment '平台id',
	title varchar(50) charset utf8 default '' not null comment '中文标题',
	content text charset utf8 not null comment '中文正文',
	created_at datetime default CURRENT_TIMESTAMP not null comment '发布时间',
	is_top tinyint(1) default 0 not null comment '是否置顶',
	weight double(18,8) default 1.******** not null comment '权重,值越大排位越靠前',
	lang_type tinyint(1) default 0 not null comment '0中文，1英文，2繁体，3韩文，4日文'
)
comment '公告列表' collate=utf8_bin;

create index idx_notice_list_created_at
	on tb_notice (created_at);

create index idx_notice_list_is_top
	on tb_notice (is_top);

create index idx_notice_list_weight
	on tb_notice (weight);

create table tb_order
(
	id bigint not null
		primary key,
	platform_id int default 0 not null comment '平台id',
	position_id bigint default 0 not null comment '持仓id',
	user_id bigint not null,
	contract_code varchar(16) not null comment '合约代码',
	offset char null comment 'O: 开仓 C: 平仓',
	entrust_type int default 0 not null comment '委托类型 0-市价 1-限价',
	mode tinyint default 3 not null comment '委托模式 1-对手价 2-最优3挡 3-最优5挡',
	order_type int default 0 not null comment '0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单',
	side char not null comment '下单方向（B买S卖）平仓单为持仓反方向',
	price decimal(36,8) not null comment '委托价格,市价单为0',
	volume int not null comment '委托张数',
	account_type int not null comment '账户模式 1：全仓 2：逐仓 3:跟单',
	lever int not null comment '杠杆',
	asset_lock decimal(36,8) default 0.******** null comment '资产锁定',
	trade_volume int not null comment '成交张数',
	trade_price decimal(36,8) default 0.******** null comment '成交均价',
	cost_fee decimal(36,8) default 0.******** null comment '成交手续费',
	cost_asset decimal(36,8) default 0.******** null comment '成交资产',
	profit decimal(36,8) default 0.******** not null comment '已实现盈亏',
	state int default 0 not null comment '委托状态 0-默认 100-用户撤销 101-市价未成交撤销 102-后台撤销 200-全部成交 201-部分成交已撤',
	create_time datetime default CURRENT_TIMESTAMP not null comment '委托时间',
	update_time datetime not null comment '更新时间'
)
comment '委托订单' collate=utf8_bin;

create table tb_plan_close_order
(
	plan_close_order_id bigint auto_increment
		primary key,
	position_id bigint not null comment '持仓id',
	user_id bigint not null comment '用户Id',
	platform_id int default 0 not null comment '平台id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	amount int not null comment '平仓张数',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价',
	stop decimal(36,8) default 0.******** not null comment '止损价',
	condition_limit tinyint default 0 not null comment '止盈条件 1-大于等于 2-小于等于',
	condition_stop tinyint default 0 not null comment '止损条件 1-大于等于 2-小于等于',
	account_type tinyint default 1 not null comment '账户模式 1:全仓，2-逐仓 3-跟单',
	create_time datetime not null comment '提交时间',
	lever int null comment '杠杠',
	trigger_price decimal(36,8) default 0.******** null comment '触发价',
	trigger_time datetime null comment '触发时间',
	trigger_status int default 1 not null comment ' 状态 1-未触发 0-取消  2-已触发 3-触发失败 5-平仓撤销',
	trigger_type int default 0 null comment '触发类型，1：计划单 2：止盈单 4：止损单'
)
comment '计划条件平仓' collate=utf8_bin;

create index idx_pid_uid
	on tb_plan_close_order (position_id, user_id);

create index idx_uid_code
	on tb_plan_close_order (user_id, contract_code, side);

create table tb_plan_order
(
	plan_order_id bigint not null comment '计划单id'
		primary key,
	user_id bigint not null comment '用户id',
	contract_code varchar(16) not null comment '合约代码',
	platform_id int default 0 not null comment '平台id',
	side char not null comment 'B买S卖',
	offset char default 'O' not null comment 'O: 开仓 C: 平仓',
	amount int not null comment '下单张数',
	status smallint default 1 not null comment '条件单状态(1: 未触发 0：取消 2：已触发 3: 触发失败）',
	create_time datetime not null comment '提交时间',
	order_time datetime null comment '触发时间',
	cancel_time datetime null comment '取消时间',
	ip_address varchar(64) null comment '委托客户IP',
	imei varchar(64) null comment '设备识别码',
	order_client tinyint not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	trigger_price decimal(36,8) null comment '触发价格',
	`condition` tinyint not null comment '1 >=, 2 <=',
	account_type tinyint not null comment '账户模式 1：全仓 2：逐仓 3：带单',
	lever int not null comment '杠杆',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价格',
	stop decimal(36,8) default 0.******** not null comment '止损价格',
	mode tinyint default 3 not null comment '委托模式 1-对手价 2-最优3挡 3-最优5挡'
)
comment '条件单' collate=utf8_bin;

create index IDX_Contract_Code
	on tb_plan_order (contract_code);

create index IDX_Status
	on tb_plan_order (status);

create index IDX_User_ID
	on tb_plan_order (user_id);

create table tb_platform_info
(
	platform_id int auto_increment
		primary key,
	platform_name varchar(200) default '' not null comment '平台名字',
	down_url varchar(100) default '' not null comment '平台下载url',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	status_type tinyint(1) default 1 null comment '1正常0 失效',
	superadmin varchar(100) default '' null comment '超级管理员账号',
	superadmin_id bigint default 0 null comment '超级管理员uid',
	platform_code varchar(20) default '' null comment '平台邀请码',
	create_manage varchar(255) default '' null
)
collate=utf8_bin;

create table tb_platform_profit
(
	id int auto_increment
		primary key,
	net_profit decimal(36,6) default 0.000000 null comment '净利润',
	trader_commission decimal(36,6) default 0.000000 null comment '交易手续费',
	close_profit decimal(36,6) default 0.000000 null comment '平仓盈亏',
	burst_profit decimal(36,6) default 0.000000 null comment '爆仓盈亏',
	capital_asset decimal(36,6) default 0.000000 null comment '资金费用',
	withdraw_profit decimal(36,6) default 0.000000 null comment '提币手续费',
	returning_amout decimal(36,6) default 0.000000 null comment '返佣清算',
	airdrop_amount decimal(36,6) default 0.000000 null comment '空头',
	bug_loss decimal(36,6) default 0.000000 null comment 'bug损失',
	total_profit decimal(36,6) default 0.000000 null,
	ctype int(1) default 1 null comment '1月，2天',
	own_at varchar(50) collate utf8_bin null comment '归属日志',
	constraint indxe_uoun
		unique (own_at)
)
collate=utf8mb4_bin;

create table tb_position
(
	id bigint not null
		primary key,
	user_id bigint not null,
	platform_id int default 0 not null comment '平台id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	price decimal(36,8) not null comment '持仓均价',
	volume int not null comment '持仓张数',
	force_price decimal(36,8) not null comment '强平价',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价
',
	stop decimal(36,8) default 0.******** not null comment '止损价
',
	account_type int not null comment '账户模式 1：全仓 2：逐仓',
	lever int not null comment '杠杆',
	init_margin decimal(36,6) default 0.000000 not null comment '初始保证金',
	margin decimal(36,6) not null comment '保证金余额（起始保证金+手续费+调整）',
	adjust_margin decimal(36,6) default 0.000000 not null comment '调整保证金',
	float_profit decimal(36,6) default 0.000000 not null comment '未实现盈亏',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	profit_ratio decimal(11,4) not null comment '盈亏率',
	margin_ratio decimal(11,6) not null comment '保证金率',
	contract_index decimal(36,8) not null comment '合约指数',
	buy_price decimal(36,8) not null comment '买入价',
	sell_price decimal(36,8) not null comment '卖出价',
	commission decimal(36,6) not null comment '手续费',
	constraint IDX_USR_CODE
		unique (user_id, contract_code, side)
)
comment '持仓数据' collate=utf8_bin;

create table tb_position_snapshot
(
	id bigint not null,
	user_id bigint not null,
	platform_id int default 0 not null comment '平台id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	price decimal(36,8) not null comment '持仓均价',
	volume int not null comment '持仓张数',
	force_price decimal(36,8) not null comment '强平价',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价
',
	stop decimal(36,8) default 0.******** not null comment '止损价
',
	account_type int not null comment '账户模式 1：全仓 2：逐仓',
	lever int not null comment '杠杆',
	init_margin decimal(36,6) default 0.000000 not null,
	margin decimal(36,6) not null comment '起始保证金',
	funding_fee decimal(36,6) default 0.000000 not null comment '资金费用',
	float_profit decimal(36,6) default 0.000000 not null comment '未实现盈亏',
	adjust_margin decimal(36,6) default 0.000000 not null comment '调整保证金',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	profit_ratio decimal(11,4) not null comment '盈亏率',
	margin_ratio decimal(11,6) not null comment '保证金率',
	contract_index decimal(36,8) not null comment '合约指数',
	buy_price decimal(36,8) not null comment '买入价',
	sell_price decimal(36,8) not null comment '卖出价',
	commission decimal(36,8) null,
	own_at date null,
	constraint IDX_USR_CODE
		unique (user_id, contract_code, side, own_at)
)
comment '持仓数据' collate=utf8_bin;

create table tb_profit_loss
(
	id int auto_increment
		primary key,
	user_id bigint null,
	total_profit decimal(36,6) default 0.000000 null,
	acc_profit decimal(36,6) default 0.000000 null,
	margn_profit decimal(36,6) default 0.000000 null,
	created_time datetime default CURRENT_TIMESTAMP null
)
collate=utf8_bin;

create table tb_share_image
(
	id int(10) auto_increment
		primary key,
	image_background varchar(128) not null comment '背景图',
	title_color char(8) not null comment '标题文字颜色',
	title_vertical smallint(5) default 0 not null comment '标题垂直位置',
	content_color char(8) not null comment '内容文字颜色',
	share_type tinyint(3) default 0 not null comment '分享类型 0-持仓分享 1-成交记录分享',
	level_type tinyint(3) not null comment '等级类型 -3至3',
	language_type tinyint(3) default 0 not null comment '语言类型 0-中文 1-英文',
	valid tinyint(1) default 1 not null comment '是否有效 0-无效 1-有效',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
comment '分享图片配置表' collate=utf8mb4_bin;

create table tb_share_text
(
	id int(10) auto_increment
		primary key,
	title_text varchar(256) not null comment '标题',
	title_size smallint unsigned not null comment '标题字号',
	share_type tinyint(3) default 0 not null comment '分享类型 0-持仓分享 1-成交记录分享',
	level_type tinyint(3) not null comment '等级类型 -3至3',
	language_type tinyint unsigned default 0 not null comment '语言类型',
	valid tinyint(1) default 1 not null comment '是否有效 0-无效 1-有效',
	create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
collate=utf8mb4_bin;

create table tb_system
(
	id int auto_increment
		primary key,
	key_name varchar(64) null comment '名称',
	content varchar(100) null comment '内容',
	constraint tb_system_key_name_uindex
		unique (key_name)
)
comment '系统配置表';

create table tb_trade
(
	trade_id bigint not null comment '成交编号',
	user_id bigint not null comment '用户id',
	position_id bigint not null comment '持仓id',
	entrust_order_id bigint default 0 not null comment '委托订单id',
	order_id bigint not null comment '条件报单编号',
	platform_id int not null comment '平台id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	offset char not null comment 'O: 开仓 C: 平仓',
	open_avg_price decimal(36,8) not null comment '开仓均价',
	price decimal(36,8) not null comment '成交价格',
	volume int not null comment '成交张数',
	trade_value decimal(36,6) not null comment '成交价值',
	commission decimal(36,6) not null comment '手续费',
	account_type int not null comment '账户模式 1：全仓 2：逐仓',
	lever int not null comment '杠杆',
	trade_amount decimal(36,6) default 0.000000 not null comment '成交金额',
	profit decimal(36,6) default 0.000000 not null comment '实现盈亏',
	close_profit decimal(36,6) default 0.000000 null comment '平仓盈亏',
	order_type int default 0 not null comment '0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价（触发止盈时持仓带来的）',
	stop decimal(36,8) default 0.******** not null comment '止损价（触发止损时持仓带来的）',
	trade_time datetime not null comment '成交时间',
	ip_address varchar(64) default '' not null comment '委托客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint default 6 not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	broke_price decimal(36,6) default 0.000000 null comment '破产价格',
	buy_price decimal(36,6) default 0.000000 null comment '买入价',
	sell_price decimal(36,6) default 0.000000 null comment '卖出价',
	raw_profit decimal(36,6) default 0.000000 not null comment '原始盈亏',
	raw_close_profit decimal(36,6) default 0.000000 not null comment '原始平仓盈亏',
	raw_blowing_fee decimal(36,6) default 0.000000 null comment '原始爆仓手续费',
	blowing_fee decimal(36,6) default 0.000000 null comment '爆仓手续费',
	trade_remark int null comment '成交备注 1-穿仓',
	constraint UK_Trade_ID
		unique (trade_id)
)
comment '成交记录' collate=utf8_bin;

create index IDX_Contract_ID
	on tb_trade (contract_code);

create index IDX_Order_ID
	on tb_trade (order_id);

create index IDX_Side
	on tb_trade (side);

create index IDX_Trade_Time
	on tb_trade (trade_time);

create index IDX_User_ID
	on tb_trade (user_id, contract_code);

alter table tb_trade
	add primary key (trade_id);

create table tb_trade_copy1
(
	trade_id bigint not null comment '成交编号',
	user_id bigint not null comment '用户id',
	position_id bigint not null comment '持仓id',
	entrust_order_id bigint default 0 not null comment '委托订单id',
	order_id bigint not null comment '条件报单编号',
	platform_id int not null comment '平台id',
	contract_code varchar(16) not null comment '合约代码',
	side char not null comment 'B买S卖',
	offset char not null comment 'O: 开仓 C: 平仓',
	open_avg_price decimal(36,8) not null comment '开仓均价',
	price decimal(36,8) not null comment '成交价格',
	volume int not null comment '成交张数',
	trade_value decimal(36,6) not null comment '成交价值',
	commission decimal(36,6) not null comment '手续费',
	account_type int not null comment '账户模式 1：全仓 2：逐仓',
	lever int not null comment '杠杆',
	trade_amount decimal(36,6) default 0.000000 not null comment '成交金额',
	profit decimal(36,6) default 0.000000 not null comment '实现盈亏',
	close_profit decimal(36,6) default 0.000000 null comment '平仓盈亏',
	order_type int default 0 not null comment '0:   市价单 1：计划单 2：止盈单 4：止损单 5：强平单',
	`limit` decimal(36,8) default 0.******** not null comment '止盈价（触发止盈时持仓带来的）',
	stop decimal(36,8) default 0.******** not null comment '止损价（触发止损时持仓带来的）',
	trade_time datetime not null comment '成交时间',
	ip_address varchar(64) default '' not null comment '委托客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint default 6 not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	broke_price decimal(36,6) default 0.000000 null comment '破产价格',
	buy_price decimal(36,6) default 0.000000 null comment '买入价',
	sell_price decimal(36,6) default 0.000000 null comment '卖出价',
	raw_profit decimal(36,6) default 0.000000 not null comment '原始盈亏',
	raw_close_profit decimal(36,6) default 0.000000 not null comment '原始平仓盈亏',
	raw_blowing_fee decimal(36,6) default 0.000000 null comment '原始爆仓手续费',
	blowing_fee decimal(36,6) default 0.000000 null comment '爆仓手续费',
	trade_remark int null comment '成交备注 1-穿仓',
	constraint UK_Trade_ID
		unique (trade_id)
)
comment '成交记录' collate=utf8_bin;

create index IDX_Contract_ID
	on tb_trade_copy1 (contract_code);

create index IDX_Order_ID
	on tb_trade_copy1 (order_id);

create index IDX_Side
	on tb_trade_copy1 (side);

create index IDX_Trade_Time
	on tb_trade_copy1 (trade_time);

create index IDX_User_ID
	on tb_trade_copy1 (user_id, contract_code);

alter table tb_trade_copy1
	add primary key (trade_id);

create table tb_user
(
	id bigint auto_increment comment '记录id(因为用户id不连续,用来保证顺序)'
		primary key,
	user_id bigint not null comment '用户id',
	user_name varchar(64) not null comment '用户名（手机号/邮箱）',
	nickname varchar(100) collate utf8mb4_bin default '' not null comment '中文昵称',
	introduce varchar(300) collate utf8mb4_bin default '' not null comment '中文介绍',
	nickname_en varchar(100) collate utf8mb4_bin default '' not null comment '英文昵称',
	introduce_en varchar(300) collate utf8mb4_bin default '' not null comment '英文介绍',
	avatar varchar(100) default '' not null comment '用户头像',
	login_passwd varchar(64) default '' not null comment '登录密码',
	fund_passwd varchar(64) default '' not null comment '资金密钥',
	totp_secret varchar(64) default '' not null comment 'totp验证器私钥',
	login_verify_phone tinyint(1) default 0 not null comment '登录两步验证手机号开关',
	login_verify_email tinyint(1) default 0 not null comment '登录两步验证邮箱开关',
	login_verify_totp tinyint(1) default 0 not null comment '登录两步验证验证器开关',
	trade_verify_fund tinyint(1) default 0 not null comment '交易资金验证码验证方式 0-永不验证 1-24小时内验证一次 2-永远验证',
	invite_code varchar(8) not null comment '本人邀请码',
	invite_parent varchar(512) default '' not null comment '上级',
	last_login_time datetime default CURRENT_TIMESTAMP not null comment '最后一次登录时间',
	last_login_ip varchar(64) default '' not null comment '最后一次登录IP',
	real_name varchar(241) default '' not null comment '用户姓名',
	card_no varchar(40) default '' not null comment '证件号码',
	verify tinyint(4) unsigned default 0 not null comment '认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过,7重置，8管理员人工通过',
	static_identity tinyint(1) default 0 not null comment '是否固定身份信息 0-用户可修改 1-用户不可修改',
	last_verify_id bigint default 0 not null comment '最后一条认证申请id',
	rest_fund_time datetime default CURRENT_TIMESTAMP not null comment '更新资金密码时间',
	rest_login_time datetime default CURRENT_TIMESTAMP not null comment '更新登录密码时间',
	enable_login tinyint(1) default 1 not null comment '1: 允许登录 0: 禁止登录',
	enable_withdraw tinyint(1) default 1 not null comment '1: 允许提币 0: 禁止提币',
	enable_trade tinyint(1) default 1 not null comment '1: 允许交易 0: 禁止交易',
	enable_simulator tinyint(1) default 0 not null comment '1: 展示模拟盘 0:不展示',
	withdraw_verify tinyint(1) default 0 not null comment '提币活体验证状态: 0-未验证 1-验证中 2-验证失败 3-已验证',
	trade_approved tinyint(1) default 0 not null comment '是否已同意交易协议',
	created_time datetime default CURRENT_TIMESTAMP not null comment '注册时间',
	phone varchar(16) null comment '手机号',
	email varchar(64) null comment '邮件',
	is_agent tinyint(1) default 0 null comment '1是经纪人，0不是',
	agent_time datetime null comment '成为经纪人时间',
	area_code varchar(10) default '86' null comment '手机区号',
	country_code varchar(10) default 'CN' null comment '国家代码',
	follow_approved tinyint(1) default 0 not null comment '是否已同意跟单协议',
	trade_confirm tinyint(1) default 0 not null comment '是否开启交易二次确认',
	platform_id int default 0 not null comment '渠道id',
	label_id int default 0 not null comment '标签id',
	content varchar(200) default '' null comment '备注',
	petname varchar(100) default '' null comment '代理昵称',
	change_style tinyint default 0 not null comment '涨跌样式 0-绿涨红跌 1-红涨绿跌',
	profit_style tinyint default 0 not null comment '收益计算方式 0-以标记价格 1-以成交价格',
	is_open_api tinyint(1) default 0 null comment '是否允许开通api',
	is_child_api tinyint(1) default 0 null comment '是否允许子用户开通api',
	show_agent_ratio tinyint(1) default 0 not null comment '是否在前端显示代理数据 true显示',
	constraint IDX_Email
		unique (email),
	constraint IDX_InviteCode
		unique (invite_code),
	constraint IDX_Phone
		unique (phone),
	constraint IDX_UserName
		unique (user_name),
	constraint uk_user_id
		unique (user_id)
)
comment '用户注册信息表' collate=utf8_bin;

create index IDX_CrestedTime
	on tb_user (created_time);

create index IDX_EnableLogin
	on tb_user (enable_login);

create index IDX_InviteParent
	on tb_user (invite_parent);

create index IDX_Nickname
	on tb_user (nickname);

create index IDX_NicknameEn
	on tb_user (nickname_en);

create table tb_user_account
(
	account_id int auto_increment
		primary key,
	user_id bigint not null,
	currency_id int not null,
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	diff decimal(36,6) default 0.000000 not null comment '待结算差值',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	platform_id int default 0 not null comment '平台id',
	warning_risk_rate decimal(4,2) default 1.00 not null comment '预警风险率',
	account_type tinyint default 1 not null comment '账户模式 1-全仓 2-逐仓'
)
comment '用户交易账户' collate=utf8_bin;

create index Uk_uid_cid
	on tb_user_account (user_id, currency_id);

create table tb_user_account_apikey
(
	id int auto_increment
		primary key,
	platform_id int default 0 not null comment '平台id',
	app_name varchar(255) default '' not null comment '名称',
	user_id bigint not null comment '用户id',
	app_key varchar(45) not null comment '公钥',
	app_secret varchar(256) not null comment '私钥',
	auth_ips varchar(255) default '' not null comment 'ip白名单, 逗号分隔',
	expired_at bigint not null comment '过期时间 秒级时间戳',
	create_time bigint not null comment '创建时间 秒级时间戳',
	state tinyint default 0 not null comment '应用状态:0-正常,-1-删除',
	constraint `key`
		unique (app_key)
);

create index idx_uid
	on tb_user_account_apikey (user_id);

create table tb_user_account_history
(
	id bigint not null comment '流水编号'
		primary key,
	position_id bigint default 0 not null comment '对应订单id',
	platform_id int default 0 not null comment '平台id',
	user_id bigint(11) not null comment '用户id',
	currency_id int not null comment '币种id',
	currency_name varchar(16) not null comment '币种',
	balance decimal(18,6) not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	type int not null comment '1：开仓手续费 2：资金费用 4：从资产账户转入 8：划转到资产账户  16：平仓盈亏  32：平仓手续费 64: 模拟盘补充资产 128-减少保证金 256-预扣佣金 512-佣金退款 1024-佣金收入 2048-交易账户划转到跟单账户 4096-跟单账户划转到交易账户 8192-资产账户划转到跟单账户 16384-跟单账户划转到资产账户',
	amount decimal(36,6) not null comment '数量',
	created_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	ip_address varchar(64) default '' not null comment '客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	margin decimal(36,6) not null comment '仓位保证金',
	account_rights decimal(36,6) not null comment '账户权益'
)
comment '用户交易账户财务记录' collate=utf8_bin;

create index idx_created_time
	on tb_user_account_history (created_time);

create index idx_order_id
	on tb_user_account_history (position_id);

create index idx_type
	on tb_user_account_history (type);

create index idx_uid
	on tb_user_account_history (user_id);

create table tb_user_account_snapshot
(
	user_id bigint not null,
	platform_id int default 0 not null comment '平台id',
	currency_id int not null,
	coin_name varchar(20) not null,
	balance decimal(36,6) default 0.000000 not null comment '账户资产',
	available decimal(36,6) default 0.000000 not null comment '可用余额',
	total_profit decimal(36,6) default 0.000000 not null comment '累计已实现盈亏',
	total_fee decimal(36,6) default 0.000000 not null comment '累计手续费',
	total_info decimal(36,6) default 0.000000 not null comment '累计转入',
	total_out decimal(36,6) default 0.000000 not null,
	account_margin decimal(36,6) default 0.000000 null comment '账户权益',
	own_at date null
)
comment '用户交易账户' collate=utf8_bin;

create table tb_user_bank
(
	id int(10) auto_increment
		primary key,
	user_id bigint not null comment '用户id',
	type tinyint(1) null comment '1 银行卡  2支付宝 3-微信',
	bank_name varchar(20) default '' not null comment '银行卡开户行',
	bank_branch_name varchar(100) default '' not null comment '银行支行',
	bank_numb varchar(200) default '' not null comment '银行卡账号/支付宝账户',
	account_holder varchar(50) default '' null comment '收款人',
	ocr_address varchar(200) default '' null comment '收款二维码地址',
	created_time datetime default CURRENT_TIMESTAMP null
)
comment '收款银行信息表' collate=utf8_bin;

create table tb_user_contract
(
	user_id bigint unsigned not null comment '用户id',
	contract_code varchar(16) not null comment '合约代码',
	side char default 'B' not null comment '方向',
	lever int default 10 not null comment '合约杠杆',
	update_time bigint(14) default 0 not null comment '更新时间戳',
	hold_buy tinyint(1) default 0 not null comment '是否持仓买多',
	hold_sell tinyint(1) default 0 not null comment '是否持仓卖空',
	plan_count int default 0 not null comment '未触发计划单数量',
	constraint user_contract_side_atype
		unique (user_id, contract_code, side)
);

create index idx_update_time
	on tb_user_contract (update_time);

create table tb_user_deposit_address
(
	user_id bigint not null comment '用户id',
	platform_id int(10) default 0 not null comment '平台id',
	coin_id int(10) not null comment '币种id',
	coin_name varchar(150) charset utf8 not null comment '币种名',
	wallet_name varchar(150) charset utf8 not null comment '大钱包币种名',
	protocol varchar(20) charset utf8 default '' not null comment '币种协议',
	address varchar(100) charset utf8 default '' not null comment '充币地址',
	update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
	primary key (user_id, wallet_name)
)
comment '用户充币地址表' collate=utf8_bin;

create index dix_coin_id
	on tb_user_deposit_address (coin_id);

create index idx_address
	on tb_user_deposit_address (address);

create index idx_coin_name
	on tb_user_deposit_address (coin_name);

create table tb_user_err_log
(
	id int auto_increment
		primary key,
	req_id bigint not null comment '请求id',
	user_id bigint not null comment '用户id',
	user_name varchar(50) not null comment '用户名',
	op_type tinyint(2) not null comment '操作类型 0-登录 1-注册 2-找回登录密码 3-登录密码设置 4-资金密码设置 5-用户名设置 6-提现申请 7-谷歌验证码设置 8-市价开仓 9-市价平仓 10-一键平仓 11-止盈止损 12-计划委托 13-计划单撤销 14-法币交易 15-绑定支付方式 16-获取验证码 17-KYC1申请 18-KYC2申请 19-人工KYC申请',
	err_code int not null comment '错误码',
	err_msg varchar(255) not null comment '错误信息',
	ip varchar(64) not null comment 'IP地址',
	imei varchar(64) not null comment '设备序列号',
	device varchar(64) not null comment '设备信息',
	os_type tinyint not null comment '设备类型 1-安卓 2-ios 3-web 4-h5',
	version varchar(20) not null comment '客户端版本',
	created_time datetime default CURRENT_TIMESTAMP not null comment '时间'
)
comment '用户接口错误日志表';

create table tb_user_log
(
	seq_id int auto_increment
		primary key,
	user_id bigint not null,
	op_type tinyint not null comment '操作类型 0-登录 1-注册 2-找回登录密码 3-设置登录密码 4-修改登录密码 5-修改资金密码 6-修改手机号 7-修改邮箱 8-提现申请',
	user_name varchar(64) not null comment '用户名',
	os_type tinyint default 0 not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	device varchar(40) default '' not null comment '设备名称',
	device_id varchar(64) default '' not null comment '设备识别码',
	lang_type tinyint(1) default 0 not null comment '语言类型 0-简中 1-英文 2-繁中 3-韩语 4-日语',
	version varchar(64) default '' not null comment 'app版本',
	ip_address varchar(64) default '' not null comment 'IP地址',
	created_time datetime default CURRENT_TIMESTAMP not null comment '操作时间'
)
comment '用户日志（设备信息）' collate=utf8_bin;

create index IDX_CreatedTime
	on tb_user_log (created_time);

create index IDX_OpType
	on tb_user_log (op_type);

create index IDX_UserId
	on tb_user_log (user_id);

create table tb_user_verify
(
	user_id bigint not null comment '用户id'
		primary key,
	platform_id int null comment '平台id',
	real_name varchar(241) collate utf8_bin default '' not null comment '用户真实名',
	surname varchar(120) collate utf8_bin default '' not null comment '姓名-姓',
	forename varchar(120) collate utf8_bin default '' not null comment '姓名-名',
	id_number varchar(40) collate utf8_bin default '' not null comment '证件号',
	ctype int(4) default 0 not null comment '0-大陆身份证，1-非大陆证件',
	state tinyint default 1 not null comment '认证状态 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-身份认证重置',
	err_code int default 0 not null comment '错误码 0-无错误 1001-证件号码已经存在 1002-证件号码与姓名不匹配 1003-身份信息与活体检测不匹配',
	last_submit datetime default CURRENT_TIMESTAMP not null comment '最后一次提交时间',
	last_manual datetime default CURRENT_TIMESTAMP not null comment '最后一次人工修改时间',
	last_audit datetime default CURRENT_TIMESTAMP not null comment '最后一次审核时间',
	last_operator varchar(50) default '' not null comment '最后一次修改操作人',
	last_auditor varchar(50) default '' not null comment '最后一次审核操作人',
	id_photo varchar(200) collate utf8_bin default '' not null comment '证件照url-人像面',
	emblem_photo varchar(200) default '' not null comment '证件照url-国徽面',
	hold_photo varchar(200) default '' not null comment '手持证件照url'
)
comment '身份认证最新状态表';

create index idx_verify_err_code
	on tb_user_verify (err_code);

create index idx_verify_id_number
	on tb_user_verify (id_number);

create index idx_verify_last_submit
	on tb_user_verify (last_submit);

create index idx_verify_state
	on tb_user_verify (state);

create table tb_user_verify_history
(
	id bigint auto_increment
		primary key,
	user_id bigint(18) not null comment '用户id',
	platform_id int default 0 not null comment '平台id',
	real_name varchar(241) collate utf8_bin default '' not null comment '用户真实名',
	surname varchar(120) collate utf8_bin default '' not null comment '姓名-姓',
	forename varchar(120) collate utf8_bin default '' not null comment '姓名-名',
	id_number varchar(40) collate utf8_bin default '' not null comment '证件号',
	type int(4) default 0 not null comment '0-大陆身份证，1-非大陆证件',
	state tinyint default 1 not null comment '认证状态: 0-未认证 1-身份信息认证中 2-身份信息认证失败 3-身份信息认证通过 4-人脸信息认证中 5-人脸信息认证失败 6-人脸信息认证通过，7-认证重置',
	err_code int default 0 not null comment '错误码',
	err_info varchar(400) default '' not null comment '错误信息',
	create_at datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	identity_update_at datetime default CURRENT_TIMESTAMP not null comment '身份信息认证更新时间',
	face_update_at datetime default CURRENT_TIMESTAMP not null comment '人脸信息认证更新时间',
	operator varchar(50) default '' not null comment '操作人',
	id_photo varchar(200) collate utf8_bin default '' not null comment '证件照url-人像面',
	emblem_photo varchar(200) default '' not null comment '证件照url-国徽面',
	hold_photo varchar(200) default '' not null comment '手持证件照url',
	birthday datetime default CURRENT_TIMESTAMP not null comment '生日',
	age int(3) default 0 not null comment '年龄',
	ckeck_operator varchar(50) collate utf8_bin default '' not null comment '重置人',
	country_code varchar(10) default 'CN' not null comment '国家代码',
	manual tinyint(1) default 0 not null comment '是否手动审核 0-自动 1-手动'
)
comment '用户认证记录表';

create index idx_verify_history_err_code
	on tb_user_verify_history (err_code);

create index idx_verify_history_number
	on tb_user_verify_history (id_number);

create index idx_verify_history_state
	on tb_user_verify_history (state);

create index idx_verify_history_uid
	on tb_user_verify_history (user_id);

create index idx_verify_history_uptime
	on tb_user_verify_history (face_update_at);

create table tb_user_wallet
(
	user_wallet_id int auto_increment
		primary key,
	user_id bigint not null,
	currency_id int not null,
	balance decimal(18,6) not null comment '资产可用数量',
	withdraw_lock decimal(18,6) not null comment '提币冻结数量',
	address varchar(128) charset utf8 default '' not null comment '充币地址',
	platform_id int default 0 not null comment '平台id',
	constraint user_id_coin_id_UNIQUE
		unique (user_id, currency_id)
)
comment '用户资产账户' collate=utf8_bin;

create index IDX_Address
	on tb_user_wallet (address);

create table tb_user_wallet_bill
(
	bill_id bigint not null comment '流水id'
		primary key,
	user_id bigint not null comment '用户标识',
	platform_id int not null comment '平台id',
	type int not null comment '1：充值  2：提现  4：划转到交易账户 8：从交易账户转入 16-邀请佣金奖励 32-代理佣金奖励 64-法币订单到账,128 空投 256-资产账户划转到跟单账户  512-跟单账户划转到资产账户  1024:佣金收入 2048-活动奖励 4096-法币卖出',
	status int default 1 not null comment '充值 1：未到账 2：已到账
  提现 1：申请已提交 2：已到账 3：总后台审核通过 4：总后台拒绝
其它为，5 平台拒绝，6 平台审核通过 ，7初审通过  8初审拒绝  2：已到账',
	currency_id int not null comment '币种id',
	currency_name varchar(16) default '' not null comment '币种名称',
	tag varchar(16) default '' not null comment '币种标签 如usdt的ERC20',
	amount decimal(36,8) not null comment '数量',
	from_addr varchar(128) default '' not null comment '来源地址',
	to_addr varchar(128) default '' not null comment '目标地址',
	tx varchar(128) default '' not null comment '交易哈希',
	confirm_num int default 0 not null comment '充提币确认数',
	balance decimal(36,8) default 0.******** not null comment '用户账户可用数量',
	lock_amount decimal(36,8) default 0.******** not null comment '冻结数量',
	commission decimal(36,8) not null comment '提现手续费',
	remarks varchar(128) default '' not null comment '提现备注',
	created_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
	approver varchar(45) default '' not null comment '审核人',
	approved_time datetime default '1970-01-01 00:00:00' not null comment '提现审核时间',
	platfom_mange varchar(45) default '' not null comment '平台审核人',
	platfom_time datetime default '1970-01-01 00:00:00' not null comment '平台审核时间',
	order_id bigint null comment '充值为空
提现大钱包CWSId
其它为定单编号',
	ip_address varchar(64) default '' not null comment '委托客户IP',
	imei varchar(64) default '' not null comment '设备识别码',
	order_client tinyint default 6 not null comment '委托客户端（1: android 2: iOS 3: WEB 4: H5 5: open_api 6: 系统自动)',
	constraint IDX_OrderID_Type
		unique (order_id, type)
)
comment '用户资产账户流水' collate=utf8_bin;

create index IDX_CreatedTime
	on tb_user_wallet_bill (created_time);

create index IDX_Status
	on tb_user_wallet_bill (status);

create index IDX_Type
	on tb_user_wallet_bill (type);

create index IDX_UserId
	on tb_user_wallet_bill (user_id);

create table tb_version
(
	version_id int auto_increment
		primary key,
	platform_id int(10) default 0 not null comment '平台id',
	os_type tinyint not null comment '客户端 1: android 2:ios',
	lang_type tinyint default 0 not null comment '语言类型 0-中文 1-英文 2-繁体 3-韩语',
	content varchar(256) default '' not null comment '更新文案',
	version int default 0 not null comment '数字版本号',
	version_string varchar(32) default '' not null comment '文字版本号',
	force_upgrade tinyint(1) default 0 not null comment '是否强制更新',
	link varchar(256) default '' not null comment '下载页面',
	url varchar(256) default '' not null comment '下载链接',
	base_url varchar(255) default '' not null comment '本地下载',
	created_time datetime default CURRENT_TIMESTAMP not null comment '上线时间',
	valid tinyint(1) default 1 not null comment '是否生效 0-无效 1-有效',
	create_admin varchar(255) default '' not null comment '创建人',
	constraint IDX_VERSION
		unique (platform_id, os_type, lang_type, version)
)
collate=utf8_bin;

create index IDX_VerStr
	on tb_version (version_string);

create table tb_version_logs
(
	id int auto_increment
		primary key,
	min_version varchar(50) default '' not null comment '最小版本',
	max_version varchar(50) default '' not null comment '最大版本',
	create_admin varchar(255) default '' not null comment '创建人',
	created_time datetime default CURRENT_TIMESTAMP not null comment '创建时间'
)
collate=utf8mb4_bin;

create table tb_wallet_account_snapshot
(
	currency_id int not null,
	platform_id int not null,
	coin_name varchar(40) not null,
	wallet_balance decimal(18,6) default 0.000000 not null comment '资产可用数量',
	withdraw_lock decimal(18,6) default 0.000000 not null comment '提币冻结数量',
	account_balance decimal(18,6) default 0.000000 not null comment '交易账户资产',
	account_available decimal(18,6) unsigned default 0.000000 not null comment '交易账户剩余',
	account_profit decimal(18,6) default 0.000000 not null comment '交易账户权益',
	own_at date not null,
	constraint user_id_coin_id_UNIQUE
		unique (own_at, currency_id, platform_id)
)
comment '用户资产账户' collate=utf8_bin;

create table tb_withdraw_address
(
	address_id int not null
		primary key,
	user_id bigint null comment '用户标识',
	currency_id int null comment '币种标识',
	addr varchar(128) null comment '提币地址',
	addr_alias varchar(32) null comment '地址别名',
	constraint IDX_UID_ALIAS
		unique (user_id, addr_alias),
	constraint IDX_UID_COIN_ADDR
		unique (user_id, currency_id, addr)
)
comment '提币地址' collate=utf8_bin;

create definer = root@`%` procedure insert_leader_day(IN _count int, IN _start datetime)
BEGIN
    DECLARE counter INT DEFAULT 0;

		DECLARE uid BIGINT DEFAULT 0;
		DECLARE profit DECIMAL(36,6) DEFAULT 0;
		DECLARE loss DECIMAL(36,6) DEFAULT 0;
		DECLARE get_profit DECIMAL(36,6) DEFAULT 0;
		DECLARE number INT DEFAULT 0;
		DECLARE total_profit DECIMAL(36,6) DEFAULT 0;
		DECLARE total_capital DECIMAL(36,6) DEFAULT 0;
		DECLARE follow_count INT DEFAULT 0;
		DECLARE profit_count INT DEFAULT 0;
		DECLARE loss_count INT DEFAULT 0;
		DECLARE trading_volume DECIMAL(36,6) DEFAULT 0;
		DECLARE own DATE;
		DECLARE create_time TIMESTAMP;
		DECLARE idList CURSOR FOR
			 SELECT user_id FROM tb_user WHERE email REGEXP '10[0-5][0-9][0-9]@qbtest.cn';
		# 循环赋初始值
		DECLARE CONTINUE HANDLER FOR NOT FOUND SET uid=NULL;

		SET own = DATE(_start);
		SET create_time = _start;

		# 打开游标
		OPEN idList;
		# 取值
		FETCH idList INTO uid;
		# 循环判断
		WHILE (uid IS NOT NULL) DO
			WHILE counter < _count DO
				SET profit = ROUND(RAND() * 1e4, 6);
				SET loss = ROUND(RAND() * 1e4, 6);
				SET @tmp = profit-loss;
				IF @tmp > 0 THEN
					SET get_profit = @tmp*0.1;
				END IF;
				SET number = TRUNCATE(RAND() * 1e2, 0);
				SET total_profit = @tmp;
				SET total_capital = ROUND(RAND() * 1e4, 6);
				SET follow_count = TRUNCATE(RAND() * 1e2, 0);
				SET profit_count = TRUNCATE(RAND() * 1e3, 0);
				SET loss_count = TRUNCATE(RAND() * 1e3, 0);
				SET trading_volume = ROUND(RAND() * 1e6, 6);

				# 插入持仓数据
        SET @str = CONCAT("INSERT IGNORE INTO tb_dealer_day_summary(trader_uid, profit, loss, get_profit, number, total_profit, total_capital, follow_count, profit_count, loss_count, trading_volume, own_at, create_time) VALUES (", uid, ",", profit, ",", loss, ",", get_profit, ",", number, ",", total_profit, ",", total_capital, ",", follow_count, ",", profit_count, ",", loss_count, ",", trading_volume, ",'", own, "','", create_time, "');");
				PREPARE es FROM @`str`;
				EXECUTE es;

        SET counter = counter + 1;
    END WHILE;

		SET counter = 0;
		# 赋值下一个游标
		FETCH idList INTO uid;
	END WHILE;
	# 关闭游标
	CLOSE idList;
END;

create definer = root@`%` procedure insert_positions(IN _count int, IN _uid bigint, IN _trader bigint, IN _code varchar(20), IN _side char)
BEGIN
    DECLARE counter INT DEFAULT 0;
		DECLARE uid BIGINT DEFAULT 0;
		DECLARE c_code VARCHAR(20) DEFAULT '';
		DECLARE p_side CHAR(1) DEFAULT '';
    DECLARE max_id BIGINT DEFAULT 0;
		DECLARE loop_flag INT DEFAULT 0;
		SET uid = _uid;
		SET c_code = _code;
		SET p_side = _side;
    SELECT IFNULL(MAX(id),10000000) INTO max_id FROM tb_follow_position WHERE id < 20000000;

    WHILE counter < _count DO
        SET max_id = max_id + 1;
        IF _uid = 0 THEN
            SET uid = ROUND(MOD(RAND() * 1e10, 1e2) + 1e3);
        END IF;
        IF _code = '' THEN
            IF MOD(max_id, 3) = 0 THEN
                SET c_code = 'BTCUSDT';
            ELSEIF MOD(max_id, 3) = 1 THEN
                SET c_code = 'ETHUSDT';
            ELSEIF MOD(max_id, 2) = 1 THEN
                SET c_code = 'BSVUSDT';
            END IF;
        END IF;
        IF _side = '' THEN
            IF MOD(max_id, 2) = 0 THEN
                SET p_side = 'B';
            ELSE
                SET p_side = 'S';
            END IF;
        END IF;

				# 插入持仓数据
        SET @str = CONCAT("INSERT INTO `basecoin`.`tb_follow_position`(`id`, `user_id`, `contract_code`, `side`, `price`, `volume`, `force_price`, `limit`, `stop`, `trader_uid`, `follow_position_id`, `lever`, `init_margin`, `margin`, `adjust_margin`, `float_profit`, `available`, `profit_ratio`, `margin_ratio`, `contract_index`, `buy_price`, `sell_price`, `commission`, `funding_amount`) VALUES (", max_id, ", ", uid, ", '", c_code, "', '", p_side, "', 123.********, 124, 123.********, 0.********, 0.********, ", _trader, ", 0, 10, 0.000000, 123.000000, 0.000000, 0.000000, 0.000000, 123.0000, 123.000000, 123.********, 123.********, 123.********, 123.000000, 0.000000)");
        PREPARE es FROM @`str`;
        EXECUTE es;

				SET loop_flag = MOD(uid, 2) + 1;
				WHILE loop_flag > 0 DO
					# 插入止盈止损数据
					SET @str2 = CONCAT("INSERT INTO `basecoin`.`tb_plan_close_order`(`position_id`, `user_id`, `contract_code`, `side`, `amount`, `limit`, `stop`, `condition_limit`, `condition_stop`, `account_type`, `create_time`) VALUES (", max_id, ", ", uid, ", '", c_code, "', '", p_side, "', 2, 234.********, 23.********, 1, 1, 1, NOW())");
					PREPARE es2 FROM @`str2`;
					EXECUTE es2;
					SET loop_flag = loop_flag - 1;
				END WHILE;

        SET counter = counter + 1;
    END WHILE;
END;

