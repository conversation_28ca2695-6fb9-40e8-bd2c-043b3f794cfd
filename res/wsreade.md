### Basecoin websoket请求文档

接入URL

wss://{localhost}:{port}/ws

eg:ws://127.0.0.1:9903/{project}/v1/ws

#### 数据压缩

暂不进行数据压缩

#### 心跳消息

服务器会向客户端发送ping消息,客户端收到消息后恢复pong消息,此为标准协议,暂不做特殊处理,大部分第三方库种会提供收到心跳处理逻辑，推荐使用第三方库处理

如无法处理，请使用以下自定义心跳，每50s主动向服务发送心跳，此方法为后备方案，一般不建议使用。

```
{
  "action": "heart", //动作:auth,req,sub
  "topic": "heart",
  "data": { 
        "app_id":1,
        "token":"用户token",
        "ts": 时间戳(秒),
        "sign": "签名"
  }
}
```

#### 鉴权(如不需要接收指定用户消息，可忽略掉鉴权)

成功建立与Websocket服务器的连接后，向websocket发送鉴权请求。

```
{
  "action": "auth", //动作:auth,req,sub
  "topic": "auth",
  "data": { 
        "app_id":1,
        "token":"用户token",
        "ts": 时间戳(秒),
        "sign": "签名"
  }
}
```

为简化鉴权,签名方式与api请求签名方式一致. 成功后收到返回:

```
{"action":"auth","topic":"auth","code":0,"ts":1577268301}
```

#### 其他

websocket 使用自定义消息类型，发的消息需包括，action,topic,data字段，具体见以上结构。请勿主动向服务器发送任何本服务不支持的协议，后端有可能会增加安全措施，对不支持的数据结构及协议判断为攻击行为，主动断开服务。

#### 订阅主题

成功建立与Websocket服务器的连接后，Websocket客户端发送如下请求以订阅/取消订阅特定主题. 以下为本项目支持的主题:

###### [1. 初始化订阅/切换交易对](# 实例:初始化订阅/切换交易对)

topic: market.contract.switch

   ```json
   {
  "action": "sub",
  //订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证
  "topic": "market.contract.switch",
  //主题
  "data": {
    //携带数据,不同请求数据不同
    "contract_code": "btc/usdt"
  }
}
   ```

该主题可收到最新成交,深度,合约明细,所有合约价格涨跌幅推送

###### [2. 订阅所有合约价格及涨跌幅](# 实例:订阅所有交易对价格及涨跌幅)

topic: contracts.market

###### [3. 切换深度精度](# 实例:切换深度精度)

    topic: market.depth.switch
    ```json
    {
      "action": "sub", //订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证
      "topic": "market.depth.switch", //主题
      "data": { //携带数据,不同请求数据不同
        "level": 2 //切换等级,切换后,订阅的交易对会收到指定级别的深度数据,级别和qb一致(0-4),0-原始精度,1-合并最后1位 2-合并最后两位 ...
      }
    }
    ```  

订阅成功后,会收到所有交易对价格及涨跌幅推送,推送频率为1分钟推送一次.

订阅后如不在需要改数据,可取消订阅,将action修改为"unSub",发送撤销订阅请求.

##### 实例:初始化订阅/切换交易对

```json
{
  "action": "sub",
  //订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证
  "topic": "market.contract.switch",
  //主题
  "data": {
    //携带数据,不同请求数据不同
    "contract_code": "btc/usdt"
  }
}
```

订阅成功后会返回数据:

```
{
  "action": "sub",
  "topic": "market.contract.switch",
  "code":0, //状态,0-成功
  "err_msg":"",
  "ts":**********, //时间
}
```

之后, 一旦所订阅的主题有更新，Websocket客户端将收到服务器推送的更新消息（push）：

```
{
  "action": "notify",
  "topic": "market.ticker",
  "ts":**********, //时间
  "data": { //不同数据内容不同
        "contract":"eth/usdt",
        .....
  }
}
```

notify数据见推送类型推送类型,请参考以下类型

以下为接收到的推送数据类型:

1. [合约指数推送](#1-合约指数推送)
2. [合约涨跌幅](#2-合约涨跌幅)
3. [所有交易对价格及涨跌幅](#3-所有交易对价格及涨跌幅)
4. [用户消息(需鉴权)](#4-用户消息)
5. [深度数据](#5-深度数据)
6. [用户账户风险率](#6-用户账户风险率)
7. [用户持仓更新通知](#7-用户持仓更新通知)
8. [交易对最新成交](#8-交易对最新成交)
9. [持仓收益及收益率](#9-持仓收益及收益率(废弃))
10. [用户跟单持仓更新通知](#10-用户跟单持仓更新通知)

##### 1 合约指数推送

notify:

```
{
  "action": "notify",
  "topic": "market.price.index",
  "ts":**********, //时间
  "data":{
        "contract_code":"BTCUSDT", //合约名
        "price":"19197.5", //标记指数价格
        "Price":"19197.5", //标记指数价格(需格式化后标记价格)
        "spot_index_price":"19197.4", //现货指数价格
        "ts":1608018244 //时间戳秒
    }
}
客户端请过滤下指数价格/成交价为0的异常数据，不予更新。
成交价格用于下单止盈止损判断，指数价格暂只供参考，成交价绘制kline及行情,最新成交数据请以最新成交价及成交量生成
```

##### 2 合约涨跌幅

notify:

```
{
  "action": "notify",
  "topic": "contract.applies",
  "ts":**********, //时间
  "data": {
             "contract_code": "BTCUSD", // 合约code
             "change_ratio": "12.23", // 当日涨跌幅 12.23%
             "change":"-0.237",//涨幅
             "high_price": "3000",//最高价(请与最新成交比较，取大）
             "low_price": "3000",//最低价（请于最新成交比较，取小）
             "trade_24h":"2999" //24h成交量
            }
}
```

##### 3 所有交易对价格及涨跌幅

notify:

```
{
  "action": "notify",
  "topic": "contracts.market",
  "ts":**********, //时间
  "data": [
      {
    	  "contract_code": "BTCUSD", // 合约code
          "price": "10234.2323", // 最新成交价价格
          "index_price":"200.3",//指数价格
          "change_ratio": "12.23", // 当日涨跌幅 12.23%
          "change":"-0.237",//涨幅
          "buy_count":133321,//看涨人数
          "sell_count":12,//看跌人数
      },
    ]
}
```

##### 4 用户消息

notify:

```
{
  "action": "notify",
  "topic": "message",
  "ts":**********, //时间
  "data": 
      {
    	 "id":1,//消息id
         "sender_id":2,//发送者用户id,0-系统发送
         "sender_nickname":"nickname",//发送者昵称
         "receiver_id":2,//接收人
         "category":1, 消息分类 1-开仓成功 2-开仓失败 3-平仓成功 4-平仓失败 5-爆仓 6-停止跟随 7-获取返佣 8-审核成功 9-审核失败 10-身份停用 11-身份撤销
         "title":"下单成功",
         "content":"",
         "language_type":1,语言id
         "create_time":"2020-11-05T17:44:17.840962+08:00",
         "extra":{ //附加数据
                  
        }
}
```

##### 5 深度数据

备注:切换交易对会收到深度数据推送,如需要修改深度等级,请切换深度精度订阅. 切换交易对后,收到深度等级将恢复默认 订阅深度

```
{
  "action": "sub", //订阅动作,sub-订阅,unSub-取消订阅 req-请求 auth-验证
  "topic": "contracts.depth", //主题
  "data": { 
        "digit":2,精度
  }
}

{
  "action": "sub",
  "topic": "market.contract.switch",
  "code":0, //状态,0-成功
  "err_msg":"",
  "ts":**********, //时间
}
```

notify:

```
{
  "action": "notify",
  "topic": "market.depth",
  "ts":**********, //时间
  "data": { 
        "level":2,
        "contract_code": "BTCUSDT", // 合约code,
        "buy":[
            {
                "price": "39.20", //价格
                "amount": 1000 //数量,张
                "total_amount":1500,//累计张
                }
        ],
        "sell":[
        {
                        "price": "40.20",
                        "amount": 1000
                        "total_amount":1500,
                        }
                ],
          }
}
```

##### 6 用户账户风险率

notify:

```
{
  "action": "notify",
  "topic": "user.account.complex",
  "ts":**********, //时间
  "data": 
      {
    	 "user_id":1,//用户id
         "risk_rate":"100.05",//账户风险率
         "force_list":[ //合约强平价修改
                {
                "contract_code":"BTCUSDT", //合约名称
                "force_price":"0.0" //强平价
                }
          ]
          }
```

##### 7 用户持仓更新通知

notify:

```
{
  "action": "notify",
  "topic": "user.position.update",//收到后更新持仓或刷新列表
  "ts":**********, //时间
  "data": 
      {
    	 "action":"update",//动作,add-添加，update-修改，remove-移除
         "position":{
            "id":1,持仓id
             "contract_code":"BTCUSDT",//合约
             "volume":2 //持仓张数
             "side"："B",//方向
             "price": 234.1231, // 持仓均价
              "lever": 100, // 杠杆倍数
             "commission": 3.412, // 手续费
             "init_margin"：1213.12，//初始保证金
             "margin": 1213.1231, // 保证金余额 注:仓位保证金=起始保证金+手续费++浮动盈亏
       }
}
```

##### 8 交易对最新成交

notify:

```
{
  "action": "notify",
  "topic": "market.ticker",
  "ts":**********, //时间
  "data": { 
        "contract_code":"BTCUSDT",//交易对名称
        "list":[
            {
                "price": 39.20, //价格
                "trade_price":"39.20",//字符串成交价
                "price_cn": "6029.02" //人民币价格
                "amount":100, //成交量,单位张
                "side":"B",//方向 B-买 S-卖
                "ts":*********,//时间
            },
        ]
  }
}
```

##### 9 持仓收益及收益率（废弃）

notify:

```
{
  "action": "notify",
  "topic": "user.position.dynamic",
  "ts":**********, //时间
  "data": { 
        "account_type"：0,//账户类型 0-合约账户 1-跟单账户
        "list":[
            {
                "position_id":1,//持仓id
                "contract_code":"BTCUSDT", //合约名称
                "profit_ratio":"-501.22",//收益率（已乘100）
                "float_profit":"36.720234",//浮动盈亏
            },
        ]
  }
}
```

##### 10 用户跟单持仓更新通知

notify:

```
{
  "action": "notify",
  "topic": "user.follow.position.update",//收到后更新持仓或刷新列表
  "ts":**********, //时间
  "data": 
      {
    	 "action":"update",//动作,add-添加，update-修改，remove-移除
         "position":{
         {
            "id":258194191828393985,持仓id
            "user_id":164820419855511077，//用户id
             "contract_code":"BTCUSDT",//合约
             "volume":2 //持仓张数
             "side"："B",//方向
             "price": "234.1231", // 持仓均价
              "lever": 100, // 杠杆倍数
             "commission": "3.412", // 手续费
             "init_margin"："1213.12"，//初始保证金
             "margin": "1213.1231", // 保证金余额 注:仓位保证金=起始保证金+手续费++浮动盈亏
             "funding_amount"："0",//累计资金费用
             "force_price":"45693.2"，//强平价格
             "trader_uid":1,//交易员用户id,
       }
}
```