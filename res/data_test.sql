INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = 1, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = 2, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = 3, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = -1, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = -2, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 0, `level_type` = -3, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = 1, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = 2, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = 3, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = -1, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = -2, `language_type` = 7, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_image` SET `image_background` = 'https://www.luckyjerry.com/res/share/share_background_de_1654592857.png', `title_color` = '#FFCE00', `title_vertical` = 560, `content_color` = '#333333', `share_type` = 1, `level_type` = -3, `language_type` = 7, `valid` = 1, `create_time` = NOW();

INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 0, `level_type` = 1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 0, `level_type` = 2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 0, `level_type` = 3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 1, `level_type` = 1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 1, `level_type` = 2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '学会做散户的叛徒，就是与庄家为伍', `title_size` = 20, `share_type` = 1, `level_type` = 3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 0, `level_type` = -1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 0, `level_type` = -2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 0, `level_type` = -3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 1, `level_type` = -1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 1, `level_type` = -2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '行情总在绝望中产生', `title_size` = 20, `share_type` = 1, `level_type` = -3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 0, `level_type` = 1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 0, `level_type` = 2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 0, `level_type` = 3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 1, `level_type` = 1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 1, `level_type` = 2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '君子问凶不问吉，高手看盘先看势', `title_size` = 20, `share_type` = 1, `level_type` = 3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 0, `level_type` = -1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 0, `level_type` = -2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 0, `level_type` = -3, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 1, `level_type` = -1, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 1, `level_type` = -2, `language_type` = 0, `valid` = 1, `create_time` = NOW();
INSERT `new_basecoin`.`tb_share_text` SET `title_text` = '老手多忍耐，新手多等待', `title_size` = 20, `share_type` = 1, `level_type` = -3, `language_type` = 0, `valid` = 1, `create_time` = NOW();

# 资产账户加资产
UPDATE tb_user_wallet SET balance=balance+'30' WHERE user_id = 164820419855511077 AND currency_id = 1;
UPDATE tb_user_wallet SET balance=balance+'300' WHERE user_id = 164820419855511077 AND currency_id = 2;
UPDATE tb_user_wallet SET balance=balance+'300000' WHERE user_id = 164820419855511077 AND currency_id = 3;
UPDATE tb_user_wallet SET balance=balance+'3000' WHERE user_id = 164820419855511077 AND currency_id = 4;
UPDATE tb_user_wallet SET balance=balance+'1500' WHERE user_id = 164820419855511077 AND currency_id = 5;
UPDATE tb_user_wallet SET balance=balance+'600000' WHERE user_id = 164820419855511077 AND currency_id = 36;

# 现货增加资产
UPDATE tb_user_account SET balance=balance+'10', available=available+'10' WHERE currency_name='BTC' AND user_id=217945316131872768;
UPDATE tb_user_account SET balance=balance+'200', available=available+'200' WHERE currency_name='ETH' AND user_id=217945316131872768;
UPDATE tb_user_account SET balance=balance+'10000', available=available+'10000' WHERE currency_name='XRP' AND user_id=217945316131872768;

# 用户下级批量增加资产
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'50' WHERE currency_id=1 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'500' WHERE currency_id=2 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'500000' WHERE currency_id=3 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'5000' WHERE currency_id=4 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'2000' WHERE currency_id=5 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');
UPDATE new_basecoin.tb_user_wallet SET balance=balance+'800000' WHERE currency_id=36 AND user_id IN(SELECT user_id FROM tb_user WHERE invite_parent LIKE '.R00001.hE2UxG.Gu28hx%');

# 风险准备金
INSERT INTO new_basecoin.tb_provisions_of_risk ( platform_id, coin_name, own_date, liquidation, over_loss, net_profit, adjust_amount ) SELECT t.platform_id, t.coin_name, DATE(NOW()), SUM(t.blowing_fee), SUM(t.wear_amount), SUM(t.net_profit), 0 FROM (SELECT platform_id,coin_name,blowing_fee,wear_amount,blowing_fee - wear_amount AS 'net_profit' FROM new_basecoin.tb_finance_asset WHERE type = 2 UNION ALL SELECT platform_id,coin_name,blowing_fee,wear_amount,blowing_fee - wear_amount AS 'net_profit' FROM tb_reverse_basecoin.tb_finance_asset WHERE type = 2) AS t GROUP BY t.platform_id, t.coin_name;
