### redis初始化数据
- 设置公共配置
```shell
set bc:server:config:common '{"public_url":"luckyjerry.com","private_url":"luckyjerry.com","h5_notice_page":"pdbit.luckyjerry.com/notice/?id=","max_api_count":1,"is_slider":true,"is_third_kyc":true,"is_phone_register":false,"gesture_interval":0,"trader_lately_position":10,"trader_lately_history":10,"show_follow_users":10,"blowing_rate":"0.05"}'
```

- 设置域名列表
```shell
rpush bc:server:config:hosts:v2 '{"recommend": true,"host_address": "pdapi.luckyjerry.com"}'
```

- 设置风险准备金调整值
```text
hmset bc:conf:riskProvisions:adjustConf "USDT" "10252365.24552562" "BTC" "745.12545211" "ETH" "52322.12566352" "XRP" "37333242.34653811" "LTC" "212363.12525223" "BCH" "12363.12525223" "EOS" "21216363.12525223" "LINK" "11216363.12525223" "ETC" "5216363.12525223"
```

- 设置用户配额
```shell
hset bc:system:quota:conf 1 '{"withdraw_once":"10000","withdraw_daily":"50000","withdraw_basic":"10000","legal_quota_min_cny":"100","legal_quota_min_usdt":"20","legal_quota_cny":"50000","legal_quota_usdt":"7000"}'
```

- 设置全局交易限制
```shell
hset bc:system:traders:key 1 '{"close_opening":false,"close_plans":false,"close_profit":false}'
```

- 设置kyc审核阈值
```shell
set bc:verify:face:threshold 0.6
```

- 初始化用户id列表
```text
使用 toolkit-go库中的idpool生成数据,并执行写入redis
```

- 数据库需要数据的表
```text
help_topic // 辅助查询表,用于crm查询上级代理
tb_about_community // 关于社区信息
tb_activity // 活动表(可选)
tb_coin_wallet_config // 多链钱包配置表
tb_contract // 合约信息表
tb_contract_depth // 合约行情配置表
tb_country_areacode // 国家code表
tb_currency // 币种列表
tb_dealer_people_config // 交易员等级配置表
tb_dealer_weight_config // 交易员排序权重表
tb_follow_config // 跟单者全局配置表
tb_language // 语言配置表
tb_legal_pay_mode // 法币配置表
tb_margin_currency // 保证金币种表
tb_platform_info // 平台信息表
tb_system // 系统配置表
tb_warn_config // 风控配置表
tb_activity_channel // 注册渠道表(可选)
tb_share_image // 分享图片表
tb_share_text // 分享文案表
tb_pay_legal_list // 三方买币法币列表
tb_legal_pay_mode // 三方买币支付方式列表
tb_third_merchants // 三方买币商家列表
```